#!/bin/bash

# 遍历 lib/enums/dx5/ 目录下的所有 dx5_*.dart 文件
for file in lib/enums/dx5/dx5_*.dart; do
  # 获取文件名（不含路径）
  filename=$(basename "$file")
  # 获取不带 dx5_ 前缀和 .dart 后缀的文件名
  base_name=${filename#dx5_}
  base_name=${base_name%.dart}
  
  # 将下划线分隔的名称转换为驼峰命名
  # 例如：decode_mode_type -> DecodeMode
  camel_case=""
  IFS='_' read -ra PARTS <<< "$base_name"
  for part in "${PARTS[@]}"; do
    # 首字母大写
    part="$(tr '[:lower:]' '[:upper:]' <<< ${part:0:1})${part:1}"
    camel_case="$camel_case$part"
  done
  
  # 替换枚举名称
  # 例如：enum DecodeMode -> enum Dx5DecodeMode
  sed -i '' "s/enum $camel_case/enum Dx5$camel_case/g" "$file"
  
  # 替换扩展名称
  # 例如：extension DecodeModeLocalization on DecodeMode -> extension DecodeModeLocalization on Dx5DecodeMode
  sed -i '' "s/extension ${camel_case}Localization on $camel_case/extension ${camel_case}Localization on Dx5$camel_case/g" "$file"
  
  # 替换类中的引用
  # 例如：case DecodeMode.prefix -> case Dx5DecodeMode.prefix
  sed -i '' "s/case $camel_case\./case Dx5$camel_case\./g" "$file"
  
  # 替换静态方法中的引用
  # 例如：static DecodeMode fromValue -> static Dx5DecodeMode fromValue
  sed -i '' "s/static $camel_case fromValue/static Dx5$camel_case fromValue/g" "$file"
  
  # 替换返回值类型
  # 例如：return Convert.fromValue(DecodeMode.values, value, DecodeMode.prefix);
  sed -i '' "s/Convert\.fromValue($camel_case\.values, value, $camel_case\./Convert.fromValue(Dx5$camel_case.values, value, Dx5$camel_case./g" "$file"
  
  # 替换静态属性引用
  # 例如：static List<DecodeMode> get options => DecodeMode.values;
  sed -i '' "s/static List<$camel_case> get options => $camel_case\.values;/static List<Dx5$camel_case> get options => Dx5$camel_case.values;/g" "$file"
  
  # 替换本地化方法中的类型
  # 例如：static List<MapEntry<DecodeMode, String>> getLocalizedEntries
  sed -i '' "s/static List<MapEntry<$camel_case, String>>/static List<MapEntry<Dx5$camel_case, String>>/g" "$file"
  
  # 替换 HiveType 注解中的适配器名称
  # 例如：@HiveType(typeId: HiveAdapterIds.decodeModeTypeId, adapterName: 'DecodeModeTypeAdapter')
  sed -i '' "s/adapterName: '${camel_case}Adapter'/adapterName: 'Dx5${camel_case}Adapter'/g" "$file"
  
  echo "已修改 $file 中的枚举名称"
done

echo "所有文件修改完成！"
