import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../../common/util/i18n.dart';
import '../../../../models/peq_config_model.dart';
import '../../../../theme/color_palettes.dart';
import '../../../../theme/text_styles.dart';
import '../../peq_controller.dart';


/// PEQ设置页面
class PEQSettingsPage extends StatelessWidget {
  final PEQController controller;

  const PEQSettingsPage({
    super.key,
    required this.controller,
  });

  @override
  Widget build(BuildContext context) {
    // 获取屏幕高度，用于确保背景色覆盖整个页面
    final screenHeight = MediaQuery.of(context).size.height;

    return Scaffold(
      // 设置整个脚手架的背景色，确保即使内容不足以填满屏幕，也有正确的背景色
      backgroundColor: ColorPalettes.instance.background,
      appBar: AppBar(
        title: Text(
          l10n.peqSettings,
          style: TextStyles.instance.h2(),
        ),
        backgroundColor: ColorPalettes.instance.card,
        elevation: 0,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back_ios,
            color: ColorPalettes.instance.firstText,
          ),
          onPressed: () => Get.back(),
        ),
      ),
      body: Container(
        // 设置最小高度为屏幕高度，确保即使内容很少，也能填满整个屏幕
        constraints: BoxConstraints(
          minHeight: screenHeight - kToolbarHeight - MediaQuery.of(context).padding.top,
        ),
        // 设置整个页面的背景色
        color: ColorPalettes.instance.background,
        width: double.infinity,
        // 使用SingleChildScrollView确保内容可滚动
        child: SingleChildScrollView(
          physics: const BouncingScrollPhysics(),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Divider(
                height: 1,
                color: ColorPalettes.instance.firstText.withValues(alpha: 0.3),
              ),
              _buildMenuItems(),
              // 添加底部空白区域，确保在滚动时也有足够的背景色
              SizedBox(height: 100),
            ],
          ),
        ),
      ),
    );
  }

  /// 构建菜单项
  Widget _buildMenuItems() {
    return Container(
      // 确保整个菜单区域都使用背景色
      color: ColorPalettes.instance.background,
      width: double.infinity, // 占据全宽
      child: Column(children: [
        // 导入导出部分
        _buildSectionWithHeader(l10n.importExport, [
          _buildImportSection(),
          const SizedBox(height: 16),
          _buildExportSection(),
        ]),

        // 配置管理部分
        _buildSectionWithHeader(
          l10n.configManagement,
          [
            Container(
              margin: const EdgeInsets.symmetric(horizontal: 16),
              child: Obx(() {
                final configs = controller.configs;
                final activeConfigId = controller.activeConfigId.value;

                if (configs.isEmpty) {
                  return Padding(
                    padding: const EdgeInsets.only(top: 8.0, bottom: 16.0),
                    child: Center(
                      child: Text(
                        l10n.noConfigsMessage,
                        style:
                            TextStyle(color: ColorPalettes.instance.secondText),
                      ),
                    ),
                  );
                }

                return Container(
                  decoration: BoxDecoration(
                    color: ColorPalettes.instance.card,
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Column(
                    children: [
                      ListView.builder(
                        // 使用builder而非separated，因为我们已经在每个项目中添加了间距
                        shrinkWrap: true,
                        physics: const NeverScrollableScrollPhysics(),
                        itemCount: configs.length,
                        itemBuilder: (context, index) {
                          final config = configs[index];
                          // 使用Container包裹ListTile以控制背景颜色
                          return Container(
                            decoration: BoxDecoration(
                              color: activeConfigId == config.id
                                  ? ColorPalettes.instance.accentLightest
                                  : ColorPalettes.instance.card,
                              // 使用card颜色作为背景
                              borderRadius: BorderRadius.circular(4),
                            ),
                            margin: const EdgeInsets.symmetric(vertical: 2),
                            child: ListTile(
                              contentPadding:
                                  const EdgeInsets.symmetric(horizontal: 4.0),
                              title: Text(
                                config.name,
                                style: TextStyle(
                                  fontWeight: activeConfigId == config.id
                                      ? FontWeight.bold
                                      : FontWeight.normal,
                                  color: ColorPalettes.instance.firstText,
                                ),
                              ),
                              subtitle: Text(
                                config.description,
                                style: TextStyle(
                                    color: ColorPalettes.instance.secondText),
                              ),
                              trailing: Row(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  // 编辑按钮
                                  IconButton(
                                    icon: Icon(Icons.edit,
                                        color: ColorPalettes.instance.accent),
                                    onPressed: () =>
                                        _showEditConfigDialog(context, config),
                                    tooltip: l10n.editConfig,
                                  ),
                                  // 删除按钮
                                  IconButton(
                                    icon: Icon(Icons.delete,
                                        color: ColorPalettes.instance.error),
                                    onPressed: () => _showDeleteConfigDialog(
                                        context, config),
                                    tooltip: l10n.deleteConfig,
                                  ),
                                ],
                              ),
                              // 不使用ListTile的selected属性，因为我们自己控制背景颜色
                              tileColor: ColorPalettes.instance.transparent,
                              // 确保透明背景，使外层Container的颜色显示
                              onTap: () {
                                // 加载配置
                                controller.switchToConfig(config.id);
                              },
                            ),
                          );
                        },
                      ),
                    ],
                  ),
                );
              }),
            )
          ],
        ),
      ]),
    );
  }

  /// 构建带标题的区域
  Widget _buildSectionWithHeader(String title, List<Widget> children) {
    return Container(
      // 确保每个部分都有正确的背景色
      color: ColorPalettes.instance.background,
      width: double.infinity,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            margin: const EdgeInsets.only(left: 16, top: 16, bottom: 8),
            child: Row(
              children: [
                Text(
                  title,
                  style: TextStyles.instance.h3(
                    fontWeight: FontWeight.w600,
                  ),
                ),
                if (title == l10n.configManagement)
                  IconButton(
                    icon: Icon(
                      Icons.add_circle_outline,
                      color: ColorPalettes.instance.accent,
                      size: 20,
                    ),
                    onPressed: () => _showAddConfigDialog(),
                    padding: EdgeInsets.zero,
                    constraints: const BoxConstraints(),
                    splashRadius: 20,
                    tooltip: l10n.addNewConfig,
                  ),
              ],
            ),
          ),
          ...children,
          Divider(
            height: 24,
            color: ColorPalettes.instance.firstText.withValues(alpha: 0.3),
          ),
        ],
      ),
    );
  }

  /// 显示添加配置对话框
  void _showAddConfigDialog() {
    // 设置对话框背景色为卡片色，确保在暗色模式下也有正确的背景色
    final dialogBackgroundColor = ColorPalettes.instance.card;
    final nameController = TextEditingController();
    final descController = TextEditingController();

    Get.dialog(
      AlertDialog(
        backgroundColor: dialogBackgroundColor,
        title: Text(l10n.addConfig, style: TextStyle(color: ColorPalettes.instance.firstText)),
        content: SizedBox(
          width: 300,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              TextField(
                controller: nameController,
                style: TextStyle(color: ColorPalettes.instance.firstText),
                decoration: InputDecoration(
                  labelText: l10n.configName,
                  hintText: l10n.configNameHint,
                  labelStyle: TextStyle(color: ColorPalettes.instance.secondText),
                  hintStyle: TextStyle(color: ColorPalettes.instance.thirdText),
                  enabledBorder: UnderlineInputBorder(
                    borderSide: BorderSide(color: ColorPalettes.instance.divider),
                  ),
                  focusedBorder: UnderlineInputBorder(
                    borderSide: BorderSide(color: ColorPalettes.instance.accent),
                  ),
                ),
              ),
              const SizedBox(height: 8),
              TextField(
                controller: descController,
                style: TextStyle(color: ColorPalettes.instance.firstText),
                decoration: InputDecoration(
                  labelText: l10n.description,
                  hintText: l10n.configDescriptionHint,
                  labelStyle: TextStyle(color: ColorPalettes.instance.secondText),
                  hintStyle: TextStyle(color: ColorPalettes.instance.thirdText),
                  enabledBorder: UnderlineInputBorder(
                    borderSide: BorderSide(color: ColorPalettes.instance.divider),
                  ),
                  focusedBorder: UnderlineInputBorder(
                    borderSide: BorderSide(color: ColorPalettes.instance.accent),
                  ),
                ),
                maxLines: 2,
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            style: TextButton.styleFrom(
              foregroundColor: ColorPalettes.instance.accent,
            ),
            child: Text(l10n.cancel),
          ),
          ElevatedButton(
            onPressed: () {
              if (nameController.text.trim().isNotEmpty) {
                controller.addNewConfig(
                  nameController.text.trim(),
                  descController.text.trim(),
                );
                Get.back();
              }
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: ColorPalettes.instance.accent,
              foregroundColor: ColorPalettes.instance.pure,
            ),
            child: Text(l10n.add),
          ),
        ],
      ),
    );
  }

  /// 构建导入区域
  Widget _buildImportSection() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Expanded(
                child: _buildActionButton(
                  icon: Icons.file_upload_outlined,
                  label: l10n.importTarget,
                  onPressed: controller.importTarget,
                  primary: true,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildActionButton(
                  icon: Icons.file_upload_outlined,
                  label: l10n.importSourceFR,
                  onPressed: controller.importSourceFR,
                  primary: true,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          _buildImportedFilesList(),
        ],
      ),
    );
  }

  /// 构建导出区域
  Widget _buildExportSection() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      child: _buildActionButton(
        icon: Icons.file_download_outlined,
        label: l10n.exportCombinedFilter,
        onPressed: controller.exportCombinedFilter,
        isFullWidth: true,
        primary: true,
      ),
    );
  }

  /// 构建操作按钮
  Widget _buildActionButton({
    required IconData icon,
    required String label,
    required VoidCallback onPressed,
    bool isFullWidth = false,
    bool primary = false,
  }) {
    final Color buttonColor = primary
        ? ColorPalettes.instance.accent
        : ColorPalettes.instance.secondText;

    return SizedBox(
      width: isFullWidth ? double.infinity : null,
      child: ElevatedButton.icon(
        onPressed: onPressed,
        icon: Icon(
          icon,
          size: 20,
          color: primary ? ColorPalettes.instance.pure : buttonColor,
        ),
        label: Text(
          label,
          style: TextStyles.instance.button(
            color: primary ? ColorPalettes.instance.pure : buttonColor,
            fontWeight: FontWeight.w500,
          ),
        ),
        style: ElevatedButton.styleFrom(
          backgroundColor: primary
              ? ColorPalettes.instance.accent
              : ColorPalettes.instance.card,
          foregroundColor: primary ? ColorPalettes.instance.pure : buttonColor,
          padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
          elevation: primary ? 2 : 1,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
            side: primary
                ? BorderSide.none
                : BorderSide(color: ColorPalettes.instance.divider, width: 1),
          ),
        ),
      ),
    );
  }

  /// 构建已导入文件列表
  Widget _buildImportedFilesList() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 目标文件列表
        Obx(() {
          if (controller.targetFiles.isEmpty) {
            return const SizedBox.shrink();
          }

          return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                l10n.targetFR,
                style: TextStyles.instance.body1().copyWith(
                      fontSize: 13,
                      color: ColorPalettes.instance.secondText,
                    ),
              ),
              const SizedBox(height: 8),
              SizedBox(
                height: 36,
                child: ListView.builder(
                  scrollDirection: Axis.horizontal,
                  itemCount: controller.targetFiles.length,
                  itemBuilder: (context, index) {
                    final file = controller.targetFiles[index];
                    return Padding(
                      padding: const EdgeInsets.only(right: 8),
                      child: Obx(() {
                        final isSelected =
                            controller.selectedTargetFile.value?.id == file.id;
                        return Container(
                          decoration: BoxDecoration(
                            color: isSelected
                                ? ColorPalettes.instance.accent
                                : ColorPalettes.instance.card,
                            borderRadius: BorderRadius.circular(16),
                            border: Border.all(
                              color: isSelected
                                  ? ColorPalettes.instance.accent
                                  : ColorPalettes.instance.divider
                                      .withValues(alpha: 0.2),
                              width: 1,
                            ),
                            boxShadow: [
                              if (isSelected)
                                BoxShadow(
                                  color: ColorPalettes.instance.accent
                                      .withValues(alpha: 0.2),
                                  blurRadius: 4,
                                  offset: const Offset(0, 2),
                                ),
                            ],
                          ),
                          child: InkWell(
                            onTap: () => controller.selectTargetFile(file),
                            borderRadius: BorderRadius.circular(16),
                            child: Padding(
                              padding: const EdgeInsets.symmetric(
                                  horizontal: 12, vertical: 6),
                              child: Row(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  if (isSelected)
                                    Padding(
                                      padding: const EdgeInsets.only(right: 4),
                                      child: Icon(
                                        Icons.check,
                                        size: 16,
                                        color: ColorPalettes.instance.pure,
                                      ),
                                    ),
                                  Text(
                                    file.name,
                                    style: TextStyle(
                                      color: isSelected
                                          ? ColorPalettes.instance.pure
                                          : ColorPalettes.instance.firstText,
                                      fontWeight: isSelected
                                          ? FontWeight.bold
                                          : FontWeight.normal,
                                      fontSize: 13,
                                    ),
                                  ),
                                  const SizedBox(width: 4),
                                  InkWell(
                                    onTap: () => _showDeleteFileDialog(
                                        context, file, 'target'),
                                    child: Icon(
                                      Icons.close,
                                      size: 14,
                                      color: isSelected
                                          ? ColorPalettes.instance.pure.withValues(alpha: 0.8)
                                          : ColorPalettes.instance.error
                                              .withValues(alpha: 0.7),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                        );
                      }),
                    );
                  },
                ),
              ),
            ],
          );
        }),

        // 源频响文件列表
        Obx(() {
          if (controller.sourceFRFiles.isEmpty) {
            return const SizedBox.shrink();
          }

          return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const SizedBox(height: 12),
              Text(
                l10n.sourceFR,
                style: TextStyles.instance.body1().copyWith(
                      fontSize: 13,
                      color: ColorPalettes.instance.secondText,
                    ),
              ),
              const SizedBox(height: 8),
              SizedBox(
                height: 36,
                child: ListView.builder(
                  scrollDirection: Axis.horizontal,
                  itemCount: controller.sourceFRFiles.length,
                  itemBuilder: (context, index) {
                    final file = controller.sourceFRFiles[index];
                    return Padding(
                      padding: const EdgeInsets.only(right: 8),
                      child: Obx(() {
                        final isSelected =
                            controller.selectedSourceFRFile.value?.id ==
                                file.id;
                        return Container(
                          decoration: BoxDecoration(
                            color: isSelected
                                ? ColorPalettes.instance.accent
                                : ColorPalettes.instance.card,
                            borderRadius: BorderRadius.circular(16),
                            border: Border.all(
                              color: isSelected
                                  ? ColorPalettes.instance.accent
                                  : ColorPalettes.instance.divider
                                      .withValues(alpha: 0.2),
                              width: 1,
                            ),
                            boxShadow: [
                              if (isSelected)
                                BoxShadow(
                                  color: ColorPalettes.instance.accent
                                      .withValues(alpha: 0.2),
                                  blurRadius: 4,
                                  offset: const Offset(0, 2),
                                ),
                            ],
                          ),
                          child: InkWell(
                            onTap: () => controller.selectSourceFRFile(file),
                            borderRadius: BorderRadius.circular(16),
                            child: Padding(
                              padding: const EdgeInsets.symmetric(
                                  horizontal: 12, vertical: 6),
                              child: Row(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  if (isSelected)
                                    Padding(
                                      padding: const EdgeInsets.only(right: 4),
                                      child: Icon(
                                        Icons.check,
                                        size: 16,
                                        color: ColorPalettes.instance.pure,
                                      ),
                                    ),
                                  Text(
                                    file.name,
                                    style: TextStyle(
                                      color: isSelected
                                          ? ColorPalettes.instance.pure
                                          : ColorPalettes.instance.firstText,
                                      fontWeight: isSelected
                                          ? FontWeight.bold
                                          : FontWeight.normal,
                                      fontSize: 13,
                                    ),
                                  ),
                                  const SizedBox(width: 4),
                                  InkWell(
                                    onTap: () => _showDeleteFileDialog(
                                        context, file, 'sourceFR'),
                                    child: Icon(
                                      Icons.close,
                                      size: 14,
                                      color: isSelected
                                          ? ColorPalettes.instance.pure.withValues(alpha: 0.8)
                                          : ColorPalettes.instance.error
                                              .withValues(alpha: 0.7),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                        );
                      }),
                    );
                  },
                ),
              ),
            ],
          );
        }),
      ],
    );
  }

  /// 显示编辑配置对话框
  void _showEditConfigDialog(BuildContext context, PEQConfigModel config) {
    // 设置对话框背景色为卡片色
    final dialogBackgroundColor = ColorPalettes.instance.card;
    final nameController = TextEditingController(text: config.name);
    final descController = TextEditingController(text: config.description);

    Get.dialog(
      AlertDialog(
        backgroundColor: dialogBackgroundColor,
        title: Text(l10n.editConfig, style: TextStyle(color: ColorPalettes.instance.firstText)),
        content: SizedBox(
          width: 300,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              TextField(
                controller: nameController,
                style: TextStyle(color: ColorPalettes.instance.firstText),
                decoration: InputDecoration(
                  labelText: l10n.configName,
                  labelStyle: TextStyle(color: ColorPalettes.instance.secondText),
                  enabledBorder: UnderlineInputBorder(
                    borderSide: BorderSide(color: ColorPalettes.instance.divider),
                  ),
                  focusedBorder: UnderlineInputBorder(
                    borderSide: BorderSide(color: ColorPalettes.instance.accent),
                  ),
                ),
              ),
              const SizedBox(height: 8),
              TextField(
                controller: descController,
                style: TextStyle(color: ColorPalettes.instance.firstText),
                decoration: InputDecoration(
                  labelText: l10n.description,
                  labelStyle: TextStyle(color: ColorPalettes.instance.secondText),
                  enabledBorder: UnderlineInputBorder(
                    borderSide: BorderSide(color: ColorPalettes.instance.divider),
                  ),
                  focusedBorder: UnderlineInputBorder(
                    borderSide: BorderSide(color: ColorPalettes.instance.accent),
                  ),
                ),
                maxLines: 2,
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            style: TextButton.styleFrom(
              foregroundColor: ColorPalettes.instance.accent,
            ),
            child: Text(l10n.cancel),
          ),
          ElevatedButton(
            onPressed: () {
              if (nameController.text.trim().isNotEmpty) {
                controller.updateConfigInfo(
                  config.id,
                  name: nameController.text.trim(),
                  description: descController.text.trim(),
                );
                Get.back();
              }
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: ColorPalettes.instance.accent,
              foregroundColor: ColorPalettes.instance.pure,
            ),
            child: Text(l10n.save),
          ),
        ],
      ),
    );
  }

  /// 显示删除配置确认对话框
  void _showDeleteConfigDialog(BuildContext context, PEQConfigModel config) {
    // 设置对话框背景色为卡片色
    final dialogBackgroundColor = ColorPalettes.instance.card;

    Get.dialog(
      AlertDialog(
        backgroundColor: dialogBackgroundColor,
        title: Text(l10n.deleteConfig, style: TextStyle(color: ColorPalettes.instance.firstText)),
        content: Text(l10n.deleteConfigConfirmation(config.name),
          style: TextStyle(color: ColorPalettes.instance.firstText)),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            style: TextButton.styleFrom(
              foregroundColor: ColorPalettes.instance.accent,
            ),
            child: Text(l10n.cancel),
          ),
          ElevatedButton(
            onPressed: () {
              controller.deleteConfig(config.id);
              Get.back();
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: ColorPalettes.instance.error,
              foregroundColor: ColorPalettes.instance.pure,
            ),
            child: Text(l10n.delete),
          ),
        ],
      ),
    );
  }

  /// 显示删除文件确认对话框
  void _showDeleteFileDialog(
      BuildContext context, dynamic file, String fileType) {
    // 设置对话框背景色为卡片色
    final dialogBackgroundColor = ColorPalettes.instance.card;
    final title = fileType == 'target' ? l10n.deleteTargetFile : l10n.deleteSourceFRFile;

    Get.dialog(
      AlertDialog(
        backgroundColor: dialogBackgroundColor,
        title: Text(title, style: TextStyle(color: ColorPalettes.instance.firstText)),
        content: Text(l10n.deleteFileConfirmation(file.name),
          style: TextStyle(color: ColorPalettes.instance.firstText)),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            style: TextButton.styleFrom(
              foregroundColor: ColorPalettes.instance.accent,
            ),
            child: Text(l10n.cancel),
          ),
          ElevatedButton(
            onPressed: () {
              Get.back();
              if (fileType == 'target') {
                controller.deleteTargetFile(file);
              } else {
                controller.deleteSourceFRFile(file);
              }
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: ColorPalettes.instance.error,
              foregroundColor: ColorPalettes.instance.pure,
            ),
            child: Text(l10n.delete),
          ),
        ],
      ),
    );
  }
}
