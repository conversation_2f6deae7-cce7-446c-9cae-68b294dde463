import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:topping_home/theme/color_palettes.dart';
import 'package:topping_home/theme/text_styles.dart';

import '../../common/util/i18n.dart';
import '../../router/routers.dart';
import 'setting_logic.dart';

/// 设置页面
class SettingPage extends StatelessWidget {
  SettingPage({super.key});

  final logic = Get.find<SettingLogic>();
  final state = Get.find<SettingLogic>().state;

  @override
  Widget build(BuildContext context) {
    return Obx(() => AnnotatedRegion<SystemUiOverlayStyle>(
          value: SystemUiOverlayStyle(
            statusBarColor: ColorPalettes.instance.transparent,
            statusBarIconBrightness: ColorPalettes.instance.isDark()
                ? Brightness.light
                : Brightness.dark,
            statusBarBrightness: ColorPalettes.instance.isDark()
                ? Brightness.dark
                : Brightness.light,
          ),
          child: Scaffold(
            backgroundColor: ColorPalettes.instance.card,
            appBar: AppBar(
              backgroundColor: ColorPalettes.instance.card,
              title: Text(
                l10n.setting,
                style: TextStyles.instance.h2(),
              ),
              centerTitle: false,
              elevation: 0,
              iconTheme: IconThemeData(
                color: ColorPalettes.instance.firstText,
              ),
            ),
            body: SingleChildScrollView(
              child: Column(
                children: [
                  Divider(
                    height: 1,
                    color: ColorPalettes.instance.firstText.withValues(alpha: 0.12),
                  ),
                  _buildMenuItems(),
                ],
              ),
            ),
          ),
        ));
  }

  /// 构建菜单项
  Widget _buildMenuItems() {
    return Column(
      children: [
        // 主题切换
        Obx(() => SwitchListTile(
            title: Text(
              state.isDarkMode.value ? l10n.darkMode : l10n.lightMode,
              style: TextStyles.instance.h3(
                fontWeight: FontWeight.w600,
              ),
            ),
            subtitle: Text(
              state.isDarkMode.value ? l10n.darkModeDescription : l10n.lightModeDescription,
              style: TextStyles.instance.body1(
                color: ColorPalettes.instance.secondText,
              ),
            ),
            value: state.isDarkMode.value,
            activeColor: ColorPalettes.instance.accent,
            activeTrackColor: ColorPalettes.instance.accentLightest,
            inactiveTrackColor:
                ColorPalettes.instance.secondText.withValues(alpha: 0.3),
            inactiveThumbColor: ColorPalettes.instance.card,
            secondary: Container(
              padding: EdgeInsets.all(8),
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: state.isDarkMode.value
                    ? ColorPalettes.instance.primary.withValues(alpha: 0.1)
                    : ColorPalettes.instance.secondary.withValues(alpha: 0.1),
              ),
              child: Icon(
                state.isDarkMode.value ? Icons.dark_mode : Icons.light_mode,
                color: state.isDarkMode.value
                    ? ColorPalettes.instance.primary
                    : ColorPalettes.instance.secondary,
                size: 24,
              ),
            ),
            onChanged: (value) {
              logic.toggleTheme();
            })),
        Divider(
            height: 1, color: ColorPalettes.instance.firstText.withValues(alpha: 0.12)),
        // 关于
        Container(
          margin: EdgeInsets.symmetric(horizontal: 16),
          child: ListTile(
            contentPadding: EdgeInsets.zero,
            leading: Icon(
              Icons.info_outline,
              color: ColorPalettes.instance.firstText,
              size: 24,
            ),
            title: Text(
              l10n.about,
              style: TextStyles.instance.h3(
                fontWeight: FontWeight.w600,
              ),
            ),
            trailing: Icon(
              Icons.arrow_forward_ios,
              color: ColorPalettes.instance.firstText,
              size: 16,
            ),
            onTap: () => AppRoutes.jumpPage(AppRoutes.aboutPage),
          ),
        ),
        // 快速开始
        Container(
          margin: EdgeInsets.symmetric(horizontal: 16),
          child: ListTile(
            contentPadding: EdgeInsets.zero,
            leading: Icon(
              Icons.play_circle_outline,
              color: ColorPalettes.instance.firstText,
              size: 24,
            ),
            title: Text(
              l10n.quickStart,
              style: TextStyles.instance.h3(
                fontWeight: FontWeight.w600,
              ),
            ),
            trailing: Icon(
              Icons.arrow_forward_ios,
              color: ColorPalettes.instance.firstText,
              size: 16,
            ),
            onTap: () => logic.onQuickStartTap(),
          ),
        ),
        // 意见反馈
        Container(
          margin: EdgeInsets.symmetric(horizontal: 16),
          child: ListTile(
            contentPadding: EdgeInsets.zero,
            leading: Icon(
              Icons.feedback_outlined,
              color: ColorPalettes.instance.firstText,
              size: 24,
            ),
            title: Text(
              l10n.feedback,
              style: TextStyles.instance.h3(
                fontWeight: FontWeight.w600,
              ),
            ),
            trailing: Icon(
              Icons.arrow_forward_ios,
              color: ColorPalettes.instance.firstText,
              size: 16,
            ),
            onTap: () => AppRoutes.jumpPage(AppRoutes.feedbackPage),
          ),
        ),
        // 自定义背景
        Container(
          margin: EdgeInsets.symmetric(horizontal: 16),
          child: ListTile(
            contentPadding: EdgeInsets.zero,
            leading: Icon(
              Icons.image_outlined,
              color: ColorPalettes.instance.firstText,
              size: 24,
            ),
            title: Text(
              l10n.customBackground,
              style: TextStyles.instance.h3(
                fontWeight: FontWeight.w600,
              ),
            ),
            trailing: Icon(
              Icons.arrow_forward_ios,
              color: ColorPalettes.instance.firstText,
              size: 16,
            ),
            onTap: () => AppRoutes.jumpPage(AppRoutes.customBackgroundPage),
          ),
        ),
      ],
    );
  }
}
