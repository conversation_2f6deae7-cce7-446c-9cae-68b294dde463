#ifndef __COMMON_H__
#define __COMMON_H__
#include "signature_tool.h"
#include <stdint.h>
#define PROTOCOL_VERSION 100
#define SERVICE_UUID "000090FB-0000-1000-8000-00805F9B34FB"
#define WRITE_CHARACTERISTIC_UUID "00008EFA-0000-1000-8000-00805F9B34FB"
#define NOTIFY_CHARACTERISTIC_UUID "00009CF1-0000-1000-8000-00805F9B34FB"
#define VENDOR_ID 0xf4fb
namespace Topping
{
    struct SupportedDevice
    {
        uint16_t vendorId;
        uint16_t productId;
        uint8_t publicKey[ECC_PUB_KEY_SIZE];
    };
    
    extern const uint8_t PRIVATE_KEY[];
    extern const SupportedDevice SUPPORTED_DEVICES[];
}
#endif