#include "common.h"
namespace Topping
{
    const uint8_t PRIVATE_KEY[] = {0xc3, 0x6a, 0x17, 0xa0, 0xcb, 0x9c, 0xe0, 0x1b, 0x66, 0x9b, 0x2a, 0xcc, 0xfd, 0x74, 0x3d, 0x93, 0x1b, 0xce, 0x65, 0x31, 0x2f, 0x1f, 0x62, 0xbe, 0xef, 0xbd, 0xfb, 0x97, 0xd5, 0xec, 0x79, 0x98};
    const SupportedDevice 
    SUPPORTED_DEVICES[] =
        {
            {
                //demo device
                .vendorId = 0x51ef,
                .productId = 0x52ef,
                .publicKey = {0xaa, 0x24, 0x60, 0x74, 0x9e, 0x16, 0x2a, 0x61, 0xe6, 0xe9, 0x82, 0x41, 0x83, 0x81, 0xa4, 0xa2, 0x4b, 0xe1, 0x96, 0x61, 0xa0, 0xca, 0xc8, 0x7e, 0xc0, 0x17, 0xbf, 0xd3, 0x03, 0xea, 0x37, 0x98, 0xad, 0x41, 0x36, 0x63, 0xde, 0x04, 0x36, 0x4a, 0x86, 0x8a, 0xc5, 0xb0, 0x8b, 0x01, 0x9c, 0x3f, 0xfb, 0x5d, 0x7d, 0x3d, 0x1c, 0x96, 0x91, 0x88, 0xb3, 0xd0, 0x4f, 0x02, 0xac, 0x54, 0xcf, 0x07}
            },

            {
                //dx5ii device 0x9a, 0x01, 0xc3, 0x16, 0x67, 0x4c, 0x4c, 0xfd, 0xe3, 0xcc, 0xe4, 0x78, 0x17, 0x3f, 0xe5, 0x16, 0xb1, 0xaf, 0x6b, 0x03, 0xcb, 0x72, 0xfd, 0x33, 0x3d, 0x63, 0x16, 0x32, 0x0a, 0x9d, 0x3f, 0xa4
                .vendorId = 0x5f4b,
                .productId = 0x6ac0,
                .publicKey = {0x9d, 0x98, 0x26, 0xcd, 0x99, 0xde, 0x6c, 0x7a, 0xd0, 0xa6, 0x22, 0x6e, 0x22, 0xe5, 0x60, 0x02, 0x27, 0xe9, 0x2f, 0x61, 0xec, 0x48, 0x6e, 0xbe, 0x01, 0x32, 0x9f, 0xaf, 0x37, 0x7d, 0xa1, 0xa9, 0x5b, 0x1b, 0xe8, 0xd0, 0x70, 0x5d, 0x84, 0x4e, 0x86, 0x6b, 0x0f, 0x8c, 0xe6, 0x09, 0xe2, 0xfa, 0x2b, 0x00, 0x93, 0xef, 0x58, 0x42, 0xc5, 0xfa, 0xe0, 0x8b, 0xb9, 0x1d, 0x55, 0x0f, 0xf9, 0xce}
            },

            {
                //d900 device 0x6c, 0x60, 0xce, 0x78, 0x72, 0x8f, 0x45, 0xef, 0x0e, 0x76, 0x96, 0xcc, 0x35, 0x75, 0x7d, 0x8a, 0x90, 0xc2, 0xae, 0xbe, 0xaf, 0xc6, 0x55, 0x4d, 0x06, 0x2c, 0xa4, 0xe2, 0x8f, 0xd2, 0xf4, 0xfb
                .vendorId = 0xa85e,
                .productId = 0x561d,
                .publicKey = {0xa4, 0x6d, 0x2c, 0x27, 0x3c, 0xfc, 0xb6, 0xd4, 0x42, 0x8c, 0x03, 0x85, 0x4f, 0x5f, 0x6a, 0x59, 0x79, 0xe5, 0x9b, 0xd9, 0x0d, 0x88, 0x00, 0x4c, 0xe3, 0x34, 0x42, 0xe3, 0x91, 0xb2, 0x6f, 0xbc, 0xfd, 0xf9, 0xa4, 0xf7, 0x3f, 0xa4, 0xc8, 0x23, 0xd3, 0xac, 0xab, 0x37, 0x0f, 0x80, 0x46, 0x63, 0x66, 0x3f, 0xc9, 0x90, 0xa3, 0x8e, 0x69, 0x13, 0x6b, 0x9d, 0x05, 0x99, 0xd0, 0x47, 0xdd, 0xce}
            },
            
            {
                .vendorId = 0,
                .productId = 0,
                .publicKey = {0}
            },
    };
}