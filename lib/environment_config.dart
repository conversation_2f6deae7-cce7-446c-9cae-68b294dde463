import 'package:flutter/services.dart';
import 'package:package_info_plus/package_info_plus.dart';

import 'common/util/log_util.dart';

/// 环境配置类，用于获取当前应用的环境信息（开发环境、测试环境或生产环境）
///
/// 提供以下功能：
/// 1. 获取当前环境类型（dev/staging/prod）
/// 2. 获取应用版本信息
/// 3. 获取环境特定的包名（不同环境使用不同的包名）
class EnvironmentConfig {
  /// 单例实例
  static final EnvironmentConfig _instance = EnvironmentConfig._internal();

  /// 获取单例实例
  factory EnvironmentConfig() => _instance;

  /// 私有构造函数
  EnvironmentConfig._internal();

  /// 环境类型常量
  static const String ENV_DEV = 'dev';
  static const String ENV_STAGING = 'staging';
  static const String ENV_PROD = 'prod';

  /// 当前环境
  late String _environment;

  /// 应用版本信息
  late PackageInfo _packageInfo;

  /// 初始化环境配置
  Future<void> init() async {
    // 获取版本信息
    _packageInfo = await PackageInfo.fromPlatform();

    // 从平台渠道获取环境信息
    try {
      const channel = MethodChannel('com.topping.home.flavor_channel');
      _environment = await channel.invokeMethod('getFlavor') ?? ENV_PROD;
    } catch (e) {
      // 如果无法从平台获取，则使用默认值或从dart-define获取
      _environment =
          const String.fromEnvironment('FLAVOR', defaultValue: ENV_PROD);
    }

    Log.i(
        '当前应用环境: $_environment, 版本: ${_packageInfo.version}+${_packageInfo.buildNumber}, 包名: $environmentPackageName');
  }

  /// 获取当前环境
  String get environment => _environment;

  /// 获取应用版本名称
  String get versionName => _packageInfo.version;

  /// 获取应用版本号
  int get versionCode => int.parse(_packageInfo.buildNumber);

  /// 获取应用包名
  String get packageName => _packageInfo.packageName;

  /// 获取环境特定的包名
  /// 根据不同环境返回不同的包名
  /// 开发环境: com.topping.home.dev
  /// 测试环境: com.topping.home.staging
  /// 生产环境: com.topping.home
  String get environmentPackageName {
    if (isDev) {
      return '$packageName.dev';
    } else if (isStaging) {
      return '$packageName.staging';
    } else {
      return packageName;
    }
  }

  /// 判断是否为开发环境
  bool get isDev => _environment == ENV_DEV;

  /// 判断是否为测试环境
  bool get isStaging => _environment == ENV_STAGING;

  /// 判断是否为生产环境
  bool get isProd => _environment == ENV_PROD;

  /// 获取应用显示版本
  String get displayVersion {
    if (isDev) {
      return '$versionName-dev (${_packageInfo.buildNumber})';
    } else if (isStaging) {
      return '$versionName-staging (${_packageInfo.buildNumber})';
    } else {
      return '$versionName (${_packageInfo.buildNumber})';
    }
  }
}
