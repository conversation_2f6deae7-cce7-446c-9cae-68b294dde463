import 'dart:async';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:topping_home/theme/color_palettes.dart';

import '../../../common/util/i18n.dart';
import '../../repositories/user_repository.dart';
import '../../../models/user_entity.dart';
import 'account_binding_state.dart';

/// 账号与绑定页面逻辑
class AccountBindingLogic extends GetxController {
  final AccountBindingState state = AccountBindingState();
  Timer? _timer;

  @override
  void onInit() {
    super.onInit();
    _checkBindingStatus();
  }

  @override
  void onClose() {
    _timer?.cancel();
    state.dispose();
    super.onClose();
  }

  // 检查绑定状态
  void _checkBindingStatus() {
    final user = UserRepository.instance.userEntity.value;
    if (user != null && user.email != null && user.email!.isNotEmpty) {
      state.isBound.value = true;
      state.emailController.text = user.email!;
    }
  }

  // 验证邮箱格式
  bool _validateEmail(String email) {
    if (email.isEmpty) {
      Get.snackbar(l10n.error, l10n.emailEmpty);
      return false;
    }

    final emailRegex = RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$');
    if (!emailRegex.hasMatch(email)) {
      Get.snackbar(l10n.error, l10n.invalidEmail);
      return false;
    }

    return true;
  }

  // 发送验证码
  Future<void> sendVerificationCode() async {
    if (!_validateEmail(state.emailController.text)) return;

    state.isLoading.value = true;
    try {
      // TODO: 调用发送验证码API
      await Future.delayed(const Duration(seconds: 1)); // 模拟网络请求

      // 开始倒计时
      state.countDown.value = 60;
      _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
        if (state.countDown.value > 0) {
          state.countDown.value--;
        } else {
          timer.cancel();
        }
      });

      Get.snackbar(
        l10n.success,
        l10n.verificationCodeSent,
        snackPosition: SnackPosition.TOP,
        backgroundColor: ColorPalettes.instance.success,
        colorText: ColorPalettes.instance.success,
      );
    } catch (e) {
      Get.snackbar(
        l10n.error,
        e.toString(),
        snackPosition: SnackPosition.TOP,
        backgroundColor: ColorPalettes.instance.error,
        colorText: ColorPalettes.instance.error,
      );
    } finally {
      state.isLoading.value = false;
    }
  }

  // 绑定邮箱
  Future<void> bindEmail() async {
    if (!_validateEmail(state.emailController.text)) return;

    if (state.verifyCodeController.text.isEmpty) {
      Get.snackbar(l10n.error, l10n.verificationCodeEmpty);
      return;
    }

    state.isLoading.value = true;
    try {
      final currentUser = UserRepository.instance.userEntity.value;
      if (currentUser == null) {
        throw Exception(l10n.userNotFound);
      }

      // TODO: 验证验证码
      await Future.delayed(const Duration(seconds: 1)); // 模拟网络请求

      // 更新用户信息
      final updatedUser = UserEntity()
        ..id = currentUser.id
        ..userPhone = currentUser.userPhone
        ..password = currentUser.password
        ..nickname = currentUser.nickname
        ..signature = currentUser.signature
        ..avatar = currentUser.avatar
        ..sex = currentUser.sex
        ..birthday = currentUser.birthday
        ..email = state.emailController.text;

      await UserRepository.instance.updateUserEntity(updatedUser);

      state.isBound.value = true;
      Get.back();
      Get.snackbar(
        l10n.success,
        l10n.emailBindSuccess,
        snackPosition: SnackPosition.TOP,
        backgroundColor: ColorPalettes.instance.success,
        colorText: ColorPalettes.instance.success,
      );
    } catch (e) {
      Get.snackbar(
        l10n.error,
        e.toString(),
        snackPosition: SnackPosition.TOP,
        backgroundColor: ColorPalettes.instance.error,
        colorText: ColorPalettes.instance.error,
      );
    } finally {
      state.isLoading.value = false;
    }
  }

  // 解绑邮箱
  Future<void> unbindEmail() async {
    final result = await Get.dialog<bool>(
      AlertDialog(
        title: Text(l10n.unbindEmail),
        content: Text(l10n.unbindEmailConfirm),
        actions: [
          TextButton(
            onPressed: () => Get.back(result: false),
            child: Text(l10n.cancel),
          ),
          TextButton(
            onPressed: () => Get.back(result: true),
            child: Text(l10n.confirm),
          ),
        ],
      ),
    );

    if (result != true) return;

    state.isLoading.value = true;
    try {
      final currentUser = UserRepository.instance.userEntity.value;
      if (currentUser == null) {
        throw Exception(l10n.userNotFound);
      }

      // 更新用户信息
      final updatedUser = UserEntity()
        ..id = currentUser.id
        ..userPhone = currentUser.userPhone
        ..password = currentUser.password
        ..nickname = currentUser.nickname
        ..signature = currentUser.signature
        ..avatar = currentUser.avatar
        ..sex = currentUser.sex
        ..birthday = currentUser.birthday
        ..email = null; // 清除邮箱

      await UserRepository.instance.updateUserEntity(updatedUser);

      state.isBound.value = false;
      state.emailController.clear();
      Get.snackbar(
        l10n.success,
        l10n.emailUnbindSuccess,
        snackPosition: SnackPosition.TOP,
        backgroundColor: ColorPalettes.instance.success,
        colorText: ColorPalettes.instance.success,
      );
    } catch (e) {
      Get.snackbar(
        l10n.error,
        e.toString(),
        snackPosition: SnackPosition.TOP,
        backgroundColor: ColorPalettes.instance.error,
        colorText: ColorPalettes.instance.error,
      );
    } finally {
      state.isLoading.value = false;
    }
  }
}
