import 'package:flutter/material.dart';
import 'package:get/get.dart';

/// 账号与绑定页面状态
class AccountBindingState {
  /// 控制器
  late TextEditingController emailController;
  late TextEditingController verifyCodeController;

  /// 倒计时
  final countDown = 0.obs;

  /// 是否正在加载
  final isLoading = false.obs;

  /// 邮箱是否已绑定
  final isBound = false.obs;

  AccountBindingState() {
    emailController = TextEditingController();
    verifyCodeController = TextEditingController();
  }

  void dispose() {
    emailController.dispose();
    verifyCodeController.dispose();
  }
}
