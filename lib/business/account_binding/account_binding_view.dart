import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:topping_home/theme/color_palettes.dart';
import 'package:topping_home/theme/text_styles.dart';
import '../../../common/util/i18n.dart';
import 'account_binding_logic.dart';

/// 账号与绑定页面
class AccountBindingPage extends StatelessWidget {
  AccountBindingPage({super.key});

  final logic = Get.find<AccountBindingLogic>();
  final state = Get.find<AccountBindingLogic>().state;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(l10n.emailBinding),
        centerTitle: true,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Card(
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                _buildHeader(),
                const SizedBox(height: 24),
                Obx(() => state.isBound.value
                    ? _buildBoundEmail()
                    : _buildBindEmailForm()),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// 构建标题
  Widget _buildHeader() {
    return Row(
      children: [
        Icon(Icons.email_outlined, color: ColorPalettes.instance.primary),
        const SizedBox(width: 8),
        Text(
          l10n.emailBinding,
          style: TextStyles.instance.h2(),
        ),
      ],
    );
  }

  /// 构建已绑定邮箱
  Widget _buildBoundEmail() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        TextField(
          controller: state.emailController,
          enabled: false,
          decoration: InputDecoration(
            labelText: l10n.boundEmail,
            border: OutlineInputBorder(),
          ),
        ),
        const SizedBox(height: 16),
        ElevatedButton(
          onPressed: logic.unbindEmail,
          style: ElevatedButton.styleFrom(
            backgroundColor: ColorPalettes.instance.error,
            padding: const EdgeInsets.symmetric(vertical: 12),
          ),
          child: Text(
            l10n.unbind,
            style: TextStyles.instance.h2(),
          ),
        ),
      ],
    );
  }

  /// 绑定邮箱表单
  Widget _buildBindEmailForm() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        // 邮箱
        TextField(
          controller: state.emailController,
          decoration: InputDecoration(
            labelText: l10n.email,
            border: OutlineInputBorder(),
          ),
          keyboardType: TextInputType.emailAddress,
        ),
        const SizedBox(height: 16),
        // 验证码
        Row(
          children: [
            Expanded(
              child: TextField(
                controller: state.verifyCodeController,
                decoration: InputDecoration(
                  labelText: l10n.verifyCode,
                  border: OutlineInputBorder(),
                ),
                keyboardType: TextInputType.number,
              ),
            ),
            const SizedBox(width: 16),
            // 发送验证码按钮
            Obx(() => ElevatedButton(
                  onPressed: state.countDown.value > 0
                      ? null
                      : logic.sendVerificationCode,
                  child: Text(
                    state.countDown.value > 0
                        ? '${state.countDown.value}s'
                        : l10n.getEmailVerificationCode,
                  ),
                )),
          ],
        ),
        const SizedBox(height: 24),
        // 绑定按钮
        Obx(() => ElevatedButton(
              onPressed: state.isLoading.value ? null : logic.bindEmail,
              style: ElevatedButton.styleFrom(
                padding: const EdgeInsets.symmetric(vertical: 12),
              ),
              child: state.isLoading.value
                  ? SizedBox(
                      width: 20,
                      height: 20,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        color: ColorPalettes.instance.firstText,
                      ),
                    )
                  : Text(
                      l10n.bind,
                      style: TextStyles.instance.h2(),
                    ),
            )),
      ],
    );
  }
}
