import 'package:get/get.dart';
import 'package:topping_home/repositories/agreement_repository.dart';
import 'package:topping_home/common/util/log_util.dart';
import 'agreement_state.dart';

/// 协议页面逻辑
class AgreementLogic extends GetxController {
  final AgreementState state = AgreementState();
  final AgreementRepository _agreementManager = AgreementRepository();

  // 添加接受协议的回调
  Function? onAccept;

  // 构造函数接收可选参数
  AgreementLogic({this.onAccept, bool showAcceptButton = false}) {
    state.showAcceptButton.value = showAcceptButton;
  }

  @override
  void onReady() {
    super.onReady();
    loadAgreementContent();
  }

  // 加载用户协议内容
  Future<void> loadAgreementContent() async {
    state.isLoading.value = true;

    try {
      final content = await _agreementManager.loadAgreementContent();
      state.agreementContent.value = content;
    } catch (e) {
      Log.e('加载用户协议失败: $e');
    } finally {
      state.isLoading.value = false;
    }
  }

  // 接受用户协议
  Future<void> acceptAgreement() async {
    await _agreementManager.acceptAgreement();
    if (onAccept != null) {
      onAccept!();
    }
  }
}
