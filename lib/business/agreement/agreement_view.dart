import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:topping_home/business/agreement/agreement_logic.dart';
import 'package:topping_home/common/util/i18n.dart';
import 'package:topping_home/theme/color_palettes.dart';
import 'package:topping_home/theme/text_styles.dart';
import '../../common/widget/background_wrapper.dart';

/// 用户协议页面
class AgreementPage extends GetView<AgreementLogic> {
  const AgreementPage({super.key});

  @override
  Widget build(BuildContext context) {
    return BackgroundWrapper(
      useDarkOverlay: true,
      darkOverlayOpacity: 0.3,
      backgroundColor: ColorPalettes.instance.background,
      child: AnnotatedRegion<SystemUiOverlayStyle>(
        value: SystemUiOverlayStyle.dark.copyWith(
          statusBarColor: ColorPalettes.instance.transparent,
          statusBarIconBrightness: Brightness.dark,
          statusBarBrightness: Brightness.light,
        ),
        child: Scaffold(
          backgroundColor: ColorPalettes.instance.transparent,
          appBar: AppBar(
            backgroundColor: ColorPalettes.instance.card,
            elevation: 0,
            title: Text(
              l10n.userAgreement,
              style: TextStyles.instance.h2(),
            ),
            centerTitle: false,
            iconTheme: IconThemeData(color: ColorPalettes.instance.firstText),
          ),
          body: Obx(() => controller.state.isLoading.value
              ? const Center(child: CircularProgressIndicator())
              : _buildContent()),
          bottomNavigationBar: Obx(() => controller.state.showAcceptButton.value
              ? _buildAcceptButton()
              : const SizedBox.shrink()),
        ),
      ),
    );
  }

  /// 构建协议内容
  Widget _buildContent() {
    if (controller.state.agreementContent.value == null) {
      return Center(
        child: Text(
          l10n.failedToLoadContent,
          style: TextStyles.instance.h2(),
        ),
      );
    }

    final agreementContent = controller.state.agreementContent.value!;

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            agreementContent['title'] ?? '',
            style: TextStyles.instance.h2(),
          ),
          const SizedBox(height: 8),
          Text(
            '${l10n.lastUpdated}: ${agreementContent['last_updated'] ?? ''}',
            style: TextStyles.instance.h3(),
          ),
          const SizedBox(height: 20),
          ..._buildSections(agreementContent),
        ],
      ),
    );
  }

  /// 构建协议内容的各个部分
  List<Widget> _buildSections(Map<String, dynamic> agreementContent) {
    List<Widget> sections = [];

    if (agreementContent.containsKey('sections')) {
      for (var section in agreementContent['sections']) {
        sections.add(const SizedBox(height: 16));
        sections.add(
          Text(
            section['title'] ?? '',
            style: TextStyles.instance.h2(),
          ),
        );
        sections.add(const SizedBox(height: 8));
        sections.add(
          Text(
            section['content'] ?? '',
            style: TextStyles.instance.h3(),
          ),
        );
      }
    }

    return sections;
  }

  /// 构建接受按钮
  Widget _buildAcceptButton() {
    return SafeArea(
      child: Container(
        padding: const EdgeInsets.all(16),
        child: ElevatedButton(
          onPressed: controller.acceptAgreement,
          style: ElevatedButton.styleFrom(
            backgroundColor: ColorPalettes.instance.primary,
            padding: const EdgeInsets.symmetric(vertical: 16),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(25),
            ),
          ),
          child: Text(
            l10n.agree,
            style: TextStyles.instance.h2(),
          ),
        ),
      ),
    );
  }
}
