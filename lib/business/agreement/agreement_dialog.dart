import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:topping_home/common/util/i18n.dart';
import 'package:topping_home/theme/color_palettes.dart';
import 'package:topping_home/theme/text_styles.dart';

import '../../repositories/agreement_repository.dart';
import '../../router/routers.dart';

/// 协议弹窗
class AgreementDialog extends StatelessWidget {
  final Function? onAgree;
  final Function? onDisagree;

  const AgreementDialog({
    super.key,
    this.onAgree,
    this.onDisagree,
  });

  /// 构建富文本内容，将协议和隐私政策链接嵌入到文本中
  List<InlineSpan> _buildRichTextSpans(BuildContext context) {
    final String content = l10n.agreementDialogContent;

    // 查找协议和隐私政策在文本中的位置
    final int agreementIndex = content.indexOf(l10n.agreementLink);
    final int privacyIndex = content.indexOf(l10n.privacyPolicy);

    // 如果文本中不包含这些链接，直接返回纯文本
    if (agreementIndex == -1 || privacyIndex == -1) {
      return [
        TextSpan(text: content),
      ];
    }

    // 分割文本并添加链接
    final List<InlineSpan> spans = [];

    // 判断哪个链接先出现
    if (agreementIndex < privacyIndex) {
      // 协议先出现
      // 添加协议前的文本
      if (agreementIndex > 0) {
        spans.add(TextSpan(text: content.substring(0, agreementIndex)));
      }

      // 添加协议链接
      spans.add(
        TextSpan(
          text: l10n.agreementLink,
          style: TextStyles.instance.h3(
            color: ColorPalettes.instance.primary,
            decoration: TextDecoration.underline,
          ),
          recognizer: TapGestureRecognizer()
            ..onTap = () => AppRoutes.jumpPage(AppRoutes.agreementPage),
        ),
      );

      // 添加协议和隐私政策之间的文本
      spans.add(TextSpan(
        text: content.substring(
          agreementIndex + l10n.agreementLink.length,
          privacyIndex,
        ),
      ));

      // 添加隐私政策链接
      spans.add(
        TextSpan(
          text: l10n.privacyPolicy,
          style: TextStyles.instance.h3(
            color: ColorPalettes.instance.primary,
            decoration: TextDecoration.underline,
          ),
          recognizer: TapGestureRecognizer()
            ..onTap = () => AppRoutes.jumpPage(AppRoutes.privacyPage),
        ),
      );

      // 添加隐私政策后的文本
      final int endIndex = privacyIndex + l10n.privacyPolicy.length;
      if (endIndex < content.length) {
        spans.add(TextSpan(text: content.substring(endIndex)));
      }
    } else {
      // 隐私政策先出现
      // 添加隐私政策前的文本
      if (privacyIndex > 0) {
        spans.add(TextSpan(text: content.substring(0, privacyIndex)));
      }

      // 添加隐私政策链接
      spans.add(
        TextSpan(
          text: l10n.privacyPolicy,
          style: TextStyles.instance.h3(
            color: ColorPalettes.instance.primary,
            decoration: TextDecoration.underline,
          ),
          recognizer: TapGestureRecognizer()
            ..onTap = () => AppRoutes.jumpPage(AppRoutes.privacyPage),
        ),
      );

      // 添加隐私政策和协议之间的文本
      spans.add(TextSpan(
        text: content.substring(
          privacyIndex + l10n.privacyPolicy.length,
          agreementIndex,
        ),
      ));

      // 添加协议链接
      spans.add(
        TextSpan(
          text: l10n.agreementLink,
          style: TextStyles.instance.h3(
            color: ColorPalettes.instance.primary,
            decoration: TextDecoration.underline,
          ),
          recognizer: TapGestureRecognizer()
            ..onTap = () => AppRoutes.jumpPage(AppRoutes.agreementPage),
        ),
      );

      // 添加协议后的文本
      final int endIndex = agreementIndex + l10n.agreementLink.length;
      if (endIndex < content.length) {
        spans.add(TextSpan(text: content.substring(endIndex)));
      }
    }

    return spans;
  }

  @override
  Widget build(BuildContext context) {
    // 使用AbsorbPointer禁用滑动手势
    return AbsorbPointer(
      absorbing: false, // 允许内部按钮点击
      child: GestureDetector(
        // 拦截所有手势
        onHorizontalDragStart: (_) {},
        onVerticalDragStart: (_) {},
        behavior: HitTestBehavior.opaque,
        // 确保点击对话框内部时不会传递事件到背景
        child: Dialog(
          backgroundColor: ColorPalettes.instance.card,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(24.r),
          ),
          // 减小水平内边距，使对话框更窄
          insetPadding: EdgeInsets.symmetric(
            horizontal: 60.w,
            vertical: 24.h,
          ),
          child: Container(
            // 使用ScreenUtil进行响应式内边距
            padding: EdgeInsets.symmetric(
              horizontal: 24.r,
              vertical: 28.r,
            ),
            // 调整约束，减小最大宽度
            constraints: BoxConstraints(
              maxHeight: 0.8.sh,
              maxWidth: 0.75.sw,
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // 添加一个顶部装饰图标，使对话框更加美观
                Container(
                  width: 56.r,
                  height: 56.r,
                  decoration: BoxDecoration(
                    color: ColorPalettes.instance.primary.withOpacity(0.1),
                    shape: BoxShape.circle,
                  ),
                  child: Icon(
                    Icons.description_outlined,
                    color: ColorPalettes.instance.primary,
                    size: 28.r,
                  ),
                ),
                SizedBox(height: 16.h),
                Text(
                  l10n.termsAndConditions,
                  style: TextStyles.instance.h2(
                    color: ColorPalettes.instance.primary,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                SizedBox(height: 20.h),
                // 使用Flexible和SingleChildScrollView使内容可滚动
                Flexible(
                  child: SingleChildScrollView(
                    child: RichText(
                      textAlign: TextAlign.start, // 改为左对齐，提高可读性
                      text: TextSpan(
                        style: TextStyles.instance.h3(
                          color: ColorPalettes.instance.firstText,
                          height: 1.5,
                        ),
                        children: _buildRichTextSpans(context),
                      ),
                    ),
                  ),
                ),
                SizedBox(height: 28.h),
                // 按钮行 - 改进按钮样式
                Row(
                  children: [
                    Expanded(
                      child: OutlinedButton(
                        onPressed: () {
                          Get.back();
                          if (onDisagree != null) {
                            onDisagree!();
                          }
                        },
                        style: OutlinedButton.styleFrom(
                          side: BorderSide(
                            color: ColorPalettes.instance.error,
                            width: 1.5,
                          ),
                          padding: EdgeInsets.symmetric(vertical: 14.h),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(30.r),
                          ),
                        ),
                        child: Text(
                          l10n.disagree,
                          style: TextStyles.instance.h3(
                            color: ColorPalettes.instance.error,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    ),
                    SizedBox(width: 16.w),
                    Expanded(
                      child: ElevatedButton(
                        onPressed: () async {
                          await AgreementRepository().acceptAllAgreements();
                          Get.back();
                          if (onAgree != null) {
                            onAgree!();
                          }
                        },
                        style: ElevatedButton.styleFrom(
                          backgroundColor: ColorPalettes.instance.success,
                          padding: EdgeInsets.symmetric(vertical: 14.h),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(30.r),
                          ),
                          elevation: 3, // 增加一点阴影效果
                        ),
                        child: Text(
                          l10n.agree,
                          style: TextStyles.instance.h3(
                            color: ColorPalettes.instance.pure,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
