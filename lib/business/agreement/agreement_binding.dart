import 'package:get/get.dart';
import 'package:topping_home/business/agreement/agreement_logic.dart';

/// 协议页面绑定类
class AgreementBinding extends Bindings {
  @override
  void dependencies() {
    // 获取路由参数
    final args = Get.arguments;
    final showAcceptButton = args?['showAcceptButton'] ?? false;
    final onAccept = args?['onAccept'];

    // 注入控制器并传递参数
    Get.lazyPut(() => AgreementLogic(
          showAcceptButton: showAcceptButton,
          onAccept: onAccept,
        ));
  }
}
