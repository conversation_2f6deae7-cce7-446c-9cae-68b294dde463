import 'package:get/get.dart';
import 'package:image_picker/image_picker.dart';
import 'package:topping_home/common/util/log_util.dart';

import '../../common/util/i18n.dart';
import '../../models/feedback_entity.dart';
import '../../repositories/feedback_repository.dart';
import 'feedback_state.dart';

/// 反馈逻辑
class FeedbackLogic extends GetxController {
  final FeedbackState state = FeedbackState();
  final ImagePicker _picker = ImagePicker();
  final FeedbackRepository _repository = FeedbackRepository();

  // 用于存储设备类型和反馈类型的映射关系
  final Map<String, int> _feedbackTypeIdMap = {};

  @override
  void onInit() {
    super.onInit();
    // 初始化时加载设备类型和反馈类型
    _loadFeedbackTypes();
    _loadDeviceTypes();
  }

  // 加载设备类型
  Future<void> _loadDeviceTypes() async {
    try {
      // 直接获取设备类型列表，不再需要检查 result.success
      final deviceTypes = await _repository.getDeviceTypes();

      // 转换为界面需要的格式
      state.apiDeviceTypes.value = deviceTypes
          .map((item) => {
                'code': item.code,
                'name': item.name,
                // 其他需要的字段
              })
          .toList();

      // 如果有设备类型，选择第一个
      if (state.apiDeviceTypes.isNotEmpty) {
        state.deviceType.value = state.apiDeviceTypes[0]['code'] ?? '';
      }
    } catch (e) {
      Log.e('加载设备类型失败: $e');
      // 可以在这里添加错误提示
    }
  }

// 加载反馈类型
  // feedback_logic.dart 修改
  Future<void> _loadFeedbackTypes() async {
    try {
      final feedbackTypes = await _repository.getFeedbackTypes();

      final processedTypes = <Map<String, dynamic>>[];
      for (var item in feedbackTypes) {
        // 使用 itemCode 作为标识符，而不是 itemName
        processedTypes.add({
          'code': item.itemValue, // 使用代码而不是名称作为标识
          'type': _getLocalizedFeedbackType(item.itemValue), // 获取本地化的名称
          'icon': _getIconForFeedbackType(item.itemValue),
          'id': item.id
        });
        _feedbackTypeIdMap[item.itemValue] = item.id;
      }
      state.apiFeedbackTypes.value = processedTypes;

      // 初始选择第一个类型
      if (state.apiFeedbackTypes.isNotEmpty) {
        state.feedbackType.value = state.apiFeedbackTypes[0]['code']; // 存储代码
      }
    } catch (e) {
      Log.e('加载反馈类型失败: $e');
    }
  }

  /// 根据代码获取本地化的反馈类型名称
  String _getLocalizedFeedbackType(String code) {
    switch (code) {
      case 'FEATURE_SUGGESTION':
        return l10n.feedbackTypeFeatureSuggestion;
      case 'BUG_REPORT':
        return l10n.feedbackTypeBugReport;
      case 'UI_OPTIMIZATION':
        return l10n.feedbackTypeUIImprovement;
      case 'OTHER':
        return l10n.feedbackTypeOther;
      default:
        return l10n.feedbackTypeOther;
    }
  }

  /// 根据反馈类型获取对应的图标
  String _getIconForFeedbackType(String code) {
    // 根据类型返回合适的图标
    switch (code) {
      case 'FEATURE_SUGGESTION':
        return '💡';
      case 'BUG_REPORT':
        return '🐛';
      case 'UI_OPTIMIZATION':
        return '🎨';
      case 'OTHER':
        return '📝';
      default:
        return '📋';
    }
  }

  /// 设置设备类型
  void setDeviceType(String type) {
    state.deviceType.value = type;
  }

  /// 设置反馈类型
  void setFeedbackType(String type) {
    state.feedbackType.value = type;
  }

  /// 设置反馈内容
  void setContent(String content) {
    state.content.value = content;
  }

  /// 设置联系方式
  void setContactInfo(String info) {
    state.contactInfo.value = info;
  }

  /// 选择图片
  Future<void> pickImage() async {
    final XFile? image = await _picker.pickImage(source: ImageSource.gallery);
    if (image != null && state.images.length < 3) {
      state.images.add(image.path);
    } else if (state.images.length >= 3) {
      Get.snackbar(l10n.feedbackTitle, l10n.feedbackImageHint);
    }
  }

  /// 移除图片
  void removeImage(int index) {
    state.images.removeAt(index);
  }

  /// 提交反馈
  Future<void> submitFeedback() async {
    if (state.deviceType.isEmpty) {
      Get.snackbar(l10n.feedbackTitle, l10n.selectDeviceType);
      return;
    }
    if (state.feedbackType.isEmpty) {
      Get.snackbar(l10n.feedbackTitle, l10n.feedbackTypeRequired);
      return;
    }
    if (state.content.isEmpty) {
      Get.snackbar(l10n.feedbackTitle, l10n.feedbackContentHint);
      return;
    }

    state.isSubmitting.value = true;
    try {
      final feedbackEntity = FeedbackEntity(
        userId: getUserId(),
        deviceCode: state.deviceType.value,
        deviceName: state.deviceType.value,
        // 假设名称与代码相同，实际从API获取
        feedbackTypeId: _feedbackTypeIdMap[state.feedbackType.value] ?? 1,
        feedbackTypeName: state.feedbackType.value,
        content: state.content.value,
        contactInfo:
            state.contactInfo.value.isEmpty ? null : state.contactInfo.value,
      );

      final result = await _repository.submitFeedback(
        feedbackEntity,
        state.images,
      );

      if (result.success) {
        Get.back();
        Get.snackbar(l10n.feedbackSuccess, l10n.feedbackSuccessHint);
      } else {
        Get.snackbar(l10n.error, result.message ?? l10n.feedbackError);
      }
    } catch (e) {
      Log.e('提交反馈失败: $e');
      Get.snackbar(l10n.error, l10n.feedbackError);
    } finally {
      state.isSubmitting.value = false;
    }
  }

  /// 获取用户ID todo 目前没有用户体系，先随便传一个，待后续补齐。
  int getUserId() {
    return 1;
  }
}
