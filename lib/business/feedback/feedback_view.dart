import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'dart:io';
import '../../common/util/i18n.dart';
import 'package:topping_home/theme/color_palettes.dart';
import 'package:topping_home/theme/text_styles.dart';
import '../../common/widget/background_wrapper.dart';
import 'feedback_logic.dart';
import 'feedback_state.dart';

/// 反馈页面
class FeedbackPage extends StatefulWidget {
  const FeedbackPage({super.key});

  @override
  State<FeedbackPage> createState() => _FeedbackPageState();
}

class _FeedbackPageState extends State<FeedbackPage> {
  final FeedbackLogic logic = Get.put(FeedbackLogic());
  final FeedbackState state = Get.find<FeedbackLogic>().state;

  @override
  Widget build(BuildContext context) {
    return BackgroundWrapper(
      useDarkOverlay: true,
      darkOverlayOpacity: 0.3,
      backgroundColor: ColorPalettes.instance.background,
      child: Obx(() => AnnotatedRegion<SystemUiOverlayStyle>(
            value: SystemUiOverlayStyle(
              statusBarColor: ColorPalettes.instance.transparent,
              statusBarIconBrightness: ColorPalettes.instance.isDark()
                  ? Brightness.light
                  : Brightness.dark,
              statusBarBrightness: ColorPalettes.instance.isDark()
                  ? Brightness.dark
                  : Brightness.light,
            ),
            child: Scaffold(
              backgroundColor: ColorPalettes.instance.transparent,
              appBar: AppBar(
                backgroundColor: ColorPalettes.instance.card,
                elevation: 0,
                title: Text(
                  l10n.feedbackTitle,
                  style: TextStyles.instance.h2(),
                ),
                centerTitle: false,
                iconTheme:
                    IconThemeData(color: ColorPalettes.instance.firstText),
              ),
              body: Column(
                children: [
                  Expanded(
                    child: SingleChildScrollView(
                      padding: const EdgeInsets.all(20),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // 设备选择
                          _buildSectionTitle(l10n.device),
                          const SizedBox(height: 12),
                          _buildDeviceDropdown(),
                          const SizedBox(height: 24),

                          // 反馈类型
                          _buildSectionTitle(l10n.selectDeviceType),
                          const SizedBox(height: 12),
                          _buildFeedbackTypes(),
                          const SizedBox(height: 24),

                          // 反馈内容
                          _buildSectionTitle(l10n.feedbackContent),
                          const SizedBox(height: 12),
                          _buildContentInput(),
                          const SizedBox(height: 24),

                          // 添加图片
                          _buildSectionTitle(l10n.addPicture),
                          const SizedBox(height: 12),
                          _buildImageUpload(),
                          const SizedBox(height: 24),

                          // 联系方式
                          _buildSectionTitle(l10n.contact),
                          const SizedBox(height: 12),
                          _buildContactInput(),
                          const SizedBox(height: 32),
                        ],
                      ),
                    ),
                  ),
                  // Fixed submit button at bottom
                  Padding(
                    padding: const EdgeInsets.all(20),
                    child: _buildSubmitButton(),
                  ),
                ],
              ),
            ),
          )),
    );
  }

  /// 构建标题
  Widget _buildSectionTitle(String title) {
    return Text(
      title,
      style: TextStyles.instance.h3(
        fontWeight: FontWeight.w600,
        color: ColorPalettes.instance.firstText,
      ),
    );
  }

  /// 构建设备选择下拉框
  Widget _buildDeviceDropdown() {
    return Obx(() => Container(
          decoration: BoxDecoration(
            color: ColorPalettes.instance.card,
            borderRadius: BorderRadius.circular(12),
            boxShadow: [
              BoxShadow(
                color: ColorPalettes.instance.shadow.withValues(alpha: 0.05),
                blurRadius: 4,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: DropdownButtonHideUnderline(
            child: DropdownButton<String>(
              value: state.deviceType.value.isEmpty
                  ? null
                  : state.deviceType.value,
              onChanged: (newValue) {
                if (newValue != null) {
                  logic.setDeviceType(newValue);
                }
              },
              items: state.apiDeviceTypes.map((device) {
                return DropdownMenuItem<String>(
                  value: device['code'],
                  child: Text(
                    device['name'],
                    style: TextStyles.instance.body1(),
                  ),
                );
              }).toList(),
              dropdownColor: ColorPalettes.instance.card,
              icon: Icon(Icons.arrow_drop_down,
                  color: ColorPalettes.instance.secondText),
              isExpanded: true,
            ),
          ),
        ));
  }

  /// 构建反馈类型
  Widget _buildFeedbackTypes() {
    return Obx(() => Wrap(
          spacing: 12,
          runSpacing: 12,
          children: state.apiFeedbackTypes.map((type) {
            final bool isSelected = state.feedbackType.value == type['type'];
            return Material(
              color: ColorPalettes.instance.transparent,
              child: Ink(
                decoration: BoxDecoration(
                  color: isSelected
                      ? ColorPalettes.instance.accentLightest
                      : ColorPalettes.instance.card,
                  borderRadius: BorderRadius.circular(20),
                  border: Border.all(
                    color: isSelected
                        ? ColorPalettes.instance.accent
                        : ColorPalettes.instance.transparent,
                    width: 1.5,
                  ),
                  boxShadow: [
                    BoxShadow(
                      color: ColorPalettes.instance.shadow.withValues(alpha: 0.05),
                      blurRadius: 4,
                      offset: const Offset(0, 1),
                    ),
                  ],
                ),
                child: InkWell(
                  onTap: () => logic.setFeedbackType(type['type']),
                  borderRadius: BorderRadius.circular(20),
                  child: Padding(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 16,
                      vertical: 8,
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Text(type['icon']),
                        const SizedBox(width: 4),
                        Text(
                          type['type'],
                          style: TextStyles.instance.body1(
                            fontWeight: isSelected
                                ? FontWeight.w600
                                : FontWeight.normal,
                            color: isSelected
                                ? ColorPalettes.instance.accent
                                : null,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            );
          }).toList(),
        ));
  }

  /// 构建反馈内容输入框
  Widget _buildContentInput() {
    return Container(
      decoration: BoxDecoration(
        color: ColorPalettes.instance.card,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: ColorPalettes.instance.shadow.withAlpha(13), // 0.05 * 255 = 13
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: TextField(
        onChanged: logic.setContent,
        maxLines: 5,
        maxLength: 500,
        style: TextStyles.instance.body1(),
        decoration: InputDecoration(
          hintText: l10n.feedbackContentHint,
          hintStyle: TextStyles.instance.body1(
            color: ColorPalettes.instance.secondText,
          ),
          border: InputBorder.none,
          contentPadding: const EdgeInsets.all(16),
          counterStyle: TextStyles.instance.body2(
            color: ColorPalettes.instance.secondText,
          ),
        ),
      ),
    );
  }

  /// 构建图片上传
  Widget _buildImageUpload() {
    return Obx(() => Wrap(
          spacing: 12,
          runSpacing: 12,
          children: [
            ...state.images.asMap().entries.map((entry) {
              return Stack(
                children: [
                  Container(
                    width: 80,
                    height: 80,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(12),
                      boxShadow: [
                        BoxShadow(
                          color: ColorPalettes.instance.shadow.withValues(alpha: 0.1),
                          blurRadius: 4,
                          offset: const Offset(0, 2),
                        ),
                      ],
                      image: DecorationImage(
                        image: FileImage(File(entry.value)),
                        fit: BoxFit.cover,
                      ),
                    ),
                  ),
                  Positioned(
                    right: 0,
                    top: 0,
                    child: GestureDetector(
                      onTap: () => logic.removeImage(entry.key),
                      child: Container(
                        padding: const EdgeInsets.all(4),
                        decoration: BoxDecoration(
                          color: ColorPalettes.instance.errorLightest,
                          shape: BoxShape.circle,
                          border: Border.all(
                            color: ColorPalettes.instance.error,
                            width: 1,
                          ),
                        ),
                        child: Icon(
                          Icons.close,
                          size: 16,
                          color: ColorPalettes.instance.error,
                        ),
                      ),
                    ),
                  ),
                ],
              );
            }),
            // 添加图片按钮
            if (state.images.length < 3)
              GestureDetector(
                onTap: logic.pickImage,
                child: Container(
                  width: 80,
                  height: 80,
                  decoration: BoxDecoration(
                    color: ColorPalettes.instance.card,
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(
                      color: ColorPalettes.instance.secondText.withValues(alpha: 0.4),
                    ),
                    boxShadow: [
                      BoxShadow(
                        color: ColorPalettes.instance.shadow.withValues(alpha: 0.05),
                        blurRadius: 4,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  child: Center(
                    child: Icon(
                      Icons.add_photo_alternate_outlined,
                      size: 32,
                      color: ColorPalettes.instance.secondText,
                    ),
                  ),
                ),
              ),
          ],
        ));
  }

  /// 构建联系方式输入框
  Widget _buildContactInput() {
    return Container(
      decoration: BoxDecoration(
        color: ColorPalettes.instance.card,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: ColorPalettes.instance.shadow.withValues(alpha: 0.05),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: TextField(
        onChanged: logic.setContactInfo,
        style: TextStyles.instance.body1(),
        decoration: InputDecoration(
          hintText: l10n.feedbackContactHint,
          hintStyle: TextStyles.instance.body1(
            color: ColorPalettes.instance.secondText,
          ),
          border: InputBorder.none,
          contentPadding: const EdgeInsets.all(16),
        ),
      ),
    );
  }

  /// 构建提交按钮
  Widget _buildSubmitButton() {
    return Obx(() => SizedBox(
          width: double.infinity,
          height: 52, // 增加高度使按钮更突出
          child: ElevatedButton(
            onPressed: state.isSubmitting.value ? null : logic.submitFeedback,
            style: ElevatedButton.styleFrom(
              backgroundColor: ColorPalettes.instance.accent,
              foregroundColor: ColorPalettes.instance.pure,
              disabledBackgroundColor:
                  ColorPalettes.instance.accentLight,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(26), // 增加圆角半径
              ),
              elevation: 1, // 减小阴影
              padding: const EdgeInsets.symmetric(vertical: 12), // 增加内边距
            ),
            child: state.isSubmitting.value
                ? SizedBox(
                    width: 24,
                    height: 24,
                    child: CircularProgressIndicator(
                      color: ColorPalettes.instance.pure,
                      strokeWidth: 2,
                    ),
                  )
                : Text(
                    l10n.feedbackSubmit,
                    style: TextStyles.instance.h3(
                      fontWeight: FontWeight.bold,
                      color: ColorPalettes.instance.pure,
                    ),
                  ),
          ),
        ));
  }

  @override
  void dispose() {
    Get.delete<FeedbackLogic>();
    super.dispose();
  }
}
