import 'package:get/get.dart';
import 'package:qr_code_scanner_plus/qr_code_scanner_plus.dart';
import 'package:topping_home/common/util/log_util.dart';

import '../../common/util/i18n.dart';
import 'qr_scan_state.dart';

/// 二维码扫描逻辑处理类
class QRScanLogic extends GetxController {
  final QRScanState state = QRScanState();
  QRViewController? controller;

  /// 初始化二维码扫描器
  void onQRViewCreated(QRViewController controller) {
    this.controller = controller;
    controller.scannedDataStream.listen((scanData) {
      if (state.isProcessing.value || scanData.code == null) return;
      state.isProcessing.value = true;

      if (validateQRCode(scanData.code!)) {
        controller.pauseCamera();
        Get.back(result: scanData.code);
      } else {
        Get.snackbar(
          l10n.error,
          l10n.invalidQRCode,
          snackPosition: SnackPosition.BOTTOM,
        );
        state.isProcessing.value = false;
      }
    });
  }

  /// 验证二维码格式
  bool validateQRCode(String code) {
    // 打印二维码内容
    Log.e('QR Code: $code');
    try {
      final uri = Uri.parse(code);
      return uri.scheme == 'fiio' &&
          (uri.host == 'device' || uri.host == 'ip') &&
          uri.path.isNotEmpty;
    } catch (e) {
      return false;
    }
  }

  /// 开关闪光灯
  void toggleFlash() async {
    await controller?.toggleFlash();
  }

  /// 切换摄像头
  void flipCamera() async {
    await controller?.flipCamera();
  }

  @override
  void onClose() {
    controller?.dispose();
    super.onClose();
  }
}
