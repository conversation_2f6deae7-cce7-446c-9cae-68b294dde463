import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:qr_code_scanner_plus/qr_code_scanner_plus.dart';

import '../../common/util/i18n.dart';
import 'qr_scan_logic.dart';
import 'package:topping_home/theme/color_palettes.dart';
import 'package:topping_home/theme/text_styles.dart';

/// 二维码扫描页面
class QRScanPage extends StatefulWidget {
  const QRScanPage({super.key});

  @override
  State<QRScanPage> createState() => _QRScanPageState();
}

class _QRScanPageState extends State<QRScanPage> {
  final QRScanLogic logic = Get.put(QRScanLogic());
  final GlobalKey qrKey = GlobalKey(debugLabel: 'QR');

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(l10n.scanQRCode),
        actions: [
          IconButton(
            icon: Icon(Icons.flash_on),
            onPressed: logic.toggleFlash,
          ),
          IconButton(
            icon: Icon(Icons.switch_camera),
            onPressed: logic.flipCamera,
          ),
        ],
      ),
      body: Stack(
        children: [
          // QR扫描视图
          QRView(
            key: qrKey,
            onQRViewCreated: logic.onQRViewCreated,
            overlay: QrScannerOverlayShape(
              borderColor: ColorPalettes.instance.error,
              borderRadius: 10,
              borderLength: 30,
              borderWidth: 10,
              cutOutSize: 300,
              overlayColor: ColorPalettes.instance.overlay,
            ),
          ),

          // 扫描区域上方说明文本
          Positioned(
            top: 40,
            left: 0,
            right: 0,
            child: Text(
              l10n.scanQRCodeHint,
              textAlign: TextAlign.center,
              style: TextStyle(
                color: ColorPalettes.instance.firstText,
                fontSize: TextStyles.instance.h2().fontSize,
                shadows: [
                  Shadow(
                    offset: Offset(0, 1),
                    blurRadius: 3,
                    color: ColorPalettes.instance.firstText.withAlpha(77),
                  ),
                ],
              ),
            ),
          ),

          // 底部说明文本
          Positioned(
            bottom: 50,
            left: 20,
            right: 20,
            child: Container(
              padding: EdgeInsets.symmetric(horizontal: 24, vertical: 16),
              decoration: BoxDecoration(
                color: ColorPalettes.instance.overlay,
                borderRadius: BorderRadius.circular(8),
              ),
              child: Text(
                l10n.scanQRCodeBottomHint,
                textAlign: TextAlign.center,
                style: TextStyle(
                  color: ColorPalettes.instance.firstText,
                  fontSize: TextStyles.instance.h2().fontSize,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
