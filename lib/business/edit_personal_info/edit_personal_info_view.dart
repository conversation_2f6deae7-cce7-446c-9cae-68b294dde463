import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:topping_home/theme/text_styles.dart';
import '../../../common/util/i18n.dart';
import 'edit_personal_info_logic.dart';
import 'package:topping_home/theme/color_palettes.dart';

/// 编辑个人信息页面
class EditPersonalInfoPage extends StatelessWidget {
  EditPersonalInfoPage({super.key});

  final logic = Get.find<EditPersonalInfoLogic>();
  final state = Get.find<EditPersonalInfoLogic>().state;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(l10n.editPersonalInfo),
        centerTitle: true,
        actions: [
          Obx(() => state.isLoading.value
              ? Center(
                  child: Padding(
                    padding: EdgeInsets.only(right: 16.0),
                    child: SizedBox(
                      width: 20,
                      height: 20,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        color: ColorPalettes.instance.firstText,
                      ),
                    ),
                  ),
                )
              : TextButton(
                  onPressed: logic.saveInfo,
                  child: Text(
                    l10n.save,
                    style: TextStyles.instance.h2(),
                  ),
                )),
        ],
      ),
      body: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            children: [
              _buildAvatarSection(),
              const SizedBox(height: 16),
              _buildInfoCard(),
            ],
          ),
        ),
      ),
    );
  }

  /// 构建头像部分
  Widget _buildAvatarSection() {
    return Center(
      child: GestureDetector(
        onTap: logic.changeAvatar,
        child: Obx(() => CircleAvatar(
              radius: 50,
              backgroundImage: NetworkImage(state.avatar.value),
              child: state.avatar.value.isEmpty
                  ? const Icon(Icons.camera_alt, size: 40)
                  : null,
            )),
      ),
    );
  }

  /// 构建信息卡片
  Widget _buildInfoCard() {
    return Card(
      child: Column(
        children: [
          _buildTextField(
            controller: state.nicknameController,
            label: l10n.nickname,
            icon: Icons.person_outline,
          ),
          const Divider(height: 1),
          _buildTextField(
            controller: state.signatureController,
            label: l10n.signature,
            icon: Icons.edit_note,
          ),
          const Divider(height: 1),
          _buildSelectableField(
            label: l10n.gender,
            icon: Icons.wc,
            value: state.sex,
            onTap: logic.selectSex,
          ),
          const Divider(height: 1),
          _buildSelectableField(
            label: l10n.birthday,
            icon: Icons.cake,
            value: state.birthday,
            onTap: logic.selectBirthday,
          ),
        ],
      ),
    );
  }

  /// 构建文本输入框
  Widget _buildTextField({
    required TextEditingController controller,
    required String label,
    required IconData icon,
  }) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16.0),
      child: TextField(
        controller: controller,
        decoration: InputDecoration(
          icon: Icon(icon),
          labelText: label,
          border: InputBorder.none,
        ),
      ),
    );
  }

  /// 构建可选字段
  Widget _buildSelectableField({
    required String label,
    required IconData icon,
    required RxString value,
    required VoidCallback onTap,
  }) {
    return ListTile(
      leading: Icon(icon),
      title: Text(label),
      trailing: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Obx(() => Text(
                value.value,
                style: TextStyles.instance.h2(),
              )),
          const Icon(Icons.chevron_right),
        ],
      ),
      onTap: onTap,
    );
  }
}
