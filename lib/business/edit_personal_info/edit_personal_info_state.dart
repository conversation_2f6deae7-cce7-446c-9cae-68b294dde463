import 'package:flutter/material.dart';
import 'package:get/get.dart';

/// 编辑个人信息状态管理
class EditPersonalInfoState {
  // 文本控制器
  late TextEditingController nicknameController;
  late TextEditingController signatureController;

  // 响应式变量
  final avatar = ''.obs;
  final sex = ''.obs;
  final birthday = ''.obs;
  final isLoading = false.obs;

  EditPersonalInfoState() {
    nicknameController = TextEditingController();
    signatureController = TextEditingController();
  }

  void dispose() {
    nicknameController.dispose();
    signatureController.dispose();
  }
}
