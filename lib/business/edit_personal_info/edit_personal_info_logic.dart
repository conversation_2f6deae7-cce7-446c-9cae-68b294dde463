import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:image_picker/image_picker.dart';
import '../../../common/util/i18n.dart';
import '../../repositories/user_repository.dart';
import '../../../models/user_entity.dart';
import 'edit_personal_info_state.dart';
import 'package:topping_home/theme/color_palettes.dart';

/// 编辑个人信息逻辑
class EditPersonalInfoLogic extends GetxController {
  final EditPersonalInfoState state = EditPersonalInfoState();

  @override
  void onInit() {
    super.onInit();
    _initUserInfo();
  }

  @override
  void onClose() {
    state.dispose();
    super.onClose();
  }

  /// 初始化用户信息
  void _initUserInfo() {
    final userEntity = UserRepository.instance.userEntity.value;
    if (userEntity != null) {
      state.nicknameController.text = userEntity.nickname ?? '';
      state.signatureController.text = userEntity.signature ?? '';
      state.avatar.value = userEntity.avatar ?? '';
      state.sex.value = userEntity.sex ?? '';
      state.birthday.value = userEntity.birthday ?? '';
    }
  }

  /// 更换头像
  Future<void> changeAvatar() async {
    final ImagePicker picker = ImagePicker();
    try {
      final XFile? image = await picker.pickImage(source: ImageSource.gallery);
      if (image != null) {
        // TODO: 实现图片上传
        state.avatar.value = image.path;
      }
    } catch (e) {
      Get.snackbar(
        l10n.error,
        e.toString(),
        snackPosition: SnackPosition.TOP,
        backgroundColor: ColorPalettes.instance.error,
        colorText: ColorPalettes.instance.firstText,
      );
    }
  }

  /// 选择性别
  void selectSex() {
    Get.bottomSheet(
      Container(
        color: ColorPalettes.instance.card,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              title: Text(l10n.male),
              onTap: () {
                state.sex.value = '男';
                Get.back();
              },
            ),
            ListTile(
              title: Text(l10n.female),
              onTap: () {
                state.sex.value = '女';
                Get.back();
              },
            ),
          ],
        ),
      ),
    );
  }

  /// 选择生日
  Future<void> selectBirthday() async {
    final DateTime? picked = await showDatePicker(
      context: Get.context!,
      initialDate: DateTime.now(),
      firstDate: DateTime(1900),
      lastDate: DateTime.now(),
    );
    if (picked != null) {
      state.birthday.value = '${picked.year}-${picked.month}-${picked.day}';
    }
  }

  /// 保存信息
  Future<void> saveInfo() async {
    state.isLoading.value = true;
    try {
      final currentUser = UserRepository.instance.userEntity.value;
      if (currentUser == null) {
        throw Exception(l10n.userNotFound);
      }

      // 更新用户信息
      final updatedUser = UserEntity()
        ..id = currentUser.id
        ..userPhone = currentUser.userPhone
        ..password = currentUser.password
        ..nickname = state.nicknameController.text
        ..signature = state.signatureController.text
        ..avatar = state.avatar.value
        ..sex = state.sex.value
        ..birthday = state.birthday.value;

      await UserRepository.instance.updateUserEntity(updatedUser);

      Get.back();
      Get.snackbar(
        l10n.success,
        l10n.updateSuccess,
        snackPosition: SnackPosition.TOP,
        backgroundColor: ColorPalettes.instance.success,
        colorText: ColorPalettes.instance.firstText,
      );
    } catch (e) {
      Get.snackbar(
        l10n.error,
        e.toString(),
        snackPosition: SnackPosition.TOP,
        backgroundColor: ColorPalettes.instance.error,
        colorText: ColorPalettes.instance.firstText,
      );
    } finally {
      state.isLoading.value = false;
    }
  }
}
