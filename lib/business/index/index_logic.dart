import 'dart:async';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:topping_ble_control/device/device_factory.dart';
import 'package:topping_ble_control/event/connection_state_event.dart';
import 'package:topping_ble_control/model/enums/ble_connection_state.dart';
import 'package:topping_ble_control/model/enums/device_mode_type.dart';
import 'package:topping_ble_control/registry/device_data_manager.dart';
import 'package:topping_home/common/util/log_util.dart';

import '../../models/device_entity.dart';
import '../../repositories/background_repository.dart';
import '../../repositories/device_repository.dart';
import '../../repositories/user_repository.dart';
import '../../router/routers.dart';
import '../../service/update_service.dart';
import '../../utils/device_sync_util.dart';
import 'index_state.dart';

/// 首页逻辑
class IndexLogic extends GetxController {
  final IndexState state = IndexState();
  final DeviceRepository storage = Get.find<DeviceRepository>();
  final BackgroundRepository backgroundManager =
      Get.find<BackgroundRepository>();
  final UpdateService _updateService = Get.find<UpdateService>();

  // 使用设备工厂替代直接使用设备管理器
  final DeviceFactory _deviceFactory = DeviceFactory();

  // 添加连接状态监听 - 使用统一的连接状态管理器
  late StreamSubscription<ConnectionStateEvent> _connectionSubscription;

  @override
  void onInit() {
    super.onInit();

    // 初始化当前值
    state.loginInfo.value = UserRepository.instance.loginEntity.value;
    state.userInfo.value = UserRepository.instance.userEntity.value;

    // 监听登录状态
    ever(UserRepository.instance.loginEntity, (loginEntity) {
      state.loginInfo.value = loginEntity;
    });

    // 监听背景图片变化
    ever(backgroundManager.currentBackground, (path) {
      state.backgroundImage.value = path;
    });

    // 监听背景透明度变化
    ever(backgroundManager.backgroundOpacity, (opacity) {
      state.backgroundOpacity.value = opacity;
    });

    // 监听背景模糊度变化
    ever(backgroundManager.backgroundBlur, (blur) {
      state.backgroundBlur.value = blur;
    });

    // 监听用户信息
    ever(UserRepository.instance.userEntity, (userEntity) {
      state.userInfo.value = userEntity;
    });

    // 监听设备存储变化
    ever(storage.deviceListStream, (_) {
      _loadSavedDevices();
    });

    // 使用设备工厂的连接状态流
    _connectionSubscription =
        _deviceFactory.connectionState.listen(_onConnectionStateChanged);
  }

  @override
  void onReady() {
    super.onReady();
    state.deviceList.value = [];
    _updateLoginInfo();
    _updateUserInfo();
    _loadSavedDevices();
    _initDeviceConnectionState();
    _restorePersistedDevices();
    _initBackgroundSettings();
    _checkAppUpdate();
  }

  @override
  void onClose() {
    _connectionSubscription.cancel();
    _deviceFactory.dispose(); // 释放设备工厂资源
    super.onClose();
  }

  /// 初始化背景图片设置
  void _initBackgroundSettings() {
    state.backgroundImage.value = backgroundManager.currentBackground.value;
    state.backgroundOpacity.value = backgroundManager.backgroundOpacity.value;
    state.backgroundBlur.value = backgroundManager.backgroundBlur.value;
    update(); // 触发UI更新
  }

  /// 自动检测更新
  Future<void> _checkAppUpdate() async {
    Log.e('自动检测更新： 开始');
    // 延迟一点时间，让应用先完成初始化
    await Future.delayed(const Duration(seconds: 2));
    await _updateService.autoCheckUpdate();
  }

  /// 加载已保存的设备
  void _loadSavedDevices() {
    final devices = storage.getAllDevices();
    Log.i('已保存设备数量: ${devices.length}');
    state.deviceList.value = devices;
  }

  /// 初始设置所有设备为断开状态，直到收到实际状态更新
  void _initDeviceConnectionState() {
    for (final device in state.deviceList) {
      device.connected = 0;
    }
    // 刷新 UI
    state.deviceList.refresh();
  }

  /// 优化的设备恢复方法 - 同步持久化数据到 DeviceDataManager
  void _restorePersistedDevices() {
    final persistedDevices = storage.getAllDevices();
    Log.i("开始恢复持久化设备到 DeviceDataManager，设备数量: ${persistedDevices.length}");

    // 获取 DeviceDataManager 实例
    final deviceDataManager = DeviceDataManager();

    for (final deviceEntity in persistedDevices) {
      // 确保设备有MAC地址
      if (deviceEntity.macAddress.isNotEmpty) {
        try {
          Log.i(
              "尝试恢复设备到 DeviceDataManager: ${deviceEntity.name}, MAC: ${deviceEntity.macAddress} deviceType: ${deviceEntity.deviceModel}");

          // 调用 DeviceDataManager 的方法来恢复设备信息
          // 这会创建 BleDevice 对象并注册到 DeviceDataManager 的内部 map 中
          // 注意：这里创建的 BleDevice 可能没有有效的 flutterDevice 或 nativeHandle
          // 但它使得 DeviceDataManager 知道这个 MAC 地址对应的设备存在
          final restoredBleDevice =
              deviceDataManager.restoreDeviceFromPersistent(
            id: deviceEntity.id, // 确保 ID 是 String 类型传递
            name: deviceEntity.name,
            macAddress: deviceEntity.macAddress,
            rssi: deviceEntity.rssi,
            deviceType: DeviceModeType.fromString(deviceEntity.deviceModel),
          );

          Log.i(
              "设备 ${restoredBleDevice.name} 已恢复到 DeviceDataManager (isPersistent: ${restoredBleDevice.isPersistent})");
        } catch (e) {
          Log.e("恢复设备 ${deviceEntity.name} 到 DeviceDataManager 失败: $e");
        }
      } else {
        Log.w("设备 ${deviceEntity.name} 没有MAC地址，无法恢复到 DeviceDataManager");
      }
    }

    // 刷新设备列表 UI (这部分逻辑保持不变)
    state.deviceList.value = persistedDevices;
    state.deviceList.refresh();
    Log.i("持久化设备恢复完成，UI 列表已更新");
  }

  /// 更新用户信息
  void _updateUserInfo() {
    state.loginInfo.value = UserRepository.instance.loginEntity.value;
    state.userInfo.value = UserRepository.instance.userEntity.value;
  }

  /// 更新登录信息
  void _updateLoginInfo() {
    state.loginInfo.value = UserRepository.instance.loginEntity.value;
  }

  /// 更新设备连接状态
  void _updateDeviceConnectionState(DeviceEntity device, bool isConnected) {
    final deviceIndex = state.deviceList.indexWhere((d) => d.id == device.id);

    if (deviceIndex != -1) {
      // 获取现有设备对象
      final existingDevice = state.deviceList[deviceIndex];

      // 只更新连接状态相关属性
      existingDevice.connected = isConnected ? 1 : 0;
      existingDevice.lastConnectedState = isConnected;

      // 使用专门的方法更新连接状态
      storage.updateDeviceConnectionState(
          existingDevice.id, isConnected ? 1 : 0);

      Log.i(
          "更新设备 ${existingDevice.name} 的连接状态为: ${isConnected ? '已连接' : '未连接'}");
    }
  }

  /// 检查登录状态并跳转
  void checkLoginAndNavigate(VoidCallback action) {
    if (!UserRepository.instance.isLogin()) {
      AppRoutes.jumpPage(AppRoutes.passwordLoginPage);
    } else {
      action();
    }
  }

  /// 集中处理连接状态变化 - 使用设备工厂
  void _onConnectionStateChanged(ConnectionStateEvent stateEvent) {
    Log.i("连接状态变更: ${stateEvent.state}, 设备句柄: ${stateEvent.deviceHandle}");
    final connectionState = stateEvent.state;
    final deviceHandle = stateEvent.deviceHandle;

    if (deviceHandle == null) {
      Log.e("无法确定当前连接的设备");
      state.connectingDeviceId.value = "";
      return;
    }

    final deviceIndex =
        state.deviceList.indexWhere((d) => d.bleDeviceId == deviceHandle);
    if (deviceIndex == -1) {
      Log.e("找不到匹配的设备实体");
      state.connectingDeviceId.value = "";
      return;
    }

    final device = state.deviceList[deviceIndex];

    switch (connectionState) {
      case BleConnectionState.connecting:
        Log.i("正在连接设备: ${device.name}");
        state.connectingDeviceId.value = device.id;
        break;
      case BleConnectionState.connected:
        state.connectingDeviceId.value = ""; // 连接成功后清空
        _updateDeviceConnectionState(device, true);

        // 使用工具类同步设备数据并跳转
        DeviceSyncUtil.syncDeviceAndNavigate(
          device: device,
          deviceFactory: _deviceFactory,
        );
        break;
      case BleConnectionState.disconnected:
        state.connectingDeviceId.value = ""; // 断开后清空
        _updateDeviceConnectionState(device, false);
        break;
      default:
        break;
    }
  }

  /// 刷新设备列表 - index_view.dart 中调用的公开方法
  Future<void> refreshDevices() async {
    Log.i("刷新设备列表");
    _loadSavedDevices();

    // 检查设备连接状态
    for (final device in state.deviceList) {
      // 使用设备工厂检查连接状态
      if (device.bleDeviceId != null) {
        final connectedDevice = _deviceFactory.getConnectedDevice();

        if (connectedDevice != null &&
            connectedDevice.nativeHandle == device.bleDeviceId) {
          // 设备实际已连接，更新UI状态
          if (device.connected != 1) {
            Log.i("发现设备 ${device.name} 实际已连接，更新状态");
            _updateDeviceConnectionState(device, true);
          }
        } else {
          // 设备实际未连接，更新UI状态
          if (device.connected == 1) {
            Log.i("发现设备 ${device.name} 实际未连接，更新状态");
            _updateDeviceConnectionState(device, false);
          }
        }
      }
    }
  }

  /// 连接一个设备
  void connectDevice(DeviceEntity device) async {
    // 1. 优先使用 MAC 地址查找最新的 BleDevice
    if (device.macAddress.isEmpty) {
      Log.e("设备 ${device.name} 没有 MAC 地址，无法可靠连接");
      state.connectingDeviceId.value = ""; // 清除连接中状态
      Get.snackbar("连接失败", "设备信息不完整 (缺少 MAC 地址)");
      return;
    }

    // 2. 从 DeviceDataManager 获取最新的 BleDevice 实例
    final bleDevice =
        DeviceDataManager().findDevice(macAddress: device.macAddress);

    if (bleDevice == null) {
      Log.e(
          "无法在 DeviceDataManager 中找到 MAC 地址为 ${device.macAddress} 的设备。可能需要重新扫描。");
      state.connectingDeviceId.value = ""; // 清除连接中状态
      Get.snackbar("连接失败", "找不到设备信息，请重新扫描后尝试。");
      return;
    }

    // 3. 确保获取到的是最新的句柄
    final latestHandle = bleDevice.nativeHandle;
    // (可选) 更新传入的 DeviceEntity 的句柄，如果其他地方需要
    device.bleDeviceId = latestHandle;

    try {
      // 4. 断开当前可能存在的连接
      Log.i("尝试断开当前设备连接 (如果存在)");
      await _deviceFactory.disconnectCurrentDevice();
      // 短暂延时确保断开完成
      await Future.delayed(const Duration(milliseconds: 100));

      // 5. 使用从 DeviceDataManager 获取的最新 BleDevice 信息进行连接
      Log.i("开始连接设备: ${bleDevice.name} (句柄: $latestHandle)");
      // 注意：传递获取到的 bleDevice 对象，而不是旧的 device 对象
      await _deviceFactory.connectDevice(bleDevice);
      Log.i("设备连接命令已发送，等待连接状态更新...");
      // 连接状态由 _onConnectionStateChanged 监听处理，这里不修改 connectingDeviceId
    } catch (e) {
      Log.e("连接设备 ${bleDevice.name} 时出错: $e");
      state.connectingDeviceId.value = ""; // 出错时清除连接中状态
      Get.snackbar("连接错误", "连接设备时发生错误: $e");
      // 尝试再次断开以清理状态
      await _deviceFactory.disconnectCurrentDevice();
    }
  }

  /// 切换设备连接状态
  Future<void> toggleDeviceConnection(DeviceEntity device) async {
    // 检查是否有其他设备正在连接
    if (state.connectingDeviceId.value.isNotEmpty &&
        state.connectingDeviceId.value != device.id) {
      Log.w("已有其他设备正在连接 (${state.connectingDeviceId.value})，请稍后再试");
      Get.snackbar("提示", "请等待当前设备连接完成");
      return;
    }

    if (device.connected == 1) {
      Log.i("设备已连接，跳转到设备详情页: ${device.name}");
      Get.toNamed(AppRoutes.deviceDetailPage, arguments: device);
    } else {
      // 检查是否已经在连接此设备
      if (state.connectingDeviceId.value == device.id) {
        Log.i("设备 ${device.name} 正在连接中...");
        return; // 避免重复触发连接
      }

      Log.i("尝试连接设备: ${device.name}, MAC: ${device.macAddress}");

      // 设置连接中状态
      state.connectingDeviceId.value = device.id;

      // 调用 connectDevice
      connectDevice(device);
    }
  }

  /// 断开指定设备的连接
  void disconnectDevice(DeviceEntity device) async {
    Log.i("请求断开设备: ${device.name} (MAC: ${device.macAddress})");
    try {
      final connectedDevice = _deviceFactory.getConnectedDevice();

      if (connectedDevice != null) {
        // 检查当前连接的设备是否是我们要断开的设备 (通过 MAC 地址比较)
        if (device.macAddress.isNotEmpty &&
            connectedDevice.flutterDevice?.remoteId.str == device.macAddress) {
          Log.i("正在断开当前连接的设备: ${connectedDevice.name}");
          await _deviceFactory.disconnectCurrentDevice();
        } else {
          Log.w(
              "请求断开的设备 (${device.name}) 与当前连接的设备 (${connectedDevice.name}) 不匹配，仍然尝试断开当前连接");
          // 即使不匹配，也执行断开，确保只有一个连接
          await _deviceFactory.disconnectCurrentDevice();
        }
      } else {
        Log.i("当前没有设备连接，无需断开");
        // 为保险起见，也调用一次断开，清理潜在的残留状态
        await _deviceFactory.disconnectCurrentDevice();
      }
    } catch (e) {
      Log.e("断开设备连接时出错: $e");
      // 即使出错，也尝试断开以清理状态
      await _deviceFactory.disconnectCurrentDevice();
    } finally {
      // 确保断开后清除连接中状态 (如果之前是这个设备在连接)
      if (state.connectingDeviceId.value == device.id) {
        state.connectingDeviceId.value = "";
      }
    }
  }

  /// 删除设备
  void deleteDevice(DeviceEntity device) {
    Log.i("删除设备: ${device.name}, ID: ${device.id}, MAC: ${device.macAddress}");

    // 如果设备已连接，先断开连接
    if (device.connected == 1) {
      Log.i("设备已连接，先断开连接");
      disconnectDevice(device);
    }

    // 从UI中移除
    state.deviceList.removeWhere((d) => d.id == device.id);

    // 从存储中删除
    storage.deleteDevice(device.id);

    Log.i("设备删除完成");
  }

  /// 判断设备是否正在连接
  bool isDeviceConnecting(String deviceId) {
    var bool = state.connectingDeviceId.value == deviceId;
    Log.i(
        "------ 检查设备是否正在连接: $bool connectingDeviceId: ${state.connectingDeviceId.value} deviceId: $deviceId");
    return bool;
  }
}
