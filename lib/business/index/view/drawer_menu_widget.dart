import 'package:flutter/material.dart';

import '../../../../common/util/i18n.dart';

/// 抽屉菜单组件
class DrawerMenuWidget extends StatelessWidget {
  final VoidCallback? onAboutTap;
  final VoidCallback? onQuickStartTap;
  final VoidCallback? onFeedbackTap;

  const DrawerMenuWidget({
    super.key,
    this.onAboutTap,
    this.onQuickStartTap,
    this.onFeedbackTap,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        ListTile(
          leading: const Icon(Icons.info_outline),
          title: Text(l10n.about),
          onTap: onAboutTap,
        ),
        ListTile(
          leading: const Icon(Icons.play_circle_outline),
          title: Text(l10n.quickStart),
          onTap: onQuickStartTap,
        ),
        ListTile(
          leading: const Icon(Icons.feedback_outlined),
          title: Text(l10n.feedback),
          onTap: onFeedbackTap,
        ),
      ],
    );
  }
}
