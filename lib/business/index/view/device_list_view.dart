import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:topping_home/theme/color_palettes.dart';
import 'package:topping_home/theme/text_styles.dart';

import '../../../common/util/i18n.dart';
import '../../../common/util/log_util.dart' as HomeLog;
import '../../../models/device_entity.dart';
import '../index_logic.dart';

/// 设备列表组件
class DeviceListView extends StatelessWidget {
  final List<DeviceEntity> devices;
  final Function(DeviceEntity) onConnect;
  final Function(DeviceEntity) onDelete;
  final Function() onAdd;

  const DeviceListView({
    super.key,
    required this.devices,
    required this.onConnect,
    required this.onDelete,
    required this.onAdd,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Expanded(
          child: GridView.builder(
            padding: const EdgeInsets.all(16),
            gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 2,
              mainAxisSpacing: 16,
              crossAxisSpacing: 16,
              childAspectRatio: 0.95, // 调整比例，给卡片更多垂直空间
            ),
            itemCount: devices.length + 1, // +1 用于添加设备按钮
            itemBuilder: (context, index) {
              if (index == devices.length) {
                // 添加设备按钮
                return InkWell(
                  splashColor: ColorPalettes.instance.transparent, // 透明的水波纹
                  highlightColor: ColorPalettes.instance.firstText
                      .withAlpha(13), // 0.05 * 255 = 13 // 轻微的高亮效果
                  onTap: () => onAdd(),
                  child: Container(
                    decoration: BoxDecoration(
                      color: ColorPalettes.instance.card,
                      borderRadius: BorderRadius.circular(12),
                      boxShadow: [
                        BoxShadow(
                          color:
                              ColorPalettes.instance.shadow.withAlpha(38), // 0.15 * 255 = 38
                          blurRadius: 8,
                          offset: const Offset(0, 2),
                        ),
                      ],
                    ),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.add_circle_outline,
                          size: 38, // 缩小图标大小
                          color: ColorPalettes.instance.firstText,
                        ),
                        const SizedBox(height: 6), // 减小间距
                        Text(
                          l10n.addDevice,
                          style: TextStyles.instance.h3(
                            fontWeight: FontWeight.w600,
                            fontSize: 14, // 缩小字体大小
                          ),
                        ),
                      ],
                    ),
                  ),
                );
              }

              final device = devices[index];
              return GestureDetector(
                onLongPress: () => _showDeleteDialog(context, device),
                child: Container(
                  decoration: BoxDecoration(
                    color: ColorPalettes.instance.card,
                    borderRadius: BorderRadius.circular(12),
                    boxShadow: [
                      BoxShadow(
                        color: ColorPalettes.instance.shadow.withAlpha(38), // 0.15 * 255 = 38
                        blurRadius: 8,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  child: InkWell(
                    splashColor: ColorPalettes.instance.transparent, // 透明的水波纹
                    highlightColor: ColorPalettes.instance.firstText
                        .withAlpha(13), // 0.05 * 255 = 13 // 轻微的高亮效果
                    onTap: () => onConnect(device),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Image.asset(
                          device.image,
                          height: 75, // 缩小图片高度
                        ),
                        const SizedBox(height: 6), // 减小间距
                        Text(
                          device.name,
                          style: TextStyles.instance.h3(
                            fontWeight: FontWeight.w600,
                            fontSize: 14, // 缩小字体大小
                          ),
                        ),
                        _buildConnectionStatus(device)
                      ],
                    ),
                  ),
                ),
              );
            },
          ),
        ),
      ],
    );
  }

  /// 构建连接状态
  Widget _buildConnectionStatus(DeviceEntity device) {
    final logic = Get.find<IndexLogic>();
    return Obx(() {
      HomeLog.Log.i(" 刷新设备 ${device.name} 的连接状态");
      if (logic.isDeviceConnecting(device.id)) {
        return Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            SizedBox(
              width: 18, // 缩小大小
              height: 18, // 缩小大小
              child: CircularProgressIndicator(
                strokeWidth: 2,
                valueColor: AlwaysStoppedAnimation<Color>(
                    ColorPalettes.instance.primary),
              ),
            ),
            const SizedBox(height: 2), // 减小间距
            Text(
              l10n.connecting,
              style: TextStyles.instance.body1(
                color: ColorPalettes.instance.primary,
                fontWeight: FontWeight.w500,
                fontSize: 12, // 缩小字体大小
              ),
            ),
          ],
        );
      } else if (device.connected == 1) {
        return Container(
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
          decoration: BoxDecoration(
            color: ColorPalettes.instance.success.withAlpha(25), // 0.1 * 255 = 25
            borderRadius: BorderRadius.circular(12),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(Icons.check_circle,
                  color: ColorPalettes.instance.success, size: 14),
              const SizedBox(width: 4),
              Text(
                l10n.connect,
                style: TextStyles.instance.body1(
                  color: ColorPalettes.instance.success,
                  fontWeight: FontWeight.w500,
                  fontSize: 12, // 缩小字体大小
                ),
              ),
            ],
          ),
        );
      } else {
        return Container(
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
          decoration: BoxDecoration(
            color: ColorPalettes.instance.secondText.withAlpha(25), // 0.1 * 255 = 25
            borderRadius: BorderRadius.circular(12),
          ),
          child: Text(
            l10n.disconnect,
            style: TextStyles.instance.body1(
              color: ColorPalettes.instance.secondText,
              fontWeight: FontWeight.w500,
              fontSize: 12, // 缩小字体大小
            ),
          ),
        );
      }
    });
  }

  /// 显示删除对话框
  void _showDeleteDialog(BuildContext context, DeviceEntity device) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: ColorPalettes.instance.card,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        title: Text(
          l10n.deleteDevice,
          style: TextStyles.instance.h2(
            color: ColorPalettes.instance.error,
            fontWeight: FontWeight.bold,
          ),
        ),
        content: Text(
          "${l10n.deleteDeviceConfirm} ${device.name}",
          style: TextStyles.instance.body1(),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(
              l10n.cancel,
              style: TextStyles.instance.button(
                color: ColorPalettes.instance.secondText,
              ),
            ),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              onDelete(device);
            },
            child: Text(
              l10n.delete,
              style: TextStyles.instance.button(
                color: ColorPalettes.instance.error,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
