import 'package:flutter/material.dart';
import 'package:topping_home/theme/color_palettes.dart';
import 'package:topping_home/theme/text_styles.dart';
import '../../../../common/util/i18n.dart';
import '../../../../models/device_entity.dart';

/// 设备卡片组件
/// 用于显示设备信息,包含设备名称、MAC地址、连接状态等
/// 支持点击连接设备和长按删除设备功能
class DeviceCard extends StatelessWidget {
  /// 设备实体对象
  final DeviceEntity device;

  /// 连接设备回调
  final VoidCallback onConnect;

  /// 删除设备回调
  final VoidCallback onDelete;

  const DeviceCard({
    super.key,
    required this.device,
    required this.onConnect,
    required this.onDelete,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      // 底部外边距
      margin: EdgeInsets.only(bottom: 16),
      // 卡片装饰
      decoration: BoxDecoration(
        color: ColorPalettes.instance.card,
        borderRadius: BorderRadius.circular(12),
      ),
      child: ListTile(
        // 内边距
        contentPadding: EdgeInsets.all(16),
        // 左侧图标
        leading: Icon(
          Icons.speaker,
          color: ColorPalettes.instance.firstText,
          size: 40,
        ),
        // 标题(设备名称)
        title: Text(
          device.name,
          style: TextStyles.instance.h2(),
        ),
        // 副标题(MAC地址和连接状态)
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            SizedBox(height: 4),
            // MAC地址
            Text(
              device.macAddress,
              style: TextStyles.instance.h2(),
            ),
            SizedBox(height: 4),
            // 连接状态
            Text(
              device.connected == 1 ? l10n.connect : l10n.disconnect,
              style: TextStyles.instance.h2(
                color: device.connected == 1
                    ? ColorPalettes.instance.success
                    : ColorPalettes.instance.error,
              ),
            ),
          ],
        ),
        // 点击连接设备
        onTap: onConnect,
        // 长按删除设备
        onLongPress: () => _showDeleteDialog(context),
      ),
    );
  }

  /// 显示删除对话框
  /// [context] - 构建对话框的上下文
  void _showDeleteDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        // 对话框背景色
        backgroundColor: ColorPalettes.instance.card,
        // 对话框形状
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        // 对话框标题
        title: Text(
          l10n.deleteDevice,
          style: TextStyles.instance.h1(),
        ),
        // 对话框内容
        content: Text(
          '${l10n.deleteDeviceConfirm} ${device.name}',
          style: TextStyles.instance.h2(),
        ),
        // 对话框按钮
        actions: [
          // 取消按钮
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(
              l10n.cancel,
              style: TextStyles.instance.h2(),
            ),
          ),
          // 删除按钮
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              onDelete();
            },
            child: Text(
              l10n.delete,
              style: TextStyles.instance.h2(
                color: ColorPalettes.instance.error,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
