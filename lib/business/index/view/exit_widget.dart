import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';

import '../../../common/util/i18n.dart';
import '../index_logic.dart';

/// 退出app小部件
class ExitAppWidget extends StatelessWidget {

  final IndexLogic controller;

  const ExitAppWidget({
    super.key,
    required this.controller,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        const Divider(),
        ListTile(
          leading: const Icon(Icons.exit_to_app),
          title: Text(l10n.exitApp),
          onTap: () {
            Get.back();
            Get.dialog(
              AlertDialog(
                title: Text(l10n.exitApp),
                content: Text(l10n.exitAppPrompt),
                actions: [
                  TextButton(
                    child: Text(l10n.cancel),
                    onPressed: () => Get.back(),
                  ),
                  TextButton(
                    child: Text(l10n.confirm),
                    onPressed: () {
                      Get.back();
                      SystemNavigator.pop(); // 退出应用
                    },
                  ),
                ],
              ),
            );
          },
        ),
      ],
    );
  }

}