import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:topping_home/common/util/i18n.dart';
import 'package:topping_home/theme/color_palettes.dart';
import 'package:topping_home/theme/text_styles.dart';

/// 空设备视图
class EmptyDeviceView extends StatelessWidget {
  final VoidCallback onAddDevice;

  const EmptyDeviceView({
    super.key,
    required this.onAddDevice,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Expanded(
          child: GridView.count(
            crossAxisCount: 2,
            padding: const EdgeInsets.all(16),
            mainAxisSpacing: 16,
            crossAxisSpacing: 16,
            children: [
              InkWell(
                onTap: () => onAddDevice(),
                child: Container(
                  decoration: BoxDecoration(
                    color: ColorPalettes.instance.card,
                    borderRadius: BorderRadius.circular(12),
                    boxShadow: [
                      BoxShadow(
                        color: ColorPalettes.instance.shadow.withAlpha(38), // 0.15 * 255 = 38
                        blurRadius: 8,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.add_circle_outline,
                        size: 48,
                        color: ColorPalettes.instance.firstText,
                      ),
                      const SizedBox(height: 12),
                      Text(
                        l10n.addDevice,
                        style: TextStyles.instance.h3(
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        l10n.addDeviceHint,
                        style: TextStyles.instance.body2(
                          color: ColorPalettes.instance.secondText,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }
}
