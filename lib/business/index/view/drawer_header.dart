import 'package:flutter/material.dart';
import 'package:topping_home/models/user_entity.dart';
import 'package:topping_home/theme/color_palettes.dart';
import 'package:topping_home/theme/text_styles.dart';

import '../../../../common/util/i18n.dart';

/// 抽屉头部组件
class DrawerHeaderWidget extends StatelessWidget {
  final UserEntity? userInfo;
  final VoidCallback? onAvatarTap;

  const DrawerHeaderWidget({
    super.key,
    this.userInfo,
    this.onAvatarTap,
  });

  @override
  Widget build(BuildContext context) {
    return UserAccountsDrawerHeader(
      margin: EdgeInsets.zero,
      decoration: BoxDecoration(
        color: Theme.of(context).primaryColor,
      ),
      // 添加上方状态栏高度的 padding
      currentAccountPictureSize:
          Size.square(TextStyles.instance.h2().fontSize!),
      accountName: Builder(builder: (context) {
        return Padding(
          padding: EdgeInsets.only(top: MediaQuery.of(context).padding.top),
          child: Text(
            userInfo?.nickname ?? l10n.login,
            style: TextStyles.instance.h2(
              fontWeight: FontWeight.bold,
            ),
          ),
        );
      }),
      accountEmail: Text(
        userInfo?.signature ?? '',
        style: TextStyles.instance.h2(),
      ),
      currentAccountPicture: GestureDetector(
          onTap: onAvatarTap,
          child: CircleAvatar(
            backgroundImage: userInfo?.avatar?.isNotEmpty == true
                ? NetworkImage(userInfo!.avatar!)
                : null,
            backgroundColor: ColorPalettes.instance.firstText.withAlpha(20),
            child: userInfo?.avatar?.isNotEmpty != true
                ? Icon(Icons.account_circle,
                    size: TextStyles.instance.h2().fontSize!,
                    color: ColorPalettes.instance.firstText.withAlpha(70))
                : null,
          )),
    );
  }
}
