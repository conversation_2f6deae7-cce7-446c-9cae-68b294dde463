import 'package:get/get.dart';
import 'package:topping_home/models/device_entity.dart';

import '../../models/login_entity.dart';
import '../../models/user_entity.dart';

/// 首页状态
class IndexState {

  late RxList<DeviceEntity> deviceList;
  late Rx<LoginEntity?> loginInfo;
  late Rx<UserEntity?> userInfo;
  late RxBool isScanning;
  late RxList<DeviceEntity> discoveredDevices;
  late Rx<String?> backgroundImage;
  late RxDouble backgroundOpacity;
  late RxDouble backgroundBlur;

  // 添加连接中状态
  late RxString connectingDeviceId;

  IndexState() {
    deviceList = RxList<DeviceEntity>();
    loginInfo = Rx<LoginEntity?>(null);
    userInfo = Rx<UserEntity?>(null);
    isScanning = RxBool(false);
    discoveredDevices = RxList<DeviceEntity>();
    backgroundImage = Rx<String?>(null);
    backgroundOpacity = 0.5.obs;
    backgroundBlur = 5.0.obs;
    connectingDeviceId = "".obs; // 记录正在连接的设备ID
  }
}
