import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:topping_home/theme/color_palettes.dart';
import 'package:topping_home/theme/text_styles.dart';
import '../../common/util/i18n.dart';
import '../../common/widget/background_wrapper.dart';
import '../../router/routers.dart';
import '../device_add/device_add_dialog.dart';
import 'index_logic.dart';
import 'view/device_list_view.dart';
import 'view/empty_device_view.dart';

/// 首页
class IndexPage extends GetView<IndexLogic> {
  const IndexPage({super.key});

  @override
  Widget build(BuildContext context) {
    // 使用 BackgroundWrapper 替代原来的背景实现
    return BackgroundWrapper(
      // 自定义背景覆盖层设置
      useDarkOverlay: true,
      darkOverlayOpacity: 0.2,
      backgroundColor: ColorPalettes.instance.background,
      child: AnnotatedRegion<SystemUiOverlayStyle>(
        value: SystemUiOverlayStyle(
          statusBarColor: ColorPalettes.instance.transparent,
          statusBarIconBrightness: ColorPalettes.instance.isDark()
              ? Brightness.light
              : Brightness.dark,
          statusBarBrightness: ColorPalettes.instance.isDark()
              ? Brightness.dark
              : Brightness.light,
        ),
        child: Scaffold(
          // 使用透明背景，因为背景由 BackgroundWrapper 提供
          backgroundColor: ColorPalettes.instance.transparent,
          appBar: _buildAppBar(),
          body: _buildBody(),
        ),
      ),
    );
  }

  /// 构建AppBar
  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      backgroundColor: ColorPalettes.instance.card,
      elevation: 0,
      title: Text(
        l10n.homeTitle,
        style: TextStyles.instance.h2(),
      ),
      centerTitle: true,
      actions: [
        IconButton(
          icon: Icon(
            Icons.settings,
            color: ColorPalettes.instance.firstText,
          ),
          onPressed: () => AppRoutes.jumpPage(AppRoutes.settingPage),
        )
      ],
    );
  }

  /// 构建主体
  Widget _buildBody() {
    return Obx(
      () => controller.state.deviceList.isEmpty
          ? EmptyDeviceView(
              onAddDevice: _showBottomSheet,
            )
          : RefreshIndicator(
              onRefresh: () => controller.refreshDevices(),
              color: ColorPalettes.instance.primary,
              backgroundColor: ColorPalettes.instance.card,
              child: DeviceListView(
                onAdd: _showBottomSheet,
                devices: controller.state.deviceList,
                onConnect: controller.toggleDeviceConnection,
                onDelete: controller.deleteDevice,
              ),
            ),
    );
  }

  /// 显示底部弹窗
  void _showBottomSheet() {
    showModalBottomSheet(
      context: Get.context!,
      isScrollControlled: true,
      backgroundColor: ColorPalettes.instance.transparent,
      builder: (context) => DeviceAddDialog(),
    );
  }
}
