import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:topping_home/common/util/i18n.dart';
import 'package:topping_home/theme/color_palettes.dart';
import 'package:topping_home/theme/text_styles.dart';

import 'cancel_account_logic.dart';

/// 注销账号页面
class CancelAccountPage extends StatelessWidget {
  CancelAccountPage({super.key});

  final logic = Get.put<CancelAccountLogic>(CancelAccountLogic());
  final state = Get.find<CancelAccountLogic>().state;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          l10n.cancelAccount,
          style: TextStyles.instance.h2(),
        ),
        centerTitle: true,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Card(
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                _buildHeader(),
                const SizedBox(height: 24),
                _buildWarningText(),
                const SizedBox(height: 24),
                _buildPasswordInput(),
                const SizedBox(height: 24),
                _buildSubmitButton(),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// 构建标题
  Widget _buildHeader() {
    return Row(
      children: [
        Icon(Icons.warning_amber_rounded, color: ColorPalettes.instance.error),
        const SizedBox(width: 8),
        Text(
          l10n.cancelAccount,
          style: TextStyles.instance.h2(),
        ),
      ],
    );
  }

  /// 构建警告文本
  Widget _buildWarningText() {
    return Text(
      l10n.cancellationInstructions,
      style: TextStyles.instance.h3(),
    );
  }

  /// 构建密码输入框
  Widget _buildPasswordInput() {
    return Obx(() => TextField(
          controller: state.passwordController,
          obscureText: true,
          decoration: InputDecoration(
            labelText: l10n.passwordHint,
            errorText: state.errorMessage.value.isEmpty
                ? null
                : state.errorMessage.value,
            border: const OutlineInputBorder(),
          ),
        ));
  }

  /// 提交按钮
  Widget _buildSubmitButton() {
    return Obx(() => ElevatedButton(
          onPressed: state.isLoading.value ? null : logic.cancelAccount,
          style: ElevatedButton.styleFrom(
            backgroundColor: ColorPalettes.instance.error,
            padding: const EdgeInsets.symmetric(vertical: 12),
          ),
          child: state.isLoading.value
              ? SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    color: ColorPalettes.instance.secondText,
                  ),
                )
              : Text(l10n.confirmCancellation),
        ));
  }
}
