import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:topping_home/common/util/i18n.dart';
import 'package:topping_home/theme/color_palettes.dart';

import '../../repositories/user_repository.dart';
import 'cancel_account_state.dart';

/// 注销账号页面逻辑
class CancelAccountLogic extends GetxController {
  final state = CancelAccountState();

  @override
  void onClose() {
    state.dispose();
    super.onClose();
  }

  /// 注销账号
  Future<void> cancelAccount() async {
    if (state.passwordController.text.isEmpty) {
      state.errorMessage.value = l10n.passwordHint;
      return;
    }

    final currentUser = UserRepository.instance.userEntity.value;
    if (currentUser?.password != state.passwordController.text) {
      state.errorMessage.value = l10n.passwordError;
      return;
    }

    final confirm = await Get.dialog<bool>(
      AlertDialog(
        title: Text(l10n.confirmCancellation),
        content: Text(l10n.cancellationInstructions),
        actions: [
          TextButton(
            onPressed: () => Get.back(result: false),
            child: Text(l10n.cancel),
          ),
          TextButton(
            onPressed: () => Get.back(result: true),
            style: TextButton.styleFrom(
                foregroundColor: ColorPalettes.instance.error),
            child: Text(l10n.confirm),
          ),
        ],
      ),
    );

    if (confirm != true) return;

    try {
      state.isLoading.value = true;
      state.errorMessage.value = '';

      // 模拟API调用
      await Future.delayed(const Duration(seconds: 1));

      // 调用 UserManager 的注销方法
      await UserRepository.instance.cancelAccount();

      Get.snackbar(l10n.tips, l10n.cancellationSuccess);
    } catch (e) {
      state.errorMessage.value = l10n.cancellationFailure;
    } finally {
      state.isLoading.value = false;
    }
  }
}
