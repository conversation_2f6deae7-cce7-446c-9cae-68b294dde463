import 'dart:async';
import 'dart:io';
import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:path_provider/path_provider.dart';
import 'package:topping_ble_control/utils/log_util.dart';
import 'package:share_plus/share_plus.dart';
import 'package:intl/intl.dart';

/// 验证日志查看器页面
class VerifyLogViewerPage extends StatefulWidget {
  const VerifyLogViewerPage({super.key});

  @override
  State<VerifyLogViewerPage> createState() => _VerifyLogViewerPageState();
}

class _VerifyLogViewerPageState extends State<VerifyLogViewerPage> {
  List<File> _logFiles = [];
  bool _isLoading = true;
  String? _selectedLogContent;
  String? _selectedFileName;
  bool _autoRefresh = false;
  Timer? _refreshTimer;

  @override
  void initState() {
    super.initState();
    _loadLogFiles();
  }

  @override
  void dispose() {
    _refreshTimer?.cancel();
    super.dispose();
  }

  /// 启用自动刷新
  void _toggleAutoRefresh() {
    setState(() {
      _autoRefresh = !_autoRefresh;
    });

    if (_autoRefresh) {
      _refreshTimer = Timer.periodic(Duration(seconds: 3), (timer) {
        _loadLogFiles();
        if (_selectedFileName != null) {
          // 刷新当前选中的文件内容
          final selectedFile = _logFiles.firstWhere(
                (file) => file.path.split('/').last == _selectedFileName,
            orElse: () => _logFiles.first,
          );
          _loadLogFileContent(selectedFile, silent: true);
        }
      });
    } else {
      _refreshTimer?.cancel();
      _refreshTimer = null;
    }
  }

  /// 加载日志文件列表
  Future<void> _loadLogFiles() async {
    // 如果是静默刷新，不显示加载状态
    if (!_autoRefresh) {
      setState(() {
        _isLoading = true;
      });
    }

    try {
      final Directory appDocDir = await getApplicationDocumentsDirectory();
      final String logDirPath = '${appDocDir.path}/verify_logs';

      final Directory logDir = Directory(logDirPath);
      if (!await logDir.exists()) {
        await logDir.create(recursive: true);
      }

      List<FileSystemEntity> entities = await logDir.list().toList();
      _logFiles = entities
          .whereType<File>()
          .where((file) => file.path.endsWith('.log') || file.path.endsWith('.txt'))
          .toList();

      // 按修改时间排序，最新的在前面
      _logFiles
          .sort((a, b) => b.lastModifiedSync().compareTo(a.lastModifiedSync()));
    } catch (e) {
      Log.e('获取验证日志文件列表失败: $e');
    } finally {
      if (!_autoRefresh) {
        setState(() {
          _isLoading = false;
        });
      } else {
        // 对于自动刷新，只在有变化时更新UI
        setState(() {});
      }
    }
  }

  /// 加载日志文件内容
  Future<void> _loadLogFileContent(File file, {bool silent = false}) async {
    if (!silent) {
      setState(() {
        _isLoading = true;
        _selectedFileName = file.path.split('/').last;
        _selectedLogContent = null; // 清除之前的内容
      });
    }

    try {
      // 尝试以UTF-8读取
      try {
        final String content = await file.readAsString();
        if (mounted) {
          setState(() {
            _selectedFileName = file.path.split('/').last;
            _selectedLogContent = content;
            if (silent) _isLoading = false;
          });
        }
      } on FileSystemException catch (e) {
        // 如果UTF-8解码失败，尝试读取字节并用latin1解码
        Log.w('UTF-8解码失败，文件: ${file.path}: $e. 尝试使用latin1解码.');
        try {
          final List<int> bytes = await file.readAsBytes();
          final String content = latin1.decode(bytes);
          if (mounted) {
            setState(() {
              _selectedFileName = file.path.split('/').last;
              _selectedLogContent =
              "注意：文件可能不是UTF-8编码，尝试使用Latin1解码：\n\n$content";
              if (silent) _isLoading = false;
            });
          }
        } catch (e2) {
          Log.e('即使使用latin1也无法读取文件: ${file.path}: $e2');
          if (mounted) {
            setState(() {
              _selectedFileName = file.path.split('/').last;
              _selectedLogContent = '读取日志内容失败，文件可能已损坏或无法访问: $e2';
              if (silent) _isLoading = false;
            });
          }
        }
      }
    } catch (e) {
      Log.e('读取日志内容时发生未知错误，文件: ${file.path}: $e');
      if (mounted) {
        setState(() {
          _selectedFileName = file.path.split('/').last;
          _selectedLogContent = '读取日志内容时发生未知错误: $e';
          if (silent) _isLoading = false;
        });
      }
    } finally {
      if (!silent && mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  /// 清除所有日志文件
  Future<void> _clearAllLogs() async {
    final bool confirm = await showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('确认清除'),
        content: Text('确定要清除所有验证日志文件吗？此操作不可恢复。'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: Text('取消'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: TextButton.styleFrom(
              foregroundColor: Colors.red,
            ),
            child: Text('确定'),
          ),
        ],
      ),
    ) ??
        false;

    if (!confirm) return;

    setState(() {
      _isLoading = true;
    });

    try {
      for (File file in _logFiles) {
        await file.delete();
      }
      _logFiles = [];
      _selectedLogContent = null;
      _selectedFileName = null;
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('所有验证日志文件已清除')),
      );
    } catch (e) {
      Log.e('清除验证日志文件失败: $e');
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('清除验证日志文件失败: $e')),
      );
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  /// 导出日志文件
  Future<void> _exportLogFile(File file) async {
    try {
      // 使用share_plus插件分享文件
      final result = await Share.shareXFiles(
        [XFile(file.path)],
        subject: '验证日志: ${file.path.split('/').last}',
      );

      if (result.status == ShareResultStatus.dismissed) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('分享已取消')),
        );
      }
    } catch (e) {
      Log.e('导出验证日志文件失败: $e');
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('导出验证日志文件失败: $e')),
      );
    }
  }

  /// 显示文件操作选项
  void _showFileOptions(File file) {
    final String fileName = file.path.split('/').last;
    showModalBottomSheet(
      context: context,
      builder: (context) => SafeArea(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: Icon(Icons.visibility),
              title: Text('查看'),
              onTap: () {
                Navigator.pop(context);
                _loadLogFileContent(file);
              },
            ),
            ListTile(
              leading: Icon(Icons.share),
              title: Text('分享'),
              onTap: () {
                Navigator.pop(context);
                _exportLogFile(file);
              },
            ),
            ListTile(
              leading: Icon(Icons.delete, color: Colors.red),
              title: Text('删除', style: TextStyle(color: Colors.red)),
              onTap: () async {
                Navigator.pop(context);
                final bool confirm = await showDialog(
                  context: context,
                  builder: (context) => AlertDialog(
                    title: Text('确认删除'),
                    content: Text('确定要删除验证日志文件 $fileName 吗？'),
                    actions: [
                      TextButton(
                        onPressed: () => Navigator.of(context).pop(false),
                        child: Text('取消'),
                      ),
                      TextButton(
                        onPressed: () => Navigator.of(context).pop(true),
                        child: Text('删除'),
                        style: TextButton.styleFrom(
                          foregroundColor: Colors.red,
                        ),
                      ),
                    ],
                  ),
                ) ??
                    false;

                if (confirm) {
                  try {
                    await file.delete();
                    if (_selectedFileName == fileName) {
                      setState(() {
                        _selectedLogContent = null;
                        _selectedFileName = null;
                      });
                    }
                    _loadLogFiles();
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(content: Text('验证日志文件已删除')),
                    );
                  } catch (e) {
                    Log.e('删除验证日志文件失败: $e');
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(content: Text('删除验证日志文件失败: $e')),
                    );
                  }
                }
              },
            ),
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('验证日志查看器'),
        actions: [
          // 添加自动刷新按钮
          IconButton(
            icon: Icon(_autoRefresh ? Icons.sync_disabled : Icons.sync),
            onPressed: _toggleAutoRefresh,
            tooltip: _autoRefresh ? '停止自动刷新' : '启用自动刷新',
          ),
          if (_logFiles.isNotEmpty)
            IconButton(
              icon: Icon(Icons.delete_forever),
              onPressed: _clearAllLogs,
              tooltip: '清除所有日志',
            ),
          IconButton(
            icon: Icon(Icons.refresh),
            onPressed: _loadLogFiles,
            tooltip: '刷新',
          ),
        ],
      ),
      body: _isLoading
          ? Center(child: CircularProgressIndicator())
          : _buildBody(),
    );
  }

  Widget _buildBody() {
    if (_logFiles.isEmpty) {
      return Center(
        child: Text(
          '没有找到验证日志文件',
          style: TextStyle(fontSize: 18),
        ),
      );
    }

    return Row(
      children: [
        // 左侧文件列表
        SizedBox(
          width: 200,
          child: ListView.builder(
            itemCount: _logFiles.length,
            itemBuilder: (context, index) {
              final File file = _logFiles[index];
              final String fileName = file.path.split('/').last;
              final bool isSelected = fileName == _selectedFileName;

              // 获取文件修改时间作为显示日期
              final DateTime fileModified = file.lastModifiedSync();
              final String dateStr = DateFormat('yyyy-MM-dd HH:mm').format(fileModified);

              return ListTile(
                title: Text(
                  fileName,
                  style: TextStyle(fontSize: 14),
                  overflow: TextOverflow.ellipsis,
                ),
                subtitle: Text(
                  '${_getFileSize(file)}\n$dateStr',
                  style: TextStyle(fontSize: 12),
                ),
                selected: isSelected,
                selectedTileColor: Colors.blue.withOpacity(0.1),
                onTap: () => _loadLogFileContent(file),
                trailing: IconButton(
                  icon: Icon(Icons.more_vert, size: 20),
                  onPressed: () => _showFileOptions(file),
                ),
                isThreeLine: true,
                dense: true,
              );
            },
          ),
        ),
        // 垂直分隔线
        VerticalDivider(width: 1),
        // 右侧内容显示
        Expanded(
          child: _selectedLogContent == null
              ? Center(
            child: Text(
              '选择左侧的日志文件查看内容',
              style: TextStyle(fontSize: 18),
            ),
          )
              : Column(
            children: [
              // 文件名标题栏
              Container(
                padding: EdgeInsets.all(8),
                color: Colors.grey.withOpacity(0.1),
                child: Row(
                  children: [
                    Expanded(
                      child: Text(
                        '文件: $_selectedFileName',
                        style: TextStyle(fontWeight: FontWeight.bold),
                      ),
                    ),
                    IconButton(
                      icon: Icon(Icons.share),
                      onPressed: () {
                        final selectedFile = _logFiles.firstWhere(
                              (file) => file.path.split('/').last == _selectedFileName,
                          orElse: () => _logFiles.first,
                        );
                        _exportLogFile(selectedFile);
                      },
                      tooltip: '分享此日志',
                    ),
                  ],
                ),
              ),
              // 日志内容
              Expanded(
                child: SingleChildScrollView(
                  padding: EdgeInsets.all(16),
                  child: SelectableText(
                    _selectedLogContent!,
                    style: TextStyle(fontFamily: 'monospace'),
                  ),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  /// 获取文件大小的可读字符串
  String _getFileSize(File file) {
    try {
      final int bytes = file.lengthSync();
      if (bytes < 1024) {
        return '$bytes B';
      } else if (bytes < 1024 * 1024) {
        return '${(bytes / 1024).toStringAsFixed(1)} KB';
      } else {
        return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
      }
    } catch (e) {
      return '未知大小';
    }
  }
}