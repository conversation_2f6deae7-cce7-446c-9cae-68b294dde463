import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:topping_home/router/routers.dart';

import '../../common/util/i18n.dart';
import '../../repositories/user_repository.dart';
import 'personal_center_state.dart';

/// 个人中心逻辑处理类
class PersonalCenterLogic extends GetxController {
  final PersonalCenterState state = PersonalCenterState();

  /// 个人中心列表项点击事件
  void onItemTap(int index) {
    switch (index) {
      case 0:
        AppRoutes.jumpPage(AppRoutes.editPersonalInfoPage);
        break;
      case 1:
        AppRoutes.jumpPage(AppRoutes.accountSecurityPage);
        break;
      case 2:
        _showLogoutDialog();
        break;
    }
  }

  /// 显示登出对话框
  void _showLogoutDialog() {
    Get.dialog(
      AlertDialog(
        title: Text(l10n.logout),
        content: Text(l10n.logoutHint),
        actions: [
          TextButton(
            child: Text(l10n.cancel),
            onPressed: () => Get.back(),
          ),
          TextButton(
            child: Text(l10n.confirm),
            onPressed: () {
              Get.back();
              UserRepository.instance.logout();
              // 可以在这里添加登出后的导航逻辑
            },
          ),
        ],
      ),
    );
  }
}
