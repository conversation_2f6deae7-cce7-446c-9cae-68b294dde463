import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../common/util/i18n.dart';
import 'personal_center_logic.dart';

/// 个人中心页面
class PersonalCenterPage extends GetView<PersonalCenterLogic> {
  const PersonalCenterPage({super.key});

  @override
  Widget build(BuildContext context) {
    final state = controller.state;

    return Scaffold(
        appBar: AppBar(
          title: Text(l10n.personalCenter),
          centerTitle: true,
          elevation: 0,
        ),
        body: ListView.builder(
          itemCount: state.dataList.length,
          itemBuilder: (context, index) => ListTile(
            title: Text(state.dataList[index]),
            trailing: Icon(Icons.arrow_forward_ios),
          ),
        ));
  }
}
