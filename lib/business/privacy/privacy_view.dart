import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:topping_home/business/privacy/privacy_logic.dart';
import 'package:topping_home/common/util/i18n.dart';
import 'package:topping_home/theme/color_palettes.dart';
import 'package:topping_home/theme/text_styles.dart';
import '../../common/widget/background_wrapper.dart';

/// 隐私政策页面
class PrivacyPage extends GetView<PrivacyLogic> {
  const PrivacyPage({super.key});

  @override
  Widget build(BuildContext context) {
    return BackgroundWrapper(
      useDarkOverlay: true,
      darkOverlayOpacity: 0.3,
      backgroundColor: ColorPalettes.instance.background,
      child: AnnotatedRegion<SystemUiOverlayStyle>(
        value: SystemUiOverlayStyle.dark.copyWith(
          statusBarColor: ColorPalettes.instance.transparent,
          statusBarIconBrightness: Brightness.dark,
          statusBarBrightness: Brightness.light,
        ),
        child: Scaffold(
          backgroundColor: ColorPalettes.instance.transparent,
          appBar: AppBar(
            backgroundColor: ColorPalettes.instance.card,
            elevation: 0,
            title: Text(
              l10n.privacyPolicyText,
              style: TextStyles.instance.h2(
                color: ColorPalettes.instance.firstText,
              ),
            ),
            centerTitle: false,
            iconTheme: IconThemeData(color: ColorPalettes.instance.firstText),
          ),
          body: Obx(() => controller.state.isLoading.value
              ? const Center(child: CircularProgressIndicator())
              : _buildContent()),
          bottomNavigationBar: Obx(() => controller.state.showAcceptButton.value
              ? _buildAcceptButton()
              : const SizedBox.shrink()),
        ),
      ),
    );
  }

  /// 构建内容
  Widget _buildContent() {
    if (controller.state.privacyContent.value == null) {
      return Center(
        child: Text(
          l10n.failedToLoadContent,
          style: TextStyles.instance.h2(
            color: ColorPalettes.instance.firstText,
          ),
        ),
      );
    }

    final privacyContent = controller.state.privacyContent.value!;

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            privacyContent['title'] ?? '',
            style: TextStyles.instance.h2(
              color: ColorPalettes.instance.firstText,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            '${l10n.lastUpdated}: ${privacyContent['last_updated'] ?? ''}',
            style: TextStyles.instance.h2(
              color: ColorPalettes.instance.firstText.withOpacity(0.5),
            ),
          ),
          const SizedBox(height: 20),
          ..._buildSections(privacyContent),
        ],
      ),
    );
  }

  /// 构建章节
  List<Widget> _buildSections(Map<String, dynamic> privacyContent) {
    List<Widget> sections = [];

    if (privacyContent.containsKey('sections')) {
      for (var section in privacyContent['sections']) {
        sections.add(const SizedBox(height: 16));
        sections.add(
          Text(
            section['title'] ?? '',
            style: TextStyles.instance.h2(
              color: ColorPalettes.instance.firstText,
              fontSize: TextStyles.instance.h2().fontSize,
              fontWeight: FontWeight.bold,
            ),
          ),
        );
        sections.add(const SizedBox(height: 8));
        sections.add(
          Text(
            section['content'] ?? '',
            style: TextStyle(
              color: ColorPalettes.instance.firstText,
              height: 1.5,
            ),
          ),
        );
      }
    }

    return sections;
  }

  /// 构建同意按钮
  Widget _buildAcceptButton() {
    return SafeArea(
      child: Container(
        padding: const EdgeInsets.all(16),
        child: ElevatedButton(
          onPressed: controller.acceptPrivacy,
          style: ElevatedButton.styleFrom(
            backgroundColor: ColorPalettes.instance.primary,
            padding: const EdgeInsets.symmetric(vertical: 16),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(25),
            ),
          ),
          child: Text(
            l10n.agree,
            style: TextStyles.instance.h2(
              fontWeight: FontWeight.bold,
              color: ColorPalettes.instance.firstText,
            ),
          ),
        ),
      ),
    );
  }
}
