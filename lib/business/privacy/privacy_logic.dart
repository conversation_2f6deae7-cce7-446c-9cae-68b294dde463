import 'package:get/get.dart';
import 'package:topping_home/common/util/log_util.dart';
import 'package:topping_home/repositories/agreement_repository.dart';
import 'privacy_state.dart';

/// 隐私政策逻辑处理类
class PrivacyLogic extends GetxController {
  final PrivacyState state = PrivacyState();
  final AgreementRepository _agreementManager = AgreementRepository();

  // 添加接受协议的回调
  Function? onAccept;

  // 构造函数接收可选参数
  PrivacyLogic({this.onAccept, bool showAcceptButton = false}) {
    state.showAcceptButton.value = showAcceptButton;
  }

  @override
  void onReady() {
    super.onReady();
    loadPrivacyContent();
  }

  /// 加载隐私政策内容
  Future<void> loadPrivacyContent() async {
    state.isLoading.value = true;

    try {
      final content = await _agreementManager.loadPrivacyContent();
      state.privacyContent.value = content;
    } catch (e) {
      Log.e('加载隐私政策失败: $e');
    } finally {
      state.isLoading.value = false;
    }
  }

  /// 接受隐私政策
  Future<void> acceptPrivacy() async {
    await _agreementManager.acceptPrivacy();
    if (onAccept != null) {
      onAccept!();
    }
  }
}
