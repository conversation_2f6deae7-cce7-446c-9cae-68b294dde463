import 'package:flutter/material.dart';
import 'package:get/get.dart';

/// 修改手机号状态
class ModifyMobileState {
  // 控制器
  late TextEditingController oldPhoneController;
  late TextEditingController newPhoneController;
  late TextEditingController oldVerifyCodeController;
  late TextEditingController newVerifyCodeController;

  // 响应式变量
  final currentStep = 0.obs; // 0: 验证旧手机, 1: 设置新手机
  final isLoading = false.obs;
  final oldPhoneCountdown = 0.obs;
  final newPhoneCountdown = 0.obs;

  ModifyMobileState() {
    oldPhoneController = TextEditingController();
    newPhoneController = TextEditingController();
    oldVerifyCodeController = TextEditingController();
    newVerifyCodeController = TextEditingController();
  }

  void dispose() {
    oldPhoneController.dispose();
    newPhoneController.dispose();
    oldVerifyCodeController.dispose();
    newVerifyCodeController.dispose();
  }
}
