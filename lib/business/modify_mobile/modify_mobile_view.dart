import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:topping_home/theme/color_palettes.dart';
import 'package:topping_home/theme/text_styles.dart';
import '../../../common/util/i18n.dart';
import 'modify_mobile_logic.dart';

/// 修改手机号页面
class ModifyMobilePage extends StatelessWidget {
  ModifyMobilePage({super.key});

  final logic = Get.find<ModifyMobileLogic>();
  final state = Get.find<ModifyMobileLogic>().state;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(l10n.modifyPhone),
        centerTitle: true,
      ),
      body: Obx(() => SingleChildScrollView(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              children: [
                _buildStepIndicator(),
                const SizedBox(height: 24),
                state.currentStep.value == 0
                    ? _buildVerifyOldPhoneStep()
                    : _buildNewPhoneStep(),
              ],
            ),
          )),
    );
  }

  /// 构建步骤指示器
  Widget _buildStepIndicator() {
    return Row(
      children: [
        Expanded(
          child: Column(
            children: [
              CircleAvatar(
                radius: 12,
                backgroundColor: ColorPalettes.instance.firstText,
                child: Text('1',
                    style: TextStyle(color: ColorPalettes.instance.firstText)),
              ),
              Text(l10n.verifyOldPhone),
            ],
          ),
        ),
        Expanded(
          child: Column(
            children: [
              CircleAvatar(
                radius: 12,
                backgroundColor: state.currentStep.value == 1
                    ? ColorPalettes.instance.firstText
                    : ColorPalettes.instance.firstText.withAlpha(30),
                child: Text(
                  '2',
                  style: TextStyles.instance.h2(
                    color: ColorPalettes.instance.firstText,
                  ),
                ),
              ),
              Text(l10n.setNewPhone),
            ],
          ),
        ),
      ],
    );
  }

  /// 构建验证旧手机号步骤
  Widget _buildVerifyOldPhoneStep() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            TextField(
              controller: state.oldPhoneController,
              decoration: InputDecoration(
                labelText: l10n.oldPhone,
                enabled: false,
              ),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: TextField(
                    controller: state.oldVerifyCodeController,
                    decoration: InputDecoration(
                      labelText: l10n.verifyCode,
                    ),
                    keyboardType: TextInputType.number,
                  ),
                ),
                const SizedBox(width: 16),
                Obx(() => ElevatedButton(
                      onPressed: state.oldPhoneCountdown.value > 0
                          ? null
                          : logic.sendOldPhoneCode,
                      child: Text(
                        state.oldPhoneCountdown.value > 0
                            ? '${state.oldPhoneCountdown.value}s'
                            : l10n.getVerificationCode,
                      ),
                    )),
              ],
            ),
            const SizedBox(height: 24),
            Obx(() => ElevatedButton(
                  onPressed:
                      state.isLoading.value ? null : logic.verifyOldPhone,
                  child: state.isLoading.value
                      ? const SizedBox(
                          width: 20,
                          height: 20,
                          child: CircularProgressIndicator(strokeWidth: 2),
                        )
                      : Text(l10n.next),
                )),
          ],
        ),
      ),
    );
  }

  /// 构建新手机号步骤
  Widget _buildNewPhoneStep() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            TextField(
              controller: state.newPhoneController,
              decoration: InputDecoration(
                labelText: l10n.newPhone,
              ),
              keyboardType: TextInputType.phone,
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: TextField(
                    controller: state.newVerifyCodeController,
                    decoration: InputDecoration(
                      labelText: l10n.verifyCode,
                    ),
                    keyboardType: TextInputType.number,
                  ),
                ),
                const SizedBox(width: 16),
                Obx(() => ElevatedButton(
                      onPressed: state.newPhoneCountdown.value > 0
                          ? null
                          : logic.sendNewPhoneCode,
                      child: Text(
                        state.newPhoneCountdown.value > 0
                            ? '${state.newPhoneCountdown.value}s'
                            : l10n.getVerificationCode,
                      ),
                    )),
              ],
            ),
            const SizedBox(height: 24),
            Obx(() => ElevatedButton(
                  onPressed:
                      state.isLoading.value ? null : logic.updateNewPhone,
                  child: state.isLoading.value
                      ? const SizedBox(
                          width: 20,
                          height: 20,
                          child: CircularProgressIndicator(strokeWidth: 2),
                        )
                      : Text(l10n.confirm),
                )),
          ],
        ),
      ),
    );
  }
}
