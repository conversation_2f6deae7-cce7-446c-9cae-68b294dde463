import 'dart:async';
import 'package:get/get.dart';
import 'package:topping_home/theme/color_palettes.dart';
import '../../../common/util/i18n.dart';
import '../../repositories/user_repository.dart';
import '../../../models/user_entity.dart';
import 'modify_mobile_state.dart';

/// 修改手机号逻辑
class ModifyMobileLogic extends GetxController {
  final ModifyMobileState state = ModifyMobileState();
  Timer? _oldTimer;
  Timer? _newTimer;

  @override
  void onInit() {
    super.onInit();
    // 设置初始手机号
    state.oldPhoneController.text =
        UserRepository.instance.userEntity.value?.userPhone ?? '';
  }

  @override
  void onClose() {
    _oldTimer?.cancel();
    _newTimer?.cancel();
    state.dispose();
    super.onClose();
  }

  /// 发送验证码到旧手机
  Future<void> sendOldPhoneCode() async {
    if (!_validatePhone(state.oldPhoneController.text)) return;

    state.isLoading.value = true;
    try {
      // TODO: 调用发送验证码API
      await Future.delayed(const Duration(seconds: 1)); // 模拟网络请求

      // 开始倒计时
      state.oldPhoneCountdown.value = 60;
      _oldTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
        if (state.oldPhoneCountdown.value > 0) {
          state.oldPhoneCountdown.value--;
        } else {
          timer.cancel();
        }
      });

      Get.snackbar(
        l10n.success,
        l10n.verificationCodeSent,
        snackPosition: SnackPosition.TOP,
        backgroundColor: ColorPalettes.instance.success,
        colorText: ColorPalettes.instance.firstText,
      );
    } catch (e) {
      Get.snackbar(
        l10n.error,
        e.toString(),
        snackPosition: SnackPosition.TOP,
        backgroundColor: ColorPalettes.instance.error,
        colorText: ColorPalettes.instance.firstText,
      );
    } finally {
      state.isLoading.value = false;
    }
  }

  /// 发送验证码到新手机
  Future<void> sendNewPhoneCode() async {
    if (!_validatePhone(state.newPhoneController.text)) return;

    state.isLoading.value = true;
    try {
      // TODO: 调用发送验证码API
      await Future.delayed(const Duration(seconds: 1)); // 模拟网络请求

      // 开始倒计时
      state.newPhoneCountdown.value = 60;
      _newTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
        if (state.newPhoneCountdown.value > 0) {
          state.newPhoneCountdown.value--;
        } else {
          timer.cancel();
        }
      });

      Get.snackbar(
        l10n.success,
        l10n.verificationCodeSent,
        snackPosition: SnackPosition.TOP,
        backgroundColor: ColorPalettes.instance.success,
        colorText: ColorPalettes.instance.firstText,
      );
    } catch (e) {
      Get.snackbar(
        l10n.error,
        e.toString(),
        snackPosition: SnackPosition.TOP,
        backgroundColor: ColorPalettes.instance.error,
        colorText: ColorPalettes.instance.firstText,
      );
    } finally {
      state.isLoading.value = false;
    }
  }

  /// 验证旧手机号
  Future<void> verifyOldPhone() async {
    if (!_validateOldPhoneStep()) return;

    state.isLoading.value = true;
    try {
      // TODO: 调用验证旧手机号API
      await Future.delayed(const Duration(seconds: 1)); // 模拟网络请求

      // 验证成功,进入下一步
      state.currentStep.value = 1;
    } catch (e) {
      Get.snackbar(
        l10n.error,
        e.toString(),
        snackPosition: SnackPosition.TOP,
        backgroundColor: ColorPalettes.instance.error,
        colorText: ColorPalettes.instance.firstText,
      );
    } finally {
      state.isLoading.value = false;
    }
  }

  /// 修改为新手机号
  Future<void> updateNewPhone() async {
    if (!_validateNewPhoneStep()) return;

    state.isLoading.value = true;
    try {
      // 获取当前用户信息
      final currentUser = UserRepository.instance.userEntity.value;
      if (currentUser == null) throw Exception(l10n.userNotFound);

      // 创建更新后的用户信息
      final updatedUser = UserEntity()
        ..id = currentUser.id
        ..userPhone = state.newPhoneController.text
        ..password = currentUser.password
        ..nickname = currentUser.nickname
        ..signature = currentUser.signature
        ..avatar = currentUser.avatar
        ..sex = currentUser.sex
        ..birthday = currentUser.birthday;

      // 更新用户信息
      await UserRepository.instance.updateUserEntity(updatedUser);

      Get.back();
      Get.snackbar(
        l10n.success,
        l10n.phoneUpdateSuccess,
        snackPosition: SnackPosition.TOP,
        backgroundColor: ColorPalettes.instance.success,
        colorText: ColorPalettes.instance.firstText,
      );
    } catch (e) {
      Get.snackbar(
        l10n.error,
        e.toString(),
        snackPosition: SnackPosition.TOP,
        backgroundColor: ColorPalettes.instance.error,
        colorText: ColorPalettes.instance.firstText,
      );
    } finally {
      state.isLoading.value = false;
    }
  }

  /// 验证手机号格式
  bool _validatePhone(String phone) {
    if (phone.isEmpty) {
      Get.snackbar(l10n.error, l10n.phoneEmpty);
      return false;
    }
    // TODO: 添加更严格的手机号格式验证
    return true;
  }

  // 验证第一步输入
  bool _validateOldPhoneStep() {
    if (!_validatePhone(state.oldPhoneController.text)) return false;
    if (state.oldVerifyCodeController.text.isEmpty) {
      Get.snackbar(l10n.error, l10n.verificationCodeEmpty);
      return false;
    }
    return true;
  }

  // 验证第二步输入
  bool _validateNewPhoneStep() {
    if (!_validatePhone(state.newPhoneController.text)) return false;
    if (state.newVerifyCodeController.text.isEmpty) {
      Get.snackbar(l10n.error, l10n.verificationCodeEmpty);
      return false;
    }
    if (state.newPhoneController.text == state.oldPhoneController.text) {
      Get.snackbar(l10n.error, l10n.samePhoneNumber);
      return false;
    }
    return true;
  }
}
