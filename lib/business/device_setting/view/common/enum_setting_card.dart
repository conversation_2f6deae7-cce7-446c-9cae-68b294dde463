import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:topping_home/theme/color_palettes.dart';
import 'package:topping_home/theme/text_styles.dart';

import '../../../../../enums/interfaces/localizable.dart';
import '../../device_setting_logic.dart';
import '../setting_card.dart';
import '../setting_dropdown.dart';

/// 通用枚举设置卡片组件
/// 用于处理不同设备类型的枚举显示，减少重复代码
///
/// 泛型参数:
/// - T: 设备使用的枚举类型
class EnumSettingCard<T extends Enum> extends StatelessWidget {
  /// 设置项标题
  final String title;

  /// 获取当前设置值的函数
  final T? Function(DeviceSettingLogic logic) getValue;

  /// 枚举选项列表
  final List<T> options;

  /// 值变更回调
  final Function(T) onChanged;

  /// 默认值
  final T defaultValue;

  const EnumSettingCard({
    super.key,
    required this.title,
    required this.getValue,
    required this.options,
    required this.onChanged,
    required this.defaultValue,
  });

  @override
  Widget build(BuildContext context) {
    final logic = Get.find<DeviceSettingLogic>();

    return Obx(() {
      final currentValue = getValue(logic) ?? defaultValue;

      // 安全地获取本地化文本
      String displayText = '';
      if (currentValue is Localizable) {
        displayText = (currentValue as Localizable).localized(context);
      } else {
        displayText = currentValue.toString();
      }

      return SettingCard(
        title: title,
        showTitle: false,
        child: SettingDropdown(
          title: title,
          value: displayText,
          onPressed: () => _showSelectionDialog(context, logic),
        ),
      );
    });
  }

  /// 显示选择对话框
  void _showSelectionDialog(BuildContext context, DeviceSettingLogic logic) {
    final currentValue = getValue(logic) ?? defaultValue;

    showModalBottomSheet(
      context: context,
      backgroundColor: ColorPalettes.instance.card,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(12)),
      ),
      builder: (BuildContext context) {
        return Container(
          padding: EdgeInsets.symmetric(vertical: 10),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Padding(
                padding:
                    const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                child:
                    Text(title, style: TextStyles.instance.h3()),
              ),
              Divider(),
              Expanded(
                child: ListView.builder(
                  itemCount: options.length,
                  itemBuilder: (context, index) {
                    final option = options[index];
                    final isSelected = option == currentValue;

                    // 安全地获取本地化文本
                    String optionText = '';
                    if (option is Localizable) {
                      optionText = (option as Localizable).localized(context);
                    } else {
                      optionText = option.toString();
                    }

                    return ListTile(
                      title: Row(
                        children: [
                          Text(optionText, style: TextStyles.instance.h3()),
                          if (isSelected) ...[
                            SizedBox(width: 8),
                            Icon(Icons.check_circle,
                                color: ColorPalettes.instance.accent,
                                size: 16),
                          ],
                        ],
                      ),
                      onTap: () {
                        onChanged(option);
                        Navigator.pop(context);
                      },
                    );
                  },
                ),
              ),
            ],
          ),
        );
      },
    );
  }
}
