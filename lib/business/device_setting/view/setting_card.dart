import 'package:flutter/material.dart';
import 'package:topping_home/theme/color_palettes.dart';
import 'package:topping_home/theme/text_styles.dart';

/// 设置卡片组件，用于包装设置项
class SettingCard extends StatelessWidget {
  /// 标题
  final String title;

  /// 子组件
  final Widget child;
  
  /// 是否显示标题
  final bool showTitle;

  const SettingCard({
    super.key,
    required this.title,
    required this.child,
    this.showTitle = true,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      color: ColorPalettes.instance.card,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(6),
      ),
      margin: EdgeInsets.symmetric(vertical: 3),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 根据showTitle决定是否显示标题
          if (showTitle)
            Padding(
              padding: const EdgeInsets.only(left: 16, top: 12, bottom: 4),
              child: Text(
                title,
                style: TextStyles.instance.h3(),
              ),
            ),
          // 原有的子组件
          child,
        ],
      ),
    );
  }
}
