import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:topping_home/theme/text_styles.dart';

import '../../../theme/color_palettes.dart';
import '../device_setting_logic.dart';

/// 音频平衡控制组件
class AudioBalanceControl extends StatelessWidget {
  const AudioBalanceControl({super.key});

  /// 音频平衡控制组件
  @override
  Widget build(BuildContext context) {
    // 获取设备设置逻辑
    final logic = Get.find<DeviceSettingLogic>();
    // 获取设备设置状态
    final state = logic.state;

    return GetBuilder<DeviceSettingLogic>(builder: (logic) {
      // 计算当前平衡值（从-95到95）
      int balanceValue = 0;

      // 如果左声道小于95（即有衰减），则平衡值为负
      if (state.leftChannel < 95) {
        balanceValue = -(95 - state.leftChannel);
      }
      // 如果右声道大于0（即有增益），则平衡值为正
      else if (state.rightChannel > 0) {
        balanceValue = state.rightChannel;
      }

      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildBalanceControl(
            context: context,
            value: balanceValue.toDouble(),
            min: -95,
            max: 95,
            divisions: 38, // 步进为5
            onChanged: (value) {
              // 使用新的统一方法
              final intValue = value.round();
              // 仅更新UI，不发送命令
              if (intValue < 0) {
                logic.setLeftChannel(95 + intValue);
                logic.setRightChannel(0);
              } else if (intValue > 0) {
                logic.setLeftChannel(95);
                logic.setRightChannel(intValue);
              } else {
                logic.setLeftChannel(95);
                logic.setRightChannel(0);
              }
            },
            onChangeEnd: (value) {
              // 使用新的统一方法
              logic.setChannelBalanceEnd(value.round());
            },
            formatValue: (value) {
              final intValue = value.round();
              if (intValue < 0) {
                return 'L ${(-intValue / 10).toStringAsFixed(1)}';
              } else if (intValue > 0) {
                return 'R ${(intValue / 10).toStringAsFixed(1)}';
              } else {
                return '0.0';
              }
            },
          ),
        ],
      );
    });
  }

  /// 构建平衡控制组件
  Widget _buildBalanceControl({
    required BuildContext context,
    required double value,
    required double min,
    required double max,
    required int divisions,
    required ValueChanged<double> onChanged,
    required ValueChanged<double> onChangeEnd,
    required String Function(double) formatValue,
  }) {
    return Column(
      children: [
        Padding(
          padding: const EdgeInsets.only(bottom: 8),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'L 9.5',
                style: TextStyles.instance.h2(),
              ),
              Text(
                formatValue(value),
                style: TextStyle(
                  color: ColorPalettes.instance.firstText,
                  fontSize: TextStyles.instance.h2().fontSize,
                  fontWeight: FontWeight.w500,
                ),
              ),
              Text(
                'R 9.5',
                style: TextStyles.instance.h2(),
              ),
            ],
          ),
        ),
        // 滑块主题
        SliderTheme(
          data: SliderTheme.of(context).copyWith(
            trackHeight: 3.0,
            thumbShape: const RoundSliderThumbShape(
              enabledThumbRadius: 8,
            ),
            overlayShape: const RoundSliderOverlayShape(
              overlayRadius: 16,
            ),
          ),
          child: Slider(
            // 滑块
            value: value,
            min: min,
            max: max,
            divisions: divisions,
            onChanged: onChanged,
            onChangeEnd: onChangeEnd,
            activeColor: ColorPalettes.instance.accent,
            inactiveColor: ColorPalettes.instance.accentLighter,
          ),
        ),
      ],
    );
  }
}
