import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:topping_home/theme/color_palettes.dart';
import 'package:topping_home/theme/text_styles.dart';
import '../../../../common/util/i18n.dart';
import '../device_setting_logic.dart';
import 'setting_card.dart';

/// 操作按钮区域组件
/// ActionButtonsSection 是一个 StatelessWidget，用于显示两个操作按钮："重置设置"和"恢复出厂设置"。
class ActionButtonsSection extends StatelessWidget {
  ActionButtonsSection({super.key});

  final logic = Get.find<DeviceSettingLogic>();

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        SettingCard(
          title: l10n.restoreFactorySettings,
          showTitle: false, // 不显示标题
          child: ListTile(
            title: Text(
              l10n.restoreFactorySettings,
              style: TextStyle(
                color: ColorPalettes.instance.firstText,
                fontSize: 14,
              ),
            ),
            trailing: Icon(
              Icons.arrow_forward_ios,
              color: ColorPalettes.instance.firstText,
              size: 12,
            ),
            onTap: () => _showRestoreFactorySettingsDialog(context),
          ),
        ),
      ],
    );
  }

  /// 显示恢复出厂设置的确认对话框。
  void _showRestoreFactorySettingsDialog(BuildContext context) {
    Get.dialog(
      AlertDialog(
        backgroundColor: ColorPalettes.instance.card,
        title: Text(
          l10n.restoreFactorySettings,
          style: TextStyles.instance.h2(),
        ),
        content: Text(
          l10n.restoreFactorySettingsConfirmation,
          style: TextStyles.instance.h3(),
        ),
        actions: [
          // 取消按钮
          TextButton(
            onPressed: () => Get.back(),
            child: Text(
              l10n.cancel,
              style: TextStyles.instance.h3(),
            ),
          ),
          // 确认按钮
          TextButton(
            onPressed: () {
              // 实现恢复出厂设置逻辑
              logic.restoreFactorySettings();
              Get.back();
            },
            child: Text(
              l10n.confirm,
              style: TextStyles.instance.h3().copyWith(
                color: ColorPalettes.instance.accent, // 使用强调色
              ),
            ),
          ),
        ],
      ),
    );
  }
}
