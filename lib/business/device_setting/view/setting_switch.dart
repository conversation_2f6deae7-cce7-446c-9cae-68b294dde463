import 'package:flutter/material.dart';
import 'package:topping_home/theme/color_palettes.dart';
import 'package:topping_home/theme/text_styles.dart';

/// 设置开关组件
class SettingSwitch extends StatelessWidget {
  /// 标题
  final String title;

  /// 当前值
  final bool value;

  /// 值变化回调
  final ValueChanged<bool> onChanged;

  const SettingSwitch({
    super.key,
    required this.title,
    required this.value,
    required this.onChanged,
  });

  @override
  Widget build(BuildContext context) {
    return ListTile(
      dense: true,
      contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 0),
      title: Text(
        title,
        style: TextStyles.instance.h3(),
      ),
      trailing: Transform.scale(
        scale: 0.8,
        child: Switch(
          value: value,
          activeColor: ColorPalettes.instance.accent, // 使用强调色替代主题色
          onChanged: onChanged,
        ),
      ),
    );
  }
}
