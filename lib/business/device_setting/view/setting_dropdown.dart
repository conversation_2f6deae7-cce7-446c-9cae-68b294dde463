import 'package:flutter/material.dart';
import 'package:topping_home/theme/text_styles.dart';
import 'package:topping_home/theme/color_palettes.dart';

/// 设置下拉组件
class SettingDropdown extends StatelessWidget {
  /// 标题
  final String title;

  /// 当前值
  final String value;

  /// 点击回调
  final VoidCallback onPressed;

  const SettingDropdown({
    super.key,
    required this.title,
    required this.value,
    required this.onPressed,
  });

  @override
  Widget build(BuildContext context) {
    // 计算可用于trailing的最大宽度，通常ListTile有一个默认的内边距
    // 屏幕宽度的一半是一个相对安全的值，防止与title重叠
    final width = MediaQuery.of(context).size.width / 2;
    
    return ListTile(
      dense: true,
      contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 0),
      title: Text(
        title,
        style: TextStyles.instance.h3(),
      ),
      trailing: Container(
        constraints: BoxConstraints(maxWidth: width),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Flexible(
              child: Text(
                value,
                style: TextStyles.instance.h3(),
                overflow: TextOverflow.ellipsis,
                maxLines: 1,
              ),
            ),
            const SizedBox(width: 4),
            Icon(Icons.arrow_forward_ios,
                color: ColorPalettes.instance.firstText, size: 12),
          ],
        ),
      ),
      onTap: onPressed,
    );
  }
}
