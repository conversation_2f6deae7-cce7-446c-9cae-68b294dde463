import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:topping_ble_control/device/device_factory.dart';
import 'package:topping_home/business/device_setting/device_setting_state.dart';
import 'package:topping_home/theme/color_palettes.dart';
import 'package:topping_home/theme/text_styles.dart';
import '../../../../common/util/i18n.dart';
import '../../../../common/util/log_util.dart';
import '../../environment_config.dart';
import 'device_setting_logic.dart';
import 'view/action_buttons_section.dart';
import 'view/audio_settings_section.dart';
import 'view/system_settings_section.dart';

/// 设备设置页面
class DeviceSettingPage extends StatefulWidget {
  const DeviceSettingPage({super.key});

  @override
  State<DeviceSettingPage> createState() => _DeviceSettingPageState();
}

class _DeviceSettingPageState extends State<DeviceSettingPage> {
  late DeviceSettingLogic logic;
  final DeviceFactory _deviceFactory = DeviceFactory();

  @override
  void initState() {
    super.initState();

    final connectedDevice = _deviceFactory.getConnectedDevice();
    final deviceId = connectedDevice?.id.toString() ?? '';

    Log.i('设备设置页面 - 获取到当前连接的设备ID: $deviceId');
    Log.i('设备设置页面 - 设备类型: ${connectedDevice?.deviceType}');

    logic = Get.put(DeviceSettingLogic(deviceId: deviceId));
  }

  @override
  Widget build(BuildContext context) {
    return AnnotatedRegion<SystemUiOverlayStyle>(
      value: SystemUiOverlayStyle(
        statusBarColor: ColorPalettes.instance.transparent,
        statusBarIconBrightness: ColorPalettes.instance.isDark()
            ? Brightness.light
            : Brightness.dark,
      ),
      child: WillPopScope(
        onWillPop: () async {
          // 使用Get.back()而不是系统的返回方式
          Get.back();
          return false; // 阻止系统默认返回行为
        },
        child: Scaffold(
          backgroundColor: ColorPalettes.instance.card,
          appBar: AppBar(
            backgroundColor: ColorPalettes.instance.card,
            elevation: 0,
            title: Text(
              l10n.advancedSettings,
              style: TextStyles.instance.h2(),
            ),
            leading: IconButton(
              icon: Icon(
                Icons.arrow_back_ios,
                color: ColorPalettes.instance.firstText,
              ),
              onPressed: () => Get.back(),
            ),
            iconTheme: IconThemeData(color: ColorPalettes.instance.firstText),
          ),
          body: SingleChildScrollView(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                children: [
                  if (!EnvironmentConfig().isProd)
                    Padding(
                      padding: const EdgeInsets.only(bottom: 8),
                      child: Text(
                        '设备类型: ${logic.isD900Device ? "D900" : "DX5"}',
                        style: TextStyle(color: Colors.grey, fontSize: 12),
                      ),
                    ),
                  SystemSettingsSection(),
                  SizedBox(height: 6),
                  AudioSettingsSection(),
                  SizedBox(height: 16),
                  ActionButtonsSection(),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
