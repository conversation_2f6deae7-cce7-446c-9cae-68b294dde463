import 'dart:async';

import 'package:get/get.dart';
import 'package:topping_ble_control/device/device_factory.dart';
import 'package:topping_ble_control/model/enums/ble_connection_state.dart';
import 'package:topping_ble_control/model/enums/device_mode_type.dart';
import 'package:topping_home/enums/d900/d900_usb_select_type.dart';
import 'package:topping_home/models/d900_device_settings.dart';
import 'package:topping_ble_control/device/d900/d900_device_manager.dart';

import '../../common/util/log_util.dart';
import '../../enums/d900/d900_iis_phase_type.dart';
import '../../enums/d900/d900_iis_dsd_channel_type.dart';
import '../../enums/dx5/dx5_decode_mode_type.dart';
import '../../enums/dx5/dx5_filter_parameter_type.dart';
import '../../models/dx5_device_settings.dart';
import '../../repositories/device_repository.dart';
import '../../utils/device_event_synchronizer.dart';
import 'device_setting_state.dart';

/// 设备设置逻辑
class DeviceSettingLogic extends GetxController {
  final DeviceSettingState state = DeviceSettingState();
  final DeviceRepository storage = Get.find<DeviceRepository>();
  final String deviceId;

  DeviceSettingLogic({required this.deviceId});

  // 使用 DeviceFactory
  final DeviceFactory _deviceFactory = DeviceFactory();

  // 缓存当前设备类型
  DeviceModeType _deviceType = DeviceModeType.unknown;

  // 事件同步器实例
  DeviceEventSynchronizer? _eventSynchronizer;

  // 连接状态监听
  StreamSubscription? _connectionSubscription;

  // 获取当前设备管理器（只读，可能为 null）
  DeviceManager? get currentDeviceManager =>
      _deviceFactory.currentDeviceManager;

  bool get isD900Device => _deviceType == DeviceModeType.dx9;

  // 暴露设备类型枚举
  DeviceModeType get deviceType => _deviceType;

  // 设置更新流订阅
  StreamSubscription? _settingsSubscription;

  @override
  void onInit() {
    super.onInit();
    Log.i('DeviceSettingLogic onInit for device $deviceId');
    _loadDeviceSettings(); // 加载初始设置并确定设备类型
    _subscribeConnectionEvents(); // 监听连接状态
    _subscribeToSettingsUpdates(); // 订阅设置更新流

    // 检查初始连接状态并启动监听器
    if (currentDeviceManager != null) {
      Log.i(
          'DeviceSettingLogic: Initial state seems connected, starting event synchronizer.');
      _startEventSynchronizer();
    }
  }

  /// 订阅蓝牙连接状态事件
  void _subscribeConnectionEvents() {
    _connectionSubscription?.cancel();
    _connectionSubscription = _deviceFactory.connectionState.listen((event) {
      Log.i('高级设置 - 设备连接状态变化: ${event.state}');
      if (event.state == BleConnectionState.connected) {
        Log.i('高级设置 - 设备已连接，请求初始设置并启动事件同步器');
        _requestInitialSettings(); // 请求初始设置
        _startEventSynchronizer(); // 启动事件同步器
      } else {
        Log.i('高级设置 - 设备已断开，停止事件同步器');
        _stopEventSynchronizer(); // 停止事件同步器
      }
    });
  }

  /// 启动事件同步器
  void _startEventSynchronizer() {
    _eventSynchronizer?.stopListening();
    final manager = currentDeviceManager;
    if (manager != null) {
      _eventSynchronizer = DeviceEventSynchronizer(
        deviceId: deviceId,
        manager: manager,
      );
      _eventSynchronizer!.startListening();
      Log.i('DeviceSettingLogic: Event synchronizer started.');
    } else {
      Log.w(
          'DeviceSettingLogic: Cannot start event synchronizer, manager is null.');
    }
  }

  /// 停止事件同步器
  void _stopEventSynchronizer() {
    _eventSynchronizer?.stopListening();
    _eventSynchronizer = null;
    Log.i('DeviceSettingLogic: Event synchronizer stopped.');
  }

  /// 请求初始设备设置
  void _requestInitialSettings() {
    Log.i('高级设置 - 依赖DeviceSyncUtil中的设置请求，只启动事件同步器');
    // 不再请求设置，只启动事件同步器监听设置变化
  }

  /// 加载设置
  void _loadDeviceSettings() {
    try {
      Log.i('从本地存储加载设备设置: $deviceId');
      final settings = storage.getDeviceSettings(deviceId);
      if (settings != null) {
        Log.i('加载设置成功: ${settings.runtimeType}');
        state.settings.value = settings;

        // 根据设置类型判断设备类型
        if (settings is D900DeviceSettings) {
          _deviceType = DeviceModeType.dx9;
        } else {
          _deviceType = DeviceModeType.dx5;
        }
        // 更新声道平衡 UI 状态
        _updateBalanceUI(settings.channelBalance ?? 0);
      } else {
        Log.e('未找到设备设置: $deviceId');
        // 如果没有设置，需要确定设备类型来创建默认设置
        // 尝试从 DeviceEntity 获取类型
        final device = storage.getDevice(deviceId);
        final isD900 = device?.deviceModel == DeviceModeType.dx9.name;

        final defaultSettings = isD900
            ? D900DeviceSettings(deviceId: deviceId)
            : Dx5DeviceSettings(deviceId: deviceId);

        state.settings.value = defaultSettings;
        _deviceType = isD900 ? DeviceModeType.dx9 : DeviceModeType.dx5;
        _updateBalanceUI(0);
        _saveDeviceSettings(); // 保存新创建的默认设置
        Log.i('创建并保存了默认设置: ${defaultSettings.runtimeType}');
      }
    } catch (e) {
      Log.e('加载设置失败: $e');
      // 发生错误时也尝试创建默认设置
      final device = storage.getDevice(deviceId);
      final isD900 = device?.deviceModel == DeviceModeType.dx9.name;
      final defaultSettings = isD900
          ? D900DeviceSettings(deviceId: deviceId)
          : Dx5DeviceSettings(deviceId: deviceId);
      state.settings.value = defaultSettings;
      _deviceType = isD900 ? DeviceModeType.dx9 : DeviceModeType.dx5;
      _updateBalanceUI(0);
      _saveDeviceSettings(); // 保存新创建的默认设置
    }
  }

  /// 更新声道平衡UI状态
  void _updateBalanceUI(int balance) {
    if (balance < 0) {
      state.leftChannel = 95 + balance;
      state.rightChannel = 0;
    } else if (balance > 0) {
      state.leftChannel = 95;
      state.rightChannel = balance;
    } else {
      state.leftChannel = 95;
      state.rightChannel = 0;
    }
    update();
  }

  /// 保存设置 (仅在创建默认设置时需要手动调用)
  void _saveDeviceSettings() {
    try {
      if (state.settings.value != null) {
        storage.saveDeviceSettings(
            deviceId, state.settings.value!); // 使用 value!
        Log.d("Device settings saved manually (likely after creation).");
      } else {
        Log.w("Attempted to save null settings.");
      }
    } catch (e) {
      Log.e('手动保存设置失败: $e');
    }
  }

  /// 安全解析枚举值的辅助函数
  T? parseEnumValueHelper<T extends Enum>(dynamic enumValue) {
    if (enumValue is T) {
      return enumValue;
    } else if (enumValue is int) {
      return enumValue as T?;
    } else {
      Log.e("类型转换错误: $enumValue 不是有效的枚举类型或整数");
      return null;
    }
  }

  /// 统一执行设备操作的辅助方法（替代原来的_executeCommandHelper）
  Future<void> _executeDeviceAction(
      String actionName, Future<void> Function(DeviceManager) action) async {
    final manager = currentDeviceManager;
    if (manager == null) {
      Log.w("无法执行 $actionName：设备未连接");
      Get.snackbar('错误', '设备未连接');
      return;
    }

    try {
      Log.i("执行操作: $actionName");
      await action(manager);
      Log.i("操作 $actionName 已完成");
    } catch (e) {
      Log.e("执行操作 $actionName 时出错: $e");
      Get.snackbar('错误', '操作失败: $e');
    }
  }

  // --- 设置方法 ---

  /// 设置主题
  void setTheme(dynamic theme) {
    // 修复类型转换问题
    int themeValue;
    if (theme is Enum) {
      themeValue = theme.index;
    } else if (theme is int) {
      themeValue = theme;
    } else {
      Log.e("设置主题时类型错误: $theme 不是有效的主题类型");
      return;
    }

    _executeDeviceAction("设置主题为 $themeValue", (manager) async {
      (manager as dynamic).setTheme(themeValue);
      return;
    });
  }

  /// 设置开关机触发
  void setPowerTrigger(dynamic trigger) {
    // 修复类型转换问题
    int triggerValue;
    if (trigger is Enum) {
      triggerValue = trigger.index;
    } else if (trigger is int) {
      triggerValue = trigger;
    } else {
      Log.e("设置开关机触发时类型错误: $trigger 不是有效的触发类型");
      return;
    }

    _executeDeviceAction("设置开关机触发为 $triggerValue", (manager) async {
      (manager as dynamic).setPowerTrigger(triggerValue);
      return;
    });
  }

  /// 设置声道平衡 (拖动结束时)
  void setChannelBalanceEnd(int balance) {
    if (balance < -95 || balance > 95) return;
    // 更新 UI 状态 (在拖动时已通过 setLeft/RightChannel 处理)
    _updateBalanceUI(balance);

    _executeDeviceAction("设置声道平衡为 $balance", (manager) async {
      (manager as dynamic).setBalance(balance);
      return;
    });
  }

  /// 设置滤波器
  void setFilter(Dx5FilterParameterType filter) {
    _executeDeviceAction("设置滤波器为 ${filter.name}", (manager) async {
      manager.setFilter(filter.value);
      return;
    }).then((_) {
      // 立即更新本地状态，确保UI更新不依赖设备回传
      if (state.settings.value != null) {
        // 确保设置对象存在并且是DX5设备设置
        if (state.settings.value is Dx5DeviceSettings) {
          Dx5DeviceSettings settings =
              state.settings.value as Dx5DeviceSettings;
          // 设置滤波器值
          settings.filter = filter;
          // 强制刷新UI
          state.settings.refresh();
          update();
          Log.i("滤波器UI已手动更新为: ${filter.name}");
        }
      }
    });
  }

  /// 设置解码模式
  void setDecodeMode(Dx5DecodeModeType mode) {
    int modeValue = mode.value;
    String modeName = mode.name;

    _executeDeviceAction("设置解码模式为 $modeName", (manager) async {
      manager.setDecodeMode(modeValue);
      return;
    }).then((_) {
      // 立即更新本地状态，确保UI更新不依赖设备回传
      if (state.settings.value != null) {
        // 确保设置对象存在并且是DX5设备设置
        if (state.settings.value is Dx5DeviceSettings) {
          Dx5DeviceSettings settings =
              state.settings.value as Dx5DeviceSettings;
          // 根据mode类型修改解码模式
          settings.decodeMode = mode;
          // 强制刷新UI
          state.settings.refresh();
          update();
          Log.i("解码模式UI已手动更新为: $modeName");
        }
      }
    });
  }

  /// 设置音频蓝牙
  void toggleAudioMonitoring(bool enabled) {
    _executeDeviceAction("${enabled ? '启用' : '禁用'}音频蓝牙", (manager) async {
      (manager as dynamic).enableAudioBluetooth(enabled);
      return;
    });
  }

  /// 设置蓝牙aptx
  void toggleBluetoothAptx(bool enabled) {
    _executeDeviceAction("${enabled ? '启用' : '禁用'}蓝牙APTX", (manager) async {
      (manager as dynamic).enableBluetoothAptx(enabled);
      return;
    });
  }

  /// 设置遥控
  void toggleRelay(bool enabled) {
    _executeDeviceAction("${enabled ? '启用' : '禁用'}遥控功能", (manager) async {
      (manager as dynamic).enableRelay(enabled);
      return;
    });
  }

  /// 设置多功能按键
  void setMultiFunctionKey(Enum keyType) {
    int keyTypeValue = keyType.index;
    _executeDeviceAction("设置多功能键为 ${keyType.name}", (manager) async {
      (manager as dynamic).setMultifunctionKey(keyTypeValue);
      return;
    });
  }

  /// 设置USB模式
  void setUsbMode(Enum usbType) {
    int usbTypeValue = usbType.index;
    Log.i("设置USB模式为: $usbType (Value: $usbTypeValue)");

    _executeDeviceAction("设置USB模式为 ${usbType.name}", (manager) async {
      manager.setUsbMode(usbTypeValue);
      return;
    });
  }

  /// 设置USB选择 (D900特有)
  void setUsbSelect(D900UsbSelectType usbSelect) {
    // 修复类型转换问题
    int selectValue = usbSelect.index;

    // D900特有设置
    _executeDeviceAction("设置USB选择为 $selectValue", (manager) async {
      if (isD900Device) {
        (manager as D900DeviceManager).setUsbSelect(selectValue);
      } else {
        // 对于DX5设备，使用通用的USB模式设置
        manager.setUsbMode(selectValue);
      }
      return;
    });
  }

  /// 设置USB DSD直通 (D900特有)
  void setUsbDsdPassthrough(bool passthrough) {
    // 修复类型转换问题
    bool passThroughValue = passthrough;
    _executeDeviceAction("设置USB DSD直通为 $passThroughValue", (manager) async {
      if (isD900Device) {
        (manager as D900DeviceManager)
            .enableUsbDsdPassthrough(passThroughValue);
      }
      return;
    });
  }

  /// 设置IIS相位 (D900特有)
  void setIisPhase(D900IisPhaseType phase) {
    // 修复类型转换问题
    int phaseValue = phase.index;

    _executeDeviceAction("设置IIS相位为 $phaseValue", (manager) async {
      if (isD900Device) {
        (manager as D900DeviceManager).setIisPhase(phaseValue);
      }
      return;
    });
  }

  /// 设置IIS DSD通道 (D900特有)
  void setIisDsdChannel(D900IisDsdChannelType channel) {
    int channelValue = channel.index;
    _executeDeviceAction("设置IIS DSD通道为 ${channel.name}", (manager) async {
      if (isD900Device) {
        (manager as D900DeviceManager).setIisDsdChannel(channelValue);
      }
      return;
    });
  }

  /// 设置屏幕亮度
  void setScreenBrightness(Enum brightness) {
    int brightnessValue = brightness.index;
    Log.i("设置屏幕亮度为: $brightness (Value: $brightnessValue)");

    _executeDeviceAction("设置屏幕亮度为 ${brightness.name}", (manager) async {
      (manager as dynamic).setScreenBrightness(brightnessValue);
      return;
    });
  }

  /// 设置语言
  void setLanguage(dynamic language) {
    // 修复类型转换问题，确保可以处理Enum类型
    int langValue = language.index;

    _executeDeviceAction("设置语言为 $langValue", (manager) async {
      (manager as dynamic).setLanguage(langValue);
      return;
    });
  }

  /// 恢复出厂设置
  void restoreFactorySettings() {
    // UI 更新会由 Synchronizer 处理设备响应
    Log.i("恢复出厂设置 --- ");
    _executeDeviceAction("恢复出厂设置", (manager) async {
      manager.restoreFactorySettings();
      return;
    });

    final isD900 = state.settings.value is D900DeviceSettings;
    final defaultSettings = isD900
        ? D900DeviceSettings(deviceId: deviceId)
        : Dx5DeviceSettings(deviceId: deviceId);
    state.settings.value = isD900
        ? defaultSettings as D900DeviceSettings
        : defaultSettings as Dx5DeviceSettings;
    _updateBalanceUI(0);
  }

  /// 添加左右声道控制方法（拖动时更新UI）
  void setLeftChannel(int value) {
    if (value >= 0 && value <= 95) {
      state.leftChannel = value;
      update(); // Update GetBuilder
    }
  }

  /// 添加左右声道控制方法（拖动时更新UI）
  void setRightChannel(int value) {
    if (value >= 0 && value <= 95) {
      state.rightChannel = value;
      update(); // Update GetBuilder
    }
  }

  /// 修改停止拖动时发送命令的方法 (重命名以匹配之前的模式)
  void setLeftChannelEnd(int value) {
    if (value >= 0 && value <= 95) {
      int balance = value == 95 ? 0 : -(95 - value);
      setChannelBalanceEnd(balance); // Call the method that sends the command
    }
  }

  /// 修改停止拖动时发送命令的方法 (重命名以匹配之前的模式)
  void setRightChannelEnd(int value) {
    if (value >= 0 && value <= 95) {
      int balance = value;
      setChannelBalanceEnd(balance); // Call the method that sends the command
    }
  }

  /// 订阅设置更新流
  void _subscribeToSettingsUpdates() {
    _settingsSubscription?.cancel();
    _settingsSubscription =
        storage.getSettingsStream(deviceId).listen((updatedSettings) {
      Log.d('DeviceSettingLogic: Received settings update from stream');
      if (updatedSettings != null) {
        Log.d(
            'DeviceSettingLogic: Updating settings with new values，updatedSettings: $updatedSettings');
        // 完全替换对象，而不是修改属性
        state.settings.value = updatedSettings;
        // 显式刷新状态
        state.settings.refresh();

        // 更新声道平衡 UI 状态
        _updateBalanceUI(updatedSettings.channelBalance ?? 0);
        // 通知 GetBuilder 更新
        update();
      }
    }, onError: (error) {
      Log.e('DeviceSettingLogic: Error in settings stream: $error');
    });

    Log.i('DeviceSettingLogic: Subscribed to settings updates stream');
  }

  /// 设置DSD MUTE (D900特有)
  void setDsdMute(int muteType) {
    _executeDeviceAction("设置DSD MUTE为 $muteType", (manager) async {
      if (isD900Device) {
        // 假设有相应的方法
        (manager as dynamic).setDsdMute(muteType);
      }
      return;
    });
  }

  /// 设置USB接口选择 (D900特有)
  void setUsbPortSelect(int portType) {
    _executeDeviceAction("设置USB接口选择为 $portType", (manager) async {
      if (isD900Device) {
        (manager as dynamic).setUsbPortSelect(portType);
      }
      return;
    });
  }

  /// 设置VU表0dDB幅值 (D900特有)
  void setVuMeterLevel(int level) {
    _executeDeviceAction("设置VU表0dDB幅值为 $level", (manager) async {
      if (isD900Device) {
        (manager as dynamic).setVuMeterLevel(level);
      }
      return;
    });
  }

  /// 设置VU条显示模式 (D900特有)
  void setVuMeterDisplay(int displayMode) {
    _executeDeviceAction("设置VU条显示模式为 $displayMode", (manager) async {
      if (isD900Device) {
        (manager as dynamic).setVuMeterDisplay(displayMode);
      }
      return;
    });
  }

  /// 设置输入选项 (位掩码)
  void setInputOptions(int optionsMask) {
    _executeDeviceAction("设置输入选项为 $optionsMask", (manager) async {
      (manager as dynamic).setInputOptions(optionsMask);
      return;
    });
  }

  /// 设置输出选项 (位掩码)
  void setOutputOptions(int optionsMask) {
    _executeDeviceAction("设置输出选项为 $optionsMask", (manager) async {
      (manager as dynamic).setOutputOptions(optionsMask);
      return;
    });
  }

  /// 设置音量步进 (D900特有)
  void setVolumeStep(int stepType) {
    _executeDeviceAction("设置音量步进为 $stepType", (manager) async {
      if (isD900Device) {
        (manager as dynamic).setVolumeStep(stepType);
      }
      return;
    });
  }

  /// 设置极性 (D900特有)
  void setPolarity(int polarityType) {
    _executeDeviceAction("设置极性为 $polarityType", (manager) async {
      if (isD900Device) {
        (manager as dynamic).setPolarity(polarityType);
      }
      return;
    });
  }

  /// 设置输出幅值 (D900特有)
  void setOutputLevel(int level) {
    _executeDeviceAction("设置输出幅值为 $level", (manager) async {
      if (isD900Device) {
        (manager as dynamic).setOutputLevel(level);
      }
      return;
    });
  }

  /// 启用/禁用PEQ
  void setPeqEnabled(bool enabled) {
    _executeDeviceAction("${enabled ? '启用' : '禁用'}PEQ", (manager) async {
      (manager as dynamic).setPeqEnabled(enabled);
      return;
    });
  }

  /// 设置PEQ预设
  void setPeqPreset(int presetIndex) {
    _executeDeviceAction("设置PEQ预设为 $presetIndex", (manager) async {
      (manager as dynamic).setPeqPreset(presetIndex);
      return;
    });
  }

  /// 设置DSD直通 (D900特有)
  void setDsdDirect(bool enabled) {
    _executeDeviceAction("${enabled ? '启用' : '禁用'}DSD直通", (manager) async {
      if (isD900Device) {
        (manager as dynamic).setDsdDirect(enabled);
      }
      return;
    });
  }

  /// 设置音量记忆方式
  void setVolumeMemoryMode(int memoryMode) {
    _executeDeviceAction("设置音量记忆方式为 $memoryMode", (manager) async {
      (manager as dynamic).setVolumeMemoryMode(memoryMode);
      return;
    });
  }

  /// 设置PEQ记忆方式
  void setPeqMemoryMode(int memoryMode) {
    _executeDeviceAction("设置PEQ记忆方式为 $memoryMode", (manager) async {
      (manager as dynamic).setPeqMemoryMode(memoryMode);
      return;
    });
  }

  /// 设置主按键功能
  void setMainKeyFunction(int keyFunction) {
    _executeDeviceAction("设置主按键功能为 $keyFunction", (manager) async {
      (manager as dynamic).setMainKeyFunction(keyFunction);
      return;
    });
  }

  /// 设置遥控A键功能
  void setRemoteAKeyFunction(int keyFunction) {
    _executeDeviceAction("设置遥控A键功能为 $keyFunction", (manager) async {
      (manager as dynamic).setRemoteAKeyFunction(keyFunction);
      return;
    });
  }

  /// 设置遥控B键功能
  void setRemoteBKeyFunction(int keyFunction) {
    _executeDeviceAction("设置遥控B键功能为 $keyFunction", (manager) async {
      (manager as dynamic).setRemoteBKeyFunction(keyFunction);
      return;
    });
  }

  /// 设置PCM滤波器 (DX5II特有)
  void setPcmFilter(int filterIndex) {
    _executeDeviceAction("设置PCM滤波器为 $filterIndex", (manager) async {
      if (!isD900Device) {
        manager.setFilter(filterIndex);
      }
      return;
    });
  }

  /// 设置耳放增益 (DX5II特有)
  void setHeadphoneGain(int gainType) {
    _executeDeviceAction("设置耳放增益为 $gainType", (manager) async {
      if (!isD900Device) {
        (manager as dynamic).setHeadphoneGain(gainType);
      }
      return;
    });
  }

  /// 设置线路模式 (DX5II特有)
  void setLineOutMode(int lineMode) {
    _executeDeviceAction("设置线路模式为 $lineMode", (manager) async {
      if (!isD900Device) {
        (manager as dynamic).setLineOutMode(lineMode);
      }
      return;
    });
  }

  /// 设置声道平衡 (通用)
  void setChannelBalance(int balance) {
    _executeDeviceAction("设置声道平衡为 $balance", (manager) async {
      manager.setBalance(balance);
      return;
    });
  }

  @override
  void onClose() {
    // _saveDeviceSettings(); // 不再需要
    _connectionSubscription?.cancel();
    _stopEventSynchronizer();
    _settingsSubscription?.cancel();
    super.onClose();
  }
}

/// 用于处理解码模式参数映射的辅助类
class DecodeModeMappingHelper {
  static Map<String, dynamic> mapNameToParam(String paramName, dynamic value) {
    final result = <String, dynamic>{};
    result[paramName] = value;
    return result;
  }
}
