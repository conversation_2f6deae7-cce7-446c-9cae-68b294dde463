import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:topping_home/common/util/i18n.dart';
import 'package:topping_home/theme/text_styles.dart';
import '../../common/util/log_util.dart';
import '../../gen/assets.gen.dart';
import '../../router/routers.dart';
import '../../common/util/preference_utils.dart';
import 'guide_logic.dart';
import 'package:topping_home/theme/color_palettes.dart';

/// 引导页 - 全屏展示
class GuidePage extends StatefulWidget {
  const GuidePage({super.key});

  @override
  State<GuidePage> createState() => _GuidePageState();
}

class _GuidePageState extends State<GuidePage> {
  final GuideLogic logic = Get.put(GuideLogic());

  final List<AssetGenImage> guideImages = [
    Assets.image.viewGuide1,
    Assets.image.viewGuide2,
    Assets.image.viewGuide3,
    Assets.image.viewGuide4,
    Assets.image.viewGuide5,
  ];

  @override
  void initState() {
    super.initState();
    // 设置引导页总数量
    logic.totalPages = guideImages.length;
    Log.i('初始化引导页，图片数量: ${guideImages.length}');
  }

  @override
  Widget build(BuildContext context) {
    return Obx(
      () => AnnotatedRegion<SystemUiOverlayStyle>(
        value: SystemUiOverlayStyle(
          statusBarColor: ColorPalettes.instance.transparent,
          statusBarIconBrightness: ColorPalettes.instance.isDark()
              ? Brightness.light
              : Brightness.dark,
          statusBarBrightness: ColorPalettes.instance.isDark()
              ? Brightness.dark
              : Brightness.light,
        ),
        child: Scaffold(
          // 移除AppBar，使页面全屏显示
          backgroundColor: ColorPalettes.instance.card,
          body: Stack(
            children: [
              PageView.builder(
                controller: logic.pageController,
                onPageChanged: logic.onPageChanged,
                itemCount: guideImages.length,
                itemBuilder: (context, index) {
                  return SizedBox(
                    width: double.infinity,
                    height: double.infinity,
                    child: guideImages[index].image(fit: BoxFit.cover),
                  );
                },
              ),
              Positioned(
                bottom: 50,
                left: 0,
                right: 0,
                child: Column(
                  children: [
                    GetBuilder<GuideLogic>(
                      builder: (logic) => Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: List.generate(
                          guideImages.length,
                          (index) => Container(
                            margin: const EdgeInsets.symmetric(
                              horizontal: 4,
                            ),
                            width: 8,
                            height: 8,
                            decoration: BoxDecoration(
                              shape: BoxShape.circle,
                              color: logic.state.currentPage == index
                                  ? ColorPalettes
                                      .instance.accentLight // 使用文本主色而不是主题色
                                  : ColorPalettes.instance.accentLighter,
                            ),
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(height: 20),
                    GetBuilder<GuideLogic>(
                      builder: (logic) => Column(
                        children: [
                          ElevatedButton(
                            onPressed: logic.nextPage,
                            style: ElevatedButton.styleFrom(
                              backgroundColor: ColorPalettes.instance.accent,
                              foregroundColor: ColorPalettes.instance.pure,
                              padding: const EdgeInsets.symmetric(
                                horizontal: 40,
                                vertical: 12,
                              ),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(25),
                              ),
                              elevation: 2,
                            ),
                            child: Text(
                              logic.state.currentPage == guideImages.length - 1
                                  ? l10n.startUsing
                                  : l10n.nextPage,
                              style: TextStyles.instance.h3(
                                color: ColorPalettes.instance.pure,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  @override
  void dispose() {
    Get.delete<GuideLogic>();
    super.dispose();
  }
}
