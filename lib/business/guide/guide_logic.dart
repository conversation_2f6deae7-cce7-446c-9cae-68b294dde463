import 'package:get/get.dart';
import 'package:flutter/material.dart';

import '../../router/routers.dart';
import '../../common/util/preference_utils.dart';
import '../../common/util/log_util.dart';
import 'guide_state.dart';

/// 引导逻辑
class GuideLogic extends GetxController {
  final GuideState state = GuideState();
  final PageController pageController = PageController();

  // 首次启动标志键
  static const String firstLaunchKey = 'is_first_launch';

  // 引导页数量
  int totalPages = 5;

  @override
  void onInit() {
    super.onInit();
    // 输出日志以便调试
    Log.i('GuideLogic初始化，总页数：$totalPages');
  }

  /// 页面切换回调
  void onPageChanged(int index) {
    state.currentPage = index;
    // 输出当前页面索引用于调试
    Log.i('当前页面索引：$index，总页数：$totalPages');
    update();
  }

  /// 下一页
  void nextPage() {
    // 输出当前状态用于调试
    Log.i('点击下一页，当前页面：${state.currentPage}，最后一页索引：${totalPages - 1}');

    // 判断是否是最后一页
    if (state.currentPage < totalPages - 1) {
      // 不是最后一页，翻到下一页
      pageController.nextPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    } else {
      // 是最后一页，保存标记并跳转到首页
      Log.i('到达最后一页，准备跳转到首页');
      // 保存已经查看过引导页的标志
      PreferenceUtils.instance.putBool(firstLaunchKey, false);

      // 使用更可靠的方式跳转到首页
      Log.i('使用Get.offAllNamed方式跳转到首页');

      // 添加延迟，确保当前页面已完全卸载
      Future.delayed(Duration(milliseconds: 100), () {
        Log.i('跳转到首页');
        // 使用命名路由跳转，确保使用正确的路由配置
        Get.offAllNamed(AppRoutes.indexPage, arguments: {'fromSplash': true});
      });
    }
  }

  /// 检查是否是首次启动
  static Future<bool> checkFirstLaunch() async {
    // 默认为首次启动
    bool isFirstLaunch = PreferenceUtils.instance.getBool(firstLaunchKey, true);
    return isFirstLaunch;
  }

  @override
  void onClose() {
    pageController.dispose();
    super.onClose();
  }
}
