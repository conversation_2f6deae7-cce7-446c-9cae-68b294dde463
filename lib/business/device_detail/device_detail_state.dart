import 'package:get/get.dart';
import '../../enums/d900/d900_display_type.dart';
import '../../enums/d900/d900_input_type.dart';
import '../../enums/d900/d900_output_type.dart';
import '../../enums/dx5/dx5_display_type.dart';
import '../../enums/dx5/dx5_headphone_gain_type.dart';
import '../../enums/dx5/dx5_input_type.dart';
import '../../enums/dx5/dx5_output_type.dart';
import '../../factories/device_settings_factory.dart';
import '../../models/d900_device_settings.dart';
import '../../models/device_entity.dart';
import '../../models/dx5_device_settings.dart';

/// 设备详情状态
class DeviceDetailState {
  final backgroundImage = Rxn<String>();
  final backgroundOpacity = 1.0.obs;
  final backgroundBlur = 0.0.obs;

  final device = Rxn<DeviceEntity>();
  // 使用 dynamic 类型以支持不同的设备设置
  final settings = Rxn<dynamic>();
  final selectedTab = 0.obs;

  // 设备连接状态
  final isConnected = false.obs;

  // 这些可用选项将根据设备类型动态获取
  final availableInputs = RxList<dynamic>([]);
  final availableOutputs = RxList<dynamic>([]);
  final availableDisplayModes = RxList<dynamic>([]);
  final availableGainLevels = Dx5HeadphoneGainType.values.obs;

  /// 是否有新固件可用
  final hasNewFirmware = false.obs;

  DeviceDetailState() {
    final args = Get.arguments;
    if (args != null && args is DeviceEntity) {
      device.value = args;
    }
  }

  bool get hasBackground => backgroundImage.value != null;

  // 判断设备类型
  bool get isD900Device =>
      DeviceSettingsFactory.isD900Device(device.value?.deviceModel ?? '');

  // 根据设备类型获取适当的设置
  dynamic get currentSettings => settings.value;

  // 获取类型化的设置
  dynamic get typedSettings {
    if (isD900Device && settings.value is D900DeviceSettings) {
      return settings.value as D900DeviceSettings;
    } else if (!isD900Device && settings.value is Dx5DeviceSettings) {
      return settings.value as Dx5DeviceSettings;
    }
    return settings.value;
  }

  String get deviceName => device.value?.name ?? '';

  String get deviceId => device.value?.id ?? '';
}
