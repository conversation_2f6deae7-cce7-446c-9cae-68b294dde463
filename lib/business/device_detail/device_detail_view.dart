import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:topping_ble_control/utils/log_util.dart';
import 'package:topping_home/theme/color_palettes.dart';
import 'package:topping_home/theme/text_styles.dart';
import '../../common/util/i18n.dart';
import '../../common/widget/background_wrapper.dart';
import '../../router/routers.dart';
import '../../service/firmware_service.dart';
import '../peq/peq_controller.dart';
import '../peq/peq_page.dart';
import 'view/device_control_page.dart';
import 'view/operation_guide_page.dart';
import 'device_detail_logic.dart';

/// 设备详情页面
class DeviceDetailPage extends StatefulWidget {
  const DeviceDetailPage({super.key});

  @override
  State<DeviceDetailPage> createState() => _DeviceDetailPageState();
}

class _DeviceDetailPageState extends State<DeviceDetailPage>
    with SingleTickerProviderStateMixin {
  final logic = Get.find<DeviceDetailLogic>();
  final state = Get.find<DeviceDetailLogic>().state;
  final firmwareService = Get.find<FirmwareService>();

  @override
  void initState() {
    super.initState();
    // 确保 DeviceDetailLogic 已经注册
    if (!Get.isRegistered<DeviceDetailLogic>()) {
      Get.put(DeviceDetailLogic());
    }
    // 设置设备信息并检查更新
    _setupDeviceAndCheckUpdate();
  }

  /// 设置设备信息并检查更新
  void _setupDeviceAndCheckUpdate() async {
    if (state.device.value != null) {
      firmwareService.setDeviceInfo(
        state.device.value!.deviceModel,
        state.device.value!.macAddress,
      );

      // 检查固件更新并处理结果
      final hasUpdate = await firmwareService.checkUpdate();

      // 如果有更新，设置状态以显示更新按钮
      if (hasUpdate) {
        Log.i('检测到新固件，显示更新按钮');
        state.hasNewFirmware.value = true;
      } else {
        Log.i('未检测到新固件或检查失败');
        state.hasNewFirmware.value = false;
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
      onWillPop: () async {
        // 使用Get.back()而不是系统的返回方式
        Get.back();
        return false; // 阻止系统默认返回行为
      },
      child: Obx(() {
        if (state.device.value == null) {
          return Scaffold(
            backgroundColor: ColorPalettes.instance.primary,
            appBar: AppBar(
              backgroundColor: ColorPalettes.instance.card,
              title: Text(
                state.deviceName,
                style: TextStyles.instance.h2(),
              ),
              centerTitle: true,
              leading: IconButton(
                icon: Icon(
                  Icons.arrow_back_ios,
                  color: ColorPalettes.instance.firstText,
                ),
                onPressed: () => Get.back(),
              ),
              actions: _buildAppBarActions(),
              iconTheme: IconThemeData(color: ColorPalettes.instance.firstText),
            ),
            body: Center(
              child: Text(
                l10n.deviceNotFound,
                style: TextStyles.instance.h2(),
              ),
            ),
          );
        }

        // 使用BackgroundWrapper替代手动实现的背景
        return BackgroundWrapper(
          useDarkOverlay: true,
          darkOverlayOpacity: 0.3,
          backgroundColor: ColorPalettes.instance.background,
          child: _buildContent(context),
        );
      }),
    );
  }

  /// 构造内容
  Widget _buildContent(BuildContext context) {
    return DefaultTabController(
      length: 3,
      initialIndex: state.selectedTab.value,
      child: Scaffold(
        backgroundColor:
            ColorPalettes.instance.transparent, // 使用透明背景，由BackgroundWrapper提供背景
        appBar: AppBar(
          backgroundColor: ColorPalettes.instance.card,
          title: Text(
            state.deviceName,
            style: TextStyles.instance.h2(),
          ),
          centerTitle: true,
          leading: IconButton(
            icon: Icon(
              Icons.arrow_back_ios,
              color: ColorPalettes.instance.firstText,
            ),
            onPressed: () => Get.back(),
          ),
          actions: _buildAppBarActions(),
          iconTheme: IconThemeData(color: ColorPalettes.instance.firstText),
        ),
        body: TabBarView(
          physics: const NeverScrollableScrollPhysics(), // 禁止滑动
          children: [
            DeviceControlPage(),
            PEQPage(),
            OperationGuidePage(),
          ],
        ),
        bottomNavigationBar: Container(
          decoration: BoxDecoration(
            color: ColorPalettes.instance.background,
            boxShadow: [
              BoxShadow(
                color: ColorPalettes.instance.overlay,
                blurRadius: 4,
                offset: Offset(0, -1),
              ),
            ],
          ),
          height: 56,
          child: TabBar(
            labelPadding: EdgeInsets.zero,
            indicator: BoxDecoration(),
            labelColor: ColorPalettes.instance.primary,
            unselectedLabelColor: ColorPalettes.instance.secondText,
            labelStyle: TextStyles.instance.h3(),
            unselectedLabelStyle: TextStyles.instance.h3(),
            onTap: (index) {
              state.selectedTab.value = index;
            },
            tabs: [
              Tab(
                icon: Icon(Icons.settings,
                    color: state.selectedTab.value == 0
                        ? ColorPalettes.instance.primary
                        : ColorPalettes.instance.secondText,
                    size: state.selectedTab.value == 0 ? 22 : 20),
                text: l10n.setting,
                iconMargin: EdgeInsets.only(bottom: 4),
              ),
              Tab(
                icon: Icon(Icons.equalizer,
                    color: state.selectedTab.value == 1
                        ? ColorPalettes.instance.primary
                        : ColorPalettes.instance.secondText,
                    size: state.selectedTab.value == 1 ? 22 : 20),
                text: l10n.peq,
                iconMargin: EdgeInsets.only(bottom: 4),
              ),
              Tab(
                icon: Icon(Icons.menu_book,
                    color: state.selectedTab.value == 2
                        ? ColorPalettes.instance.primary
                        : ColorPalettes.instance.secondText,
                    size: state.selectedTab.value == 2 ? 22 : 20),
                text: l10n.guide,
                iconMargin: EdgeInsets.only(bottom: 4),
              ),
            ],
          ),
        ),
        floatingActionButton: _buildFirmwareUpdateButton(),
      ),
    );
  }

  /// 构建AppBar右侧按钮
  List<Widget> _buildAppBarActions() {
    final List<Widget> actions = [];

    if (state.selectedTab.value == 1) {
      // 获取PEQ控制器
      final PEQController peqController = Get.find<PEQController>();

      // 添加PEQ设置按钮
      actions.add(
        IconButton(
          icon: Icon(Icons.settings, color: ColorPalettes.instance.firstText),
          onPressed: () => peqController.openSettingsPage(),
        ),
      );
    }

    return actions;
  }

  Widget _buildFirmwareUpdateButton() {
    return Obx(() {
      if (!state.hasNewFirmware.value) {
        return const SizedBox.shrink();
      }

      return Container(
        margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        child: ElevatedButton.icon(
          onPressed: () {
            // 直接跳转到固件更新页面，并传递设备对象
            Get.toNamed(AppRoutes.firmwareUpdatePage,
                arguments: state.device.value);

            // 记录日志
            Log.i('点击固件升级按钮，跳转到固件升级页面');
          },
          style: ElevatedButton.styleFrom(
            backgroundColor: ColorPalettes.instance.primary,
            foregroundColor: ColorPalettes.instance.pure,
            padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
          ),
          icon: Icon(
            Icons.system_update,
            color: ColorPalettes.instance.pure,
          ),
          label: Text(
            l10n.firmwareUpdateTitle, // 使用固件升级而不是检查更新
            style: TextStyles.instance.h3(),
          ),
        ),
      );
    });
  }
}
