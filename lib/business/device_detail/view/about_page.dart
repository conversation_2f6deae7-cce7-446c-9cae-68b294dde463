import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:topping_control/business/device_detail/logic/device_detail_logic.dart';
import 'package:topping_control/business/device_detail/state/device_detail_state.dart';
import 'package:topping_control/common/ui/color_palettes.dart';
import 'package:topping_control/common/ui/text_styles.dart';
import 'package:topping_control/generated/l10n.dart';
import 'package:topping_control/model/device_settings.dart';

/// 关于页面
class AboutPage extends StatefulWidget {
  const AboutPage({Key? key}) : super(key: key);

  @override
  State<AboutPage> createState() => _AboutPageState();
}

class _AboutPageState extends State<AboutPage> {
  late DeviceDetailLogic logic;
  late DeviceDetailState state;
  late S l10n;
  late String deviceId;
  late bool isD900Device;
  DeviceSettings? settings;

  @override
  void initState() {
    super.initState();
    final arguments = Get.arguments as Map<String, dynamic>;
    deviceId = arguments['deviceId'] as String;
    isD900Device = arguments['isD900Device'] as bool;
    
    logic = Get.find<DeviceDetailLogic>(tag: deviceId);
    state = logic.state;
    l10n = S.of(context);
  }

  @override
  Widget build(BuildContext context) {
    l10n = S.of(context);
    
    return Scaffold(
      backgroundColor: ColorPalettes.instance.background,
      appBar: AppBar(
        backgroundColor: ColorPalettes.instance.background,
        elevation: 0,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back_ios,
            color: ColorPalettes.instance.firstText,
          ),
          onPressed: () => Get.back(),
        ),
        title: Text(
          '关于',
          style: TextStyles.instance.h2(),
        ),
        centerTitle: true,
      ),
      body: GetBuilder<DeviceDetailLogic>(
        tag: deviceId,
        builder: (logic) {
          settings = logic.state.settings;
          return SingleChildScrollView(
            padding: const EdgeInsets.all(16),
            child: Column(
              children: [
                _buildDeviceInfoCard(context),
                const SizedBox(height: 16),
                _buildAppInfoCard(context),
              ],
            ),
          );
        },
      ),
    );
  }

  /// 构建设备信息卡片
  Widget _buildDeviceInfoCard(BuildContext context) {
    return _buildInfoCard(
      context,
      title: '设备信息',
      children: [
        _buildInfoItem('设备型号', isD900Device ? 'D900' : 'DX5II'),
        _buildInfoItem('设备名称', _getDeviceName()),
        _buildInfoItem('固件版本', _getFirmwareVersion()),
        _buildInfoItem('硬件版本', _getHardwareVersion()),
        _buildInfoItem('序列号', _getSerialNumber()),
      ],
    );
  }

  /// 构建应用信息卡片
  Widget _buildAppInfoCard(BuildContext context) {
    return _buildInfoCard(
      context,
      title: '应用信息',
      children: [
        _buildInfoItem('应用名称', 'Topping Control'),
        _buildInfoItem('应用版本', _getAppVersion()),
        _buildInfoItem('构建版本', _getBuildNumber()),
        _buildInfoItem('开发商', 'Topping Audio'),
      ],
    );
  }

  /// 构建信息卡片
  Widget _buildInfoCard(
    BuildContext context, {
    required String title,
    required List<Widget> children,
  }) {
    return Card(
      color: ColorPalettes.instance.card,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(6),
      ),
      margin: EdgeInsets.symmetric(vertical: 3),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              title,
              style: TextStyles.instance.h2(),
            ),
            const SizedBox(height: 12),
            ...children,
          ],
        ),
      ),
    );
  }

  /// 构建信息项
  Widget _buildInfoItem(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 80,
            child: Text(
              label,
              style: TextStyles.instance.h3().copyWith(
                color: ColorPalettes.instance.secondText,
              ),
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Text(
              value,
              style: TextStyles.instance.h3(),
            ),
          ),
        ],
      ),
    );
  }

  /// 获取设备名称
  String _getDeviceName() {
    // TODO: 从设备获取实际名称
    return state.deviceName ?? (isD900Device ? 'D900' : 'DX5II');
  }

  /// 获取固件版本
  String _getFirmwareVersion() {
    // TODO: 从设备获取实际固件版本
    return '1.0.0';
  }

  /// 获取硬件版本
  String _getHardwareVersion() {
    // TODO: 从设备获取实际硬件版本
    return '1.0';
  }

  /// 获取序列号
  String _getSerialNumber() {
    // TODO: 从设备获取实际序列号
    return 'TG240001';
  }

  /// 获取应用版本
  String _getAppVersion() {
    // TODO: 从包信息获取实际应用版本
    return '1.0.0';
  }

  /// 获取构建版本
  String _getBuildNumber() {
    // TODO: 从包信息获取实际构建版本
    return '1';
  }
}
