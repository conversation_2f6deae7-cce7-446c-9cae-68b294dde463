import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:flutter_pdfview/flutter_pdfview.dart';
import 'dart:io';
import 'package:flutter/services.dart';
import 'package:path_provider/path_provider.dart';
import 'package:topping_home/common/util/log_util.dart';

import '../device_detail_logic.dart';
import '../device_detail_state.dart';

/// 操作指南页面
class OperationGuidePage extends StatefulWidget {
  const OperationGuidePage({super.key});

  @override
  State<OperationGuidePage> createState() => _OperationGuidePageState();
}

class _OperationGuidePageState extends State<OperationGuidePage> {
  late final DeviceDetailLogic logic;
  late final DeviceDetailState state;
  String? localPath;
  bool isReady = false;
  String errorMessage = '';

  @override
  void initState() {
    super.initState();
    // 确保 DeviceDetailLogic 已经注册
    if (!Get.isRegistered<DeviceDetailLogic>()) {
      Get.put(DeviceDetailLogic());
    }
    logic = Get.find<DeviceDetailLogic>();
    state = logic.state;
    loadPdfFromAssets();
  }

  Future<void> loadPdfFromAssets() async {
    try {
      // 创建一个 Completer 来处理异步操作
      final dir = await getApplicationDocumentsDirectory();
      final file = File("${dir.path}/operation_guide.pdf");

      // 从 assets 加载 PDF 文件
      final data = await rootBundle.load("assets/pdfs/operation_guide.pdf");
      final bytes = data.buffer.asUint8List();

      // 写入到本地文件
      await file.writeAsBytes(bytes, flush: true);

      setState(() {
        localPath = file.path;
        isReady = true;
      });
    } catch (e) {
      setState(() {
        errorMessage = e.toString();
      });
      Log.e('PDF加载错误: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      // 直接返回 Stack 而不是 Column
      children: <Widget>[
        if (errorMessage.isNotEmpty)
          Center(child: Text(errorMessage))
        else if (!isReady || localPath == null)
          const Center(child: CircularProgressIndicator())
        else
          PDFView(
            filePath: localPath!,
            enableSwipe: true,
            swipeHorizontal: false,
            autoSpacing: true,
            pageFling: false,
            onRender: (pages) {
              setState(() {
                isReady = true;
              });
            },
            onError: (error) {
              setState(() {
                errorMessage = error.toString();
              });
              Log.e('PDF渲染错误: $error');
            },
            onPageError: (page, error) {
              setState(() {
                errorMessage = '$page: ${error.toString()}';
              });
              Log.e('PDF页面 $page 错误: $error');
            },
          ),
      ],
    );
  }
}
