import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:topping_control/business/device_detail/logic/device_detail_logic.dart';
import 'package:topping_control/business/device_detail/state/device_detail_state.dart';
import 'package:topping_control/common/ui/color_palettes.dart';
import 'package:topping_control/common/ui/text_styles.dart';
import 'package:topping_control/generated/l10n.dart';
import 'package:topping_control/model/device_settings.dart';

/// 遥控与高级设置页面 (DX5II专用)
class RemoteAdvancedSettingsPage extends StatefulWidget {
  const RemoteAdvancedSettingsPage({Key? key}) : super(key: key);

  @override
  State<RemoteAdvancedSettingsPage> createState() => _RemoteAdvancedSettingsPageState();
}

class _RemoteAdvancedSettingsPageState extends State<RemoteAdvancedSettingsPage> {
  late DeviceDetailLogic logic;
  late DeviceDetailState state;
  late S l10n;
  late String deviceId;
  late bool isD900Device;
  DeviceSettings? settings;

  @override
  void initState() {
    super.initState();
    final arguments = Get.arguments as Map<String, dynamic>;
    deviceId = arguments['deviceId'] as String;
    isD900Device = arguments['isD900Device'] as bool;
    
    logic = Get.find<DeviceDetailLogic>(tag: deviceId);
    state = logic.state;
    l10n = S.of(context);
  }

  @override
  Widget build(BuildContext context) {
    l10n = S.of(context);
    
    return Scaffold(
      backgroundColor: ColorPalettes.instance.background,
      appBar: AppBar(
        backgroundColor: ColorPalettes.instance.background,
        elevation: 0,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back_ios,
            color: ColorPalettes.instance.firstText,
          ),
          onPressed: () => Get.back(),
        ),
        title: Text(
          '遥控与高级设置',
          style: TextStyles.instance.h2(),
        ),
        centerTitle: true,
      ),
      body: GetBuilder<DeviceDetailLogic>(
        tag: deviceId,
        builder: (logic) {
          settings = logic.state.settings;
          return SingleChildScrollView(
            padding: const EdgeInsets.all(16),
            child: Column(
              children: [
                _buildRemoteControlCard(context),
                const SizedBox(height: 6),
                _buildMainButtonFunctionCard(context),
                const SizedBox(height: 6),
                _buildRemoteAButtonFunctionCard(context),
                const SizedBox(height: 6),
                _buildRemoteBButtonFunctionCard(context),
              ],
            ),
          );
        },
      ),
    );
  }

  /// 构建遥控器卡片
  Widget _buildRemoteControlCard(BuildContext context) {
    return _buildSettingCard(
      context,
      title: l10n.relay,
      child: ListTile(
        dense: true,
        contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
        title: Text(
          l10n.relay,
          style: TextStyles.instance.h3(),
        ),
        trailing: Switch(
          value: _getRemoteControlEnabled(),
          onChanged: (value) => _setRemoteControlEnabled(value),
          activeColor: ColorPalettes.instance.accent,
        ),
      ),
    );
  }

  /// 构建多功能按键功能卡片
  Widget _buildMainButtonFunctionCard(BuildContext context) {
    return _buildSettingCard(
      context,
      title: l10n.mainButtonFunction,
      child: ListTile(
        dense: true,
        contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
        title: Text(
          l10n.mainButtonFunction,
          style: TextStyles.instance.h3(),
        ),
        trailing: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              _getMainButtonFunctionText(),
              style: TextStyles.instance.h3().copyWith(
                color: ColorPalettes.instance.secondText,
              ),
            ),
            const SizedBox(width: 8),
            Icon(
              Icons.arrow_forward_ios,
              color: ColorPalettes.instance.firstText,
              size: 12,
            ),
          ],
        ),
        onTap: () => _showMainButtonFunctionSelectionDialog(context),
      ),
    );
  }

  /// 构建遥控A键功能卡片
  Widget _buildRemoteAButtonFunctionCard(BuildContext context) {
    return _buildSettingCard(
      context,
      title: l10n.remoteAButtonFunction,
      child: ListTile(
        dense: true,
        contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
        title: Text(
          l10n.remoteAButtonFunction,
          style: TextStyles.instance.h3(),
        ),
        trailing: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              _getRemoteAButtonFunctionText(),
              style: TextStyles.instance.h3().copyWith(
                color: ColorPalettes.instance.secondText,
              ),
            ),
            const SizedBox(width: 8),
            Icon(
              Icons.arrow_forward_ios,
              color: ColorPalettes.instance.firstText,
              size: 12,
            ),
          ],
        ),
        onTap: () => _showRemoteAButtonFunctionSelectionDialog(context),
      ),
    );
  }

  /// 构建遥控B键功能卡片
  Widget _buildRemoteBButtonFunctionCard(BuildContext context) {
    return _buildSettingCard(
      context,
      title: l10n.remoteBButtonFunction,
      child: ListTile(
        dense: true,
        contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
        title: Text(
          l10n.remoteBButtonFunction,
          style: TextStyles.instance.h3(),
        ),
        trailing: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              _getRemoteBButtonFunctionText(),
              style: TextStyles.instance.h3().copyWith(
                color: ColorPalettes.instance.secondText,
              ),
            ),
            const SizedBox(width: 8),
            Icon(
              Icons.arrow_forward_ios,
              color: ColorPalettes.instance.firstText,
              size: 12,
            ),
          ],
        ),
        onTap: () => _showRemoteBButtonFunctionSelectionDialog(context),
      ),
    );
  }

  /// 构建设置卡片
  Widget _buildSettingCard(
    BuildContext context, {
    required String title,
    required Widget child,
  }) {
    return Card(
      color: ColorPalettes.instance.card,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(6),
      ),
      margin: EdgeInsets.symmetric(vertical: 3),
      child: child,
    );
  }

  /// 获取遥控器状态
  bool _getRemoteControlEnabled() {
    if (settings != null && settings is Dx5DeviceSettings) {
      var dx5Settings = settings as Dx5DeviceSettings;
      return dx5Settings.remoteControl;
    }
    return false;
  }

  /// 设置遥控器状态
  void _setRemoteControlEnabled(bool value) {
    logic.toggleRelay(value);
  }

  /// 获取多功能按键功能文本
  String _getMainButtonFunctionText() {
    int functionValue = _getMainButtonFunctionValue();
    switch (functionValue) {
      case 0:
        return l10n.multiFunctionKeyOutputSelect;
      case 1:
        return l10n.multiFunctionKeyInputSelect;
      case 2:
        return l10n.multiFunctionKeyHomeSelect;
      case 3:
        return l10n.multiFunctionKeyBrightnessSelect;
      case 4:
        return l10n.multiFunctionKeyScreenOff;
      case 5:
        return l10n.multiFunctionKeyMute;
      case 6:
        return 'EQ选择';
      case 7:
        return '开关机触发选择';
      case 8:
        return 'PCM滤波器';
      case 9:
        return '耳放增益';
      default:
        return l10n.multiFunctionKeyOutputSelect;
    }
  }

  /// 获取多功能按键功能值
  int _getMainButtonFunctionValue() {
    if (settings != null && settings is Dx5DeviceSettings) {
      var dx5Settings = settings as Dx5DeviceSettings;
      return dx5Settings.multiFunctionKey?.index ?? 0;
    }
    return 0;
  }

  /// 获取遥控A键功能文本
  String _getRemoteAButtonFunctionText() {
    // TODO: 实现遥控A键功能文本获取，根据文档默认是EQ选择
    return 'EQ选择';
  }

  /// 获取遥控B键功能文本
  String _getRemoteBButtonFunctionText() {
    // TODO: 实现遥控B键功能文本获取，根据文档默认是耳放增益
    return '耳放增益';
  }

  /// 显示多功能按键功能选择对话框
  void _showMainButtonFunctionSelectionDialog(BuildContext context) {
    final functions = [
      {'name': l10n.multiFunctionKeyOutputSelect, 'value': 0},
      {'name': l10n.multiFunctionKeyInputSelect, 'value': 1},
      {'name': l10n.multiFunctionKeyHomeSelect, 'value': 2},
      {'name': l10n.multiFunctionKeyBrightnessSelect, 'value': 3},
      {'name': l10n.multiFunctionKeyScreenOff, 'value': 4},
      {'name': l10n.multiFunctionKeyMute, 'value': 5},
      {'name': 'EQ选择', 'value': 6},
      {'name': '开关机触发选择', 'value': 7},
      {'name': 'PCM滤波器', 'value': 8},
      {'name': '耳放增益', 'value': 9},
    ];

    showModalBottomSheet(
      context: context,
      backgroundColor: ColorPalettes.instance.card,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(12)),
      ),
      builder: (BuildContext context) {
        return Container(
          padding: EdgeInsets.symmetric(vertical: 10),
          child: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                  child: Text(
                    l10n.mainButtonFunction,
                    style: TextStyles.instance.h3(),
                  ),
                ),
                Divider(color: ColorPalettes.instance.divider),
                ListView.builder(
                  shrinkWrap: true,
                  physics: NeverScrollableScrollPhysics(),
                  itemCount: functions.length,
                  itemBuilder: (context, index) {
                    final function = functions[index];
                    bool isSelected = _getMainButtonFunctionValue() == function['value'];
                    return ListTile(
                      title: Row(
                        children: [
                          Text(
                            function['name'] as String,
                            style: TextStyles.instance.h3(),
                          ),
                          if (isSelected) ...[
                            SizedBox(width: 8),
                            Icon(
                              Icons.check_circle,
                              color: ColorPalettes.instance.accent,
                              size: 16,
                            ),
                          ],
                        ],
                      ),
                      onTap: () {
                        logic.setMainButtonFunction(function['value']);
                        Navigator.pop(context);
                      },
                    );
                  },
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  /// 显示遥控A键功能选择对话框
  void _showRemoteAButtonFunctionSelectionDialog(BuildContext context) {
    // TODO: 实现遥控A键功能选择对话框
    Get.snackbar(l10n.tip, "遥控A键功能选择开发中");
  }

  /// 显示遥控B键功能选择对话框
  void _showRemoteBButtonFunctionSelectionDialog(BuildContext context) {
    // TODO: 实现遥控B键功能选择对话框
    Get.snackbar(l10n.tip, "遥控B键功能选择开发中");
  }
}
