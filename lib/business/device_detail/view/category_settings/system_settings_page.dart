import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:topping_home/theme/color_palettes.dart';
import 'package:topping_home/theme/text_styles.dart';
import '../../../../common/util/i18n.dart';
import '../../../../common/widget/background_wrapper.dart';
import '../../../../models/d900_device_settings.dart';
import '../../../../models/dx5_device_settings.dart';
import '../../device_detail_logic.dart';
import '../../device_detail_state.dart';

/// 系统设置页面
class SystemSettingsPage extends StatelessWidget {
  late final DeviceDetailLogic logic;
  late final DeviceDetailState state;
  late final String deviceId;
  late final bool isD900Device;

  dynamic get settings => state.settings.value;

  SystemSettingsPage({super.key}) {
    final args = Get.arguments as Map<String, dynamic>;
    deviceId = args['deviceId'] ?? '';
    isD900Device = args['isD900Device'] ?? false;

    // 尝试获取已存在的 DeviceDetailLogic 实例
    try {
      logic = Get.find<DeviceDetailLogic>();
      state = logic.state;
    } catch (e) {
      // 如果没有找到，创建一个新的实例
      logic = Get.put(DeviceDetailLogic());
      state = logic.state;
    }
  }

  @override
  Widget build(BuildContext context) {
    return BackgroundWrapper(
      child: Scaffold(
        backgroundColor: ColorPalettes.instance.transparent,
        appBar: AppBar(
          backgroundColor: ColorPalettes.instance.card,
          title: Text(
            '高级设置',
            style: TextStyles.instance.h2(),
          ),
          centerTitle: true,
          leading: IconButton(
            icon: Icon(
              Icons.arrow_back_ios,
              color: ColorPalettes.instance.firstText,
            ),
            onPressed: () => Get.back(),
          ),
          iconTheme: IconThemeData(color: ColorPalettes.instance.firstText),
        ),
        body: SingleChildScrollView(
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              children: [
                // 两个设备都有相同的高级设置
                _buildPowerTriggerCard(context),
                const SizedBox(height: 6),
                if (isD900Device) ...[
                  _buildDsdDirectCard(context),
                  const SizedBox(height: 6),
                ],
                _buildVolumeMemoryCard(context),
                const SizedBox(height: 6),
                _buildPeqMemoryCard(context),
                const SizedBox(height: 6),
                _buildRemoteControlCard(context),
                const SizedBox(height: 6),
                _buildMainButtonFunctionCard(context),
                const SizedBox(height: 6),
                _buildRemoteAButtonFunctionCard(context),
                const SizedBox(height: 6),
                _buildRemoteBButtonFunctionCard(context),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// 构建开关机触发卡片
  Widget _buildPowerTriggerCard(BuildContext context) {
    return _buildSettingCard(
      context,
      title: l10n.powerTrigger,
      child: Obx(() => ListTile(
            dense: true,
            contentPadding:
                const EdgeInsets.symmetric(horizontal: 16, vertical: 0),
            title: Text(
              l10n.powerTrigger,
              style: TextStyles.instance.h3(),
            ),
            trailing: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  _getPowerTriggerText(),
                  style: TextStyles.instance.h3(),
                ),
                const SizedBox(width: 4),
                Icon(Icons.arrow_forward_ios,
                    color: ColorPalettes.instance.firstText, size: 12),
              ],
            ),
            onTap: () => _showPowerTriggerSelectionDialog(context),
          )),
    );
  }

  /// 构建DSD直通卡片 (D900专用)
  Widget _buildDsdDirectCard(BuildContext context) {
    return _buildSettingCard(
      context,
      title: l10n.dsdDirect,
      child: Obx(() => ListTile(
            dense: true,
            contentPadding:
                const EdgeInsets.symmetric(horizontal: 16, vertical: 0),
            title: Text(
              l10n.dsdDirect,
              style: TextStyles.instance.h3(),
            ),
            trailing: Transform.scale(
              scale: 0.8,
              child: Switch(
                value: _getDsdDirectEnabled(),
                activeColor: ColorPalettes.instance.accent,
                onChanged: (value) => _setDsdDirectEnabled(value),
              ),
            ),
          )),
    );
  }

  /// 构建音量记忆方式卡片
  Widget _buildVolumeMemoryCard(BuildContext context) {
    return _buildSettingCard(
      context,
      title: l10n.volumeMemoryMode,
      child: Obx(() => ListTile(
            dense: true,
            contentPadding:
                const EdgeInsets.symmetric(horizontal: 16, vertical: 0),
            title: Text(
              l10n.volumeMemoryMode,
              style: TextStyles.instance.h3(),
            ),
            trailing: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  _getVolumeMemoryText(),
                  style: TextStyles.instance.h3(),
                ),
                const SizedBox(width: 4),
                Icon(Icons.arrow_forward_ios,
                    color: ColorPalettes.instance.firstText, size: 12),
              ],
            ),
            onTap: () => _showVolumeMemorySelectionDialog(context),
          )),
    );
  }

  /// 构建PEQ记忆方式卡片
  Widget _buildPeqMemoryCard(BuildContext context) {
    return _buildSettingCard(
      context,
      title: l10n.peqMemoryMode,
      child: Obx(() => ListTile(
            dense: true,
            contentPadding:
                const EdgeInsets.symmetric(horizontal: 16, vertical: 0),
            title: Text(
              l10n.peqMemoryMode,
              style: TextStyles.instance.h3(),
            ),
            trailing: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  _getPeqMemoryText(),
                  style: TextStyles.instance.h3(),
                ),
                const SizedBox(width: 4),
                Icon(Icons.arrow_forward_ios,
                    color: ColorPalettes.instance.firstText, size: 12),
              ],
            ),
            onTap: () => _showPeqMemorySelectionDialog(context),
          )),
    );
  }

  /// 构建遥控器卡片
  Widget _buildRemoteControlCard(BuildContext context) {
    return _buildSettingCard(
      context,
      title: l10n.relay,
      child: Obx(() => ListTile(
            dense: true,
            contentPadding:
                const EdgeInsets.symmetric(horizontal: 16, vertical: 0),
            title: Text(
              l10n.relay,
              style: TextStyles.instance.h3(),
            ),
            trailing: Transform.scale(
              scale: 0.8,
              child: Switch(
                value: _getRemoteControlEnabled(),
                activeColor: ColorPalettes.instance.accent,
                onChanged: (value) => _setRemoteControlEnabled(value),
              ),
            ),
          )),
    );
  }

  /// 构建主按键功能卡片
  Widget _buildMainButtonFunctionCard(BuildContext context) {
    return _buildSettingCard(
      context,
      title: l10n.mainButtonFunction,
      child: Obx(() => ListTile(
            dense: true,
            contentPadding:
                const EdgeInsets.symmetric(horizontal: 16, vertical: 0),
            title: Text(
              l10n.mainButtonFunction,
              style: TextStyles.instance.h3(),
            ),
            trailing: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  _getMainButtonFunctionText(),
                  style: TextStyles.instance.h3(),
                ),
                const SizedBox(width: 4),
                Icon(Icons.arrow_forward_ios,
                    color: ColorPalettes.instance.firstText, size: 12),
              ],
            ),
            onTap: () => _showMainButtonFunctionSelectionDialog(context),
          )),
    );
  }

  /// 构建遥控A键功能卡片
  Widget _buildRemoteAButtonFunctionCard(BuildContext context) {
    return _buildSettingCard(
      context,
      title: l10n.remoteAButtonFunction,
      child: Obx(() => ListTile(
            dense: true,
            contentPadding:
                const EdgeInsets.symmetric(horizontal: 16, vertical: 0),
            title: Text(
              l10n.remoteAButtonFunction,
              style: TextStyles.instance.h3(),
            ),
            trailing: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  _getRemoteAButtonFunctionText(),
                  style: TextStyles.instance.h3(),
                ),
                const SizedBox(width: 4),
                Icon(Icons.arrow_forward_ios,
                    color: ColorPalettes.instance.firstText, size: 12),
              ],
            ),
            onTap: () => _showRemoteAButtonFunctionSelectionDialog(context),
          )),
    );
  }

  /// 构建遥控B键功能卡片
  Widget _buildRemoteBButtonFunctionCard(BuildContext context) {
    return _buildSettingCard(
      context,
      title: l10n.remoteBButtonFunction,
      child: Obx(() => ListTile(
            dense: true,
            contentPadding:
                const EdgeInsets.symmetric(horizontal: 16, vertical: 0),
            title: Text(
              l10n.remoteBButtonFunction,
              style: TextStyles.instance.h3(),
            ),
            trailing: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  _getRemoteBButtonFunctionText(),
                  style: TextStyles.instance.h3(),
                ),
                const SizedBox(width: 4),
                Icon(Icons.arrow_forward_ios,
                    color: ColorPalettes.instance.firstText, size: 12),
              ],
            ),
            onTap: () => _showRemoteBButtonFunctionSelectionDialog(context),
          )),
    );
  }

  /// 构建设置卡片
  Widget _buildSettingCard(
    BuildContext context, {
    required String title,
    required Widget child,
  }) {
    return Card(
      color: ColorPalettes.instance.card,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(6),
      ),
      margin: EdgeInsets.symmetric(vertical: 3),
      child: child,
    );
  }

  /// 获取开关机触发文本
  String _getPowerTriggerText() {
    int triggerValue = _getPowerTriggerValue();
    switch (triggerValue) {
      case 0:
        return l10n.signal;
      case 1:
        return "12V";
      case 2:
        return l10n.close;
      default:
        return l10n.signal;
    }
  }

  /// 获取开关机触发值
  int _getPowerTriggerValue() {
    if (settings != null) {
      if (isD900Device && settings is D900DeviceSettings) {
        var d900Settings = settings as D900DeviceSettings;
        return d900Settings.powerTrigger.index;
      } else if (!isD900Device && settings is Dx5DeviceSettings) {
        var dx5Settings = settings as Dx5DeviceSettings;
        return dx5Settings.powerTrigger.index;
      }
    }
    return 0;
  }

  /// 获取DSD直通状态 (D900)
  bool _getDsdDirectEnabled() {
    if (settings != null && isD900Device && settings is D900DeviceSettings) {
      var d900Settings = settings as D900DeviceSettings;
      return d900Settings.usbDsdPassthrough;
    }
    return false;
  }

  /// 设置DSD直通状态 (D900)
  void _setDsdDirectEnabled(bool value) {
    logic.setDsdDirect(value);
  }

  /// 获取音量记忆方式文本
  String _getVolumeMemoryText() {
    int memoryValue = _getVolumeMemoryValue();
    switch (memoryValue) {
      case 0:
        return l10n.followOutput;
      case 1:
        return l10n.globalMemory;
      case 2:
        return l10n.noMemory;
      default:
        return l10n.followOutput;
    }
  }

  /// 获取音量记忆方式值
  int _getVolumeMemoryValue() {
    // TODO: 从设备设置中获取实际值，目前返回默认值
    return 0;
  }

  /// 获取PEQ记忆方式文本
  String _getPeqMemoryText() {
    int memoryValue = _getPeqMemoryValue();
    switch (memoryValue) {
      case 0:
        return l10n.followOutput;
      case 1:
        return l10n.globalMemory;
      case 2:
        return l10n.noMemory;
      default:
        return l10n.followOutput;
    }
  }

  /// 获取PEQ记忆方式值
  int _getPeqMemoryValue() {
    // TODO: 从设备设置中获取实际值，目前返回默认值
    return 0;
  }

  /// 获取遥控器状态
  bool _getRemoteControlEnabled() {
    if (settings != null) {
      if (isD900Device && settings is D900DeviceSettings) {
        var d900Settings = settings as D900DeviceSettings;
        return d900Settings.remoteControl;
      } else if (!isD900Device && settings is Dx5DeviceSettings) {
        var dx5Settings = settings as Dx5DeviceSettings;
        return dx5Settings.remoteControl;
      }
    }
    return false;
  }

  /// 设置遥控器状态
  void _setRemoteControlEnabled(bool value) {
    logic.toggleRelay(value);
  }

  /// 获取主按键功能文本
  String _getMainButtonFunctionText() {
    int functionValue = _getMainButtonFunctionValue();
    if (isD900Device) {
      switch (functionValue) {
        case 0:
          return l10n.multiFunctionKeyOutputSelect;
        case 1:
          return l10n.multiFunctionKeyInputSelect;
        case 2:
          return l10n.multiFunctionKeyHomeSelect;
        case 3:
          return l10n.multiFunctionKeyBrightnessSelect;
        case 4:
          return l10n.multiFunctionKeyScreenOff;
        case 5:
          return l10n.multiFunctionKeyMute;
        case 6:
          return 'EQ选择';
        case 7:
          return '开关机触发选择';
        default:
          return l10n.multiFunctionKeyOutputSelect;
      }
    } else {
      switch (functionValue) {
        case 0:
          return l10n.multiFunctionKeyOutputSelect;
        case 1:
          return l10n.multiFunctionKeyInputSelect;
        case 2:
          return l10n.multiFunctionKeyHomeSelect;
        case 3:
          return l10n.multiFunctionKeyBrightnessSelect;
        case 4:
          return l10n.multiFunctionKeyScreenOff;
        case 5:
          return l10n.multiFunctionKeyMute;
        case 6:
          return 'EQ选择';
        case 7:
          return '开关机触发选择';
        case 8:
          return 'PCM滤波器';
        case 9:
          return '耳放增益';
        default:
          return l10n.multiFunctionKeyOutputSelect;
      }
    }
  }

  /// 获取主按键功能值
  int _getMainButtonFunctionValue() {
    if (settings != null) {
      if (isD900Device && settings is D900DeviceSettings) {
        var d900Settings = settings as D900DeviceSettings;
        return d900Settings.multiFunctionKey?.index ?? 0;
      } else if (!isD900Device && settings is Dx5DeviceSettings) {
        var dx5Settings = settings as Dx5DeviceSettings;
        return dx5Settings.multiFunctionKey?.index ?? 1;
      }
    }
    return isD900Device ? 0 : 1;
  }

  /// 获取遥控A键功能文本
  String _getRemoteAButtonFunctionText() {
    // TODO: 实现遥控A键功能文本获取
    return l10n.inputSelection;
  }

  /// 获取遥控B键功能文本
  String _getRemoteBButtonFunctionText() {
    // TODO: 实现遥控B键功能文本获取
    return l10n.outputSelection;
  }

  /// 显示开关机触发选择对话框
  void _showPowerTriggerSelectionDialog(BuildContext context) {
    final triggerList = [
      {'name': l10n.signal, 'value': 0},
      {'name': '12V', 'value': 1},
      {'name': l10n.close, 'value': 2},
    ];

    showModalBottomSheet(
      context: context,
      backgroundColor: ColorPalettes.instance.card,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(12)),
      ),
      builder: (BuildContext context) {
        return Container(
          padding: EdgeInsets.symmetric(vertical: 10),
          child: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                  child: Text(
                    l10n.powerTrigger,
                    style: TextStyles.instance.h3(),
                  ),
                ),
                Divider(color: ColorPalettes.instance.divider),
                ListView.builder(
                  shrinkWrap: true,
                  physics: NeverScrollableScrollPhysics(),
                  itemCount: triggerList.length,
                  itemBuilder: (context, index) {
                    final trigger = triggerList[index];
                    bool isSelected = _getPowerTriggerValue() == trigger['value'];
                    return ListTile(
                      title: Row(
                        children: [
                          Text(
                            trigger['name'] as String,
                            style: TextStyles.instance.h3(),
                          ),
                          if (isSelected) ...[
                            SizedBox(width: 8),
                            Icon(
                              Icons.check_circle,
                              color: ColorPalettes.instance.accent,
                              size: 16,
                            ),
                          ],
                        ],
                      ),
                      onTap: () {
                        logic.setPowerTrigger(trigger['value']);
                        Navigator.pop(context);
                      },
                    );
                  },
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  /// 显示音量记忆方式选择对话框
  void _showVolumeMemorySelectionDialog(BuildContext context) {
    final memoryList = [
      {'name': l10n.followOutput, 'value': 0},
      {'name': l10n.globalMemory, 'value': 1},
      {'name': l10n.noMemory, 'value': 2},
    ];

    showModalBottomSheet(
      context: context,
      backgroundColor: ColorPalettes.instance.card,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(12)),
      ),
      builder: (BuildContext context) {
        return Container(
          padding: EdgeInsets.symmetric(vertical: 10),
          child: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                  child: Text(
                    l10n.volumeMemoryMode,
                    style: TextStyles.instance.h3(),
                  ),
                ),
                Divider(color: ColorPalettes.instance.divider),
                ListView.builder(
                  shrinkWrap: true,
                  physics: NeverScrollableScrollPhysics(),
                  itemCount: memoryList.length,
                  itemBuilder: (context, index) {
                    final memory = memoryList[index];
                    bool isSelected = _getVolumeMemoryValue() == memory['value'];
                    return ListTile(
                      title: Row(
                        children: [
                          Text(
                            memory['name'] as String,
                            style: TextStyles.instance.h3(),
                          ),
                          if (isSelected) ...[
                            SizedBox(width: 8),
                            Icon(
                              Icons.check_circle,
                              color: ColorPalettes.instance.accent,
                              size: 16,
                            ),
                          ],
                        ],
                      ),
                      onTap: () {
                        logic.setVolumeMemoryType(memory['value'] as int);
                        Navigator.pop(context);
                      },
                    );
                  },
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  /// 显示PEQ记忆方式选择对话框
  void _showPeqMemorySelectionDialog(BuildContext context) {
    final memoryList = [
      {'name': l10n.followOutput, 'value': 0},
      {'name': l10n.globalMemory, 'value': 1},
      {'name': l10n.noMemory, 'value': 2},
    ];

    showModalBottomSheet(
      context: context,
      backgroundColor: ColorPalettes.instance.card,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(12)),
      ),
      builder: (BuildContext context) {
        return Container(
          padding: EdgeInsets.symmetric(vertical: 10),
          child: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                  child: Text(
                    l10n.peqMemoryMode,
                    style: TextStyles.instance.h3(),
                  ),
                ),
                Divider(color: ColorPalettes.instance.divider),
                ListView.builder(
                  shrinkWrap: true,
                  physics: NeverScrollableScrollPhysics(),
                  itemCount: memoryList.length,
                  itemBuilder: (context, index) {
                    final memory = memoryList[index];
                    bool isSelected = _getPeqMemoryValue() == memory['value'];
                    return ListTile(
                      title: Row(
                        children: [
                          Text(
                            memory['name'] as String,
                            style: TextStyles.instance.h3(),
                          ),
                          if (isSelected) ...[
                            SizedBox(width: 8),
                            Icon(
                              Icons.check_circle,
                              color: ColorPalettes.instance.accent,
                              size: 16,
                            ),
                          ],
                        ],
                      ),
                      onTap: () {
                        logic.setPeqMemoryType(memory['value'] as int);
                        Navigator.pop(context);
                      },
                    );
                  },
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  /// 显示主按键功能选择对话框
  void _showMainButtonFunctionSelectionDialog(BuildContext context) {
    if (isD900Device) {
      _showD900MainButtonFunctionSelectionDialog(context);
    } else {
      _showDx5MainButtonFunctionSelectionDialog(context);
    }
  }

  /// 显示D900主按键功能选择对话框
  void _showD900MainButtonFunctionSelectionDialog(BuildContext context) {
    final functions = [
      {'name': l10n.multiFunctionKeyOutputSelect, 'value': 0},
      {'name': l10n.multiFunctionKeyInputSelect, 'value': 1},
      {'name': l10n.multiFunctionKeyHomeSelect, 'value': 2},
      {'name': l10n.multiFunctionKeyBrightnessSelect, 'value': 3},
      {'name': l10n.multiFunctionKeyScreenOff, 'value': 4},
      {'name': l10n.multiFunctionKeyMute, 'value': 5},
      {'name': 'EQ选择', 'value': 6},
      {'name': '开关机触发选择', 'value': 7},
    ];

    showModalBottomSheet(
      context: context,
      backgroundColor: ColorPalettes.instance.card,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(12)),
      ),
      builder: (BuildContext context) {
        return Container(
          padding: EdgeInsets.symmetric(vertical: 10),
          child: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                  child: Text(
                    l10n.mainButtonFunction,
                    style: TextStyles.instance.h3(),
                  ),
                ),
                Divider(color: ColorPalettes.instance.divider),
                ListView.builder(
                  shrinkWrap: true,
                  physics: NeverScrollableScrollPhysics(),
                  itemCount: functions.length,
                  itemBuilder: (context, index) {
                    final function = functions[index];
                    bool isSelected = _getMainButtonFunctionValue() == function['value'];
                    return ListTile(
                      title: Row(
                        children: [
                          Text(
                            function['name'] as String,
                            style: TextStyles.instance.h3(),
                          ),
                          if (isSelected) ...[
                            SizedBox(width: 8),
                            Icon(
                              Icons.check_circle,
                              color: ColorPalettes.instance.accent,
                              size: 16,
                            ),
                          ],
                        ],
                      ),
                      onTap: () {
                        logic.setMainButtonFunction(function['value']);
                        Navigator.pop(context);
                      },
                    );
                  },
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  /// 显示DX5主按键功能选择对话框
  void _showDx5MainButtonFunctionSelectionDialog(BuildContext context) {
    final functions = [
      {'name': l10n.multiFunctionKeyOutputSelect, 'value': 0},
      {'name': l10n.multiFunctionKeyInputSelect, 'value': 1},
      {'name': l10n.multiFunctionKeyHomeSelect, 'value': 2},
      {'name': l10n.multiFunctionKeyBrightnessSelect, 'value': 3},
      {'name': l10n.multiFunctionKeyScreenOff, 'value': 4},
      {'name': l10n.multiFunctionKeyMute, 'value': 5},
      {'name': 'EQ选择', 'value': 6},
      {'name': '开关机触发选择', 'value': 7},
      {'name': 'PCM滤波器', 'value': 8},
      {'name': '耳放增益', 'value': 9},
    ];

    showModalBottomSheet(
      context: context,
      backgroundColor: ColorPalettes.instance.card,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(12)),
      ),
      builder: (BuildContext context) {
        return Container(
          padding: EdgeInsets.symmetric(vertical: 10),
          child: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                  child: Text(
                    l10n.mainButtonFunction,
                    style: TextStyles.instance.h3(),
                  ),
                ),
                Divider(color: ColorPalettes.instance.divider),
                ListView.builder(
                  shrinkWrap: true,
                  physics: NeverScrollableScrollPhysics(),
                  itemCount: functions.length,
                  itemBuilder: (context, index) {
                    final function = functions[index];
                    bool isSelected = _getMainButtonFunctionValue() == function['value'];
                    return ListTile(
                      title: Row(
                        children: [
                          Text(
                            function['name'] as String,
                            style: TextStyles.instance.h3(),
                          ),
                          if (isSelected) ...[
                            SizedBox(width: 8),
                            Icon(
                              Icons.check_circle,
                              color: ColorPalettes.instance.accent,
                              size: 16,
                            ),
                          ],
                        ],
                      ),
                      onTap: () {
                        logic.setMainButtonFunction(function['value']);
                        Navigator.pop(context);
                      },
                    );
                  },
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  /// 显示遥控A键功能选择对话框
  void _showRemoteAButtonFunctionSelectionDialog(BuildContext context) {
    // TODO: 实现遥控A键功能选择对话框
    Get.snackbar(l10n.tip, l10n.remoteAButtonFunctionSelectionInDevelopment);
  }

  /// 显示遥控B键功能选择对话框
  void _showRemoteBButtonFunctionSelectionDialog(BuildContext context) {
    // TODO: 实现遥控B键功能选择对话框
    Get.snackbar(l10n.tip, l10n.remoteBButtonFunctionSelectionInDevelopment);
  }
}
