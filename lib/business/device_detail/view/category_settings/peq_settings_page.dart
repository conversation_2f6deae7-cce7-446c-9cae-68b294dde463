import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:topping_control/business/device_detail/logic/device_detail_logic.dart';
import 'package:topping_control/business/device_detail/state/device_detail_state.dart';
import 'package:topping_control/common/ui/color_palettes.dart';
import 'package:topping_control/common/ui/text_styles.dart';
import 'package:topping_control/generated/l10n.dart';
import 'package:topping_control/model/device_settings.dart';

/// PEQ设置页面
class PeqSettingsPage extends StatefulWidget {
  const PeqSettingsPage({Key? key}) : super(key: key);

  @override
  State<PeqSettingsPage> createState() => _PeqSettingsPageState();
}

class _PeqSettingsPageState extends State<PeqSettingsPage> {
  late DeviceDetailLogic logic;
  late DeviceDetailState state;
  late S l10n;
  late String deviceId;
  late bool isD900Device;
  DeviceSettings? settings;

  @override
  void initState() {
    super.initState();
    final arguments = Get.arguments as Map<String, dynamic>;
    deviceId = arguments['deviceId'] as String;
    isD900Device = arguments['isD900Device'] as bool;
    
    logic = Get.find<DeviceDetailLogic>(tag: deviceId);
    state = logic.state;
    l10n = S.of(context);
  }

  @override
  Widget build(BuildContext context) {
    l10n = S.of(context);
    
    return Scaffold(
      backgroundColor: ColorPalettes.instance.background,
      appBar: AppBar(
        backgroundColor: ColorPalettes.instance.background,
        elevation: 0,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back_ios,
            color: ColorPalettes.instance.firstText,
          ),
          onPressed: () => Get.back(),
        ),
        title: Text(
          'PEQ设置',
          style: TextStyles.instance.h2(),
        ),
        centerTitle: true,
      ),
      body: GetBuilder<DeviceDetailLogic>(
        tag: deviceId,
        builder: (logic) {
          settings = logic.state.settings;
          return SingleChildScrollView(
            padding: const EdgeInsets.all(16),
            child: Column(
              children: [
                _buildPeqSwitchCard(context),
                const SizedBox(height: 6),
                _buildPeqSelectionCard(context),
                const SizedBox(height: 6),
                _buildPeqResetCard(context),
              ],
            ),
          );
        },
      ),
    );
  }

  /// 构建PEQ开关卡片
  Widget _buildPeqSwitchCard(BuildContext context) {
    return _buildSettingCard(
      context,
      title: 'PEQ开关',
      child: ListTile(
        dense: true,
        contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
        title: Text(
          'PEQ开关',
          style: TextStyles.instance.h3(),
        ),
        trailing: Switch(
          value: _getPeqSwitchValue(),
          onChanged: (value) {
            logic.setPeqSwitch(value);
          },
          activeColor: ColorPalettes.instance.accent,
        ),
      ),
    );
  }

  /// 构建PEQ选择卡片
  Widget _buildPeqSelectionCard(BuildContext context) {
    return _buildSettingCard(
      context,
      title: 'PEQ选择',
      child: ListTile(
        dense: true,
        contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
        title: Text(
          'PEQ选择',
          style: TextStyles.instance.h3(),
        ),
        trailing: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              _getPeqSelectionText(),
              style: TextStyles.instance.h3().copyWith(
                color: ColorPalettes.instance.secondText,
              ),
            ),
            const SizedBox(width: 8),
            Icon(
              Icons.arrow_forward_ios,
              color: ColorPalettes.instance.firstText,
              size: 12,
            ),
          ],
        ),
        onTap: () => _showPeqSelectionDialog(context),
      ),
    );
  }

  /// 构建PEQ重置卡片
  Widget _buildPeqResetCard(BuildContext context) {
    return _buildSettingCard(
      context,
      title: 'PEQ重置',
      child: ListTile(
        dense: true,
        contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
        title: Text(
          'PEQ重置',
          style: TextStyles.instance.h3(),
        ),
        trailing: Icon(
          Icons.arrow_forward_ios,
          color: ColorPalettes.instance.firstText,
          size: 12,
        ),
        onTap: () => _showPeqResetDialog(context),
      ),
    );
  }

  /// 构建设置卡片
  Widget _buildSettingCard(
    BuildContext context, {
    required String title,
    required Widget child,
  }) {
    return Card(
      color: ColorPalettes.instance.card,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(6),
      ),
      margin: EdgeInsets.symmetric(vertical: 3),
      child: child,
    );
  }

  /// 获取PEQ开关值
  bool _getPeqSwitchValue() {
    if (settings != null) {
      if (isD900Device && settings is D900DeviceSettings) {
        var d900Settings = settings as D900DeviceSettings;
        return d900Settings.peqSwitch ?? false;
      } else if (!isD900Device && settings is Dx5DeviceSettings) {
        var dx5Settings = settings as Dx5DeviceSettings;
        return dx5Settings.peqSwitch ?? false;
      }
    }
    return false;
  }

  /// 获取PEQ选择文本
  String _getPeqSelectionText() {
    int selectionValue = _getPeqSelectionValue();
    switch (selectionValue) {
      case 0:
        return 'PEQ1';
      case 1:
        return 'PEQ2';
      case 2:
        return 'PEQ3';
      case 3:
        return 'PEQ4';
      case 4:
        return 'PEQ5';
      default:
        return 'PEQ1';
    }
  }

  /// 获取PEQ选择值
  int _getPeqSelectionValue() {
    if (settings != null) {
      if (isD900Device && settings is D900DeviceSettings) {
        var d900Settings = settings as D900DeviceSettings;
        return d900Settings.peqSelection?.index ?? 0;
      } else if (!isD900Device && settings is Dx5DeviceSettings) {
        var dx5Settings = settings as Dx5DeviceSettings;
        return dx5Settings.peqSelection?.index ?? 0;
      }
    }
    return 0;
  }

  /// 显示PEQ选择对话框
  void _showPeqSelectionDialog(BuildContext context) {
    final peqOptions = [
      {'name': 'PEQ1', 'value': 0},
      {'name': 'PEQ2', 'value': 1},
      {'name': 'PEQ3', 'value': 2},
      {'name': 'PEQ4', 'value': 3},
      {'name': 'PEQ5', 'value': 4},
    ];

    showModalBottomSheet(
      context: context,
      backgroundColor: ColorPalettes.instance.card,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(12)),
      ),
      builder: (BuildContext context) {
        return Container(
          padding: EdgeInsets.symmetric(vertical: 10),
          child: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                  child: Text(
                    'PEQ选择',
                    style: TextStyles.instance.h3(),
                  ),
                ),
                Divider(color: ColorPalettes.instance.divider),
                ListView.builder(
                  shrinkWrap: true,
                  physics: NeverScrollableScrollPhysics(),
                  itemCount: peqOptions.length,
                  itemBuilder: (context, index) {
                    final option = peqOptions[index];
                    bool isSelected = _getPeqSelectionValue() == option['value'];
                    return ListTile(
                      title: Row(
                        children: [
                          Text(
                            option['name'] as String,
                            style: TextStyles.instance.h3(),
                          ),
                          if (isSelected) ...[
                            SizedBox(width: 8),
                            Icon(
                              Icons.check_circle,
                              color: ColorPalettes.instance.accent,
                              size: 16,
                            ),
                          ],
                        ],
                      ),
                      onTap: () {
                        logic.setPeqSelection(option['value']);
                        Navigator.pop(context);
                      },
                    );
                  },
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  /// 显示PEQ重置对话框
  void _showPeqResetDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          backgroundColor: ColorPalettes.instance.card,
          title: Text(
            'PEQ重置',
            style: TextStyles.instance.h2(),
          ),
          content: Text(
            '确定要重置PEQ设置吗？',
            style: TextStyles.instance.h3(),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: Text(
                l10n.cancel,
                style: TextStyles.instance.h3().copyWith(
                  color: ColorPalettes.instance.secondText,
                ),
              ),
            ),
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                logic.resetPeq();
              },
              child: Text(
                l10n.confirm,
                style: TextStyles.instance.h3().copyWith(
                  color: ColorPalettes.instance.accent,
                ),
              ),
            ),
          ],
        );
      },
    );
  }
}
