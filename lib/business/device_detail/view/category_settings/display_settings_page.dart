import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:topping_home/theme/color_palettes.dart';
import 'package:topping_home/theme/text_styles.dart';
import '../../../../common/util/i18n.dart';
import '../../../../common/widget/background_wrapper.dart';
import '../../../../models/d900_device_settings.dart';
import '../../../../models/dx5_device_settings.dart';
import '../../device_detail_logic.dart';
import '../../device_detail_state.dart';

/// 显示设置页面
class DisplaySettingsPage extends StatelessWidget {
  late final DeviceDetailLogic logic;
  late final DeviceDetailState state;
  late final String deviceId;
  late final bool isD900Device;

  dynamic get settings => state.settings.value;

  DisplaySettingsPage({super.key}) {
    final args = Get.arguments as Map<String, dynamic>;
    deviceId = args['deviceId'] ?? '';
    isD900Device = args['isD900Device'] ?? false;

    // 尝试获取已存在的 DeviceDetailLogic 实例
    try {
      logic = Get.find<DeviceDetailLogic>();
      state = logic.state;
    } catch (e) {
      // 如果没有找到，创建一个新的实例
      logic = Get.put(DeviceDetailLogic());
      state = logic.state;
    }
  }

  @override
  Widget build(BuildContext context) {
    return BackgroundWrapper(
      child: Scaffold(
        backgroundColor: ColorPalettes.instance.transparent,
        appBar: AppBar(
          backgroundColor: ColorPalettes.instance.card,
          title: Text(
            l10n.displaySettings,
            style: TextStyles.instance.h2(),
          ),
          centerTitle: true,
          leading: IconButton(
            icon: Icon(
              Icons.arrow_back_ios,
              color: ColorPalettes.instance.firstText,
            ),
            onPressed: () => Get.back(),
          ),
          iconTheme: IconThemeData(color: ColorPalettes.instance.firstText),
        ),
        body: SingleChildScrollView(
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              children: [
                _buildDisplayViewCard(context),
                const SizedBox(height: 6),
                _buildThemeCard(context),
                const SizedBox(height: 6),
                _buildBrightnessCard(context),
                const SizedBox(height: 6),
                _buildVuMeterLevelCard(context),
                const SizedBox(height: 6),
                _buildVuMeterDisplayCard(context),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// 构建显示界面卡片
  Widget _buildDisplayViewCard(BuildContext context) {
    return _buildSettingCard(
      context,
      title: l10n.displayView,
      child: Obx(() => ListTile(
            dense: true,
            contentPadding:
                const EdgeInsets.symmetric(horizontal: 16, vertical: 0),
            title: Text(
              l10n.displayView,
              style: TextStyles.instance.h3(),
            ),
            trailing: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  _getDisplayModeText(),
                  style: TextStyles.instance.h3(),
                ),
                const SizedBox(width: 4),
                Icon(Icons.arrow_forward_ios,
                    color: ColorPalettes.instance.firstText, size: 12),
              ],
            ),
            onTap: () => _showDisplayModeSelectionDialog(context),
          )),
    );
  }

  /// 构建主题设置卡片
  Widget _buildThemeCard(BuildContext context) {
    return _buildSettingCard(
      context,
      title: l10n.theme,
      child: Obx(() => ListTile(
            dense: true,
            contentPadding:
                const EdgeInsets.symmetric(horizontal: 16, vertical: 0),
            title: Text(
              l10n.theme,
              style: TextStyles.instance.h3(),
            ),
            trailing: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  _getThemeText(),
                  style: TextStyles.instance.h3(),
                ),
                const SizedBox(width: 4),
                Icon(Icons.arrow_forward_ios,
                    color: ColorPalettes.instance.firstText, size: 12),
              ],
            ),
            onTap: () => _showThemeSelectionDialog(context),
          )),
    );
  }

  /// 构建主页设置卡片
  Widget _buildHomePageCard(BuildContext context) {
    return _buildSettingCard(
      context,
      title: l10n.multiFunctionKeyHomeSelect,
      child: Obx(() => ListTile(
            dense: true,
            contentPadding:
                const EdgeInsets.symmetric(horizontal: 16, vertical: 0),
            title: Text(
              l10n.multiFunctionKeyHomeSelect,
              style: TextStyles.instance.h3(),
            ),
            trailing: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  _getHomePageText(),
                  style: TextStyles.instance.h3(),
                ),
                const SizedBox(width: 4),
                Icon(Icons.arrow_forward_ios,
                    color: ColorPalettes.instance.firstText, size: 12),
              ],
            ),
            onTap: () => _showHomePageSelectionDialog(context),
          )),
    );
  }

  /// 构建亮度设置卡片
  Widget _buildBrightnessCard(BuildContext context) {
    return _buildSettingCard(
      context,
      title: l10n.screenBrightness,
      child: Obx(() => ListTile(
            dense: true,
            contentPadding:
                const EdgeInsets.symmetric(horizontal: 16, vertical: 0),
            title: Text(
              l10n.screenBrightness,
              style: TextStyles.instance.h3(),
            ),
            trailing: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  _getBrightnessText(),
                  style: TextStyles.instance.h3(),
                ),
                const SizedBox(width: 4),
                Icon(Icons.arrow_forward_ios,
                    color: ColorPalettes.instance.firstText, size: 12),
              ],
            ),
            onTap: () => _showBrightnessSelectionDialog(context),
          )),
    );
  }

  /// 构建VU表0dDB幅值设置卡片
  Widget _buildVuMeterLevelCard(BuildContext context) {
    return _buildSettingCard(
      context,
      title: l10n.classicVu0dBAmplitude,
      child: Obx(() => ListTile(
            dense: true,
            contentPadding:
                const EdgeInsets.symmetric(horizontal: 16, vertical: 0),
            title: Text(
              l10n.classicVu0dBAmplitude,
              style: TextStyles.instance.h3(),
            ),
            trailing: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  _getVuMeterLevelText(),
                  style: TextStyles.instance.h3(),
                ),
                const SizedBox(width: 4),
                Icon(Icons.arrow_forward_ios,
                    color: ColorPalettes.instance.firstText, size: 12),
              ],
            ),
            onTap: () => _showVuMeterLevelSelectionDialog(context),
          )),
    );
  }

  /// 构建VU条显示模式设置卡片
  Widget _buildVuMeterDisplayCard(BuildContext context) {
    return _buildSettingCard(
      context,
      title: l10n.vuMeterDisplayMode,
      child: Obx(() => ListTile(
            dense: true,
            contentPadding:
                const EdgeInsets.symmetric(horizontal: 16, vertical: 0),
            title: Text(
              l10n.vuMeterDisplayMode,
              style: TextStyles.instance.h3(),
            ),
            trailing: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  _getVuMeterDisplayText(),
                  style: TextStyles.instance.h3(),
                ),
                const SizedBox(width: 4),
                Icon(Icons.arrow_forward_ios,
                    color: ColorPalettes.instance.firstText, size: 12),
              ],
            ),
            onTap: () => _showVuMeterDisplaySelectionDialog(context),
          )),
    );
  }

  /// 构建设置卡片
  Widget _buildSettingCard(
    BuildContext context, {
    required String title,
    required Widget child,
  }) {
    return Card(
      color: ColorPalettes.instance.card,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(6),
      ),
      margin: EdgeInsets.symmetric(vertical: 3),
      child: child,
    );
  }

  /// 获取主题文本
  String _getThemeText() {
    int themeValue = _getThemeValue();
    if (isD900Device) {
      // D900设备的9种主题
      switch (themeValue) {
        case 0:
          return l10n.aurora;
        case 1:
          return '橙色';
        case 2:
          return '秘鲁色';
        case 3:
          return '豆绿色';
        case 4:
          return '深卡其色';
        case 5:
          return '玫瑰棕色';
        case 6:
          return '蓝色';
        case 7:
          return '幻紫色';
        case 8:
          return '白色';
        default:
          return l10n.aurora;
      }
    } else {
      // DX5II设备的3种主题
      switch (themeValue) {
        case 0:
          return l10n.aurora;
        case 1:
          return l10n.classic;
        case 2:
          return l10n.simple;
        default:
          return l10n.aurora;
      }
    }
  }

  /// 获取主题值
  int _getThemeValue() {
    if (settings != null) {
      if (isD900Device && settings is D900DeviceSettings) {
        var d900Settings = settings as D900DeviceSettings;
        return d900Settings.theme.index;
      } else if (!isD900Device && settings is Dx5DeviceSettings) {
        var dx5Settings = settings as Dx5DeviceSettings;
        return dx5Settings.theme.index;
      }
    }
    return 0;
  }

  /// 获取显示模式文本
  String _getDisplayModeText() {
    if (settings != null) {
      return settings.displayType?.localized(Get.context!) ?? l10n.normal;
    }
    return l10n.normal;
  }

  /// 获取主页文本
  String _getHomePageText() {
    // TODO: 实现主页文本获取
    return l10n.normal;
  }

  /// 获取亮度文本
  String _getBrightnessText() {
    int brightnessValue = _getBrightnessValue();
    switch (brightnessValue) {
      case 0:
        return l10n.low;
      case 1:
        return l10n.middle;
      case 2:
        return l10n.high;
      case 3:
        return l10n.auto;
      default:
        return l10n.middle;
    }
  }

  /// 获取亮度值
  int _getBrightnessValue() {
    if (settings != null) {
      if (isD900Device && settings is D900DeviceSettings) {
        var d900Settings = settings as D900DeviceSettings;
        return d900Settings.screenBrightness?.index ?? 1;
      } else if (!isD900Device && settings is Dx5DeviceSettings) {
        var dx5Settings = settings as Dx5DeviceSettings;
        return dx5Settings.screenBrightness?.index ?? 1;
      }
    }
    return 1;
  }

  /// 获取VU表0dDB幅值文本
  String _getVuMeterLevelText() {
    int levelValue = _getVuMeterLevelValue();
    switch (levelValue) {
      case 0:
        return "+4dBu";
      case 1:
        return "+8dBu";
      case 2:
        return "+12dBu";
      case 3:
        return "+16dBu";
      default:
        return "+4dBu";
    }
  }

  /// 获取VU表0dDB幅值值
  int _getVuMeterLevelValue() {
    // TODO: 从设备设置中获取实际值，目前返回默认值
    return 0;
  }

  /// 获取VU条显示模式文本
  String _getVuMeterDisplayText() {
    int displayValue = _getVuMeterDisplayValue();
    switch (displayValue) {
      case 0:
        return l10n.fullOpen;
      case 1:
        return l10n.halfOpen;
      case 2:
        return l10n.close;
      default:
        return l10n.fullOpen;
    }
  }

  /// 获取VU条显示模式值
  int _getVuMeterDisplayValue() {
    // TODO: 从设备设置中获取实际值，目前返回默认值
    return 0;
  }

  /// 显示主题选择对话框
  void _showThemeSelectionDialog(BuildContext context) {
    if (isD900Device) {
      _showD900ThemeSelectionDialog(context);
      return;
    }
    _showDx5ThemeSelectionDialog(context);
  }

  /// 显示DX5主题选择对话框
  void _showDx5ThemeSelectionDialog(BuildContext context) {
    final themes = [
      {'name': l10n.aurora, 'value': 0},
      {'name': l10n.classic, 'value': 1},
      {'name': l10n.simple, 'value': 2},
    ];

    showModalBottomSheet(
      context: context,
      backgroundColor: ColorPalettes.instance.card,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(12)),
      ),
      builder: (BuildContext context) {
        return Container(
          padding: EdgeInsets.symmetric(vertical: 10),
          child: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                  child: Text(
                    l10n.theme,
                    style: TextStyles.instance.h3(),
                  ),
                ),
                Divider(color: ColorPalettes.instance.divider),
                ListView.builder(
                  shrinkWrap: true,
                  physics: NeverScrollableScrollPhysics(),
                  itemCount: themes.length,
                  itemBuilder: (context, index) {
                    final theme = themes[index];
                    bool isSelected = _getThemeValue() == theme['value'];
                    return ListTile(
                      title: Row(
                        children: [
                          Text(
                            theme['name'] as String,
                            style: TextStyles.instance.h3(),
                          ),
                          if (isSelected) ...[
                            SizedBox(width: 8),
                            Icon(
                              Icons.check_circle,
                              color: ColorPalettes.instance.accent,
                              size: 16,
                            ),
                          ],
                        ],
                      ),
                      onTap: () {
                        logic.setTheme(theme['value']);
                        Navigator.pop(context);
                      },
                    );
                  },
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  /// 显示D900主题选择对话框
  void _showD900ThemeSelectionDialog(BuildContext context) {
    final themes = [
      {'name': '荧光', 'value': 0},
      {'name': '暗色', 'value': 1},
      {'name': '橙色', 'value': 2},
      {'name': '豆绿色', 'value': 3},
      {'name': '深卡其色', 'value': 4},
      {'name': '玫瑰棕色', 'value': 5},
      {'name': '蓝色', 'value': 6},
      {'name': '红色', 'value': 7},
      {'name': '白色', 'value': 8},
    ];

    showModalBottomSheet(
      context: context,
      backgroundColor: ColorPalettes.instance.card,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(12)),
      ),
      builder: (BuildContext context) {
        return Container(
          padding: EdgeInsets.symmetric(vertical: 10),
          child: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                  child: Text(
                    l10n.theme,
                    style: TextStyles.instance.h3(),
                  ),
                ),
                Divider(color: ColorPalettes.instance.divider),
                ListView.builder(
                  shrinkWrap: true,
                  physics: NeverScrollableScrollPhysics(),
                  itemCount: themes.length,
                  itemBuilder: (context, index) {
                    final theme = themes[index];
                    bool isSelected = _getThemeValue() == theme['value'];
                    return ListTile(
                      title: Row(
                        children: [
                          Text(
                            theme['name'] as String,
                            style: TextStyles.instance.h3(),
                          ),
                          if (isSelected) ...[
                            SizedBox(width: 8),
                            Icon(
                              Icons.check_circle,
                              color: ColorPalettes.instance.accent,
                              size: 16,
                            ),
                          ],
                        ],
                      ),
                      onTap: () {
                        logic.setTheme(theme['value']);
                        Navigator.pop(context);
                      },
                    );
                  },
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  /// 显示显示模式选择对话框
  void _showDisplayModeSelectionDialog(BuildContext context) {
    if (isD900Device) {
      _showD900DisplaySelectionDialog(context);
      return;
    }
    _showDx5DisplaySelectionDialog(context);
  }

  /// 显示DX5显示模式选择对话框
  void _showDx5DisplaySelectionDialog(BuildContext context) {
    showModalBottomSheet(
      context: context,
      backgroundColor: ColorPalettes.instance.card,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(12)),
      ),
      builder: (BuildContext context) {
        return Container(
          padding: EdgeInsets.symmetric(vertical: 10),
          child: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                  child: Text(
                    l10n.displayView,
                    style: TextStyles.instance.h3(),
                  ),
                ),
                Divider(color: ColorPalettes.instance.divider),
                ListView.builder(
                  shrinkWrap: true,
                  physics: NeverScrollableScrollPhysics(),
                  itemCount: state.availableDisplayModes.length,
                  itemBuilder: (context, index) {
                    final displayMode = state.availableDisplayModes[index];
                    bool isSelected = settings?.displayType == displayMode;
                    return ListTile(
                      title: Row(
                        children: [
                          Text(
                            displayMode.localized(context),
                            style: TextStyles.instance.h3(),
                          ),
                          if (isSelected) ...[
                            SizedBox(width: 8),
                            Icon(
                              Icons.check_circle,
                              color: ColorPalettes.instance.accent,
                              size: 16,
                            ),
                          ],
                        ],
                      ),
                      onTap: () {
                        logic.setDisplayMode(displayMode);
                        Navigator.pop(context);
                      },
                    );
                  },
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  /// 显示D900显示模式选择对话框
  void _showD900DisplaySelectionDialog(BuildContext context) {
    showModalBottomSheet(
      context: context,
      backgroundColor: ColorPalettes.instance.card,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(12)),
      ),
      builder: (BuildContext context) {
        return Container(
          padding: EdgeInsets.symmetric(vertical: 10),
          child: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                  child: Text(
                    l10n.displayView,
                    style: TextStyles.instance.h3(),
                  ),
                ),
                Divider(color: ColorPalettes.instance.divider),
                ListView.builder(
                  shrinkWrap: true,
                  physics: NeverScrollableScrollPhysics(),
                  itemCount: state.availableDisplayModes.length,
                  itemBuilder: (context, index) {
                    final displayMode = state.availableDisplayModes[index];
                    bool isSelected = false;

                    if (settings is D900DeviceSettings) {
                      var d900Settings = settings as D900DeviceSettings;
                      isSelected = d900Settings.displayType == displayMode;
                    }

                    return ListTile(
                      title: Row(
                        children: [
                          Text(
                            displayMode.localized(context),
                            style: TextStyles.instance.h3(),
                          ),
                          if (isSelected) ...[
                            SizedBox(width: 8),
                            Icon(
                              Icons.check_circle,
                              color: ColorPalettes.instance.accent,
                              size: 16,
                            ),
                          ],
                        ],
                      ),
                      onTap: () {
                        logic.setDisplayMode(displayMode);
                        Navigator.pop(context);
                      },
                    );
                  },
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  /// 显示主页选择对话框
  void _showHomePageSelectionDialog(BuildContext context) {
    // TODO: 实现主页选择对话框
    Get.snackbar(l10n.tip, l10n.homePageSelectionInDevelopment);
  }

  /// 显示亮度选择对话框
  void _showBrightnessSelectionDialog(BuildContext context) {
    final brightnessList = [
      {'name': l10n.low, 'value': 0},
      {'name': l10n.middle, 'value': 1},
      {'name': l10n.high, 'value': 2},
      {'name': l10n.auto, 'value': 3},
    ];

    showModalBottomSheet(
      context: context,
      backgroundColor: ColorPalettes.instance.card,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(12)),
      ),
      builder: (BuildContext context) {
        return Container(
          padding: EdgeInsets.symmetric(vertical: 10),
          child: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                  child: Text(
                    l10n.screenBrightness,
                    style: TextStyles.instance.h3(),
                  ),
                ),
                Divider(color: ColorPalettes.instance.divider),
                ListView.builder(
                  shrinkWrap: true,
                  physics: NeverScrollableScrollPhysics(),
                  itemCount: brightnessList.length,
                  itemBuilder: (context, index) {
                    final brightness = brightnessList[index];
                    bool isSelected = _getBrightnessValue() == brightness['value'];
                    return ListTile(
                      title: Row(
                        children: [
                          Text(
                            brightness['name'] as String,
                            style: TextStyles.instance.h3(),
                          ),
                          if (isSelected) ...[
                            SizedBox(width: 8),
                            Icon(
                              Icons.check_circle,
                              color: ColorPalettes.instance.accent,
                              size: 16,
                            ),
                          ],
                        ],
                      ),
                      onTap: () {
                        logic.setScreenBrightness(brightness['value']);
                        Navigator.pop(context);
                      },
                    );
                  },
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  /// 显示VU表0dDB幅值选择对话框
  void _showVuMeterLevelSelectionDialog(BuildContext context) {
    final levelList = [
      {'name': '+4dBu', 'value': 0},
      {'name': '+8dBu', 'value': 1},
      {'name': '+12dBu', 'value': 2},
      {'name': '+16dBu', 'value': 3},
    ];

    showModalBottomSheet(
      context: context,
      backgroundColor: ColorPalettes.instance.card,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(12)),
      ),
      builder: (BuildContext context) {
        return Container(
          padding: EdgeInsets.symmetric(vertical: 10),
          child: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                  child: Text(
                    l10n.classicVu0dBAmplitude,
                    style: TextStyles.instance.h3(),
                  ),
                ),
                Divider(color: ColorPalettes.instance.divider),
                ListView.builder(
                  shrinkWrap: true,
                  physics: NeverScrollableScrollPhysics(),
                  itemCount: levelList.length,
                  itemBuilder: (context, index) {
                    final level = levelList[index];
                    bool isSelected = _getVuMeterLevelValue() == level['value'];
                    return ListTile(
                      title: Row(
                        children: [
                          Text(
                            level['name'] as String,
                            style: TextStyles.instance.h3(),
                          ),
                          if (isSelected) ...[
                            SizedBox(width: 8),
                            Icon(
                              Icons.check_circle,
                              color: ColorPalettes.instance.accent,
                              size: 16,
                            ),
                          ],
                        ],
                      ),
                      onTap: () {
                        logic.setVuMeterLevel(level['value'] as int);
                        Navigator.pop(context);
                      },
                    );
                  },
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  /// 显示VU条显示模式选择对话框
  void _showVuMeterDisplaySelectionDialog(BuildContext context) {
    final displayList = [
      {'name': l10n.fullOpen, 'value': 0},
      {'name': l10n.halfOpen, 'value': 1},
      {'name': l10n.close, 'value': 2},
    ];

    showModalBottomSheet(
      context: context,
      backgroundColor: ColorPalettes.instance.card,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(12)),
      ),
      builder: (BuildContext context) {
        return Container(
          padding: EdgeInsets.symmetric(vertical: 10),
          child: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                  child: Text(
                    l10n.vuMeterDisplayMode,
                    style: TextStyles.instance.h3(),
                  ),
                ),
                Divider(color: ColorPalettes.instance.divider),
                ListView.builder(
                  shrinkWrap: true,
                  physics: NeverScrollableScrollPhysics(),
                  itemCount: displayList.length,
                  itemBuilder: (context, index) {
                    final display = displayList[index];
                    bool isSelected = _getVuMeterDisplayValue() == display['value'];
                    return ListTile(
                      title: Row(
                        children: [
                          Text(
                            display['name'] as String,
                            style: TextStyles.instance.h3(),
                          ),
                          if (isSelected) ...[
                            SizedBox(width: 8),
                            Icon(
                              Icons.check_circle,
                              color: ColorPalettes.instance.accent,
                              size: 16,
                            ),
                          ],
                        ],
                      ),
                      onTap: () {
                        logic.setVuMeterDisplay(display['value'] as int);
                        Navigator.pop(context);
                      },
                    );
                  },
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}
