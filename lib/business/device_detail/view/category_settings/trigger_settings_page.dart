import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:topping_control/business/device_detail/logic/device_detail_logic.dart';
import 'package:topping_control/business/device_detail/state/device_detail_state.dart';
import 'package:topping_control/common/ui/color_palettes.dart';
import 'package:topping_control/common/ui/text_styles.dart';
import 'package:topping_control/generated/l10n.dart';
import 'package:topping_control/model/device_settings.dart';

/// 触发设置页面 (DX5II专用)
class TriggerSettingsPage extends StatefulWidget {
  const TriggerSettingsPage({Key? key}) : super(key: key);

  @override
  State<TriggerSettingsPage> createState() => _TriggerSettingsPageState();
}

class _TriggerSettingsPageState extends State<TriggerSettingsPage> {
  late DeviceDetailLogic logic;
  late DeviceDetailState state;
  late S l10n;
  late String deviceId;
  late bool isD900Device;
  DeviceSettings? settings;

  @override
  void initState() {
    super.initState();
    final arguments = Get.arguments as Map<String, dynamic>;
    deviceId = arguments['deviceId'] as String;
    isD900Device = arguments['isD900Device'] as bool;
    
    logic = Get.find<DeviceDetailLogic>(tag: deviceId);
    state = logic.state;
    l10n = S.of(context);
  }

  @override
  Widget build(BuildContext context) {
    l10n = S.of(context);
    
    return Scaffold(
      backgroundColor: ColorPalettes.instance.background,
      appBar: AppBar(
        backgroundColor: ColorPalettes.instance.background,
        elevation: 0,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back_ios,
            color: ColorPalettes.instance.firstText,
          ),
          onPressed: () => Get.back(),
        ),
        title: Text(
          '触发设置',
          style: TextStyles.instance.h2(),
        ),
        centerTitle: true,
      ),
      body: GetBuilder<DeviceDetailLogic>(
        tag: deviceId,
        builder: (logic) {
          settings = logic.state.settings;
          return SingleChildScrollView(
            padding: const EdgeInsets.all(16),
            child: Column(
              children: [
                _buildPowerTriggerCard(context),
              ],
            ),
          );
        },
      ),
    );
  }

  /// 构建开关机触发卡片
  Widget _buildPowerTriggerCard(BuildContext context) {
    return _buildSettingCard(
      context,
      title: l10n.powerTrigger,
      child: ListTile(
        dense: true,
        contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
        title: Text(
          l10n.powerTrigger,
          style: TextStyles.instance.h3(),
        ),
        trailing: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              _getPowerTriggerText(),
              style: TextStyles.instance.h3().copyWith(
                color: ColorPalettes.instance.secondText,
              ),
            ),
            const SizedBox(width: 8),
            Icon(
              Icons.arrow_forward_ios,
              color: ColorPalettes.instance.firstText,
              size: 12,
            ),
          ],
        ),
        onTap: () => _showPowerTriggerSelectionDialog(context),
      ),
    );
  }

  /// 构建设置卡片
  Widget _buildSettingCard(
    BuildContext context, {
    required String title,
    required Widget child,
  }) {
    return Card(
      color: ColorPalettes.instance.card,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(6),
      ),
      margin: EdgeInsets.symmetric(vertical: 3),
      child: child,
    );
  }

  /// 获取开关机触发文本
  String _getPowerTriggerText() {
    int triggerValue = _getPowerTriggerValue();
    switch (triggerValue) {
      case 0:
        return l10n.signal;
      case 1:
        return "12V";
      case 2:
        return l10n.close;
      default:
        return l10n.signal;
    }
  }

  /// 获取开关机触发值
  int _getPowerTriggerValue() {
    if (settings != null && settings is Dx5DeviceSettings) {
      var dx5Settings = settings as Dx5DeviceSettings;
      return dx5Settings.powerTrigger.index;
    }
    return 0;
  }

  /// 显示开关机触发选择对话框
  void _showPowerTriggerSelectionDialog(BuildContext context) {
    final triggerList = [
      {'name': l10n.signal, 'value': 0},
      {'name': '12V', 'value': 1},
      {'name': l10n.close, 'value': 2},
    ];

    showModalBottomSheet(
      context: context,
      backgroundColor: ColorPalettes.instance.card,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(12)),
      ),
      builder: (BuildContext context) {
        return Container(
          padding: EdgeInsets.symmetric(vertical: 10),
          child: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                  child: Text(
                    l10n.powerTrigger,
                    style: TextStyles.instance.h3(),
                  ),
                ),
                Divider(color: ColorPalettes.instance.divider),
                ListView.builder(
                  shrinkWrap: true,
                  physics: NeverScrollableScrollPhysics(),
                  itemCount: triggerList.length,
                  itemBuilder: (context, index) {
                    final trigger = triggerList[index];
                    bool isSelected = _getPowerTriggerValue() == trigger['value'];
                    return ListTile(
                      title: Row(
                        children: [
                          Text(
                            trigger['name'] as String,
                            style: TextStyles.instance.h3(),
                          ),
                          if (isSelected) ...[
                            SizedBox(width: 8),
                            Icon(
                              Icons.check_circle,
                              color: ColorPalettes.instance.accent,
                              size: 16,
                            ),
                          ],
                        ],
                      ),
                      onTap: () {
                        logic.setPowerTrigger(trigger['value']);
                        Navigator.pop(context);
                      },
                    );
                  },
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}
