import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:topping_control/business/device_detail/logic/device_detail_logic.dart';
import 'package:topping_control/business/device_detail/state/device_detail_state.dart';
import 'package:topping_control/common/ui/color_palettes.dart';
import 'package:topping_control/common/ui/text_styles.dart';
import 'package:topping_control/generated/l10n.dart';
import 'package:topping_control/model/device_settings.dart';

/// 记忆方式设置页面 (DX5II专用)
class MemorySettingsPage extends StatefulWidget {
  const MemorySettingsPage({Key? key}) : super(key: key);

  @override
  State<MemorySettingsPage> createState() => _MemorySettingsPageState();
}

class _MemorySettingsPageState extends State<MemorySettingsPage> {
  late DeviceDetailLogic logic;
  late DeviceDetailState state;
  late S l10n;
  late String deviceId;
  late bool isD900Device;
  DeviceSettings? settings;

  @override
  void initState() {
    super.initState();
    final arguments = Get.arguments as Map<String, dynamic>;
    deviceId = arguments['deviceId'] as String;
    isD900Device = arguments['isD900Device'] as bool;
    
    logic = Get.find<DeviceDetailLogic>(tag: deviceId);
    state = logic.state;
    l10n = S.of(context);
  }

  @override
  Widget build(BuildContext context) {
    l10n = S.of(context);
    
    return Scaffold(
      backgroundColor: ColorPalettes.instance.background,
      appBar: AppBar(
        backgroundColor: ColorPalettes.instance.background,
        elevation: 0,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back_ios,
            color: ColorPalettes.instance.firstText,
          ),
          onPressed: () => Get.back(),
        ),
        title: Text(
          '记忆方式设置',
          style: TextStyles.instance.h2(),
        ),
        centerTitle: true,
      ),
      body: GetBuilder<DeviceDetailLogic>(
        tag: deviceId,
        builder: (logic) {
          settings = logic.state.settings;
          return SingleChildScrollView(
            padding: const EdgeInsets.all(16),
            child: Column(
              children: [
                _buildVolumeMemoryCard(context),
                const SizedBox(height: 6),
                _buildPeqMemoryCard(context),
              ],
            ),
          );
        },
      ),
    );
  }

  /// 构建音量记忆方式卡片
  Widget _buildVolumeMemoryCard(BuildContext context) {
    return _buildSettingCard(
      context,
      title: l10n.volumeMemoryMode,
      child: ListTile(
        dense: true,
        contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
        title: Text(
          l10n.volumeMemoryMode,
          style: TextStyles.instance.h3(),
        ),
        trailing: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              _getVolumeMemoryText(),
              style: TextStyles.instance.h3().copyWith(
                color: ColorPalettes.instance.secondText,
              ),
            ),
            const SizedBox(width: 8),
            Icon(
              Icons.arrow_forward_ios,
              color: ColorPalettes.instance.firstText,
              size: 12,
            ),
          ],
        ),
        onTap: () => _showVolumeMemorySelectionDialog(context),
      ),
    );
  }

  /// 构建PEQ记忆方式卡片
  Widget _buildPeqMemoryCard(BuildContext context) {
    return _buildSettingCard(
      context,
      title: l10n.peqMemoryMode,
      child: ListTile(
        dense: true,
        contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
        title: Text(
          l10n.peqMemoryMode,
          style: TextStyles.instance.h3(),
        ),
        trailing: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              _getPeqMemoryText(),
              style: TextStyles.instance.h3().copyWith(
                color: ColorPalettes.instance.secondText,
              ),
            ),
            const SizedBox(width: 8),
            Icon(
              Icons.arrow_forward_ios,
              color: ColorPalettes.instance.firstText,
              size: 12,
            ),
          ],
        ),
        onTap: () => _showPeqMemorySelectionDialog(context),
      ),
    );
  }

  /// 构建设置卡片
  Widget _buildSettingCard(
    BuildContext context, {
    required String title,
    required Widget child,
  }) {
    return Card(
      color: ColorPalettes.instance.card,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(6),
      ),
      margin: EdgeInsets.symmetric(vertical: 3),
      child: child,
    );
  }

  /// 获取音量记忆方式文本
  String _getVolumeMemoryText() {
    int memoryValue = _getVolumeMemoryValue();
    switch (memoryValue) {
      case 0:
        return '跟随输入';
      case 1:
        return '跟随输出';
      case 2:
        return '无';
      default:
        return '跟随输出';
    }
  }

  /// 获取音量记忆方式值
  int _getVolumeMemoryValue() {
    // TODO: 从设备设置中获取实际值，目前返回默认值
    return 1; // 默认跟随输出
  }

  /// 获取PEQ记忆方式文本
  String _getPeqMemoryText() {
    int memoryValue = _getPeqMemoryValue();
    switch (memoryValue) {
      case 0:
        return '跟随输入';
      case 1:
        return '跟随输出';
      case 2:
        return '无';
      default:
        return '跟随输出';
    }
  }

  /// 获取PEQ记忆方式值
  int _getPeqMemoryValue() {
    // TODO: 从设备设置中获取实际值，目前返回默认值
    return 1; // 默认跟随输出
  }

  /// 显示音量记忆方式选择对话框
  void _showVolumeMemorySelectionDialog(BuildContext context) {
    final memoryOptions = [
      {'name': '跟随输入', 'value': 0},
      {'name': '跟随输出', 'value': 1},
      {'name': '无', 'value': 2},
    ];

    showModalBottomSheet(
      context: context,
      backgroundColor: ColorPalettes.instance.card,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(12)),
      ),
      builder: (BuildContext context) {
        return Container(
          padding: EdgeInsets.symmetric(vertical: 10),
          child: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                  child: Text(
                    l10n.volumeMemoryMode,
                    style: TextStyles.instance.h3(),
                  ),
                ),
                Divider(color: ColorPalettes.instance.divider),
                ListView.builder(
                  shrinkWrap: true,
                  physics: NeverScrollableScrollPhysics(),
                  itemCount: memoryOptions.length,
                  itemBuilder: (context, index) {
                    final option = memoryOptions[index];
                    bool isSelected = _getVolumeMemoryValue() == option['value'];
                    return ListTile(
                      title: Row(
                        children: [
                          Text(
                            option['name'] as String,
                            style: TextStyles.instance.h3(),
                          ),
                          if (isSelected) ...[
                            SizedBox(width: 8),
                            Icon(
                              Icons.check_circle,
                              color: ColorPalettes.instance.accent,
                              size: 16,
                            ),
                          ],
                        ],
                      ),
                      onTap: () {
                        logic.setVolumeMemoryMode(option['value']);
                        Navigator.pop(context);
                      },
                    );
                  },
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  /// 显示PEQ记忆方式选择对话框
  void _showPeqMemorySelectionDialog(BuildContext context) {
    final memoryOptions = [
      {'name': '跟随输入', 'value': 0},
      {'name': '跟随输出', 'value': 1},
      {'name': '无', 'value': 2},
    ];

    showModalBottomSheet(
      context: context,
      backgroundColor: ColorPalettes.instance.card,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(12)),
      ),
      builder: (BuildContext context) {
        return Container(
          padding: EdgeInsets.symmetric(vertical: 10),
          child: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                  child: Text(
                    l10n.peqMemoryMode,
                    style: TextStyles.instance.h3(),
                  ),
                ),
                Divider(color: ColorPalettes.instance.divider),
                ListView.builder(
                  shrinkWrap: true,
                  physics: NeverScrollableScrollPhysics(),
                  itemCount: memoryOptions.length,
                  itemBuilder: (context, index) {
                    final option = memoryOptions[index];
                    bool isSelected = _getPeqMemoryValue() == option['value'];
                    return ListTile(
                      title: Row(
                        children: [
                          Text(
                            option['name'] as String,
                            style: TextStyles.instance.h3(),
                          ),
                          if (isSelected) ...[
                            SizedBox(width: 8),
                            Icon(
                              Icons.check_circle,
                              color: ColorPalettes.instance.accent,
                              size: 16,
                            ),
                          ],
                        ],
                      ),
                      onTap: () {
                        logic.setPeqMemoryMode(option['value']);
                        Navigator.pop(context);
                      },
                    );
                  },
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}
