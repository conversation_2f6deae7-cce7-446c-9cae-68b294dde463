import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:topping_ble_control/service/ota_server.dart';

import '../../common/util/i18n.dart';
import '../../common/widget/animated_progress_indicator.dart';
import '../../l10n/app_localizations.dart';
import '../../service/firmware_service.dart';
import '../../theme/color_palettes.dart';
import '../../theme/text_styles.dart';
import 'firmware_update_logic.dart';

/// 固件升级页面
class FirmwareUpdatePage extends GetView<FirmwareUpdateLogic> {
  const FirmwareUpdatePage({super.key});

  @override
  Widget build(BuildContext context) {
    return Obx(() => Scaffold(
          appBar: AppBar(
            title:
                Text(l10n.firmwareUpdateTitle, style: TextStyles.instance.h2()),
            backgroundColor: ColorPalettes.instance.background,
            centerTitle: true,
            iconTheme: IconThemeData(color: ColorPalettes.instance.firstText),
            elevation: 0,
          ),
          body: _buildBody(),
        ));
  }

  Widget _buildBody() {
    final context = Get.context!;
    final l10n = AppLocalizations.of(context)!;
    final firmwareInfo = FirmwareService.to.latestFirmware.value;
    if (firmwareInfo == null) {
      return _buildNoUpdateView();
    }

    return SingleChildScrollView(
      padding: EdgeInsets.all(16.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 固件信息卡片
          Container(
            width: double.infinity,
            decoration: BoxDecoration(
              color: ColorPalettes.instance.card,
              borderRadius: BorderRadius.circular(16.r),
              boxShadow: [
                BoxShadow(
                  color: ColorPalettes.instance.shadow.withValues(alpha: 0.1),
                  blurRadius: 10,
                  offset: const Offset(0, 4),
                ),
              ],
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // 头部标题
                Container(
                  padding:
                      EdgeInsets.symmetric(horizontal: 20.w, vertical: 16.h),
                  decoration: BoxDecoration(
                    color: ColorPalettes.instance.primary.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.only(
                      topLeft: Radius.circular(16.r),
                      topRight: Radius.circular(16.r),
                    ),
                  ),
                  child: Row(
                    children: [
                      Icon(
                        Icons.system_update_alt,
                        color: ColorPalettes.instance.primary,
                        size: 24.sp,
                      ),
                      SizedBox(width: 12.w),
                      Text(
                        l10n.firmwareUpdateNewFirmwareFound,
                        style: TextStyles.instance.h2(
                          color: ColorPalettes.instance.primary,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                ),

                // 固件信息内容
                Padding(
                  padding: EdgeInsets.all(20.w),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      _buildInfoRow(l10n.firmwareUpdateFirmwareName,
                          firmwareInfo.firmwareName),
                      Divider(
                          height: 16.h,
                          color:
                              ColorPalettes.instance.divider.withValues(alpha: 0.5)),
                      _buildInfoRow(
                          l10n.firmwareUpdateVersion, firmwareInfo.versionName),
                      Divider(
                          height: 16.h,
                          color:
                              ColorPalettes.instance.divider.withValues(alpha: 0.5)),
                      _buildInfoRow(l10n.firmwareUpdateDeviceModel,
                          firmwareInfo.deviceModel),
                      Divider(
                          height: 16.h,
                          color:
                              ColorPalettes.instance.divider.withValues(alpha: 0.5)),
                      _buildInfoRow(
                        l10n.firmwareUpdateFileSize,
                        '${(firmwareInfo.fileSize / 1024).toStringAsFixed(2)} KB',
                      ),
                      Divider(
                          height: 16.h,
                          color:
                              ColorPalettes.instance.divider.withValues(alpha: 0.5)),
                      _buildInfoRow(l10n.firmwareUpdateDescription,
                          firmwareInfo.description),
                      SizedBox(height: 16.h),
                      Container(
                        padding: EdgeInsets.symmetric(
                            horizontal: 12.w, vertical: 8.h),
                        decoration: BoxDecoration(
                          color: ColorPalettes.instance.error.withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(8.r),
                          border:
                              Border.all(color: ColorPalettes.instance.error.withValues(alpha: 0.3)),
                        ),
                        child: Row(
                          children: [
                            Icon(Icons.warning_amber_rounded,
                                color: ColorPalettes.instance.error, size: 18.sp),
                            SizedBox(width: 8.w),
                            Expanded(
                              child: Text(
                                l10n.firmwareUpdateMandatoryUpdateNote,
                                style: TextStyle(
                                  color: ColorPalettes.instance.error,
                                  fontSize: 14.sp,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),

          SizedBox(height: 24.h),

          // 进度状态卡片
          Container(
            width: double.infinity,
            decoration: BoxDecoration(
              color: ColorPalettes.instance.card,
              borderRadius: BorderRadius.circular(16.r),
              boxShadow: [
                BoxShadow(
                  color: ColorPalettes.instance.shadow.withValues(alpha: 0.1),
                  blurRadius: 10,
                  offset: const Offset(0, 4),
                ),
              ],
            ),
            child: Padding(
              padding: EdgeInsets.all(20.w),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // 标题
                  Row(
                    children: [
                      Icon(
                        Icons.sync_rounded,
                        color: ColorPalettes.instance.primary,
                        size: 22.sp,
                      ),
                      SizedBox(width: 10.w),
                      Text(
                        l10n.firmwareUpdateStatus,
                        style: TextStyles.instance.h3(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                  SizedBox(height: 20.h),

                  // 下载进度
                  Obx(() {
                    if (FirmwareService.to.isDownloading.value) {
                      return AnimatedProgressIndicator(
                        value: FirmwareService.to.downloadProgress.value,
                        label: l10n.firmwareUpdateDownloading,
                        valueColor: ColorPalettes.instance.primary,
                      );
                    }
                    return SizedBox.shrink();
                  }),

                  // 升级进度
                  Obx(() {
                    if (controller.state.upgrading.value) {
                      return Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // 升级进度条
                          AnimatedProgressIndicator(
                            value: OtaServer.to.updatePer.value / 100,
                            label: l10n.firmwareUpdateUpgrading,
                            valueColor: ColorPalettes.instance.accent,
                          ),

                          SizedBox(height: 16.h),

                          // 升级状态信息
                          Container(
                            padding: EdgeInsets.symmetric(
                                horizontal: 16.w, vertical: 12.h),
                            decoration: BoxDecoration(
                              color: ColorPalettes.instance.primary
                                  .withValues(alpha: 0.1),
                              borderRadius: BorderRadius.circular(12.r),
                            ),
                            child: Row(
                              children: [
                                Icon(
                                  Icons.info_outline,
                                  color: ColorPalettes.instance.primary,
                                  size: 24.sp,
                                ),
                                SizedBox(width: 12.w),
                                Expanded(
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Text(
                                        l10n.firmwareUpdateUpgrading,
                                        style: TextStyles.instance.body1(
                                          fontWeight: FontWeight.bold,
                                        ),
                                      ),
                                      SizedBox(height: 4.h),
                                      Text(
                                        l10n.firmwareUpdateDoNotDisconnect,
                                        style: TextStyles.instance.caption(),
                                      ),
                                    ],
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      );
                    }
                    return SizedBox.shrink();
                  }),

                  // 当无下载或升级时显示的提示
                  Obx(() {
                    if (!FirmwareService.to.isDownloading.value &&
                        !controller.state.upgrading.value) {
                      return Container(
                        padding: EdgeInsets.symmetric(vertical: 16.h),
                        child: Center(
                          child: Text(
                            l10n.firmwareUpdateReadyToUpdate,
                            style: TextStyles.instance.body1(
                              color: ColorPalettes.instance.secondText,
                            ),
                          ),
                        ),
                      );
                    }
                    return SizedBox.shrink();
                  }),
                ],
              ),
            ),
          ),

          // 操作按钮
          Obx(() {
            if (controller.state.upgrading.value) {
              // 升级中显示取消按钮
              return Container(
                width: double.infinity,
                padding: EdgeInsets.symmetric(horizontal: 16.w),
                child: ElevatedButton.icon(
                  onPressed: controller.cancelUpgrade,
                  icon: Icon(Icons.cancel_outlined),
                  label: Text(l10n.firmwareUpdateCancel),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: ColorPalettes.instance.error,
                    foregroundColor: ColorPalettes.instance.pure,
                    padding: EdgeInsets.symmetric(vertical: 16.h),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12.r),
                    ),
                    elevation: 2,
                  ),
                ),
              );
            } else if (FirmwareService.to.isDownloading.value) {
              // 下载中显示禁用按钮
              return Container(
                width: double.infinity,
                padding: EdgeInsets.symmetric(horizontal: 16.w),
                child: ElevatedButton.icon(
                  onPressed: null, // 禁用按钮
                  icon: SizedBox(
                    width: 20.w,
                    height: 20.h,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(ColorPalettes.instance.pure.withValues(alpha: 0.7)),
                    ),
                  ),
                  label: Text(l10n.firmwareUpdateDownloading),
                  style: ElevatedButton.styleFrom(
                    padding: EdgeInsets.symmetric(vertical: 16.h),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12.r),
                    ),
                  ),
                ),
              );
            } else {
              // 开始升级按钮
              return Container(
                width: double.infinity,
                padding: EdgeInsets.symmetric(horizontal: 16.w),
                child: ElevatedButton.icon(
                  onPressed: controller.startFirmwareUpdate,
                  icon: Icon(
                    Icons.system_update,
                    color: ColorPalettes.instance.pure,
                  ),
                  label: Text(
                    l10n.firmwareUpdateStart,
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w500,
                      color: ColorPalettes.instance.pure,
                    ),
                  ),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: ColorPalettes.instance.primary,
                    foregroundColor: ColorPalettes.instance.pure,
                    padding: EdgeInsets.symmetric(vertical: 16.h),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12.r),
                    ),
                    elevation: 3,
                  ),
                ),
              );
            }
          }),

          // 底部空白
          SizedBox(height: 24.h),
        ],
      ),
    );
  }

  /// 构建信息行
  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 6.h),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 标签和冒号
          SizedBox(
            width: 150.w,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(
                  child: Text(
                    label,
                    style: TextStyles.instance.body2(
                      color: ColorPalettes.instance.secondText,
                      fontWeight: FontWeight.w500,
                    ),
                    overflow: TextOverflow.ellipsis,
                    softWrap: false,
                  ),
                ),
                Text(
                  ':',
                  style: TextStyles.instance.body2(
                    color: ColorPalettes.instance.secondText,
                  ),
                ),
              ],
            ),
          ),

          // 间距
          SizedBox(width: 16.w),

          // 值
          Expanded(
            child: Text(
              value,
              style: TextStyles.instance.body1(),
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ],
      ),
    );
  }

  /// 构建无更新视图
  Widget _buildNoUpdateView() {
    final context = Get.context!;
    final l10n = AppLocalizations.of(context)!;
    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(24.w),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // 状态图标
          Container(
            width: 120.w,
            height: 120.h,
            decoration: BoxDecoration(
              color: ColorPalettes.instance.success.withValues(alpha: 0.1),
              shape: BoxShape.circle,
            ),
            child: Icon(
              Icons.check_circle_outline,
              size: 60.sp,
              color: ColorPalettes.instance.success,
            ),
          ),

          SizedBox(height: 32.h),

          // 状态标题
          Text(
            l10n.firmwareUpdateLatestVersion,
            style: TextStyles.instance.h1(
              fontWeight: FontWeight.bold,
            ),
          ),

          SizedBox(height: 16.h),

          // 状态描述
          Text(
            l10n.firmwareUpdateNoNeed,
            style: TextStyles.instance.body1(
              color: ColorPalettes.instance.secondText,
            ),
            textAlign: TextAlign.center,
          ),

          SizedBox(height: 40.h),

          // 返回按钮
          SizedBox(
            width: 200.w,
            child: ElevatedButton.icon(
              onPressed: () => Get.back(),
              icon: Icon(Icons.arrow_back),
              label: Text(l10n.firmwareUpdateBack),
              style: ElevatedButton.styleFrom(
                backgroundColor: ColorPalettes.instance.primary,
                foregroundColor: ColorPalettes.instance.pure,
                padding: EdgeInsets.symmetric(vertical: 16.h),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12.r),
                ),
                elevation: 2,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
