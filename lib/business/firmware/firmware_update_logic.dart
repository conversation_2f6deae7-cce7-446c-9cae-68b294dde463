import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:topping_ble_control/device/device_factory.dart';
import 'package:topping_ble_control/model/enums/ble_connection_state.dart';
import 'package:topping_ble_control/service/ota_server.dart';
import 'package:topping_ble_control/utils/string_utils.dart';
import 'package:topping_home/common/util/i18n.dart';
import 'package:topping_home/router/routers.dart';
import 'package:topping_home/theme/color_palettes.dart';
import '../../common/util/log_util.dart';
import '../../service/firmware_service.dart';
import 'firmware_update_state.dart';
import '../../http/api/api_service.dart';

/// 固件升级控制器
class FirmwareUpdateLogic extends GetxController {
  final FirmwareUpdateState state = FirmwareUpdateState();
  final FirmwareService _firmwareService = Get.find<FirmwareService>();
  final DeviceFactory _deviceFactory = DeviceFactory();
  final ApiService _apiService = Get.find<ApiService>();

  // 通过Get.arguments传入设备实例
  @override
  void onInit() {
    super.onInit();

    Log.i("固件升级页面初始化，传入参数: ${Get.arguments}");

    // 如果有传入设备参数，则设置固件服务的设备信息
    if (Get.arguments != null) {
      final device = Get.arguments;
      _firmwareService.setDeviceInfo(
        device.deviceModel,
        device.macAddress,
      );

      // 检查固件更新
      _firmwareService.checkUpdate().then((hasUpdate) {
        if (hasUpdate) {
          Log.i("检测到新固件，可以开始升级");
          state.hasNewFirmware.value = true;
        } else {
          Log.i("未检测到新固件");
          state.hasNewFirmware.value = false;
        }
      });
    }

    // 监听OTA服务器的升级状态
    ever(OtaServer.to.upgradeSuccess, (bool success) {
      if (success) {
        if (!state.upgradeSuccess.value) {
          Log.i("OTA Server reported upgrade success.");
          state.upgradeSuccess.value = true;
          state.upgrading.value = false;
          Get.snackbar(
            l10n.firmwareUpdateSuccessTitle,
            l10n.firmwareUpdateSuccessMessage,
            backgroundColor: ColorPalettes.instance.success,
            colorText: ColorPalettes.instance.pure,
            snackPosition: SnackPosition.BOTTOM,
          );
          _reportSuccessToServer();

          // 延迟一段时间后返回首页
          Future.delayed(const Duration(milliseconds: 1500), () {
            Get.offAllNamed(AppRoutes.indexPage);
            Get.snackbar(
              l10n.deviceRebootTitle,
              l10n.deviceRebootMessage,
              backgroundColor: ColorPalettes.instance.info,
              colorText: ColorPalettes.instance.pure,
              duration: const Duration(seconds: 5),
              snackPosition: SnackPosition.BOTTOM,
            );
          });
        }
      }
    });

    // 监听设备连接状态
    _deviceFactory.connectionState.listen((connectionState) {
      if (connectionState.state == BleConnectionState.disconnected &&
          state.upgrading.value &&
          (OtaServer.to.upgradeSuccess.value ||
              OtaServer.to.updatePer.value > 95)) {
        if (!state.upgradeSuccess.value) {
          Log.i("设备在升级完成后断开连接，视为升级成功");
          state.upgradeSuccess.value = true;
          state.upgrading.value = false;

          Get.snackbar(
            l10n.firmwareUpdateSuccessTitle,
            l10n.firmwareUpdateSuccessRebootMessage,
            backgroundColor: ColorPalettes.instance.success,
            colorText: ColorPalettes.instance.pure,
            duration: const Duration(seconds: 5),
            snackPosition: SnackPosition.BOTTOM,
          );
          _reportSuccessToServer();

          // 延迟一段时间后返回首页
          Future.delayed(const Duration(milliseconds: 500), () {
            Get.offAllNamed(AppRoutes.indexPage);
            Get.snackbar(
              l10n.deviceRebootTitle,
              l10n.deviceRebootMessage,
              backgroundColor: ColorPalettes.instance.info,
              colorText: ColorPalettes.instance.pure,
              duration: const Duration(seconds: 5),
              snackPosition: SnackPosition.BOTTOM,
            );
          });
        }
      }
    });

    // 监听是否正在升级
    ever(OtaServer.to.isUpgrading, (bool value) {
      state.upgrading.value = value;
    });
  }

  @override
  void onClose() {
    _deviceFactory.dispose();
    super.onClose();
  }

  /// 开始固件升级
  Future<void> startFirmwareUpdate() async {
    // 获取当前连接的蓝牙设备
    final connectedDevice = _deviceFactory.getConnectedDevice();
    if (connectedDevice == null || connectedDevice.flutterDevice == null) {
      Log.i("无法获取已连接的设备信息");
      return;
    }
    Log.i("开始固件升级");

    // 下载固件文件
    Get.snackbar(
      l10n.firmwareUpdateDownloadingTitle,
      l10n.firmwareUpdateDownloadingMessage,
      backgroundColor: ColorPalettes.instance.info,
      colorText: ColorPalettes.instance.pure,
    );

    final filePath = await _firmwareService.downloadFirmware();

    if (filePath == null) {
      Get.snackbar(
        l10n.firmwareUpdateErrorTitle,
        l10n.firmwareUpdateDownloadFailed,
        backgroundColor: ColorPalettes.instance.error,
        colorText: ColorPalettes.instance.pure,
      );
      return;
    }

    // 开始升级
    Get.snackbar(
      l10n.firmwareUpdateUpgradingTitle,
      l10n.firmwareUpdateUpgradingMessage,
      backgroundColor: ColorPalettes.instance.info,
      colorText: ColorPalettes.instance.pure,
    );

    try {
      // 设置设备
      final success =
          await OtaServer.to.setDevice(connectedDevice.flutterDevice!);

      if (!success) {
        Get.snackbar(
          l10n.firmwareUpdateErrorTitle,
          l10n.firmwareUpdateSetDeviceFailed,
          backgroundColor: ColorPalettes.instance.error,
          colorText: ColorPalettes.instance.pure,
        );
        return;
      }

      // 默认开启RWCP模式
      OtaServer.to.mIsRWCPEnabled.value = true;

      // 协商MTU并设置负载大小
      await OtaServer.to.restPayloadSize();

      // 开始升级
      OtaServer.to.startUpdate();
      state.upgradeSuccess.value = false;

      // 启用RWCP模式
      await Future.delayed(const Duration(seconds: 1));
      OtaServer.to.writeMsg(StringUtils.hexStringToBytes("000A022E01"));

      state.upgrading.value = true;

      Log.i("已启用RWCP模式进行固件升级");
    } catch (e) {
      Get.snackbar(
        l10n.firmwareUpdateErrorTitle,
        l10n.firmwareUpdateErrorDuringUpdate(e.toString()),
        backgroundColor: ColorPalettes.instance.error,
        colorText: ColorPalettes.instance.pure,
      );
    }
  }

  /// 向服务器报告升级成功
  Future<void> _reportSuccessToServer() async {
    final device = _deviceFactory.getConnectedDevice();
    final firmwareInfo = _firmwareService.latestFirmware.value;

    if (device == null ||
        device.flutterDevice == null ||
        device.flutterDevice!.remoteId.str.isEmpty) {
      Log.w("无法报告升级成功：获取不到设备信息或MAC地址");
      return;
    }
    if (firmwareInfo == null || firmwareInfo.versionName.isEmpty) {
      Log.w("无法报告升级成功：获取不到新固件版本信息");
      return;
    }

    final macAddress = device.flutterDevice!.remoteId.str;
    final firmwareVersion = firmwareInfo.versionName;

    try {
      Log.i(
          "Reporting firmware upgrade success to server for MAC: $macAddress, Version: $firmwareVersion");
      await _apiService.reportFirmwareUpgradeSuccess(
          macAddress, firmwareVersion);
      Log.i("Successfully reported firmware upgrade to server.");
    } catch (e, s) {
      Log.e("Failed to report firmware upgrade success to server", e, s);
    }
  }

  /// 取消升级
  void cancelUpgrade() {
    if (state.upgrading.value) {
      OtaServer.to.stopUpgrade();
      state.upgrading.value = false;
      Get.snackbar(
        l10n.firmwareUpdateCancelledTitle,
        l10n.firmwareUpdateCancelledMessage,
        backgroundColor: ColorPalettes.instance.warning,
        colorText: ColorPalettes.instance.pure,
      );
    }
  }
}
