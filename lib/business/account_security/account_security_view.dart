import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:topping_home/common/util/i18n.dart';
import 'package:topping_home/theme/text_styles.dart';

import 'account_security_logic.dart';
import 'account_security_state.dart';

/// 账号安全页面
class AccountSecurityPage extends StatelessWidget {
  AccountSecurityPage({super.key});

  final AccountSecurityLogic logic = Get.find<AccountSecurityLogic>();
  final AccountSecurityState state = Get.find<AccountSecurityLogic>().state;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        appBar: AppBar(
          title: Text(
            l10n.accountSecurity,
            style: TextStyles.instance.h2(),
          ),
          centerTitle: true,
          elevation: 0,
        ),
        body: ListView.builder(
          itemCount: state.dataList.length,
          itemBuilder: (context, index) => ListTile(
            title: Text(state.dataList[index]),
            trailing: Icon(Icons.arrow_forward_ios),
            onTap: () => logic.onItemTap(index),
          ),
        ));
  }
}
