import 'package:get/get.dart';
import 'package:topping_home/router/routers.dart';

import 'account_security_state.dart';

/// 账号安全设置逻辑
class AccountSecurityLogic extends GetxController {
  final AccountSecurityState state = AccountSecurityState();

  void onItemTap(int index) {
    switch (index) {
      case 0:
        // 登录密码修改
        AppRoutes.jumpPage(AppRoutes.passwordModifyPage);
        break;
      case 1:
        // 账号与绑定设置
        AppRoutes.jumpPage(AppRoutes.accountBindPage);
        break;
      case 2:
        // 修改手机号
        AppRoutes.jumpPage(AppRoutes.modifyPhonePage);
        break;
      case 3:
        // 注销账号
        AppRoutes.jumpPage(AppRoutes.cancelAccountPage);
        break;
    }
  }
}
