import 'package:get/get.dart';

import '../../../../../models/peq_band_model.dart';
import '../../../../enums/dx5/dx5_filter_type.dart';
import '../../../../enums/dx5/dx5_frequency_type.dart';

/// PEQ波段模型
class PEQBand {
  final int id;
  RxBool enabled; // 是否启用该波段
  RxDouble gain; // 增益值
  RxDouble frequency; // 频率
  Rx<Dx5FilterType> filterType; // 枚举类型
  RxDouble q; // Q值
  RxString name; // 波段名称
  Rx<Dx5FrequencyType> frequencyType; // 频率类型：中心频率或拐点频率

  PEQBand({
    required this.id,
    bool enabled = false,
    double gain = 0.0,
    required double frequency,
    required Dx5FilterType filterType,
    double q = 2.1,
    String name = "",
    Dx5FrequencyType frequencyType = Dx5FrequencyType.centerFrequency,
  })  : enabled = enabled.obs,
        gain = gain.obs,
        frequency = frequency.obs,
        filterType = filterType.obs,
        q = q.obs,
        name = name.obs,
        frequencyType = frequencyType.obs;

  // 从PEQBandModel创建PEQBand
  factory PEQBand.fromPEQBandModel(PEQBandModel model) {
    return PEQBand(
      id: model.id,
      enabled: model.enabled,
      gain: model.gain,
      frequency: model.frequency,
      filterType: model.filterType,
      q: model.q,
      name: model.name,
    );
  }

  // 转换为PEQBandModel
  PEQBandModel toModel() {
    return PEQBandModel(
      id: id,
      enabled: enabled.value,
      gain: gain.value,
      frequency: frequency.value,
      filterType: filterType.value,
      q: q.value,
      name: name.value,
    );
  }

  // toString
  @override
  String toString() {
    return 'PEQBand{id: $id, enabled: $enabled, gain: $gain, frequency: $frequency, filterType: $filterType, q: $q, name: $name}';
  }
}
