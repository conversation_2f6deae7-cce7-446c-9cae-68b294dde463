import '../../../../../models/imported_file_model.dart';

/// 导入/导出文件模型
class ImportedFile {
  final String id; // 文件唯一标识
  final String name; // 文件名称
  final List<Map<String, double>> data; // 频率和分贝值数据
  final String type; // 文件类型：'target' 或 'sourceFR'
  final DateTime importTime; // 导入时间

  ImportedFile({
    required this.id,
    required this.name,
    required this.data,
    required this.type,
    DateTime? importTime,
  }) : importTime = importTime ?? DateTime.now();

  // 获取最小频率
  double get minFrequency {
    if (data.isEmpty) return 20.0;
    return data
        .map((e) => e['frequency'] ?? 20.0)
        .reduce((a, b) => a < b ? a : b);
  }

  // 获取最大频率
  double get maxFrequency {
    if (data.isEmpty) return 20000.0;
    return data
        .map((e) => e['frequency'] ?? 20000.0)
        .reduce((a, b) => a > b ? a : b);
  }

  // 获取最小分贝值
  double get minDB {
    if (data.isEmpty) return -20.0;
    return data.map((e) => e['db'] ?? -20.0).reduce((a, b) => a < b ? a : b);
  }

  // 获取最大分贝值
  double get maxDB {
    if (data.isEmpty) return 20.0;
    return data.map((e) => e['db'] ?? 20.0).reduce((a, b) => a > b ? a : b);
  }

  // 根据频率获取最接近的dB值
  double getDBAtFrequency(double frequency) {
    if (data.isEmpty) return 0.0;

    // 找到频率完全匹配的数据点
    final exactMatch = data.where((e) => e['frequency'] == frequency).toList();
    if (exactMatch.isNotEmpty) {
      return exactMatch.first['db'] ?? 0.0;
    }

    // 找到最接近的两个频率点进行线性插值
    List<Map<String, double>> sortedData = List.from(data);
    sortedData.sort(
        (a, b) => (a['frequency'] ?? 0.0).compareTo(b['frequency'] ?? 0.0));

    // 如果频率低于最小值或高于最大值，返回边界值
    if (frequency <= sortedData.first['frequency']!) {
      return sortedData.first['db'] ?? 0.0;
    }
    if (frequency >= sortedData.last['frequency']!) {
      return sortedData.last['db'] ?? 0.0;
    }

    // 找到频率在其间的两个数据点
    Map<String, double>? lowerPoint;
    Map<String, double>? upperPoint;

    for (int i = 0; i < sortedData.length - 1; i++) {
      if (sortedData[i]['frequency']! <= frequency &&
          frequency <= sortedData[i + 1]['frequency']!) {
        lowerPoint = sortedData[i];
        upperPoint = sortedData[i + 1];
        break;
      }
    }

    if (lowerPoint != null && upperPoint != null) {
      // 线性插值
      double lowerFreq = lowerPoint['frequency']!;
      double upperFreq = upperPoint['frequency']!;
      double lowerDB = lowerPoint['db']!;
      double upperDB = upperPoint['db']!;

      double ratio = (frequency - lowerFreq) / (upperFreq - lowerFreq);
      return lowerDB + ratio * (upperDB - lowerDB);
    }

    return 0.0; // 默认返回值
  }

  // 创建此文件的副本
  ImportedFile copyWith({
    String? id,
    String? name,
    List<Map<String, double>>? data,
    String? type,
    DateTime? importTime,
  }) {
    return ImportedFile(
      id: id ?? this.id,
      name: name ?? this.name,
      data: data ?? this.data,
      type: type ?? this.type,
      importTime: importTime ?? this.importTime,
    );
  }

  // 从ImportedFileModel创建ImportedFile
  factory ImportedFile.fromModel(ImportedFileModel? model) {
    if (model == null) {
      return ImportedFile(
        id: '',
        name: '',
        data: [],
        type: '',
      );
    }

    return ImportedFile(
      id: model.id,
      name: model.name,
      data: model.data,
      type: model.type,
      importTime: model.importTime,
    );
  }
}
