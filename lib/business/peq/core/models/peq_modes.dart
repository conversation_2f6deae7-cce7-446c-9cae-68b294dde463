/// PEQ模式定义
class PEQModes {
  // 处理模式
  static const String allChannels = 'LR';
  static const String leftChannel = 'L';
  static const String rightChannel = 'R';

  // UI显示模式
  static const String allChannelsDisplay = 'L+R';
  static const String separateChannelsDisplay = 'L/R';

  // 列表定义
  static const List<String> uiModes = [
    allChannelsDisplay,
    separateChannelsDisplay
  ];
  static const List<String> processingModes = [
    allChannels,
    leftChannel,
    rightChannel
  ];

  // 判断是否是全声道模式
  static bool isAllChannelsMode(String mode) => mode == allChannels;

  // 判断是否是左声道模式
  static bool isLeftChannelMode(String mode) => mode == leftChannel;

  // 判断是否是右声道模式
  static bool isRightChannelMode(String mode) => mode == rightChannel;

  // 根据UI模式获取处理模式
  static String getProcessingMode(String uiMode, String currentMode) {
    if (uiMode == separateChannelsDisplay) {
      // 当选择L/R时，保持当前L或R模式，或默认为L
      return (currentMode == leftChannel || currentMode == rightChannel)
          ? currentMode
          : leftChannel;
    } else if (uiMode == allChannelsDisplay) {
      return allChannels; // 当UI显示为"L+R"时，使用"LR"处理模式
    } else {
      return allChannels; // 默认情况
    }
  }
}
