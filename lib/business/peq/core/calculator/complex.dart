import 'dart:math';

/// 复数类，用于传递函数计算
class Complex {
  double real;
  double imag;

  Complex(this.real, this.imag);

  /// 从极坐标创建复数 (r*e^(j*theta))
  static Complex fromPolar(double magnitude, double angle) {
    return Complex(magnitude * cos(angle), magnitude * sin(angle));
  }

  /// 加法
  Complex operator +(Complex other) {
    return Complex(real + other.real, imag + other.imag);
  }

  /// 减法
  Complex operator -(Complex other) {
    return Complex(real - other.real, imag - other.imag);
  }

  /// 乘法
  Complex operator *(Complex other) {
    return Complex(real * other.real - imag * other.imag,
        real * other.imag + imag * other.real);
  }

  /// 除法
  Complex operator /(Complex other) {
    double denominator = other.real * other.real + other.imag * other.imag;
    return Complex((real * other.real + imag * other.imag) / denominator,
        (imag * other.real - real * other.imag) / denominator);
  }

  /// 计算复数的幅度（模）
  double magnitude() {
    return sqrt(real * real + imag * imag);
  }

  /// 返回复数的相位角（辐度）
  double phase() {
    return atan2(imag, real);
  }

  @override
  String toString() {
    return '$real ${imag >= 0 ? '+' : ''}${imag}i';
  }
}
