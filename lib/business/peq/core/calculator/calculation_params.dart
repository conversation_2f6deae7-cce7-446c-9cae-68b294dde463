import 'package:flutter/foundation.dart';

import 'filter_response_calculator.dart';

/// 计算请求参数
class CalculationParams {
  final List<Map<String, dynamic>> filters;
  final int preampGain;
  final double minFreq;
  final double maxFreq;
  final int points;

  CalculationParams({
    required this.filters,
    required this.preampGain,
    this.minFreq = 20.0,
    this.maxFreq = 20000.0,
    this.points = 100,
  });
}

/// 在隔离线程中执行的计算函数
List<Map<String, double>> _calculateInIsolate(CalculationParams params) {
  // 在隔离计算，避免阻塞UI线程
  return FilterResponseCalculator.calculateCombinedResponseWithPreamp(
      params.filters, params.preampGain,
      minFreq: params.minFreq, maxFreq: params.maxFreq, points: params.points);
}

/// 公开的异步计算函数 - 使用compute在单独的isolate中计算
Future<List<Map<String, double>>> calculateResponseAsync(
    List<Map<String, dynamic>> filters, int preampGain,
    {double minFreq = 20.0, double maxFreq = 20000.0, int points = 100}) async {
  // 如果没有滤波器，直接返回空列表
  if (filters.isEmpty) {
    return [];
  }

  final params = CalculationParams(
    filters: filters,
    preampGain: preampGain,
    minFreq: minFreq,
    maxFreq: maxFreq,
    points: points,
  );

  // 使用compute函数在隔离线程中执行计算
  return await compute(_calculateInIsolate, params);
}
