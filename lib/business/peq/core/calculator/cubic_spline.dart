/// 三次樣條插值
class CubicSpline {
  final List<double> _x;
  final List<double> _y;
  late List<double> _a;
  late List<double> _b;
  late List<double> _c;
  late List<double> _d;

  CubicSpline(List<double> x, List<double> y)
      : _x = x,
        _y = y {
    final int n = x.length - 1;
    _a = List.filled(n, 0.0);
    _b = List.filled(n, 0.0);
    _c = List.filled(n, 0.0);
    _d = List.filled(n, 0.0);
    _calculateCoefficients();
  }

  void _calculateCoefficients() {
    final int n = _x.length - 1;

    List<double> h = List.filled(n, 0.0);
    List<double> alpha = List.filled(n, 0.0);
    List<double> l = List.filled(n + 1, 0.0);
    List<double> mu = List.filled(n, 0.0);
    List<double> z = List.filled(n + 1, 0.0);

    for (int i = 0; i < n; i++) {
      h[i] = _x[i + 1] - _x[i];
    }

    for (int i = 1; i < n; i++) {
      alpha[i] =
          3.0 * ((_y[i + 1] - _y[i]) / h[i] - (_y[i] - _y[i - 1]) / h[i - 1]);
    }

    l[0] = 1.0;
    mu[0] = 0.0;
    z[0] = 0.0;

    for (int i = 1; i < n; i++) {
      l[i] = 2.0 * (_x[i + 1] - _x[i - 1]) - h[i - 1] * mu[i - 1];
      mu[i] = h[i] / l[i];
      z[i] = (alpha[i] - h[i - 1] * z[i - 1]) / l[i];
    }

    l[n] = 1.0;
    z[n] = 0.0;

    // 二階導數
    List<double> c = List.filled(n + 1, 0.0);

    for (int j = n - 1; j >= 0; j--) {
      c[j] = z[j] - mu[j] * c[j + 1];
      _b[j] = (_y[j + 1] - _y[j]) / h[j] - h[j] * (c[j + 1] + 2.0 * c[j]) / 3.0;
      _d[j] = (c[j + 1] - c[j]) / (3.0 * h[j]);
      _a[j] = _y[j];
      _c[j] = c[j];
    }
  }

  double interpolate(double x) {
    int i = _findInterval(x);
    double dx = x - _x[i];
    return _a[i] + _b[i] * dx + _c[i] * dx * dx + _d[i] * dx * dx * dx;
  }

  int _findInterval(double x) {
    if (x <= _x[0]) return 0;
    if (x >= _x[_x.length - 1]) return _x.length - 2;

    int low = 0;
    int high = _x.length - 1;

    while (low <= high) {
      int mid = (low + high) ~/ 2;
      if (_x.length <= mid + 1) return low;

      if (x < _x[mid]) {
        high = mid - 1;
      } else if (x > _x[mid + 1]) {
        low = mid + 1;
      } else {
        return mid;
      }
    }

    return low;
  }
}
