import 'dart:math' as math;
import 'dart:ui';

/// 坐标转换器
class CoordinateConverter {
  static const double minFreq = 20;
  static const double maxFreq = 20000;

  final Size size;
  final Map<String, double> dbRange;

  CoordinateConverter(this.size, this.dbRange);

  /// 频率到X坐标转换
  double frequencyToX(double freq) {
    double logFreq = math.log(freq) / math.ln10;
    double logMin = math.log(minFreq) / math.ln10;
    double logMax = math.log(maxFreq) / math.ln10;

    double normalized = (logFreq - logMin) / (logMax - logMin);
    return normalized * size.width;
  }

  /// dB值到Y坐标转换
  double dbToY(double db) {
    double minDb = dbRange['minDb']!;
    double maxDb = dbRange['maxDb']!;

    double clampedDb = math.max(minDb, math.min(db, maxDb));
    double normalized = 1.0 - (clampedDb - minDb) / (maxDb - minDb);
    return normalized * size.height;
  }

  /// X坐标到频率转换
  double xToFrequency(double x) {
    double normalized = x / size.width;
    double logMin = math.log(minFreq) / math.ln10;
    double logMax = math.log(maxFreq) / math.ln10;
    double logFreq = logMin + normalized * (logMax - logMin);
    return math.pow(10, logFreq).toDouble();
  }

  /// Y坐标到dB值转换
  double yToDb(double y) {
    double minDb = dbRange['minDb']!;
    double maxDb = dbRange['maxDb']!;
    double normalized = y / size.height;
    // 反向计算，注意 Y 轴是反的 (0 在顶部)
    double clampedDb = (1.0 - normalized) * (maxDb - minDb) + minDb;
    return clampedDb;
  }
}
