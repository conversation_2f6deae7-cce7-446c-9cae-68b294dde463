import 'dart:math' as math;
import 'package:topping_home/common/util/log_util.dart';

/// 专门用于曲线平滑的工具类
class CurveSmoother {
  /// 使用三次样条插值进行数据平滑
  static List<Map<String, double>> splineInterpolate(
      List<Map<String, double>> originalData, List<double> targetFrequencies) {
    if (originalData.isEmpty || targetFrequencies.isEmpty) return [];

    try {
      // 确保原始数据按频率排序
      List<Map<String, double>> sortedData = List.from(originalData);
      sortedData.sort((a, b) => a['frequency']!.compareTo(b['frequency']!));

      // 提取原始数据的频率和dB值
      List<double> xOriginal = sortedData.map((e) => e['frequency']!).toList();
      List<double> yOriginal = sortedData.map((e) => e['db']!).toList();

      // 检查数据有效性
      if (xOriginal.any((x) => !x.isFinite) ||
          yOriginal.any((y) => !y.isFinite)) {
        Log.e('数据包含无效值，无法进行插值');
        return originalData;
      }

      List<double> yInterpolated;
      try {
        // 使用三次样条插值
        yInterpolated =
            splineInterpolation(xOriginal, yOriginal, targetFrequencies);
      } catch (e) {
        Log.e('三次样条插值失败，尝试线性插值: $e');
        // 如果三次样条插值失败，回退到线性插值
        yInterpolated =
            linearInterpolation(xOriginal, yOriginal, targetFrequencies);
      }

      // 构建结果
      List<Map<String, double>> result = [];
      for (int i = 0; i < targetFrequencies.length; i++) {
        if (i < yInterpolated.length && yInterpolated[i].isFinite) {
          result
              .add({'frequency': targetFrequencies[i], 'db': yInterpolated[i]});
        }
      }

      return result;
    } catch (e) {
      Log.e('插值过程中发生错误: $e');
      return originalData; // 错误时返回原始数据
    }
  }

  /// 自然三次样条插值实现
  static List<double> splineInterpolation(
    List<double> x,
    List<double> y,
    List<double> targetX,
  ) {
    if (x.length != y.length || x.length < 3) {
      throw ArgumentError('输入数据点太少或x、y长度不匹配');
    }

    int n = x.length;
    List<double> h = List<double>.filled(n - 1, 0);
    for (int i = 0; i < n - 1; i++) {
      h[i] = x[i + 1] - x[i];
      if (h[i] <= 0) {
        throw ArgumentError('x值必须严格递增');
      }
    }

    // 计算三对角矩阵
    List<double> alpha = List<double>.filled(n - 1, 0);
    for (int i = 1; i < n - 1; i++) {
      alpha[i] =
          3 / h[i] * (y[i + 1] - y[i]) - 3 / h[i - 1] * (y[i] - y[i - 1]);
    }

    // 求解三对角矩阵
    List<double> l = List<double>.filled(n, 0);
    List<double> mu = List<double>.filled(n, 0);
    List<double> z = List<double>.filled(n, 0);
    l[0] = 1;
    mu[0] = 0;
    z[0] = 0;

    for (int i = 1; i < n - 1; i++) {
      l[i] = 2 * (x[i + 1] - x[i - 1]) - h[i - 1] * mu[i - 1];
      mu[i] = h[i] / l[i];
      z[i] = (alpha[i] - h[i - 1] * z[i - 1]) / l[i];
    }

    l[n - 1] = 1;
    z[n - 1] = 0;

    // 计算二阶导数
    List<double> c = List<double>.filled(n, 0);
    List<double> b = List<double>.filled(n - 1, 0);
    List<double> d = List<double>.filled(n - 1, 0);

    for (int j = n - 2; j >= 0; j--) {
      c[j] = z[j] - mu[j] * c[j + 1];
      b[j] = (y[j + 1] - y[j]) / h[j] - h[j] * (c[j + 1] + 2 * c[j]) / 3;
      d[j] = (c[j + 1] - c[j]) / (3 * h[j]);
    }

    // 对目标x进行插值
    List<double> result = List<double>.filled(targetX.length, 0);

    for (int i = 0; i < targetX.length; i++) {
      double xi = targetX[i];

      // 边界检查
      if (xi <= x[0]) {
        result[i] = y[0];
        continue;
      }
      if (xi >= x[n - 1]) {
        result[i] = y[n - 1];
        continue;
      }

      // 二分查找区间
      int low = 0;
      int high = n - 2;
      while (low <= high) {
        int mid = (low + high) ~/ 2;
        if (x[mid] <= xi && xi < x[mid + 1]) {
          low = mid;
          break;
        } else if (xi < x[mid]) {
          high = mid - 1;
        } else {
          low = mid + 1;
        }
      }

      // 计算插值
      double dx = xi - x[low];
      result[i] =
          y[low] + b[low] * dx + c[low] * dx * dx + d[low] * dx * dx * dx;
    }

    return result;
  }

  /// 线性插值实现
  static List<double> linearInterpolation(
      List<double> x, List<double> y, List<double> targetX) {
    if (x.length != y.length || x.isEmpty) {
      throw ArgumentError('输入数据点为空或x、y长度不匹配');
    }

    List<double> result = List<double>.filled(targetX.length, 0);

    for (int i = 0; i < targetX.length; i++) {
      double xi = targetX[i];

      // 处理边界情况
      if (xi <= x[0]) {
        result[i] = y[0];
        continue;
      }
      if (xi >= x[x.length - 1]) {
        result[i] = y[y.length - 1];
        continue;
      }

      // 找到xi所在的区间
      int j = 0;
      while (j < x.length - 1 && x[j + 1] < xi) {
        j++;
      }

      // 线性插值公式: y = y1 + (x - x1) * (y2 - y1) / (x2 - x1)
      double t = (xi - x[j]) / (x[j + 1] - x[j]);
      result[i] = y[j] + t * (y[j + 1] - y[j]);
    }

    return result;
  }

  /// B样条插值实现
  static List<double> bSplineInterpolation(
    List<double> x,
    List<double> y,
    List<double> targetX, {
    int degree = 3, // 默认使用三次B样条
  }) {
    if (x.length != y.length || x.length < degree + 1) {
      throw ArgumentError('输入数据点太少或x、y长度不匹配');
    }

    // 为简化实现，这里使用简单的方式构建样条，可能不如专业库的精确
    // 对于实际应用，可能需要使用更复杂的算法或专业库

    // 使用线性插值作为简单替代
    return linearInterpolation(x, y, targetX);
  }

  /// 使用Savitzky-Golay滤波器平滑数据
  static List<Map<String, double>> smoothWithSavGol(
    List<Map<String, double>> data, {
    int windowSize = 9,
    int polynomialOrder = 2,
  }) {
    // 检查参数有效性
    if (data.isEmpty) return data;
    if (windowSize.isEven) windowSize++; // 确保窗口大小为奇数
    if (windowSize < 5) windowSize = 5; // 确保最小窗口大小
    if (polynomialOrder >= windowSize) polynomialOrder = windowSize - 2;
    if (polynomialOrder < 1) polynomialOrder = 1;
    if (data.length < windowSize) {
      // 数据点太少，无法应用Savitzky-Golay算法
      return data;
    }

    // 提取dB值用于平滑
    List<double> yValues = data.map((point) => point['db']!).toList();
    List<double> smoothedYValues = _applySavitzkyGolayFilter(
      yValues,
      windowSize,
      polynomialOrder,
    );

    // 创建新的平滑数据列表
    List<Map<String, double>> smoothedData = [];
    for (int i = 0; i < data.length; i++) {
      smoothedData.add({
        'frequency': data[i]['frequency']!,
        'db': smoothedYValues[i],
      });
    }

    return smoothedData;
  }

  /// 使用移动平均平滑算法处理曲线数据
  static List<Map<String, double>> smoothWithMovingAverage(
    List<Map<String, double>> data, {
    int windowSize = 5,
  }) {
    if (data.isEmpty) return data;
    if (windowSize < 3) windowSize = 3;
    if (data.length < windowSize) return data;

    List<double> yValues = data.map((point) => point['db']!).toList();
    List<double> smoothedYValues = List.filled(yValues.length, 0.0);

    // 应用移动平均
    int halfWindow = windowSize ~/ 2;
    for (int i = 0; i < yValues.length; i++) {
      double sum = 0.0;
      int count = 0;
      for (int j = i - halfWindow; j <= i + halfWindow; j++) {
        if (j >= 0 && j < yValues.length) {
          sum += yValues[j];
          count++;
        }
      }
      smoothedYValues[i] = sum / count;
    }

    // 创建新的平滑数据列表
    List<Map<String, double>> smoothedData = [];
    for (int i = 0; i < data.length; i++) {
      smoothedData.add({
        'frequency': data[i]['frequency']!,
        'db': smoothedYValues[i],
      });
    }

    return smoothedData;
  }

  /// 使用高斯平滑算法处理曲线数据
  static List<Map<String, double>> smoothWithGaussian(
    List<Map<String, double>> data, {
    double sigma = 1.0,
    int windowSize = 5,
  }) {
    if (data.isEmpty) return data;
    if (windowSize.isEven) windowSize++; // 确保核大小为奇数
    if (windowSize < 3) windowSize = 3;
    if (data.length < windowSize) return data;

    // 生成高斯核
    List<double> kernel = _generateGaussianKernel(windowSize, sigma);

    // 提取dB值用于平滑
    List<double> yValues = data.map((point) => point['db']!).toList();
    List<double> smoothedYValues = List.filled(yValues.length, 0.0);

    // 应用高斯滤波
    int halfKernel = windowSize ~/ 2;
    for (int i = 0; i < yValues.length; i++) {
      double weightedSum = 0.0;
      double weightSum = 0.0;

      for (int j = 0; j < windowSize; j++) {
        int dataIndex = i + (j - halfKernel);
        if (dataIndex >= 0 && dataIndex < yValues.length) {
          weightedSum += yValues[dataIndex] * kernel[j];
          weightSum += kernel[j];
        }
      }

      smoothedYValues[i] = weightedSum / weightSum;
    }

    // 创建新的平滑数据列表
    List<Map<String, double>> smoothedData = [];
    for (int i = 0; i < data.length; i++) {
      smoothedData.add({
        'frequency': data[i]['frequency']!,
        'db': smoothedYValues[i],
      });
    }

    return smoothedData;
  }

  /// 生成高斯核
  static List<double> _generateGaussianKernel(int size, double sigma) {
    List<double> kernel = List<double>.filled(size, 0.0);
    int center = size ~/ 2;
    double sum = 0.0;

    for (int i = 0; i < size; i++) {
      int x = i - center;
      // 高斯公式
      kernel[i] = math.exp(-(x * x) / (2 * sigma * sigma));
      sum += kernel[i];
    }

    // 归一化核
    for (int i = 0; i < size; i++) {
      kernel[i] /= sum;
    }

    return kernel;
  }

  /// 计算Savitzky-Golay滤波器系数
  static List<double> _calculateSavitzkyGolayCoefficients(
    int windowSize,
    int polynomialOrder,
    int derivOrder,
  ) {
    int halfWindow = windowSize ~/ 2;
    List<List<double>> a = List.generate(
      windowSize,
      (_) => List<double>.filled(polynomialOrder + 1, 0.0),
    );

    // 构建范德蒙德矩阵
    for (int i = 0; i < windowSize; i++) {
      double x = (i - halfWindow).toDouble();
      a[i][0] = 1.0;
      for (int j = 1; j <= polynomialOrder; j++) {
        a[i][j] = a[i][j - 1] * x;
      }
    }

    // 计算QR分解
    List<List<double>> q = List.generate(
      windowSize,
      (_) => List<double>.filled(windowSize, 0.0),
    );
    List<List<double>> r = List.generate(
      windowSize,
      (_) => List<double>.filled(polynomialOrder + 1, 0.0),
    );

    for (int k = 0; k < polynomialOrder + 1; k++) {
      // 复制当前列
      List<double> v = List<double>.filled(windowSize, 0.0);
      for (int i = 0; i < windowSize; i++) {
        v[i] = a[i][k];
      }

      // 实现修正的格拉姆-施密特正交化
      for (int j = 0; j < k; j++) {
        double dot = 0.0;
        for (int i = 0; i < windowSize; i++) {
          dot += v[i] * q[i][j];
        }
        r[j][k] = dot;
        for (int i = 0; i < windowSize; i++) {
          v[i] -= dot * q[i][j];
        }
      }

      // 标准化
      double norm = 0.0;
      for (int i = 0; i < windowSize; i++) {
        norm += v[i] * v[i];
      }
      norm = math.sqrt(norm);

      if (norm > 1e-10) {
        r[k][k] = norm;
        for (int i = 0; i < windowSize; i++) {
          q[i][k] = v[i] / norm;
        }
      } else {
        r[k][k] = 0.0;
      }
    }

    // 回代求解
    List<double> b = List<double>.filled(polynomialOrder + 1, 0.0);
    b[derivOrder] = 1.0; // 对应导数阶数

    List<double> c = List<double>.filled(polynomialOrder + 1, 0.0);
    for (int i = polynomialOrder; i >= 0; i--) {
      c[i] = b[i];
      for (int j = i + 1; j <= polynomialOrder; j++) {
        if (r[i][i].abs() > 1e-10) {
          c[i] -= r[i][j] * c[j] / r[i][i];
        }
      }
    }

    // 计算滤波器系数
    List<double> coeffs = List<double>.filled(windowSize, 0.0);
    for (int i = 0; i < windowSize; i++) {
      for (int j = 0; j <= polynomialOrder; j++) {
        coeffs[i] += c[j] * q[i][j];
      }
    }

    return coeffs;
  }

  /// 应用Savitzky-Golay滤波器进行数据平滑
  static List<double> _applySavitzkyGolayFilter(
    List<double> data,
    int windowSize,
    int polynomialOrder,
  ) {
    int halfWindow = windowSize ~/ 2;
    int n = data.length;
    List<double> result = List<double>.filled(n, 0.0);

    // 计算零阶导数的滤波器系数
    List<double> coeffs = _calculateSavitzkyGolayCoefficients(
      windowSize,
      polynomialOrder,
      0, // 零阶导数，用于平滑
    );

    // 应用滤波器到数据中间部分
    for (int i = halfWindow; i < n - halfWindow; i++) {
      double sum = 0.0;
      for (int j = 0; j < windowSize; j++) {
        sum += coeffs[j] * data[i - halfWindow + j];
      }
      result[i] = sum;
    }

    // 处理边缘
    // 左边缘
    for (int i = 0; i < halfWindow; i++) {
      // 使用数据左侧的窗口
      List<double> edgeCoeffs = _calculateSavitzkyGolayCoefficients(
        windowSize,
        polynomialOrder,
        0,
      );

      double sum = 0.0;
      for (int j = 0; j < windowSize; j++) {
        int index = math.max(0, j - i);
        sum += edgeCoeffs[j] * data[index];
      }
      result[i] = sum;
    }

    // 右边缘
    for (int i = n - halfWindow; i < n; i++) {
      // 使用数据右侧的窗口
      List<double> edgeCoeffs = _calculateSavitzkyGolayCoefficients(
        windowSize,
        polynomialOrder,
        0,
      );

      double sum = 0.0;
      for (int j = 0; j < windowSize; j++) {
        int index =
            math.min(n - 1, n - windowSize + j + (i - (n - halfWindow)));
        sum += edgeCoeffs[j] * data[index];
      }
      result[i] = sum;
    }

    return result;
  }

  /// 使用B样条插值平滑曲线（更高级的平滑效果）
  static List<Map<String, double>> bSplineSmooth(
      List<Map<String, double>> rawData, List<double> targetFrequencies,
      {int degree = 3}) {
    if (rawData.length < degree + 1) return rawData; // 数据点不够，不进行B样条插值

    try {
      // 提取原始数据点
      List<double> xOrig = rawData.map((e) => e['frequency']!).toList();
      List<double> yOrig = rawData.map((e) => e['db']!).toList();

      // 检查数据有效性
      if (xOrig.any((x) => !x.isFinite) || yOrig.any((y) => !y.isFinite)) {
        Log.e('数据包含无效值，无法进行B样条插值');
        return rawData;
      }

      // 使用原始数据列表
      List<double> ySmoothed;
      try {
        // 尝试B样条插值
        ySmoothed = bSplineInterpolation(xOrig, yOrig, targetFrequencies,
            degree: degree);
      } catch (e) {
        Log.e('B样条插值失败，回退到三次样条插值: $e');
        try {
          // 如果B样条失败，尝试三次样条
          ySmoothed = splineInterpolation(xOrig, yOrig, targetFrequencies);
        } catch (e2) {
          Log.e('三次样条插值也失败，回退到线性插值: $e2');
          // 如果三次样条也失败，回退到线性插值
          ySmoothed = linearInterpolation(xOrig, yOrig, targetFrequencies);
        }
      }

      // 构建结果
      List<Map<String, double>> result = [];
      for (int i = 0; i < targetFrequencies.length; i++) {
        if (i < ySmoothed.length && ySmoothed[i].isFinite) {
          result.add({'frequency': targetFrequencies[i], 'db': ySmoothed[i]});
        }
      }

      return result;
    } catch (e) {
      Log.e('B样条平滑过程中发生错误: $e');
      return rawData; // 错误时返回原始数据
    }
  }
}
