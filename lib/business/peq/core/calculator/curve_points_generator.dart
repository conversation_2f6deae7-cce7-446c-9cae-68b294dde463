// core/calculator/curve_points_generator.dart
import 'dart:math' as math;

import '../../../../common/util/log_util.dart';
import '../models/peq_band.dart';
import 'cubic_spline.dart';
// FilterResponseCalculator 可能用到 Complex
import 'curve_smoother.dart'; // 添加导入曲线平滑器
import 'filter_response_calculator.dart';

class CurvePointsGenerator {
  static const double minFreq = 20.0; // 保持常量定义
  static const double maxFreq = 20000.0;

  // --- 新方法：生成对数频率点 (从 CoordinateConverter 移过来或保持独立) ---
  static List<double> generateLogFrequencies(int count,
      {double fMin = minFreq, double fMax = maxFreq}) {
    List<double> frequencies = [];
    double logMin = math.log(fMin) / math.ln10;
    double logMax = math.log(fMax) / math.ln10;
    if (count <= 1) {
      // 处理边界情况
      if (count == 1) {
        frequencies.add(math.pow(10, (logMin + logMax) / 2.0).toDouble());
      }
      return frequencies;
    }
    double step = (logMax - logMin) / (count - 1);

    for (int i = 0; i < count; i++) {
      double logFreq = logMin + i * step;
      frequencies.add(math.pow(10, logFreq).toDouble());
    }
    return frequencies;
  }

  /// 对原始数据进行插值，以匹配目标频率点
  /// 使用更高级的三次样条插值方法，确保曲线更加平滑
  static List<Map<String, double>> interpolateData(
      List<Map<String, double>> originalData, List<double> targetFrequencies) {
    if (originalData.isEmpty || targetFrequencies.isEmpty) {
      return [];
    }

    try {
      // 使用曲线平滑器的splineInterpolate方法进行平滑插值
      return CurveSmoother.splineInterpolate(originalData, targetFrequencies);
    } catch (e) {
      Log.e('平滑插值失败: $e');

      // 如果高级插值失败，回退到简单的三次样条插值
      List<double> x = originalData.map((p) => p['frequency']!).toList();
      List<double> y = originalData.map((p) => p['db']!).toList();

      try {
        var spline = CubicSpline(x, y);
        return targetFrequencies
            .map((freq) => {'frequency': freq, 'db': spline.interpolate(freq)})
            .toList();
      } catch (e2) {
        Log.e('三次样条插值也失败: $e2');

        // 如果都失败了，使用简单的线性插值作为最后的回退方案
        return _linearInterpolate(originalData, targetFrequencies);
      }
    }
  }

  /// 简单的线性插值作为回退方案
  static List<Map<String, double>> _linearInterpolate(
      List<Map<String, double>> originalData, List<double> targetFrequencies) {
    // 确保原始数据按频率排序
    originalData.sort((a, b) => a['frequency']!.compareTo(b['frequency']!));

    List<Map<String, double>> result = [];

    for (double freq in targetFrequencies) {
      // 如果频率超出范围，使用边界值
      if (freq <= originalData.first['frequency']!) {
        result.add({'frequency': freq, 'db': originalData.first['db']!});
        continue;
      }
      if (freq >= originalData.last['frequency']!) {
        result.add({'frequency': freq, 'db': originalData.last['db']!});
        continue;
      }

      // 找到频率点所在区间
      int i = 0;
      while (i < originalData.length - 1 &&
          originalData[i + 1]['frequency']! < freq) {
        i++;
      }

      // 线性插值
      double x0 = originalData[i]['frequency']!;
      double y0 = originalData[i]['db']!;
      double x1 = originalData[i + 1]['frequency']!;
      double y1 = originalData[i + 1]['db']!;

      double t = (freq - x0) / (x1 - x0);
      double db = y0 + t * (y1 - y0);

      result.add({'frequency': freq, 'db': db});
    }

    return result;
  }

  /// 为单个波段生成原始曲线数据 (返回 Map 列表)
  static List<Map<String, double>> generateBandCurveRawData(PEQBand band,
      {int points = 100}) {
    final List<Map<String, double>> rawData = [];
    if (!band.enabled.value) return rawData;

    final tf = FilterResponseCalculator.getTransferFunction(
        band.filterType.value,
        band.frequency.value,
        band.gain.value,
        band.q.value);

    final frequencies = generateLogFrequencies(points);

    Log.i(
        '单个滤波器原始数据: 类型=${band.filterType.value}, Freq=${band.frequency.value}, Gain=${band.gain.value}, Q=${band.q.value}');

    for (final double freq in frequencies) {
      double responseDb = FilterResponseCalculator.calculateResponse(
          tf['num']!, tf['den']!, freq);
      rawData.add({'frequency': freq, 'db': responseDb});

      // 调试打印关键点
      if ((freq > 990 && freq < 1010) || (freq > 1990 && freq < 2010)) {
        Log.i(
            '  Freq: ${freq.toStringAsFixed(1)} Hz, Response: ${responseDb.toStringAsFixed(2)} dB');
      }
    }
    return rawData;
  }

  /// 生成所有波段的组合原始曲线数据 (返回 Map 列表)
  static List<Map<String, double>> generateCombinedCurveRawData(
      List<PEQBand> bands,
      {int points = 500}) {
    List<Map<String, double>> rawData = [];
    if (bands.isEmpty) return rawData;

    PEQBand? preamp = bands.first; // 第一个通常是前级
    List<Map<String, dynamic>> filters = [];
    for (int i = 1; i < bands.length; i++) {
      // 从第二个开始是实际滤波器
      final band = bands[i];
      if (band.enabled.value) {
        filters.add({
          'type': band.filterType.value,
          'f0': band.frequency.value,
          'gaindB': band.gain.value,
          'Q': band.q.value,
          'enabled': true, // 确保只添加启用的
          // 获取传递函数可以优化性能，避免重复计算
          'tf': FilterResponseCalculator.getTransferFunction(
              band.filterType.value,
              band.frequency.value,
              band.gain.value,
              band.q.value)
        });
      }
    }

    // 如果没有启用的滤波器，并且前级也未启用，返回空或只有前级影响的平线
    if (filters.isEmpty && (!preamp.enabled.value)) {
      // 可以选择返回空列表，或者返回一个代表0dB（或前级值）的平线数据
      // 这里返回空列表，让绘制逻辑处理
      return [];
    }

    final frequencies = generateLogFrequencies(points);
    final double preampGainDb =
        (preamp.enabled.value) ? preamp.gain.value : 0.0;

    Log.i('合并滤波器原始数据: 滤波器数量=${filters.length}, 前级增益=$preampGainDb dB');

    for (final double freq in frequencies) {
      double totalResponseDb = preampGainDb; // 从前级增益开始

      for (var filter in filters) {
        // final tf = FilterResponseCalculator.getTransferFunction(filter['type'], filter['f0'], filter['gaindB'], filter['Q']); // 可以使用预计算的 tf
        final tf = filter['tf'] as Map<String, List<double>>;
        double db = FilterResponseCalculator.calculateResponse(
            tf['num']!, tf['den']!, freq);
        totalResponseDb += db;
      }
      rawData.add({'frequency': freq, 'db': totalResponseDb});

      // 调试打印关键点
      if ((freq > 990 && freq < 1010) || (freq > 1990 && freq < 2010)) {
        Log.i(
            '  Freq: ${freq.toStringAsFixed(1)} Hz, Total Response: ${totalResponseDb.toStringAsFixed(2)} dB');
      }
    }
    return rawData;
  }

  /// 生成 C++ 风格的组合原始曲线数据 (返回 Map 列表)
  /// 注意：移除了 gainValue 参数，因为前级增益已在 bands[0] 中处理
  /// 需要传入源数据的频率点列表 frequencies
  static List<Map<String, double>> generateCombinedCurveRawDataC(
    List<PEQBand> bands,
    List<double> sourceDbValues,
    List<double> frequencies,
    List<Map<String, double>>? targetData, // 新增目标曲线参数
  ) {
    List<Map<String, double>> rawData = [];
    if (bands.isEmpty ||
        sourceDbValues.isEmpty ||
        frequencies.isEmpty ||
        sourceDbValues.length != frequencies.length) {
      return rawData;
    }

    // 计算所有波段的组合滤波器线性响应
    final List<double> combinedFilterLinearResponse =
        _calculateCombinedFilterLinearResponse(bands, frequencies);

    // 如果提供了目标曲线数据，进行插值以匹配频率点
    List<double> targetDbValues = [];
    if (targetData != null && targetData.isNotEmpty) {
      final interpolatedTarget = interpolateData(targetData, frequencies);
      targetDbValues = interpolatedTarget.map((p) => p['db']!).toList();
    }

    for (int i = 0; i < frequencies.length; i++) {
      if (i >= sourceDbValues.length ||
          i >= combinedFilterLinearResponse.length) {
        continue;
      }

      double finalResponseDb;
      double filterResponseDb =
          20 * math.log(combinedFilterLinearResponse[i].abs()) / math.ln10;

      if (targetDbValues.isNotEmpty && i < targetDbValues.length) {
        // 补偿模式：计算 S(f) + H(f) - T(f)
        finalResponseDb =
            sourceDbValues[i] + filterResponseDb - targetDbValues[i];
      } else {
        // 无目标曲线时，保持原始行为（仅加滤波器响应）
        finalResponseDb = sourceDbValues[i] + filterResponseDb;
      }

      rawData.add({'frequency': frequencies[i], 'db': finalResponseDb});

      // 调试日志 - 记录特定频率点，如1000 Hz附近和2000 Hz附近
      if ((frequencies[i] > 990 && frequencies[i] < 1010) ||
          (frequencies[i] > 1990 && frequencies[i] < 2010)) {
        Log.i(
            '  Freq: ${frequencies[i].toStringAsFixed(1)} Hz, Filtered Response: ${finalResponseDb.toStringAsFixed(2)} dB '
            '(Source: ${sourceDbValues[i].toStringAsFixed(2)}, Filter: ${filterResponseDb.toStringAsFixed(2)}${targetDbValues.isNotEmpty ? ", Target: ${targetDbValues[i].toStringAsFixed(2)}" : ""})');
      }
    }
    return rawData;
  }

  /// 计算组合滤波器线性响应 (辅助方法)
  static List<double> _calculateCombinedFilterLinearResponse(
      List<PEQBand> bands, List<double> frequencies) {
    final List<double> response =
        List.filled(frequencies.length, 1.0); // 线性域初始值为1

    for (int bandIndex = 0; bandIndex < bands.length; bandIndex++) {
      // 包括前级 band 0
      final band = bands[bandIndex];
      if (band.enabled.value) {
        final tf = FilterResponseCalculator.getTransferFunction(
            band.filterType.value,
            band.frequency.value,
            band.gain.value, // 直接使用 gain 值
            band.q.value);

        for (int i = 0; i < frequencies.length; i++) {
          // 计算 dB 响应
          double dbResponse = FilterResponseCalculator.calculateResponse(
              tf['num']!, tf['den']!, frequencies[i]);
          // 转换为线性幅度
          double linearResponse = math.pow(10, dbResponse / 20.0).toDouble();
          // 累乘线性幅度
          response[i] *= linearResponse;
        }
      }
    }
    return response;
  }
}
