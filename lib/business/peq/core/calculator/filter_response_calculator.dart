import 'dart:math';

import '../../../../enums/dx5/dx5_filter_type.dart';
import 'complex.dart';

/// 滤波器响应计算器
class FilterResponseCalculator {
  static const double pi = 3.1415926535897932;

  // 添加静态缓存
  static final Map<String, List<Map<String, double>>> _responseCache = {};

  // 生成缓存键
  static String _generateCacheKey(
      List<Map<String, dynamic>> filters, int preampGain) {
    return '${filters.map((f) => "${f['type']}_${f['f0']}_${f['gaindB']}_${f['Q']}_${f['enabled']}").join("|")}_$preampGain';
  }

  /// 计算峰值滤波器的传递函数系数
  static Map<String, List<double>> peakingTF(
      double f0, double gaindB, double Q) {
    double w0 = 2 * pi * f0;
    double gain = pow(10, gaindB / 20.0) as double; // 转换为倍数

    List<double> num = [1, sqrt(gain) * (1 / Q) * w0, pow(w0, 2) as double];
    List<double> den = [1, ((1 / Q) / sqrt(gain)) * w0, pow(w0, 2) as double];

    return {
      'num': num,
      'den': den,
    };
  }

  /// 计算低通滤波器的传递函数系数
  static Map<String, List<double>> lowPassTF(double f0, double Q) {
    double w0 = 2 * pi * f0;

    List<double> num = [0, 0, pow(w0, 2) as double];
    List<double> den = [1, (1 / Q) * w0, pow(w0, 2) as double];

    return {
      'num': num,
      'den': den,
    };
  }

  /// 计算高通滤波器的传递函数系数
  static Map<String, List<double>> highPassTF(double f0, double Q) {
    double w0 = 2 * pi * f0;

    List<double> num = [1, 0, 0];
    List<double> den = [1, w0 / Q, pow(w0, 2) as double];

    return {
      'num': num,
      'den': den,
    };
  }

  /// 计算低架滤波器的传递函数系数
  static Map<String, List<double>> lowShelfTF(
      double f0, double gaindB, double Q) {
    double w0 = 2 * pi * f0;
    double A = sqrt(pow(10, gaindB / 20) as double); // 转换为倍数

    List<double> num = [1, (w0 * sqrt(A)) / Q, A * pow(w0, 2)];
    List<double> den = [1, (w0) / (Q * sqrt(A)), (1 / A) * pow(w0, 2)];

    return {
      'num': num,
      'den': den,
    };
  }

  /// 计算高架滤波器的传递函数系数
  static Map<String, List<double>> highShelfTF(
      double f0, double gaindB, double Q) {
    double w0 = 2 * pi * f0;
    double A = sqrt(pow(10, gaindB / 20) as double); // 转换为倍数

    List<double> num = [A, (w0 * sqrt(A)) / Q, pow(w0, 2) as double];
    List<double> den = [(1 / A), (w0) / (Q * sqrt(A)), pow(w0, 2) as double];

    return {
      'num': num,
      'den': den,
    };
  }

  /// 获取指定滤波器类型的传递函数系数
  static Map<String, List<double>> getTransferFunction(
      Dx5FilterType type, double f0, double gaindB, double Q) {
    switch (type) {
      case Dx5FilterType.peakingFilter:
        return peakingTF(f0, gaindB, Q);
      case Dx5FilterType.lowPassFilter:
        return lowPassTF(f0, Q);
      case Dx5FilterType.highPassFilter:
        return highPassTF(f0, Q);
      case Dx5FilterType.lowShelfFilter:
        return lowShelfTF(f0, gaindB, Q);
      case Dx5FilterType.highShelfFilter:
        return highShelfTF(f0, gaindB, Q);
    }
  }

  /// 计算单个频率点的响应
  static double calculateResponse(
      List<double> num, List<double> den, double freq) {
    // 将频率转换为复数形式 s = j*ω
    double omega = 2 * pi * freq;

    // 计算分子的复数响应
    Complex numResponse = Complex(0, 0);
    for (int i = 0; i < num.length; i++) {
      // 使用toDouble()而不是as double进行类型转换
      double power = (num.length - 1 - i).toDouble();
      // j^power * num[i]
      Complex term;
      if (power == 0) {
        term = Complex(num[i], 0);
      } else if (power == 1) {
        term = Complex(0, num[i]);
      } else if (power == 2) {
        term = Complex(-num[i], 0);
      } else if (power == 3) {
        term = Complex(0, -num[i]);
      } else {
        double angle = (power % 4) * pi / 2;
        term = Complex.fromPolar(num[i], angle);
      }

      // 将s替换为jω
      if (power > 0) {
        term = Complex(
            term.real * pow(omega, power), term.imag * pow(omega, power));
      }

      numResponse = numResponse + term;
    }

    // 计算分母的复数响应
    Complex denResponse = Complex(0, 0);
    for (int i = 0; i < den.length; i++) {
      // 使用toDouble()而不是as double进行类型转换
      double power = (den.length - 1 - i).toDouble();
      // j^power * den[i]
      Complex term;
      if (power == 0) {
        term = Complex(den[i], 0);
      } else if (power == 1) {
        term = Complex(0, den[i]);
      } else if (power == 2) {
        term = Complex(-den[i], 0);
      } else if (power == 3) {
        term = Complex(0, -den[i]);
      } else {
        double angle = (power % 4) * pi / 2;
        term = Complex.fromPolar(den[i], angle);
      }

      // 将s替换为jω
      if (power > 0) {
        term = Complex(
            term.real * pow(omega, power), term.imag * pow(omega, power));
      }

      denResponse = denResponse + term;
    }

    // 计算传递函数的复数响应
    Complex response = numResponse / denResponse;

    // 计算幅度并转换为dB
    double magnitude = response.magnitude();
    return 20 * log(magnitude) / log(10);
  }

  /// 计算指定频率范围内的滤波器响应
  static List<Map<String, double>> calculateFilterResponse(
      Dx5FilterType type, double f0, double gaindB, double Q,
      {double minFreq = 20.0, double maxFreq = 20000.0, int points = 100}) {
    // 获取传递函数系数
    final tf = getTransferFunction(type, f0, gaindB, Q);
    final num = tf['num']!;
    final den = tf['den']!;

    // 创建频率点列表（对数分布）
    List<double> frequencies = [];
    double logMin = log(minFreq) / log(10);
    double logMax = log(maxFreq) / log(10);
    double step = (logMax - logMin) / (points - 1);

    for (int i = 0; i < points; i++) {
      double logFreq = logMin + i * step;
      double freq = pow(10, logFreq) as double;
      frequencies.add(freq);
    }

    // 计算每个频率点的响应
    List<Map<String, double>> response = [];
    for (double freq in frequencies) {
      double db = calculateResponse(num, den, freq);

      response.add({
        'frequency': freq,
        'db': db,
      });
    }

    return response;
  }

  /// 计算组合响应（带前置增益）
  static List<Map<String, double>> calculateCombinedResponseWithPreamp(
      List<Map<String, dynamic>> filters, int preampGain,
      {double minFreq = 20.0, double maxFreq = 20000.0, int points = 100}) {
    // 创建频率点列表（对数分布）
    List<double> frequencies = [];
    double logMin = log(minFreq) / log(10);
    double logMax = log(maxFreq) / log(10);
    double step = (logMax - logMin) / (points - 1);

    for (int i = 0; i < points; i++) {
      double logFreq = logMin + i * step;
      double freq = pow(10, logFreq) as double;
      frequencies.add(freq);
    }

    // 计算每个频率点的组合响应
    List<Map<String, double>> response = [];
    for (int i = 0; i < frequencies.length; i++) {
      double freq = frequencies[i];
      double totalDb = preampGain.toDouble(); // 初始值为前置增益

      // 计算所有启用的滤波器的响应之和
      for (final filter in filters) {
        if (filter['enabled'] == true) {
          Dx5FilterType type = filter['type'];
          double f0 = filter['f0'];
          double gaindB = filter['gaindB'];
          double q = filter['Q'];

          // 获取传递函数系数
          final tf = getTransferFunction(type, f0, gaindB, q);
          final num = tf['num']!;
          final den = tf['den']!;

          // 计算该滤波器在这个频率点的响应
          double db = calculateResponse(num, den, freq);
          // 累加dB值
          totalDb += db;
        }
      }

      response.add({
        'frequency': freq,
        'db': totalDb,
      });
    }

    return response;
  }

  /// 带缓存的组合响应计算（带前置增益）
  static List<Map<String, double>> calculateCombinedResponseWithPreampCached(
      List<Map<String, dynamic>> filters, int preampGain,
      {double minFreq = 20.0, double maxFreq = 20000.0, int points = 100}) {
    final cacheKey = _generateCacheKey(filters, preampGain);

    // 检查缓存
    if (_responseCache.containsKey(cacheKey)) {
      return _responseCache[cacheKey]!;
    }

    // 计算结果
    final result = calculateCombinedResponseWithPreamp(filters, preampGain,
        minFreq: minFreq, maxFreq: maxFreq, points: points);

    // 存入缓存
    _responseCache[cacheKey] = result;

    return result;
  }

  /// 清除缓存
  static void clearCache() {
    _responseCache.clear();
  }

  /// 限制缓存大小
  static void limitCacheSize(int maxSize) {
    if (_responseCache.length > maxSize) {
      final keysToRemove =
          _responseCache.keys.take(_responseCache.length - maxSize).toList();
      for (final key in keysToRemove) {
        _responseCache.remove(key);
      }
    }
  }
}
