import 'package:get/get.dart';
import 'dart:math' as math;

import '../../../../../common/util/i18n.dart';
import '../../../../../models/peq_band_model.dart';
import '../../../../enums/dx5/dx5_filter_type.dart';
import '../../../../enums/dx5/dx5_frequency_type.dart';
import '../models/peq_band.dart';
import '../models/peq_modes.dart';

/// PEQ波段服务
class PEQBandService {
  // 前级增益
  final RxInt preampGain = 0.obs;

  // 左右声道的EQ波段
  final RxList<PEQBand> leftBands = <PEQBand>[].obs;
  final RxList<PEQBand> rightBands = <PEQBand>[].obs;

  // 全声道的EQ波段
  final RxList<PEQBand> allBands = <PEQBand>[].obs;

  // 波段数量上限
  final int maxBands = 11;

  // 最后添加的波段ID（用于自动选择）
  int _lastAddedBandId = -1;

  /// 设置前级增益
  void setPreampGain(int value) {
    // 限制在-40到+40范围
    preampGain.value = math.max(-40, math.min(40, value));
  }

  /// 获取当前波段基于选择的模式
  List<PEQBand> getCurrentBands(String mode) {
    if (PEQModes.isAllChannelsMode(mode)) {
      return allBands;
    }
    return PEQModes.isLeftChannelMode(mode) ? leftBands : rightBands;
  }

  /// 确保所有波段都已初始化
  void ensureBandsInitialized() {
    if (leftBands.isEmpty) {
      initPEQData();
    }
  }

  /// 初始化PEQ数据
  void initPEQData() {
    // 初始化前级波段（主波段）
    leftBands.add(PEQBand(
        id: 0,
        enabled: true,
        frequency: 1000.0,
        filterType: Dx5FilterType.peakingFilter,
        frequencyType: Dx5FrequencyType.centerFrequency,
        gain: 0.0,
        q: 1.41,
        name: l10n.preAmplification));

    rightBands.add(PEQBand(
        id: 0,
        enabled: true,
        frequency: 1000.0,
        filterType: Dx5FilterType.peakingFilter,
        frequencyType: Dx5FrequencyType.centerFrequency,
        gain: 0.0,
        q: 1.41,
        name: l10n.preAmplification));

    // 初始化全声道波段
    allBands.add(PEQBand(
        id: 0,
        enabled: true,
        frequency: 1000.0,
        filterType: Dx5FilterType.peakingFilter,
        frequencyType: Dx5FrequencyType.centerFrequency,
        gain: 0.0,
        q: 1.41,
        name: l10n.preAmplification));
  }

  /// 加载波段数据，从模型中初始化
  void loadBandsFromModels(
    List<PEQBandModel> allChannelBands,
    List<PEQBandModel> leftChannelBands,
    List<PEQBandModel> rightChannelBands,
  ) {
    // 清空现有波段
    allBands.clear();
    leftBands.clear();
    rightBands.clear();

    // 确保至少有前级波段
    if (allChannelBands.isEmpty) {
      allBands.add(PEQBand(
          id: 0,
          enabled: true,
          frequency: 1000.0,
          filterType: Dx5FilterType.peakingFilter,
          frequencyType: Dx5FrequencyType.centerFrequency,
          gain: 0.0,
          q: 1.41,
          name: l10n.preAmplification));
    } else {
      for (final model in allChannelBands) {
        allBands.add(PEQBand.fromPEQBandModel(model));
      }
    }

    if (leftChannelBands.isEmpty) {
      leftBands.add(PEQBand(
          id: 0,
          enabled: true,
          frequency: 1000.0,
          filterType: Dx5FilterType.peakingFilter,
          frequencyType: Dx5FrequencyType.centerFrequency,
          gain: 0.0,
          q: 1.41,
          name: l10n.preAmplification));
    } else {
      for (final model in leftChannelBands) {
        leftBands.add(PEQBand.fromPEQBandModel(model));
      }
    }

    if (rightChannelBands.isEmpty) {
      rightBands.add(PEQBand(
          id: 0,
          enabled: true,
          frequency: 1000.0,
          filterType: Dx5FilterType.peakingFilter,
          frequencyType: Dx5FrequencyType.centerFrequency,
          gain: 0.0,
          q: 1.41,
          name: l10n.preAmplification));
    } else {
      for (final model in rightChannelBands) {
        rightBands.add(PEQBand.fromPEQBandModel(model));
      }
    }
  }

  /// 获取下一个可用的ID，确保在所有模式中唯一
  int getNextAvailableId() {
    final allIds = <int>{};
    allIds.addAll(allBands.map((b) => b.id));
    allIds.addAll(leftBands.map((b) => b.id));
    allIds.addAll(rightBands.map((b) => b.id));

    int newId = 0;
    while (allIds.contains(newId)) {
      newId++;
    }
    return newId;
  }

  /// 切换波段启用状态（仅操作当前模式）
  void toggleBandEnabled(int bandId, String mode) {
    final currentBands = PEQModes.isAllChannelsMode(mode)
        ? allBands
        : (PEQModes.isLeftChannelMode(mode) ? leftBands : rightBands);

    final band = currentBands.firstWhere((b) => b.id == bandId);
    band.enabled.value = !band.enabled.value;
  }

  /// 更新波段的滤波器类型（仅操作当前模式）
  void updateBandFilterType(int bandId, Dx5FilterType filterType, String mode) {
    final currentBands = PEQModes.isAllChannelsMode(mode)
        ? allBands
        : (PEQModes.isLeftChannelMode(mode) ? leftBands : rightBands);

    final band = currentBands.firstWhere((b) => b.id == bandId);
    band.filterType.value = filterType;

    switch (filterType) {
      case Dx5FilterType.peakingFilter:
      case Dx5FilterType.lowShelfFilter:
      case Dx5FilterType.highShelfFilter:
        band.frequencyType.value = Dx5FrequencyType.centerFrequency;
        break;
      case Dx5FilterType.lowPassFilter:
      case Dx5FilterType.highPassFilter:
        band.frequencyType.value = Dx5FrequencyType.cornerFrequency;
        break;
    }
  }

  /// 更新波段频率（仅操作当前模式）
  void updateBandFrequency(int bandId, double frequency, String mode) {
    final currentBands = PEQModes.isAllChannelsMode(mode)
        ? allBands
        : (PEQModes.isLeftChannelMode(mode) ? leftBands : rightBands);

    final band = currentBands.firstWhere((b) => b.id == bandId);
    band.frequency.value = frequency;
  }

  /// 更新波段增益（仅操作当前模式）
  void updateBandGain(int bandId, double gain, String mode) {
    final currentBands = PEQModes.isAllChannelsMode(mode)
        ? allBands
        : (PEQModes.isLeftChannelMode(mode) ? leftBands : rightBands);

    final band = currentBands.firstWhere((b) => b.id == bandId);
    band.gain.value = gain;
  }

  /// 更新波段Q值（仅操作当前模式）
  void updateBandQ(int bandId, double q, String mode) {
    final currentBands = PEQModes.isAllChannelsMode(mode)
        ? allBands
        : (PEQModes.isLeftChannelMode(mode) ? leftBands : rightBands);

    final band = currentBands.firstWhere((b) => b.id == bandId);
    band.q.value = q;
  }

  /// 添加新波段（仅添加到当前模式）
  void addNewBand(String mode) {
    final currentBands = PEQModes.isAllChannelsMode(mode)
        ? allBands
        : (PEQModes.isLeftChannelMode(mode) ? leftBands : rightBands);

    if (currentBands.length >= maxBands) {
      return;
    }

    int newId = getNextAvailableId();

    double newFreq = calculateOptimalFrequency(currentBands);

    final newBand = PEQBand(
      id: newId,
      enabled: true,
      frequency: newFreq,
      filterType: Dx5FilterType.peakingFilter,
      frequencyType: Dx5FrequencyType.centerFrequency,
      gain: 0.0,
      q: 1.41,
    );

    currentBands.add(newBand);

    _lastAddedBandId = newId;
  }

  /// 获取最后添加的波段
  PEQBand? getLastAddedBand(String mode) {
    if (_lastAddedBandId < 0) return null;

    final currentBands = PEQModes.isAllChannelsMode(mode)
        ? allBands
        : (PEQModes.isLeftChannelMode(mode) ? leftBands : rightBands);

    try {
      return currentBands.firstWhere((band) => band.id == _lastAddedBandId);
    } catch (e) {
      return null;
    }
  }

  /// 删除波段（仅从当前模式删除）
  void removeBand(int bandId, String mode) {
    if (bandId == 0) {
      return; // 不允许删除前级波段
    }

    final currentBands = PEQModes.isAllChannelsMode(mode)
        ? allBands
        : (PEQModes.isLeftChannelMode(mode) ? leftBands : rightBands);

    currentBands.removeWhere((band) => band.id == bandId);
  }

  /// 重置所有波段（仅重置当前模式）
  void resetBands(String mode) {
    final currentBands = PEQModes.isAllChannelsMode(mode)
        ? allBands
        : (PEQModes.isLeftChannelMode(mode) ? leftBands : rightBands);

    for (var band in currentBands) {
      if (band.id != 0) {
        band.enabled.value = false;
        band.gain.value = 0.0;
        band.q.value = 1.41;
      }
    }

    /// 确保前级波段（ID=0）保持启用
    final preampBand = currentBands.firstWhere((b) => b.id == 0);
    preampBand.enabled.value = true;
  }

  /// 计算最佳频率
  double calculateOptimalFrequency(List<PEQBand> bands) {
    double newFreq = 1000.0; // 默认中间频率

    if (bands.isNotEmpty) {
      // 获取所有频率并排序
      final freqs = bands.map((b) => b.frequency.value).toList()..sort();

      // 找出最大的频率间隔
      double maxGap = 0.0;
      double gapStart = 20.0; // 最小频率

      for (int i = 0; i < freqs.length; i++) {
        final currentFreq = freqs[i];
        final gap =
            i == 0 ? (currentFreq - 20.0) : (currentFreq - freqs[i - 1]);

        if (gap > maxGap) {
          maxGap = gap;
          gapStart = i == 0 ? 20.0 : freqs[i - 1];
        }
      }

      // 还要检查最高频率到20kHz的间隔
      if (freqs.isNotEmpty) {
        final gap = 20000.0 - freqs.last;
        if (gap > maxGap) {
          maxGap = gap;
          gapStart = freqs.last;
        }
      }

      // 在最大间隔中取中点作为新频率
      newFreq = gapStart + maxGap / 2;

      // 限制在20Hz-20kHz范围内
      newFreq = math.max(20.0, math.min(20000.0, newFreq));
    }

    return newFreq;
  }
}
