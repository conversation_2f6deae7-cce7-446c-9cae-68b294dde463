import 'dart:io';
import 'dart:math' as math;

import 'package:get/get.dart';
import '../../../../../common/util/log_util.dart';
import '../../../../../common/util/i18n.dart';
import '../models/imported_file.dart';
import 'file_dialog_service.dart';
import 'package:csv/csv.dart';
import 'package:uuid/uuid.dart';
import '../../../../../models/imported_file_model.dart';
import '../calculator/curve_smoother.dart';
import '../calculator/curve_points_generator.dart';

/// PEQ文件服务
class PEQFileService {
  // 注入依赖
  final FileDialogService _dialogService;

  PEQFileService(this._dialogService);

  // 目标文件和源频响文件列表
  final RxList<ImportedFile> targetFiles = <ImportedFile>[].obs;
  final RxList<ImportedFile> sourceFRFiles = <ImportedFile>[].obs;

  /// 加载来自模型的目标文件列表
  void loadTargetFilesFromModels(List<ImportedFileModel> models) {
    targetFiles.clear();
    for (final model in models) {
      targetFiles.add(ImportedFile.fromModel(model));
    }
  }

  /// 加载来自模型的源频响文件列表
  void loadSourceFRFilesFromModels(List<ImportedFileModel> models) {
    sourceFRFiles.clear();
    for (final model in models) {
      sourceFRFiles.add(ImportedFile.fromModel(model));
    }
  }

  /// 导出组合滤波器
  Future<void> exportCombinedFilter(List<Map<String, double>> data) async {
    try {
      // 转换数据为CSV格式
      final csvData = [
        ['Frequency', 'Gain'], // 表头
        ...data.map((point) => [point['frequency'], point['db']]),
      ];

      // 使用CSV转换器
      final csvString = const ListToCsvConverter().convert(csvData);

      // 转换为字节数组
      final bytes = csvString.codeUnits;

      // 使用对话框服务保存字节数据
      final success = await _dialogService.saveBytesToFile(
        l10n.saveMergedFilterFile,
        ['csv'],
        bytes,
        suggestedName: 'combined_filter.csv',
      );

      if (success) {
        _dialogService.showInfoDialog(l10n.exportSuccess);
      }
    } catch (e) {
      _dialogService.showErrorDialog(l10n.errorExportingFile + ': ' + e.toString());
    }
  }

  /// 解析CSV文件为频率响应数据
  List<Map<String, double>> parseCSVToFrequencyResponse(String csvContent) {
    final List<Map<String, double>> result = [];

    try {
      // 移除BOM标记（如果存在）
      if (csvContent.startsWith('\uFEFF')) {
        csvContent = csvContent.substring(1);
      }

      // 规范化行尾
      csvContent = csvContent.replaceAll('\r\n', '\n').replaceAll('\r', '\n');

      // 日志记录CSV内容前100个字符
      Log.i(
          'CSV内容前100个字符: ${csvContent.substring(0, math.min(100, csvContent.length))}');

      // 检查第一行是否是 Topping_csv_file
      List<String> lines = csvContent.split('\n');
      int startIndex = 0;

      if (lines.isNotEmpty && lines[0].trim() == "Topping_csv_file") {
        startIndex = 1;
        Log.i('检测到 Topping_csv_file 标识行，从第二行开始解析');
      }

      // 如果没有足够的行，返回空列表
      if (lines.length < startIndex + 2) {
        // 至少需要一个标题行和一个数据行
        Log.i('CSV文件行数不足');
        return result;
      }

      // 获取实际的标题行
      String headerLine = lines[startIndex].trim();
      Log.i('标题行: $headerLine');

      // 尝试确定分隔符
      String delimiter = ',';
      if (headerLine.contains('\t')) {
        delimiter = '\t';
        Log.i('检测到制表符分隔符');
      }

      // 解析标题行
      List<String> headers = headerLine.split(delimiter);

      // 如果标题行分割后列数不足，尝试用空格分割
      if (headers.length < 2) {
        headers = headerLine.split(RegExp(r'\s+'));
        if (headers.length >= 2) {
          delimiter = ' ';
          Log.i('使用空格作为分隔符');
        }
      }

      Log.i('分隔后的标题: ${headers.join(" | ")}');
      Log.i('标题列数: ${headers.length}');

      // 标识频率和dB列
      int freqIndex = -1;
      int dbIndex = -1;

      // 先尝试通过列名匹配
      for (int i = 0; i < headers.length; i++) {
        String column = headers[i].trim().toLowerCase();
        if (column.contains('fre') ||
            column.contains('hz') ||
            column.contains('frequency')) {
          freqIndex = i;
          Log.i('找到频率列索引: $freqIndex (${headers[i]})');
        } else if (column.contains('db') ||
            column.contains('raw') ||
            column.contains('response')) {
          dbIndex = i;
          Log.i('找到dB列索引: $dbIndex (${headers[i]})');
        }
      }

      // 如果无法通过列名匹配，使用默认的前两列
      if (freqIndex == -1 && headers.isNotEmpty) {
        freqIndex = 0;
        Log.i('使用默认频率列索引: 0');
      }

      if (dbIndex == -1 && headers.length > 1) {
        dbIndex = 1;
        Log.i('使用默认dB列索引: 1');
      }

      // 如果仍然没有有效的索引，返回空结果
      if (freqIndex == -1 || dbIndex == -1 || freqIndex == dbIndex) {
        Log.i('无法确定有效的频率和dB列');
        return result;
      }

      // 解析数据行
      for (int i = startIndex + 1; i < lines.length; i++) {
        String line = lines[i].trim();
        if (line.isEmpty) continue;

        List<String> values;
        if (delimiter == ' ') {
          values = line.split(RegExp(r'\s+'));
        } else {
          values = line.split(delimiter);
        }

        // 确保行有足够的列
        if (values.length <= math.max(freqIndex, dbIndex)) {
          continue;
        }

        try {
          double freq = double.parse(values[freqIndex].trim());
          double db = double.parse(values[dbIndex].trim());

          result.add({
            'frequency': freq,
            'db': db,
          });
        } catch (e) {
          // 跳过无法解析的行
          Log.i('行 ${i + 1} 解析错误: $e');
          continue;
        }
      }

      Log.i('成功解析 ${result.length} 个数据点');
    } catch (e) {
      Log.i('CSV解析过程中出错: $e');
    }

    return result;
  }

  /// 导入目标文件
  Future<ImportedFile?> importTargetFile(String filePath) async {
    try {
      final file = File(filePath);
      if (!await file.exists()) {
        _dialogService.showErrorDialog('${l10n.error}: ${l10n.cannotOpenFileOrItsDirectory}');
        return null;
      }

      final content = await file.readAsString();
      List<Map<String, double>> data = parseCSVToFrequencyResponse(content);

      if (data.isEmpty) {
        _dialogService.showErrorDialog(l10n.cannotExtractValidFrequencyAndResponseDataFromCSV);
        return null;
      }

      // ===== 新增：应用平滑处理 =====
      Log.i('原始目标数据点数量: ${data.length}');

      // 1. 重采样到更多点
      final frequencies = CurvePointsGenerator.generateLogFrequencies(1000);
      final resampledData =
          CurvePointsGenerator.interpolateData(data, frequencies);

      // 2. 应用Savitzky-Golay平滑
      final smoothedData = CurveSmoother.smoothWithSavGol(resampledData,
          windowSize: 9, // 窗口大小，越大平滑效果越强
          polynomialOrder: 2 // 多项式阶数，建议保持在2-3
          );

      Log.i('平滑后的目标数据点数量: ${smoothedData.length}');
      // ===== 平滑处理结束 =====

      // 创建导入的文件对象，使用平滑后的数据
      final fileName = file.path.split(Platform.pathSeparator).last;
      final importedFile = ImportedFile(
        id: const Uuid().v4(),
        name: fileName,
        data: smoothedData, // 使用平滑后的数据
        type: 'target',
      );

      // 添加到目标文件列表
      targetFiles.add(importedFile);
      return importedFile;
    } catch (e) {
      _dialogService.showErrorDialog('${l10n.errorImportingFile}: $e');
      return null;
    }
  }

  /// 导入源频响文件
  Future<ImportedFile?> importSourceFRFile(String filePath) async {
    try {
      final file = File(filePath);
      if (!await file.exists()) {
        _dialogService.showErrorDialog('${l10n.error}: ${l10n.cannotOpenFileOrItsDirectory}');
        return null;
      }

      final content = await file.readAsString();
      List<Map<String, double>> data = parseCSVToFrequencyResponse(content);

      if (data.isEmpty) {
        _dialogService.showErrorDialog(l10n.cannotExtractValidFrequencyAndResponseDataFromCSV);
        return null;
      }

      // ===== 新增：应用平滑处理 =====
      Log.i('原始源频响数据点数量: ${data.length}');

      // 1. 重采样到更多点
      final frequencies = CurvePointsGenerator.generateLogFrequencies(1000);
      final resampledData =
          CurvePointsGenerator.interpolateData(data, frequencies);

      // 2. 应用Savitzky-Golay平滑
      final smoothedData = CurveSmoother.smoothWithSavGol(resampledData,
          windowSize: 11, // 对源频响使用更大的窗口，获得更平滑的效果
          polynomialOrder: 2);

      Log.i('平滑后的源频响数据点数量: ${smoothedData.length}');
      // ===== 平滑处理结束 =====

      // 创建导入的文件对象，使用平滑后的数据
      final fileName = file.path.split(Platform.pathSeparator).last;
      final importedFile = ImportedFile(
        id: const Uuid().v4(),
        name: fileName,
        data: smoothedData, // 使用平滑后的数据
        type: 'sourceFR',
      );

      // 添加到源频响文件列表
      sourceFRFiles.add(importedFile);
      return importedFile;
    } catch (e) {
      _dialogService.showErrorDialog('${l10n.errorImportingFile}: $e');
      return null;
    }
  }

  /// 导出滤波器数据到文件
  Future<bool> exportFilterData(
      String filePath, List<Map<String, double>> data) async {
    try {
      final file = File(filePath);

      // 转换数据为CSV格式
      final csvData = [
        ['Frequency', 'Gain'], // 表头
        ...data.map((point) => [point['frequency'], point['db']]),
      ];

      // 使用CSV转换器
      final csvString = const ListToCsvConverter().convert(csvData);

      // 写入文件，使用字节而非字符串
      await file.writeAsBytes(csvString.codeUnits);
      return true;
    } catch (e) {
      _dialogService.showErrorDialog('${l10n.errorExportingFile}: $e');
      return false;
    }
  }

  /// 删除目标文件
  void deleteTargetFile(String fileId) {
    targetFiles.removeWhere((file) => file.id == fileId);
  }

  /// 删除源频响文件
  void deleteSourceFRFile(String fileId) {
    sourceFRFiles.removeWhere((file) => file.id == fileId);
  }
}
