import '../../../../../models/peq_band_model.dart';
import '../calculator/calculation_params.dart';
import '../calculator/filter_response_calculator.dart';
import '../models/peq_band.dart';

/// 包装器类，用于调用FilterResponseCalculator的静态方法
class FilterCalculatorWrapper {
  // 是否启用异步计算
  final bool useAsyncCalculation;

  FilterCalculatorWrapper({this.useAsyncCalculation = true});

  /// 生成合并滤波器数据（同步方法，带缓存）
  List<Map<String, dynamic>> generateCombinedFilterData(
      List<PEQBand> bands, int preampGain) {
    // 创建FilterResponseCalculator可以识别的滤波器列表
    final List<Map<String, dynamic>> filters = _convertBandsToFilters(bands);

    if (filters.isEmpty) {
      return [];
    }

    // 使用缓存版本计算响应
    return FilterResponseCalculator.calculateCombinedResponseWithPreampCached(
        filters, preampGain,
        minFreq: 20.0, maxFreq: 20000.0, points: 150 // 增加点数以获得更平滑的曲线
        );
  }

  /// 异步生成合并滤波器数据
  Future<List<Map<String, dynamic>>> generateCombinedFilterDataAsync(
      List<PEQBand> bands, int preampGain) async {
    final List<Map<String, dynamic>> filters = _convertBandsToFilters(bands);

    if (filters.isEmpty) {
      return [];
    }

    if (useAsyncCalculation) {
      // 使用异步计算（在隔离线程中）
      return await calculateResponseAsync(filters, preampGain, points: 150);
    } else {
      // 使用同步但有缓存的方法
      return generateCombinedFilterData(bands, preampGain);
    }
  }

  /// 生成单个波段的滤波器数据
  List<Map<String, dynamic>> generateBandFilterData(PEQBand band) {
    if (!band.enabled.value) {
      return [];
    }

    return FilterResponseCalculator.calculateFilterResponse(
        band.filterType.value,
        band.frequency.value,
        band.gain.value,
        band.q.value,
        points: 100);
  }

  /// 帮助方法：将PEQBand对象列表转换为滤波器字典列表
  List<Map<String, dynamic>> _convertBandsToFilters(List<PEQBand> bands) {
    final List<Map<String, dynamic>> filters = [];

    for (final band in bands) {
      if (band.enabled.value) {
        filters.add({
          'type': band.filterType.value,
          'f0': band.frequency.value,
          'gaindB': band.gain.value,
          'Q': band.q.value,
          'enabled': true,
        });
      }
    }

    return filters;
  }

  /// 清除计算缓存
  void clearCache() {
    FilterResponseCalculator.clearCache();
  }

  /// 限制缓存大小以防止内存泄漏
  void limitCacheSize(int maxSize) {
    FilterResponseCalculator.limitCacheSize(maxSize);
  }

  /// 计算合成响应用于导出
  List<Map<String, double>> calculateCombinedResponse(
      List<PEQBandModel> bands, int preampGain) {
    // 创建FilterResponseCalculator可以识别的滤波器列表
    final List<Map<String, dynamic>> filters = [];

    for (final band in bands) {
      if (band.enabled) {
        filters.add({
          'type': band.filterType,
          'f0': band.frequency,
          'gaindB': band.gain,
          'Q': band.q,
          'enabled': true,
        });
      }
    }

    if (filters.isEmpty) {
      return [];
    }

    // 计算响应，这次生成更多点以获得精确的导出结果
    final response =
        FilterResponseCalculator.calculateCombinedResponseWithPreamp(
            filters, preampGain,
            minFreq: 20.0, maxFreq: 20000.0, points: 240 // 每个倍频程40个点，总共6个倍频程
            );

    // 转换为输出格式
    return response.map((point) {
      return {
        'frequency': point['frequency'] as double,
        'db': point['dB'] as double,
      };
    }).toList();
  }
}
