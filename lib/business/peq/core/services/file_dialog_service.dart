import 'dart:io';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:topping_home/theme/color_palettes.dart';
import 'package:topping_home/theme/text_styles.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:share_plus/share_plus.dart';
import 'package:file_picker/file_picker.dart';

import '../../../../common/util/log_util.dart';
import '../../../../common/util/i18n.dart';

/// 文件对话框服务类
class FileDialogService {
  /// 显示文件打开对话框
  Future<File?> showOpenDialog(
      String title, List<String> allowedExtensions) async {
    try {
      final result = await FilePicker.platform.pickFiles(
        dialogTitle: title,
        type: FileType.custom,
        allowedExtensions: allowedExtensions,
      );

      if (result != null &&
          result.files.isNotEmpty &&
          result.files.first.path != null) {
        return File(result.files.first.path!);
      }
    } catch (e) {
      showErrorDialog(l10n.error + ': ' + e.toString());
    }
    return null;
  }

  /// 显示多文件打开对话框，返回文件路径列表
  Future<List<String>?> showOpenFileDialog({
    required String title,
    required List<String> extensions,
    bool allowMultiple = false,
  }) async {
    try {
      final result = await FilePicker.platform.pickFiles(
        dialogTitle: title,
        type: FileType.custom,
        allowedExtensions: extensions,
        allowMultiple: allowMultiple,
      );

      if (result != null && result.files.isNotEmpty) {
        return result.files
            .where((file) => file.path != null)
            .map((file) => file.path!)
            .toList();
      }
    } catch (e) {
      showErrorDialog(l10n.error + ': ' + e.toString());
    }
    return null;
  }

  /// 显示文件保存对话框，返回文件路径
  Future<String?> showSaveFileDialog({
    required String title,
    String? suggestedName,
  }) async {
    try {
      final path = await FilePicker.platform.saveFile(
        dialogTitle: title,
        fileName: suggestedName,
      );

      return path;
    } catch (e) {
      showErrorDialog(l10n.errorSavingFile + e.toString());
    }
    return null;
  }

  /// 显示文件保存对话框
  Future<File?> showSaveDialog(
      String title, List<String> allowedExtensions) async {
    try {
      final path = await FilePicker.platform.saveFile(
        dialogTitle: title,
        type: FileType.custom,
        allowedExtensions: allowedExtensions,
      );

      if (path != null) {
        // 确保文件扩展名正确
        String filePath = path;
        if (allowedExtensions.isNotEmpty &&
            !filePath.toLowerCase().endsWith('.${allowedExtensions.first}')) {
          filePath += '.${allowedExtensions.first}';
        }
        return File(filePath);
      }
    } catch (e) {
      showErrorDialog(l10n.errorSavingFile + e.toString());
    }
    return null;
  }

  /// 显示文件保存对话框，并保存字节数据到文件（适用于所有平台，特别是移动平台）
  Future<bool> saveBytesToFile(
      String title, List<String> allowedExtensions, List<int> bytes,
      {String? suggestedName}) async {
    try {
      Log.i('开始保存文件，字节数: ${bytes.length}');

      // 移动平台特殊处理
      if (Platform.isAndroid || Platform.isIOS) {
        Log.i('检测到移动平台: ${Platform.operatingSystem}');

        // 为移动平台创建临时文件
        String fileName =
            suggestedName ?? 'export_${DateTime.now().millisecondsSinceEpoch}';

        // 确保文件扩展名正确
        if (allowedExtensions.isNotEmpty &&
            !fileName.toLowerCase().endsWith('.${allowedExtensions.first}')) {
          fileName += '.${allowedExtensions.first}';
        }

        // 创建临时目录
        final tempDir = await Directory.systemTemp.createTemp('export_');
        final filePath = '${tempDir.path}/$fileName';
        Log.i('移动平台保存路径: $filePath');

        // 创建文件并写入字节
        final file = File(filePath);
        await file.writeAsBytes(bytes);
        Log.i('文件保存成功: $filePath');

        // 显示保存成功信息并提供共享选项
        showShareFileDialog(filePath, '导出数据文件');
        return true;
      }

      // 桌面平台使用FilePicker
      final path = await FilePicker.platform.saveFile(
        dialogTitle: title,
        type: FileType.custom,
        allowedExtensions: allowedExtensions,
        fileName: suggestedName,
      );

      if (path != null) {
        Log.i('文件保存路径: $path');
        // 确保文件扩展名正确
        String filePath = path;
        if (allowedExtensions.isNotEmpty &&
            !filePath.toLowerCase().endsWith('.${allowedExtensions.first}')) {
          filePath += '.${allowedExtensions.first}';
        }

        // 创建文件并写入字节数据
        final file = File(filePath);
        await file.writeAsBytes(bytes);
        Log.i('文件保存成功');
        return true;
      } else {
        Log.i('用户取消了文件保存');
      }
    } catch (e, stackTrace) {
      final errorMsg = l10n.errorSavingFile + e.toString();
      Log.e(errorMsg);
      Log.e('堆栈: $stackTrace');
      showErrorDialog(errorMsg);
    }
    return false;
  }

  /// 显示信息对话框
  void showInfoDialog(String message) {
    Get.snackbar(
      l10n.infoTitle,
      message,
      snackPosition: SnackPosition.BOTTOM,
      backgroundColor: ColorPalettes.instance.success,
      colorText: ColorPalettes.instance.firstText,
    );
  }

  /// 显示导出成功对话框
  void showExportSuccessDialog(String filePath, String content) {
    Get.dialog(
      AlertDialog(
        title: Text(l10n.exportSuccess),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(l10n.fileSavedTo),
            const SizedBox(height: 8),
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: ColorPalettes.instance.firstText.withValues(alpha: 0.08),
                borderRadius: BorderRadius.circular(4),
              ),
              width: double.infinity,
              child: SelectableText(
                filePath,
                style: TextStyles.instance.h2(),
              ),
            ),
            const SizedBox(height: 16),
            Text(l10n.fileContentPreview),
            const SizedBox(height: 8),
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: ColorPalettes.instance.firstText.withValues(alpha: 0.08),
                borderRadius: BorderRadius.circular(4),
              ),
              width: double.infinity,
              height: 100,
              child: SingleChildScrollView(
                child: Text(
                  content.length > 500
                      ? '${content.substring(0, 500)}...'
                      : content,
                  style: TextStyles.instance.h2(),
                ),
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: Text(l10n.ok),
          ),
          TextButton(
            onPressed: () {
              // 尝试打开文件所在的文件夹
              openFileLocation(filePath);
            },
            child: Text(l10n.openFileLocation),
          ),
        ],
      ),
    );
  }

  /// 显示分享文件对话框
  void showShareFileDialog(String filePath, String content) {
    try {
      Get.dialog(
        AlertDialog(
          title: Text(l10n.exportSuccess),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(l10n.fileExported),
              const SizedBox(height: 8),
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: ColorPalettes.instance.firstText.withValues(alpha: 0.08),
                  borderRadius: BorderRadius.circular(4),
                ),
                width: double.infinity,
                child: SelectableText(
                  filePath,
                  style: TextStyles.instance.h2(),
                ),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Get.back(),
              child: Text(l10n.ok),
            ),
            TextButton(
              onPressed: () {
                // 尝试分享文件
                shareFile(filePath, l10n.sharedFilterFile);
                Get.back();
              },
              child: Text(l10n.shareFile),
            ),
          ],
        ),
      );
    } catch (e) {
      Log.e("显示分享对话框错误: $e");
      showInfoDialog(l10n.fileExported);
    }
  }

  /// 打开文件位置
  Future<void> openFileLocation(String filePath) async {
    try {
      final File file = File(filePath);

      // 获取文件的父目录
      final String directoryPath = file.parent.path;

      // 创建指向该目录的Uri
      final Uri dirUri;

      if (Platform.isWindows) {
        // Windows需要特殊处理
        dirUri = Uri.file(directoryPath, windows: true);
      } else {
        dirUri = Uri.directory(directoryPath);
      }

      // 尝试打开目录
      final bool launched = await launchUrl(dirUri);
      if (!launched) {
        // 如果无法打开目录，尝试直接打开文件
        final Uri fileUri = Uri.file(filePath);
        final bool fileLaunched = await launchUrl(fileUri);

        if (!fileLaunched) {
          showErrorDialog(l10n.cannotOpenFileOrItsDirectory);
        }
      }
    } catch (e) {
      showErrorDialog(l10n.errorOpeningFileLocation + e.toString());
    }
  }

  /// 分享文件
  Future<void> shareFile(String filePath, String fileName) async {
    try {
      final file = File(filePath);

      // 使用share_plus插件分享文件
      await Share.shareXFiles(
        [XFile(file.path)],
        text: l10n.sharedFilterFile,
      );
    } catch (e) {
      showErrorDialog('${l10n.errorSharingFile}: $e');
    }
  }

  /// 显示错误对话框
  void showErrorDialog(String message) {
    Get.snackbar(
      l10n.error,
      message,
      snackPosition: SnackPosition.BOTTOM,
      backgroundColor: ColorPalettes.instance.error,
      colorText: ColorPalettes.instance.pure,
    );
  }
}
