import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:topping_home/business/peq/peq_controller.dart';
import 'package:topping_home/business/peq/ui/tables/band_table.dart';
import 'package:topping_home/theme/color_palettes.dart';
import '../device_detail/device_detail_logic.dart';
import 'ui/controls/display_mode_switches.dart';
import 'ui/display/eq_display_area.dart';
import 'ui/controls/parameter_knobs.dart';
import 'ui/controls/peq_mode_selector.dart';

/// PEQ UI实现
class PEQPage extends StatelessWidget {
  PEQPage({super.key});

  // 获取设备详情逻辑
  final DeviceDetailLogic deviceDetailLogic = Get.find<DeviceDetailLogic>();

  // 通过Get.find获取已注册的PEQ控制器
  final PEQController peqController = Get.find<PEQController>();

  @override
  Widget build(BuildContext context) {
    // 使用LayoutBuilder获取可用空间的约束
    return LayoutBuilder(
      builder: (context, constraints) {
        // 计算可用高度
        final availableHeight = constraints.maxHeight;

        return Container(
          color: ColorPalettes.instance.background,
          child: Column(
            children: [
              // 上半部分 - 主要内容区域，使用Expanded而非固定高度
              Expanded(
                flex: 7, // 使用比例分配空间
                child: SingleChildScrollView(
                  child: Column(
                    children: [
                    // PEQ模式选择器区域
                    _buildCard(
                      child: PEQModeSelector(controller: peqController),
                      padding: EdgeInsets.all(8.w), // 使用screenutil适配内边距
                      margin: EdgeInsets.fromLTRB(
                          8.w, 4.h, 8.w, 2.h), // 使用screenutil适配边距
                    ),

                    // EQ显示区域 + 显示模式开关整合在一起
                    _buildCard(
                      child: Column(
                        children: [
                          // 显示模式切换按钮
                          Padding(
                            padding: EdgeInsets.fromLTRB(
                                12.w, 4.h, 12.w, 0), // 使用screenutil适配内边距
                            child:
                                DisplayModeSwitches(controller: peqController),
                          ),

                          // EQ显示区域
                          EQDisplayArea(
                            controller: peqController,
                            // 传递可用高度的百分比给EQ显示区域
                            availableHeight: availableHeight * 0.3,
                          ),
                        ],
                      ),
                      padding: EdgeInsets.zero,
                      margin: EdgeInsets.fromLTRB(
                          8.w, 2.h, 8.w, 2.h), // 使用screenutil适配边距
                    ),

                    // 参数调节器区域
                    _buildCard(
                      child: ParameterKnobs(controller: peqController),
                      padding: EdgeInsets.symmetric(
                          vertical: 4.h), // 使用screenutil适配内边距
                      margin: EdgeInsets.fromLTRB(
                          8.w, 2.h, 8.w, 4.h), // 使用screenutil适配边距
                    ),
                    ],
                  ),
                ),
              ),

              // 下半部分 - 滤波器列表
              Expanded(
                flex: 5,
                child: _buildCard(
                  child: BandTable(controller: peqController),
                  padding: EdgeInsets.zero,
                  margin: EdgeInsets.all(4.w), // 使用screenutil适配边距
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  /// 构建带有阴影和圆角的卡片
  Widget _buildCard({
    required Widget child,
    EdgeInsets padding = EdgeInsets.zero,
    EdgeInsets margin = const EdgeInsets.symmetric(horizontal: 8),
  }) {
    return Container(
      margin: margin,
      decoration: BoxDecoration(
        color: ColorPalettes.instance.card,
        borderRadius: BorderRadius.circular(6.r), // 使用screenutil适配圆角
        boxShadow: [
          BoxShadow(
            color: ColorPalettes.instance.shadow.withValues(alpha: 0.03),
            blurRadius: 2.r, // 使用screenutil适配模糊半径
            offset: Offset(0, 1.h), // 使用screenutil适配阴影偏移
          ),
        ],
      ),
      child: Padding(
        padding: padding,
        child: child,
      ),
    );
  }
}
