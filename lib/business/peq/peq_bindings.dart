import 'package:get/get.dart';

import '../../../../repositories/device_repository.dart';
import '../../repositories/peq_repository.dart';
import 'core/services/file_dialog_service.dart';
import 'core/services/filter_calculator_wrapper.dart';
import 'core/services/peq_band_service.dart';
import 'core/services/peq_file_service.dart';
import 'peq_state.dart';
import 'peq_controller.dart';

/// PEQ相关依赖绑定类
class PEQBindings extends Bindings {
  final String deviceId;

  PEQBindings(this.deviceId);

  @override
  void dependencies() {
    // 1. 注册基础服务 - 无依赖的服务
    Get.put(FileDialogService(), permanent: true);
    Get.put(FilterCalculatorWrapper(), permanent: true);

    // 2. 注册存储库和数据访问层
    final deviceRepository = Get.find<DeviceRepository>();
    Get.put(PEQRepository(deviceRepository), permanent: true);

    // 3. 注册依赖于基础服务的服务
    Get.put(PEQFileService(Get.find<FileDialogService>()), permanent: true);
    Get.put(PEQBandService(), permanent: true);
    Get.put(UIStateManager(), permanent: true);

    // 4. 最后注册控制器
    Get.put<PEQController>(PEQController(
      deviceId: deviceId,
      peqRepository: Get.find<PEQRepository>(),
      bandService: Get.find<PEQBandService>(),
      filterCalculator: Get.find<FilterCalculatorWrapper>(),
      fileService: Get.find<PEQFileService>(),
      uiStateManager: Get.find<UIStateManager>(),
      dialogService: Get.find<FileDialogService>(),
    ));
  }
}
