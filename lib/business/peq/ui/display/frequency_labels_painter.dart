import 'package:flutter/material.dart';
import 'package:topping_home/theme/color_palettes.dart';
import 'package:topping_home/theme/text_styles.dart';

import '../../core/calculator/coordinate_converter.dart';

/// 频率标签绘制类
class FrequencyLabelsPainter extends CustomPainter {
  final bool isDarkMode;
  final int updateCounter;

  FrequencyLabelsPainter({
    required this.isDarkMode,
    required this.updateCounter,
  });

  @override
  void paint(Canvas canvas, Size size) {
    // 创建坐标转换器 - 使用标准的频率范围
    final converter = CoordinateConverter(
      size,
      {'minDb': -30.0, 'maxDb': 30.0}, // 使用标准dB范围，但这里不重要，因为我们只关心X轴
    );

    // 绘制频率标签
    drawFrequencyLabels(canvas, size, converter);
  }

  void drawFrequencyLabels(Canvas canvas, Size size, CoordinateConverter converter) {
    // 频率标签样式
    final freqTextStyle = TextStyles.instance.h4().copyWith(
        color: ColorPalettes.instance.firstText.withAlpha(isDarkMode ? 180 : 200),
        fontSize: 9,
    );
    
    final freqTextPainter = TextPainter(
        textDirection: TextDirection.ltr, textAlign: TextAlign.center);
    
    // 要显示的频率点
    final frequencies = [20, 50, 100, 200, 500, 1000, 2000, 5000, 10000, 20000];

    for (final freq in frequencies) {
      // 格式化标签文本
      String label = (freq >= 1000) ? '${freq ~/ 1000}k' : '$freq';
      
      freqTextPainter.text = TextSpan(text: label, style: freqTextStyle);
      freqTextPainter.layout();
      
      // 使用converter获取X坐标
      final x = converter.frequencyToX(freq.toDouble());
      
      // 绘制标签在中间位置
      freqTextPainter.paint(
        canvas,
        Offset(x - freqTextPainter.width / 2, 0),
      );
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    if (oldDelegate is FrequencyLabelsPainter) {
      return updateCounter != oldDelegate.updateCounter ||
          isDarkMode != oldDelegate.isDarkMode;
    }
    return true;
  }
}
