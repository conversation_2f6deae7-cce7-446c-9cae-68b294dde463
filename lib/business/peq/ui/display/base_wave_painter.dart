import 'package:flutter/material.dart';

import '../../core/calculator/coordinate_converter.dart';

/// 基础绘制类
abstract class BaseWavePainter extends CustomPainter {
  final int updateCounter;
  final bool isDarkMode;

  BaseWavePainter({required this.updateCounter, this.isDarkMode = true});

  void drawGrid(Canvas canvas, Size size, Map<String, double> dbRange, CoordinateConverter converter);

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    if (oldDelegate is BaseWavePainter) {
      return updateCounter != oldDelegate.updateCounter ||
          isDarkMode != oldDelegate.isDarkMode;
    }
    return true;
  }
}
