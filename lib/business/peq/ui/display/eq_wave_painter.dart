import 'dart:math';
import 'base_wave_painter.dart';

import 'package:flutter/material.dart';
import 'package:topping_home/common/util/log_util.dart';
import 'package:topping_home/theme/color_palettes.dart';
import 'package:topping_home/theme/text_styles.dart';

import '../../core/calculator/coordinate_converter.dart';
import '../../core/calculator/curve_points_generator.dart';
import '../../core/models/peq_band.dart';
import 'grid_painter.dart';

/// EQ波形绘制类
class EQWavePainter extends BaseWavePainter {
  // 定义用于平滑曲线的目标点数
  static const int smoothCurvePoints = 200; // 增加插值点数量，从500提高到1000，使曲线更平滑

  // 为各种曲线定义静态颜色常量，以便在UI中重用
  static const Color targetCurveColor = Color.fromRGBO(216, 216, 216, 1);
  static const Color sourceFRCurveColor = Color.fromRGBO(255, 0, 0, 1);
  static const Color combinedCurveColor = Color.fromRGBO(255, 85, 0, 1);
  static const Color filteredFRCurveColor = Color.fromRGBO(0, 255, 255, 1);

  final List<PEQBand> bands;
  // 移除 dbRange 初始化
  final bool showCombined;
  final bool showEachFilter;
  final bool showTarget;
  final bool showSourceFR;
  final bool showFilteredFR;
  final bool isCompensatedMode;
  final List<Map<String, double>>? targetData; // 保持原始 Map 格式
  final List<Map<String, double>>? sourceFRData; // 保持原始 Map 格式
  final bool drawFrequencyLabels; // 是否绘制频率标签

  static const List<Color> filterColors = [
    Color.fromRGBO(38, 73, 155, 1), // 深蓝色
    Color.fromRGBO(140, 28, 140, 1), // 紫色
    Color.fromRGBO(146, 51, 57, 1), // 红褐色
    Color.fromRGBO(58, 43, 166, 1), // 蓝紫色
    Color.fromRGBO(29, 102, 83, 1), // 深绿色
    Color.fromRGBO(61, 82, 96, 1), // 灰蓝色
    Color.fromRGBO(174, 201, 93, 1), // 黄绿色
    Color.fromRGBO(135, 192, 215, 1), // 浅蓝色
    Color.fromRGBO(211, 135, 135, 1), // 粉红色
    Color.fromRGBO(132, 214, 185, 1), // 薄荷绿
  ];

  EQWavePainter({
    required this.bands,
    this.showCombined = false,
    this.showEachFilter = false,
    this.showTarget = false,
    this.showSourceFR = false,
    this.showFilteredFR = false,
    this.isCompensatedMode = true,
    this.targetData,
    this.sourceFRData,
    required super.updateCounter,
    required super.isDarkMode,
    this.drawFrequencyLabels = true, // 默认绘制频率标签
  });

  @override
  void paint(Canvas canvas, Size size) {
    Log.d("EQWavePainter paint started. Size: $size");
    // 生成标准的目标频率点，用于插值
    final List<double> standardFrequencies =
        CurvePointsGenerator.generateLogFrequencies(smoothCurvePoints);
    List<Map<String, double>> targetRawData = [];
    if (showTarget && targetData != null && targetData!.isNotEmpty) {
      targetRawData = _getTargetPointsRawData(standardFrequencies); // 使用插值
      Log.d(
          "Target raw data prepared (interpolated): ${targetRawData.length} points");
    }
    List<Map<String, double>> sourceFRRawData = [];
    if (showSourceFR && sourceFRData != null && sourceFRData!.isNotEmpty) {
      sourceFRRawData = _getSourceFRPointsRawData(standardFrequencies); // 使用插值
      Log.d(
          "Source FR raw data prepared (interpolated): ${sourceFRRawData.length} points");
    }
    List<Map<String, double>> combinedRawData = [];
    List<List<Map<String, double>>> individualBandsRawData = [];
    List<Map<String, double>> filteredFRRawData = [];
    if (bands.isNotEmpty) {
      // Combined 和 Individual 已经是计算生成的，点数足够，无需插值
      // 也可以统一使用 smoothCurvePoints 来计算它们以保持一致性
      if (showCombined) {
        combinedRawData = CurvePointsGenerator.generateCombinedCurveRawData(
            bands,
            points: smoothCurvePoints);
        Log.d("Combined raw data prepared: ${combinedRawData.length} points");
      }
      if (showEachFilter) {
        individualBandsRawData =
            _getIndividualBandsRawData(points: smoothCurvePoints);
        Log.d(
            "Individual bands raw data prepared: ${individualBandsRawData.length} bands, total points: ${individualBandsRawData.expand((l) => l).length}");
      }
      // Filtered FR 需要基于插值后的 Source FR 数据进行计算
      if (showFilteredFR &&
          sourceFRData != null &&
          sourceFRData!.isNotEmpty &&
          sourceFRRawData.isNotEmpty) {
        // 提取插值后的 source dB 值
        List<double> interpolatedSourceDbValues =
            sourceFRRawData.map((p) => p['db']!).toList();
        // 使用标准的频率点和插值后的dB值计算 Filtered FR
        filteredFRRawData = _getFilteredFRRawData(
            interpolatedSourceDbValues, standardFrequencies);
        Log.d(
            "Filtered FR raw data prepared (based on interpolated source): ${filteredFRRawData.length} points");
      }
    }

    final Map<String, double> dynamicDbRange = _calculateDynamicDbRange(
      targetRawData,
      sourceFRRawData,
      combinedRawData,
      individualBandsRawData.expand((list) => list).toList(), // 展平列表
      filteredFRRawData,
    );
    Log.d(
        "Dynamic dB range calculated: Min=${dynamicDbRange['minDb']?.toStringAsFixed(1)}, Max=${dynamicDbRange['maxDb']?.toStringAsFixed(1)}");

    // 为频率标签预留空间
    final double labelPadding = 15.0;
    // 调整图表绘制区域的高度，为标签预留空间
    final double chartHeight = size.height - labelPadding;
    // 创建一个新的尺寸对象，用于坐标转换
    final chartSize = Size(size.width, chartHeight);

    final converter = CoordinateConverter(chartSize, dynamicDbRange);
    Log.d("CoordinateConverter created with adjusted chart height.");

    drawGrid(canvas, size, dynamicDbRange, converter); // 传递 converter
    drawAxisLabels(canvas, size, dynamicDbRange, converter); // 传递 converter
    Log.d("Grid and labels drawn.");


    if (showTarget && targetRawData.isNotEmpty) {
      Log.d("Drawing Target Curve...");

      _drawCurveFromData(
          canvas,
          targetRawData,
          Paint()
            ..color = targetCurveColor
            ..style = PaintingStyle.stroke
            ..strokeWidth = 2.0,
          converter);
    }

    if (showSourceFR && sourceFRRawData.isNotEmpty) {
      Log.d("Drawing Source FR Curve...");

      _drawCurveFromData(
          canvas,
          sourceFRRawData,
          Paint()
            ..color = sourceFRCurveColor
            ..style = PaintingStyle.stroke
            ..strokeWidth = 2.0,
          converter);
    }

    if (showEachFilter && individualBandsRawData.isNotEmpty) {
      Log.d("Drawing Individual Bands...");
      int drawnFilters = 0;
      for (final bandData in individualBandsRawData) {
        final colorIndex = drawnFilters % filterColors.length;
        final color = filterColors[colorIndex];
        final bandPaint = Paint()
          ..color = color.withAlpha((180))
          ..style = PaintingStyle.stroke
          ..strokeWidth = 1.5;
        final offsets = bandData
            .map((p) => Offset(converter.frequencyToX(p['frequency']!),
                converter.dbToY(p['db']!)))
            .toList();
        GridPainter.drawDashedCurve(canvas, offsets, bandPaint);
        drawnFilters++;
      }
    }

    if (showCombined && combinedRawData.isNotEmpty) {
      Log.d("Drawing Combined Curve...");
      _drawCurveFromData(
          canvas,
          combinedRawData,
          Paint()
            ..color = combinedCurveColor
            ..style = PaintingStyle.stroke
            ..strokeWidth = 2.0,
          converter);
    }

    if (showFilteredFR && filteredFRRawData.isNotEmpty) {
      Log.d("Drawing Filtered FR Curve...");
      _drawCurveFromData(
          canvas,
          filteredFRRawData,
          Paint()
            ..color = filteredFRCurveColor
            ..style = PaintingStyle.stroke
            ..strokeWidth = 2.0,
          converter);
    }

    bool curvesDrawn = showTarget && targetRawData.isNotEmpty ||
        showSourceFR && sourceFRRawData.isNotEmpty ||
        showCombined && combinedRawData.isNotEmpty ||
        showFilteredFR && filteredFRRawData.isNotEmpty ||
        showEachFilter && individualBandsRawData.isNotEmpty;

    bool hasEnabledBands = bands.any((b) => b.enabled.value);
    if (!curvesDrawn && !hasEnabledBands) {
      Log.d("Drawing Flat Line...");
      final flatPaint = Paint()
        ..color = ColorPalettes.instance.accent
        ..style = PaintingStyle.stroke
        ..strokeWidth = 2.0;
      // 使用调整后的图表尺寸
      final chartSize = Size(size.width, size.height - 15.0);
      GridPainter.drawFlatLine(canvas, chartSize, flatPaint,
          converter.dbToY(0)); // 使用 converter 获取 0dB Y坐标
    }
    Log.d("EQWavePainter paint finished.");
  }

  /// 获取目标曲线的原始数据
  List<Map<String, double>> _getTargetPointsRawData(
      List<double> targetFrequencies) {
    List<Map<String, double>> originalPoints = [];
    if (targetData == null) return originalPoints;
    if (!isCompensatedMode) {
      // 原始模式：对导入的 targetData 进行插值
      originalPoints = targetData!
          .map((d) => {
                'frequency': (d['frequency'] as num).toDouble(),
                'db': (d['db'] as num).toDouble()
              })
          .toList();
      return CurvePointsGenerator.interpolateData(
          originalPoints, targetFrequencies);
    } else {
      // 补偿模式：目标是 0dB 线，直接在 targetFrequencies 上生成
      List<Map<String, double>> compensatedData = [];
      for (var freq in targetFrequencies) {
        compensatedData.add({'frequency': freq, 'db': 0.0});
      }
      return compensatedData;
    }
  }

  /// 获取 Source FR 曲线的原始数据
  List<Map<String, double>> _getSourceFRPointsRawData(
      List<double> targetFrequencies) {
    List<Map<String, double>> originalSourcePoints = [];
    if (sourceFRData == null) return originalSourcePoints;
    originalSourcePoints = sourceFRData!
        .map((d) => {
              'frequency': (d['frequency'] as num).toDouble(),
              'db': (d['db'] as num).toDouble()
            })
        .toList();
    List<Map<String, double>> pointsToInterpolate;
    if (isCompensatedMode && targetData != null && targetData!.isNotEmpty) {
      // 补偿模式：先对原始 Source 点进行补偿计算
      List<Map<String, double>> originalTargetPoints = targetData!
          .map((d) => {
                'frequency': (d['frequency'] as num).toDouble(),
                'db': (d['db'] as num).toDouble()
              })
          .toList();
      // 确保原始目标数据已排序，以便查找
      originalTargetPoints
          .sort((a, b) => a['frequency']!.compareTo(b['frequency']!));
      List<Map<String, double>> compensatedSourcePoints = [];
      for (final sourcePoint in originalSourcePoints) {
        double targetDb = _findTargetDbForFrequency(
            sourcePoint['frequency']!, originalTargetPoints);
        compensatedSourcePoints.add({
          'frequency': sourcePoint['frequency']!,
          'db': sourcePoint['db']! - targetDb
        });
      }
      pointsToInterpolate = compensatedSourcePoints;
    } else {
      // Raw模式：直接使用原始 Source 点
      pointsToInterpolate = originalSourcePoints;
    }
    // 对需要插值的点集 (原始或补偿后的) 进行插值
    return CurvePointsGenerator.interpolateData(
        pointsToInterpolate, targetFrequencies);
  }

  /// 计算每个波段的曲线
  List<List<Map<String, double>>> _getIndividualBandsRawData(
      {int points = 100}) {
    List<List<Map<String, double>>> allBandsData = [];
    for (int i = 1; i < bands.length; i++) {
      // 跳过前级 Band 0
      if (bands[i].enabled.value) {
        allBandsData.add(CurvePointsGenerator.generateBandCurveRawData(bands[i],
            points: points)); // 使用传入的点数
      }
    }
    return allBandsData;
  }

  /// 计算 Filtered FR 曲线
  List<Map<String, double>> _getFilteredFRRawData(
      List<double> sourceDbValues, List<double> frequencies) {
    if (sourceDbValues.isEmpty ||
        frequencies.isEmpty ||
        sourceDbValues.length != frequencies.length) {
      return [];
    }
    // 调用 C++ 风格的计算，传入插值后的源 dB 和标准频率点
    return CurvePointsGenerator.generateCombinedCurveRawDataC(
        bands,
        sourceDbValues,
        frequencies, // 传递标准频率点
        targetData);
  }

  /// 绘制曲线
  void _drawCurveFromData(Canvas canvas, List<Map<String, double>> pointsData,
      Paint paint, CoordinateConverter converter) {
    if (pointsData.length < 2) return;

    final path = Path();
    bool isFirst = true;

    // 使用Path.cubicTo方法绘制更平滑的曲线
    for (int i = 0; i < pointsData.length; i++) {
      final point = pointsData[i];
      double x = converter.frequencyToX(point['frequency']!);
      double db = point['db']!;
      // 限制在 ±30 dB
      double clampedDb = db.clamp(-30.0, 30.0);
      double y = converter.dbToY(clampedDb);

      if (isFirst) {
        path.moveTo(x, y);
        isFirst = false;
      } else {
        // 检查前一点是否超出范围，若超出则断开路径
        final prevPoint = pointsData[i - 1];
        double prevDb = prevPoint['db']!;
        if (prevDb > 30.0 || prevDb < -30.0) {
          path.moveTo(x, y);
        } else {
          // 使用更平滑的绘制方式
          if (i < pointsData.length - 1 && i > 1) {
            // 计算控制点，使曲线更平滑
            final nextPoint = pointsData[i + 1];
            final prevPrevPoint = pointsData[i - 2];

            // 确保前一点和下一点在有效范围内
            if (nextPoint['db']! <= 30.0 &&
                nextPoint['db']! >= -30.0 &&
                prevPrevPoint['db']! <= 30.0 &&
                prevPrevPoint['db']! >= -30.0) {
              double x1 = converter.frequencyToX(prevPoint['frequency']!);
              double y1 = converter.dbToY(prevPoint['db']!.clamp(-30.0, 30.0));

              // 添加张力系数，控制曲线的平滑程度
              double tension = 0.25;

              // 计算控制点
              double cx1 = x1 + (x - x1) * tension;
              double cy1 = y1;
              double cx2 = x - (x - x1) * tension;
              double cy2 = y;

              path.cubicTo(cx1, cy1, cx2, cy2, x, y);
            } else {
              // 如果不满足条件，仍然使用直线
              path.lineTo(x, y);
            }
          } else {
            // 对于起始和结束点，使用直线
            path.lineTo(x, y);
          }
        }
      }
    }

    // 启用抗锯齿，使曲线更平滑
    paint.isAntiAlias = true;

    canvas.drawPath(path, paint);
  }

  /// 计算动态 dB 范围
  Map<String, double> _calculateDynamicDbRange(
    List<Map<String, double>> targetData,
    List<Map<String, double>> sourceData,
    List<Map<String, double>> combinedData,
    List<Map<String, double>> individualData,
    List<Map<String, double>> filteredData,
  ) {
    double minDbOverall = double.infinity;
    double maxDbOverall = double.negativeInfinity;
    bool dataFound = false;

    // ... (现有查找 min/max dB 的逻辑保持不变) ...
    final allData = [
      targetData,
      sourceData,
      combinedData,
      individualData,
      filteredData
    ];
    for (final dataSet in allData) {
      if (dataSet.isNotEmpty) {
        dataFound = true;
        for (final point in dataSet) {
          final db = point['db'];
          if (db != null && db.isFinite) {
            minDbOverall = min(minDbOverall, db);
            maxDbOverall = max(maxDbOverall, db);
          }
        }
      }
    }

    // 默认范围先设定为包含 +/- 30
    double minDb = -30.0;
    double maxDb = 30.0;

    if (dataFound && minDbOverall.isFinite && maxDbOverall.isFinite) {

      // 确保最终范围至少是 [-30, 30]
      minDb = min(minDbOverall - 2.0, -30.0); // 取数据最小值下方2dB 和 -30 中更小的那个
      maxDb = max(maxDbOverall + 2.0, 30.0); // 取数据最大值上方2dB 和 +30 中更大的那个

    } else {
      // 如果没有数据，确保范围是 [-30, 30] 或你希望的默认值
      minDb = -30.0;
      maxDb = 30.0;
    }

    // 确保最小范围跨度，例如至少 24dB (类似之前的逻辑)，但要尊重 +/- 30 的边界
    if (maxDb - minDb < 24.0) {
      double center = (maxDb + minDb) / 2;
      // 扩展时，不能超出 [-30, 30]
      minDb = max(-30.0, center - 12.0);
      maxDb = min(30.0, center + 12.0);
    }

    // 最后再次确认边界没有超出 +/- 30 (如果上面逻辑可能导致超出)
    minDb = max(minDb, -30.0); // 确保最低不低于 -30
    maxDb = min(maxDb, 30.0); // 确保最高不超过 +30

    Log.d(
        "Final Dynamic dB range: Min=${minDb.toStringAsFixed(1)}, Max=${maxDb.toStringAsFixed(1)}");
    return {'minDb': minDb, 'maxDb': maxDb};
  }

  // --- 修改 drawGrid 和 drawAxisLabels 以使用 CoordinateConverter ---
  @override
  void drawGrid(Canvas canvas, Size size, Map<String, double> dbRange,
      CoordinateConverter converter) {
    // 为频率标签预留空间
    final double labelPadding = 15.0;
    // 调整图表绘制区域的高度，为标签预留空间
    final double chartHeight = size.height - labelPadding;

    // 创建一个新的尺寸对象，只用于网格绘制
    final gridSize = Size(size.width, chartHeight);

    // 现在 GridPainter.drawGrid 接收 converter 和调整后的尺寸
    GridPainter.drawGrid(canvas, gridSize, dbRange, converter, isDarkMode);
  }

  // `drawAxisLabels` 内部逻辑也要使用 converter
  void drawAxisLabels(Canvas canvas, Size size, Map<String, double> dbRange,
      CoordinateConverter converter) {
    // --- 频率轴 (X轴) ---
    // 只在 drawFrequencyLabels 为 true 时绘制频率标签
    if (drawFrequencyLabels) {
      final freqTextStyle = TextStyles.instance.h4().copyWith(
          color:
              ColorPalettes.instance.firstText.withAlpha((isDarkMode ? 180 : 200)),
          fontSize: 9 // 进一步减小字体
          );
      final freqTextPainter = TextPainter(
          textDirection: TextDirection.ltr, textAlign: TextAlign.center);
      final frequencies = [20, 50, 100, 200, 500, 1000, 2000, 5000, 10000, 20000];

      // 为频率标签预留的空间
      final double labelPadding = 15.0;
      // 调整图表绘制区域的高度，为标签预留空间
      final double chartHeight = size.height - labelPadding;

      for (final freq in frequencies) {
        String label = (freq >= 1000) ? '${freq ~/ 1000}k' : '$freq';
        freqTextPainter.text = TextSpan(text: label, style: freqTextStyle);
        freqTextPainter.layout();
        // 使用 converter 获取 X 坐标
        final x = converter.frequencyToX(freq.toDouble());
        // 调整绘制位置，避免与网格重叠
        freqTextPainter.paint(canvas,
            Offset(x - freqTextPainter.width / 2, chartHeight + 5)); // 在图表下方留出足够空间
      }
    }

    // --- 分贝轴 (Y轴) ---
    final dbTextStyle = TextStyles.instance.h4().copyWith(
        color:
            ColorPalettes.instance.firstText.withAlpha((isDarkMode ? 160 : 180)),
        fontSize: 9 // 进一步减小字体
        );
    final dbTextPainter = TextPainter(
        textDirection: TextDirection.ltr, textAlign: TextAlign.right);
    final double minDb = dbRange['minDb']!;
    final double maxDb = dbRange['maxDb']!;
    double dbStep = 5.0; // 可以根据 maxDb - minDb 动态调整
    if (maxDb - minDb > 60) {
      dbStep = 15.0;
    } else if (maxDb - minDb > 40) {
      dbStep = 10.0;
    }

    List<double> decibels = [];
    // 从 0 开始向两边扩展，确保标签是整数或半数
    decibels.add(0.0);
    for (double db = dbStep; db <= maxDb; db += dbStep) {
      if (db <= maxDb) decibels.add(db);
    }
    for (double db = -dbStep; db >= minDb; db -= dbStep) {
      if (db >= minDb) decibels.add(db);
    }
    decibels.sort(); // 排序

    for (final db in decibels) {
      // 只显示整数 dB 标签
      if (db != db.roundToDouble()) continue;

      dbTextPainter.text = TextSpan(text: '${db.toInt()}', style: dbTextStyle);
      dbTextPainter.layout();
      // 使用 converter 获取 Y 坐标
      final y = converter.dbToY(db);
      // 绘制在左侧外部
      dbTextPainter.paint(
          canvas,
          Offset(
              -dbTextPainter.width - 3, y - dbTextPainter.height / 2)); // 稍微向左移
    }
  }

// _findTargetDbForFrequency 现在需要接收原始 targetPoints 列表
  double _findTargetDbForFrequency(
      double freq, List<Map<String, double>> originalTargetPoints) {
    if (originalTargetPoints.isEmpty) return 0.0;
    // 查找逻辑不变，只是数据源变成参数
    for (final data in originalTargetPoints) {
      if (data['frequency'] == freq) {
        return data['db']!; // 确保非空
      }
    }
    double? lowerFreq;
    double? lowerDb;
    double? upperFreq;
    double? upperDb;
    for (final data in originalTargetPoints) {
      final currentFreq = data['frequency']!;
      if (currentFreq < freq &&
          (lowerFreq == null || currentFreq > lowerFreq)) {
        lowerFreq = currentFreq;
        lowerDb = data['db']!;
      }
      if (currentFreq > freq &&
          (upperFreq == null || currentFreq < upperFreq)) {
        upperFreq = currentFreq;
        upperDb = data['db']!;
      }
    }
    if (lowerFreq == null) return upperDb ?? 0.0;
    if (upperFreq == null) return lowerDb ?? 0.0;
    if ((upperFreq - lowerFreq).abs() < 1e-9) return lowerDb ?? 0.0;
    final ratio = (freq - lowerFreq) / (upperFreq - lowerFreq);
    return lowerDb! + ratio * (upperDb! - lowerDb);
  }

  // 移除 EQWavePainter 内部的 _frequencyToX, _log10, _dbToY 方法，统一使用 converter

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    // shouldRepaint 逻辑可以保持不变或根据需要优化
    return super.shouldRepaint(oldDelegate);
  }
}
