import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

import '../../peq_controller.dart';
import 'eq_wave_painter.dart';
import 'frequency_labels_painter.dart';

/// EQ显示区域
class EQDisplayArea extends StatelessWidget {
  /// PEQ控制器
  final PEQController controller;

  /// 可用高度，用于响应式布局
  final double? availableHeight;

  const EQDisplayArea(
      {super.key, required this.controller, this.availableHeight});

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    // 绘制PEQ显示区域的容器 - 移除标题部分
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        // 图表容器
        Container(
          // 使用传入的可用高度或默认值，并使用ScreenUtil适配
          height: availableHeight ?? 140.h,
          width: double.infinity,
          margin: EdgeInsets.only(left: 16.w, right: 16.w, top: 2.h, bottom: 0),
          child: Obx(() {
            // 使用updateTrigger确保图表在值变化时重绘
            controller.updateTrigger.value;

            // 使用CustomPaint自定义绘制
            return CustomPaint(
              // 使用EQWavePainter绘制波形图
              painter: EQWavePainter(
                bands: controller.currentBands,
                showCombined:
                    controller.isDisplayModeActive(controller.displayModes[3]),
                showEachFilter:
                    controller.isDisplayModeActive(controller.displayModes[2]),
                showTarget: controller
                        .isDisplayModeActive(controller.displayModes[0]) &&
                    controller.selectedTargetFile.value != null,
                showSourceFR: controller
                        .isDisplayModeActive(controller.displayModes[1]) &&
                    controller.selectedSourceFRFile.value != null,
                showFilteredFR: controller
                        .isDisplayModeActive(controller.displayModes[4]) &&
                    controller.selectedSourceFRFile.value != null,
                isCompensatedMode: controller.isCompensatedMode.value,
                targetData: controller.selectedTargetFile.value?.data,
                sourceFRData: controller.selectedSourceFRFile.value?.data,
                updateCounter: controller.updateTrigger.value,
                isDarkMode: isDarkMode,
                drawFrequencyLabels: false, // 不在图表内绘制频率标签
              ),
              size: Size.infinite,
            );
          }),
        ),

        // 频率标签容器
        Container(
          height: 20, // 标签高度
          width: double.infinity,
          margin: const EdgeInsets.only(left: 16, right: 16, bottom: 2),
          padding: const EdgeInsets.only(top: 2),
          child: CustomPaint(
            painter: FrequencyLabelsPainter(
              isDarkMode: isDarkMode,
              updateCounter: controller.updateTrigger.value,
            ),
            size: Size.infinite,
          ),
        ),
      ],
    );
  }
}
