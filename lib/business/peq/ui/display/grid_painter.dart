import 'dart:math';

import 'package:flutter/material.dart';
import 'package:topping_home/theme/color_palettes.dart';

import '../../../../common/util/log_util.dart';
// 引入 CoordinateConverter
import '../../core/calculator/coordinate_converter.dart';

/// 网格绘制类
class GridPainter {
  // frequencyGridLines, dbGridLines 保持不变

  // 常用频率点（对数分布）
  static const List<double> frequencyGridLines = [
    20.0,
    30.0,
    40.0,
    50.0,
    60.0,
    70.0,
    80.0,
    90.0,
    100.0,
    200.0,
    300.0,
    400.0,
    500.0,
    600.0,
    700.0,
    800.0,
    900.0,
    1000.0,
    2000.0,
    3000.0,
    4000.0,
    5000.0,
    6000.0,
    7000.0,
    8000.0,
    9000.0,
    10000.0,
    20000.0
  ];

  // 常用dB点
  static const List<double> dbGridLines = [
    -40.0,
    -30.0,
    -20.0,
    -10.0,
    0.0,
    10.0,
    20.0,
    30.0,
    40.0
  ];

  /// 绘制EQ网格 (接收 CoordinateConverter)
  /// 注意: size 应该是调整后的图表尺寸，不包含标签的空间
  static void drawGrid(Canvas canvas, Size size, Map<String, double> dbRange,
      CoordinateConverter converter,
      [bool isDarkMode = true]) {
    // 绘制白色背景
    canvas.drawRect(
      Rect.fromLTWH(0, 0, size.width, size.height),
      Paint()..color = ColorPalettes.instance.pure, // 使用主题中的纯白色
    );

    // 使用主题中的灰色作为网格线颜色
    final Color gridColor = ColorPalettes.instance.thirdText.withAlpha(50);
    final Color mainGridColor = ColorPalettes.instance.thirdText.withAlpha(70);
    final Color zeroDbColor = ColorPalettes.instance.thirdText.withAlpha(100);

    final gridPaint = Paint()
      ..color = gridColor
      ..style = PaintingStyle.stroke
      ..strokeWidth = 0.4; // 统一线宽

    final mainGridPaint = Paint()
      ..color = mainGridColor
      ..style = PaintingStyle.stroke
      ..strokeWidth = 0.5;

    final zeroPaint = Paint()
      ..color = zeroDbColor
      ..style = PaintingStyle.stroke
      ..strokeWidth = 0.7;

    // 绘制 0dB 水平线
    double zeroY = converter.dbToY(0.0);
    canvas.drawLine(Offset(0, zeroY), Offset(size.width, zeroY), zeroPaint);

    // 绘制主要频率垂直线
    List<double> mainFrequencies = [20.0, 100.0, 1000.0, 10000.0, 20000.0];
    for (double freq in mainFrequencies) {
      double x = converter.frequencyToX(freq);
      canvas.drawLine(Offset(x, 0), Offset(x, size.height), mainGridPaint);
    }

    // 绘制次要频率垂直线
    for (double freq in frequencyGridLines) {
      // 使用预定义的 frequencyGridLines
      if (!mainFrequencies.contains(freq)) {
        double x = converter.frequencyToX(freq);
        canvas.drawLine(
            Offset(x, 0), Offset(x, size.height), gridPaint); // 使用普通网格画笔
      }
    }

    // 绘制水平 dB 网格线
    final double minDb = dbRange['minDb']!;
    final double maxDb = dbRange['maxDb']!;
    double dbStep = 5.0; // 可以根据 maxDb - minDb 动态调整
    if (maxDb - minDb > 60) {
      dbStep = 15.0;
    } else if (maxDb - minDb > 40) {
      dbStep = 10.0;
    }

    List<double> dbLines = [];
    // 从 0 开始向两边扩展
    for (double db = dbStep; db <= maxDb; db += dbStep) {
      if (db <= maxDb) dbLines.add(db);
    }
    for (double db = -dbStep; db >= minDb; db -= dbStep) {
      if (db >= minDb) dbLines.add(db);
    }

    for (double db in dbLines) {
      if (db == 0) continue; // 跳过 0dB 线
      double y = converter.dbToY(db);
      // 主要 dB 线 (例如 10 的倍数)
      if (db % 10 == 0) {
        canvas.drawLine(Offset(0, y), Offset(size.width, y), mainGridPaint);
      } else {
        canvas.drawLine(Offset(0, y), Offset(size.width, y), gridPaint);
      }
    }
  }

  // 移除 GridPainter 内部的 _frequencyToX, _log10

  /// 绘制曲线 (实线)
  static void drawCurve(
      Canvas canvas, List<Offset> points, Paint paint, Size size) {
    if (points.isEmpty) {
      Log.i('绘制曲线 - 无点可绘制');
      return;
    }
    // 打印传入的点数量和范围
    Log.i('绘制曲线开始 - 颜色: ${paint.color}, 点数: ${points.length}, '
        '宽度: ${size.width}, 高度: ${size.height}');
    Log.i('X范围: ${points.first.dx.toStringAsFixed(2)} - '
        '${points.last.dx.toStringAsFixed(2)}, '
        'Y范围: ${points.first.dy.toStringAsFixed(2)} - '
        '${points.last.dy.toStringAsFixed(2)}');
    // _logKeyPoints(points, paint, size);
    final path = Path();
    path.moveTo(points.first.dx, points.first.dy);
    for (int i = 1; i < points.length; i++) {
      path.lineTo(points[i].dx, points[i].dy);
    }

    canvas.drawPath(path, paint);
  }

  /// 绘制虚线曲线
  static void drawDashedCurve(Canvas canvas, List<Offset> points, Paint paint) {
    if (points.isEmpty) return;

    // 定义虚线样式
    final dashWidth = 3.0;
    final dashSpace = 3.0;

    double distance = 0.0;
    final path = Path();
    path.moveTo(points.first.dx, points.first.dy);

    for (int i = 1; i < points.length; i++) {
      final Offset p1 = points[i - 1];
      final Offset p2 = points[i];
      final double dx = p2.dx - p1.dx;
      final double dy = p2.dy - p1.dy;
      final double segmentLength = sqrt(dx * dx + dy * dy);

      if (distance < dashWidth) {
        // 绘制实线部分
        if (distance + segmentLength >= dashWidth) {
          // 需要分段
          final double t = (dashWidth - distance) / segmentLength;
          final double splitX = p1.dx + dx * t;
          final double splitY = p1.dy + dy * t;
          path.lineTo(splitX, splitY);

          // 开始绘制空白部分
          distance = dashWidth;
          path.moveTo(splitX, splitY);
        } else {
          // 整段为实线
          path.lineTo(p2.dx, p2.dy);
          distance += segmentLength;
        }
      } else if (distance < dashWidth + dashSpace) {
        // 空白部分
        if (distance + segmentLength >= dashWidth + dashSpace) {
          // 需要分段
          final double t = (dashWidth + dashSpace - distance) / segmentLength;
          final double splitX = p1.dx + dx * t;
          final double splitY = p1.dy + dy * t;

          // 移动到分段点，开始新的实线部分
          path.moveTo(splitX, splitY);
          distance = dashWidth + dashSpace;
        } else {
          // 整段为空白
          distance += segmentLength;
          path.moveTo(p2.dx, p2.dy);
        }
      } else {
        // 重置距离计数器
        distance = 0.0;
        i--; // 重新处理当前点
      }
    }

    canvas.drawPath(path, paint);
  }

  /// 绘制平直线 (接收 Y 坐标)
  static void drawFlatLine(
      Canvas canvas, Size size, Paint paint, double yPosition) {
    canvas.drawLine(Offset(0, yPosition), Offset(size.width, yPosition), paint);
  }
}
