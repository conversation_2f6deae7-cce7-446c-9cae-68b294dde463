import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:topping_home/common/util/i18n.dart';
import 'package:topping_home/theme/color_palettes.dart';
import 'package:topping_home/theme/text_styles.dart';
import '../../peq_controller.dart';
import 'band_row.dart';

/// 频段表格
class BandTable extends StatelessWidget {
  final PEQController controller; // PEQ控制器

  const BandTable({super.key, required this.controller}); // 构造函数

  @override
  Widget build(BuildContext context) {
    // 构建界面
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 标题行 - 使用响应式内边距
        Padding(
          padding: EdgeInsets.fromLTRB(16.w, 6.h, 16.w, 3.h), // 使用screenutil适配内边距
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              // 左侧标题 - 使用与其他页面一致的h3样式
              Text(
                l10n.filter,
                style: TextStyles.instance.h3(
                  color: ColorPalettes.instance.firstText,
                  fontWeight: FontWeight.w600,
                ),
              ),
              // 右侧计数信息
              Obx(() => Text(
                    '${controller.currentBands.length - 1}/${controller.maxBands - 1}',
                    style: TextStyles.instance.h5(
                      color: ColorPalettes.instance.secondText,
                    ),
                  )),
            ],
          ),
        ),
        // 表头和列表
        Expanded(
          child: Padding(
            padding: EdgeInsets.fromLTRB(16.w, 0, 16.w, 6.h), // 使用screenutil适配内边距
            child: Column(
              mainAxisSize: MainAxisSize.min, // 修改为最小化主轴尺寸
              children: [
                _buildTableHeader(), // 构建表头
                const SizedBox(height: 2), // 进一步减小间距
                // 使用Expanded替代SizedBox，让列表高度自适应
                Expanded(
                  child: _buildBandsListWithAddButton(),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  /// 构建表头
  Widget _buildTableHeader() {
    return Container(
      padding:
          EdgeInsets.symmetric(vertical: 6.h, horizontal: 4.w), // 使用screenutil适配内边距
      decoration: BoxDecoration(
        color: ColorPalettes.instance.accent.withValues(alpha: 0.08),
        borderRadius: BorderRadius.circular(4),
      ),
      child: Row(
        children: [
          // 复选框和编号区域（固定宽度）
          const SizedBox(width: 5),
          const SizedBox(width: 20), // 复选框列宽度
          const SizedBox(width: 5),
          const SizedBox(width: 20), // 编号列宽度
          const SizedBox(width: 5),

          // 类型列 - 需与行对齐，缩小宽度
          Expanded(
            flex: 3,
            child: Padding(
              padding: const EdgeInsets.only(left: 4),
              child: Text(
                l10n.type,
                style: TextStyles.instance.h5(
                    color: ColorPalettes.instance.firstText,
                    fontWeight: FontWeight.w600),
              ),
            ),
          ),
          const SizedBox(width: 10), // 添加与行内容相同的间距

          // 频率列 - 需与行对齐，扩大宽度
          Expanded(
            flex: 3,
            child: Text(
              l10n.frequency,
              style: TextStyles.instance.h5(
                  color: ColorPalettes.instance.firstText,
                  fontWeight: FontWeight.w600),
            ),
          ),

          // 增益列 - 需与行对齐
          Expanded(
            flex: 2,
            child: Text(
              l10n.gain,
              style: TextStyles.instance.h5(
                  color: ColorPalettes.instance.firstText,
                  fontWeight: FontWeight.w600),
            ),
          ),
          const SizedBox(width: 10), // 增益和Q值之间添加间距

          // Q值列 - 需与行对齐
          Expanded(
            flex: 2,
            child: Text(
              l10n.q,
              style: TextStyles.instance.h5(
                  color: ColorPalettes.instance.firstText,
                  fontWeight: FontWeight.w600),
            ),
          ),

          // 操作列
          const SizedBox(width: 20),
        ],
      ),
    );
  }

  /// 构建频段列表和添加按钮
  Widget _buildBandsListWithAddButton() {
    return Obx(() {
      controller.updateTrigger.value; // 触发更新
      final bands = controller.currentBands;
      final canAddMore = controller.currentBands.length < controller.maxBands;

      // 使用 Stack 将添加按钮放在列表底部但视觉上分离
      return Stack(
        children: [
          // 波段列表
          ClipRRect(
            borderRadius: BorderRadius.circular(4),
            child: Container(
              decoration: BoxDecoration(
                border: Border.all(
                  color: ColorPalettes.instance.divider,
                  width: 1,
                ),
                borderRadius: BorderRadius.circular(4),
              ),
              child: ListView.builder(
                padding: EdgeInsets.only(bottom: 64.h), // 为底部按钮留出空间
                // 项目数量 = 波段数量（不包括添加按钮）
                itemCount: bands.length,
                itemBuilder: (context, index) {
                  // 显示波段行，使用合并后的 BandRow 组件
                  return BandRow(
                    key: ValueKey('band-${bands[index].id}-$index'),
                    band: bands[index],
                    index: index,
                    controller: controller,
                    allowDelete: index > 0, // 只允许删除非第一个波段（前级）
                  );
                },
              ),
            ),
          ),

          // 添加滤波按钮 - 放在底部中央
          Positioned(
            left: 16.w,
            right: 16.w,
            bottom: 8.h,
            child: _buildAddFilterRow(canAddMore),
          ),
        ],
      );
    });
  }

  /// 构建添加滤波按钮行
  Widget _buildAddFilterRow(bool canAddMore) {
    // 判断当前主题
    final isDarkMode = Theme.of(Get.context!).brightness == Brightness.dark;

    // 根据主题选择适合的颜色
    final Color buttonColor = canAddMore
        ? ColorPalettes.instance.accent
        : ColorPalettes.instance.firstText.withValues(alpha: 0.3);

    final Color textIconColor = canAddMore
        ? (isDarkMode ? ColorPalettes.instance.firstText : ColorPalettes.instance.pure) // 根据暗色模式调整文字颜色
        : ColorPalettes.instance.firstText.withValues(alpha: 0.3);

    // 创建一个更现代、更显眼的按钮
    return Container(
      height: 38, // 增加高度使按钮更显眼
      margin: EdgeInsets.symmetric(vertical: 8.h), // 增加垂直边距
      child: Material(
        color: ColorPalettes.instance.transparent,
        child: InkWell(
          onTap: canAddMore ? () => controller.addNewBand() : null,
          child: Container(
            decoration: BoxDecoration(
              gradient: canAddMore
                  ? LinearGradient(
                      colors: [
                        buttonColor,
                        buttonColor.withValues(alpha: 0.8),
                      ],
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                    )
                  : null,
              color: canAddMore ? null : buttonColor,
              borderRadius: BorderRadius.circular(8), // 更大的圆角
              boxShadow: canAddMore
                  ? [
                      BoxShadow(
                        color: buttonColor.withValues(alpha: 0.3),
                        blurRadius: 8,
                        offset: const Offset(0, 2),
                      ),
                    ]
                  : null,
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.add_circle,
                  color: textIconColor,
                  size: 20, // 增大图标尺寸
                ),
                const SizedBox(width: 10),
                Text(
                  l10n.addFilter,
                  style: TextStyles.instance.h5(
                    color: textIconColor,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
