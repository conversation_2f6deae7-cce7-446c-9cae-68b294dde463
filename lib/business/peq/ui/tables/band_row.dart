import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:topping_home/common/util/i18n.dart';
import 'package:topping_home/theme/color_palettes.dart';
import 'package:topping_home/theme/text_styles.dart';

import '../../../../enums/dx5/dx5_filter_type.dart';
import '../../core/models/peq_band.dart';
import '../../peq_controller.dart';

/// PEQ频段行组件
class BandRow extends StatelessWidget {
  /// PEQ频段数据
  final PEQBand band;

  /// 频段索引
  final int index;

  /// PEQ控制器
  final PEQController controller;

  /// 是否允许删除
  final bool allowDelete;

  /// 构造函数
  const BandRow({
    super.key,
    required this.band,
    required this.index,
    required this.controller,
    this.allowDelete = true, // 默认允许删除
  });

  /// 构建组件
  @override
  Widget build(BuildContext context) {
    return Obx(() {
      final isSelected = controller.selectedBandId.value == band.id;
      return Container(
        margin: const EdgeInsets.only(bottom: 4),
        decoration: BoxDecoration(
          color: isSelected
              ? ColorPalettes.instance.primary.withValues(alpha: 0.08) // 使用主题色但透明度非常低
              : ColorPalettes.instance.transparent,
          borderRadius: BorderRadius.circular(4),
        ),
        child: InkWell(
          onTap: () => controller.selectBand(band.id),
          child: Row(
            children: [
              // 复选框和编号区域（固定宽度）
              const SizedBox(width: 5),
              _buildColorBlock(), // 使用色块替代复选框
              const SizedBox(width: 5),
              _buildBandNumber(),
              const SizedBox(width: 5),

              // 类型列 - 缩小宽度
              Expanded(
                flex: 3,
                child: index == 0
                    ? _buildPreampName() // 前级标签应与类型标题对齐
                    : _buildFilterTypeDropdown(),
              ),
              const SizedBox(width: 10),

              // 频率列 - 扩大宽度，只显示频率值，不显示频率类型
              Expanded(
                flex: 3,
                child: index == 0
                    ? const SizedBox() // 前级行不显示频率
                    : _buildFrequencyValueOnly(),
              ),

              // 增益列
              Expanded(
                flex: 2,
                child: _buildGainText(),
              ),
              const SizedBox(width: 10), // 增益和Q值之间添加间距

              // Q值列
              Expanded(
                flex: 2,
                child: index == 0
                    ? const SizedBox() // 前级行不显示Q值
                    : _buildQText(),
              ),

              // 操作列 - 添加删除按钮
              SizedBox(
                width: 20,
                child: allowDelete // 仅在允许删除时显示删除按钮
                    ? GestureDetector(
                        onTap: () => controller.removeBand(band.id),
                        child: Icon(
                          Icons.close,
                          size: 14,
                          color: ColorPalettes.instance.error.withValues(alpha: 0.7),
                        ),
                      )
                    : const SizedBox(), // 不允许删除时显示空容器
              ),
            ],
          ),
        ),
      );
    });
  }

  /// 构建色块选择器，替代原来的复选框
  Widget _buildColorBlock() {
    return SizedBox(
      width: 20,
      height: 20,
      child: Center(
        child: GestureDetector(
          onTap: () {
            controller.toggleBandEnabled(band.id);
          },
          child: Container(
            width: 16,
            height: 16,
            decoration: BoxDecoration(
              color: band.enabled.value
                  ? ColorPalettes.instance.accent // 使用强调色替代主题色
                  : ColorPalettes.instance.transparent, // 禁用时显示透明
              borderRadius: BorderRadius.circular(2),
              border: Border.all(
                color: band.enabled.value
                    ? ColorPalettes.instance.accent // 使用强调色替代主题色
                    : ColorPalettes.instance.secondText
                        .withValues(alpha: 0.5), // 禁用时使用灰色边框
                width: 1.5,
              ),
            ),
            // 添加勾选标记
            child: band.enabled.value
                ? Center(
                    child: Icon(
                      Icons.check,
                      size: 12,
                      color: ColorPalettes.instance.pure,
                    ),
                  )
                : null,
          ),
        ),
      ),
    );
  }

  /// 构建频段编号
  Widget _buildBandNumber() {
    return SizedBox(
      width: 20,
      child: Text(
        '${index + 1}', // 频段编号从1开始
        style: TextStyles.instance.h5(
          color: band.enabled.value
              ? ColorPalettes.instance.firstText
              : ColorPalettes.instance.firstText.withValues(alpha: 0.3),
        ),
        textAlign: TextAlign.center,
      ),
    );
  }

  /// 构建前级名称
  Widget _buildPreampName() {
    return Padding(
      padding: const EdgeInsets.only(left: 4),
      child: Text(
        l10n.preamplifier, // 固定文本
        style: TextStyles.instance.h5(
          color: band.enabled.value
              ? ColorPalettes.instance.firstText
              : ColorPalettes.instance.firstText.withValues(alpha: 0.3),
        ),
      ),
    );
  }

  /// 构建滤波器类型下拉框
  Widget _buildFilterTypeDropdown() {
    return DropdownButtonHideUnderline(
      child: DropdownButton<Dx5FilterType>(
        value: band.filterType.value,
        isDense: true,
        isExpanded: true,
        iconSize: 12,
        icon: Icon(
          Icons.arrow_drop_down,
          color: band.enabled.value
              ? ColorPalettes.instance.firstText
              : ColorPalettes.instance.firstText.withValues(alpha: 0.3),
        ),
        style: TextStyles.instance.h5(
          color: band.enabled.value
              ? ColorPalettes.instance.firstText
              : ColorPalettes.instance.firstText.withValues(alpha: 0.3),
        ),
        onChanged: band.enabled.value
            ? (Dx5FilterType? value) {
                if (value != null) {
                  controller.updateBandFilterType(band.id, value);
                }
              }
            : null,
        items: Dx5FilterType.values
            .map((type) {
              // 前级不需要显示滤波器类型选择
              if (index == 0) {
                return null; // 返回null会被过滤掉
              }
              return DropdownMenuItem<Dx5FilterType>(
                value: type,
                child: Builder(builder: (context) {
                  return Text(
                    type.localized(context),
                    style: TextStyles.instance.h5(
                      color: band.enabled.value
                          ? ColorPalettes.instance.firstText
                          : ColorPalettes.instance.firstText.withValues(alpha: 0.3),
                    ),
                    overflow: TextOverflow.ellipsis,
                  );
                }),
              );
            })
            .whereType<DropdownMenuItem<Dx5FilterType>>()
            .toList(), // 过滤null值
      ),
    );
  }

  /// 构建只显示频率值的组件
  Widget _buildFrequencyValueOnly() {
    return Text(
      '${band.frequency.value.toStringAsFixed(1)} Hz',
      style: TextStyles.instance.h5(
        color: band.enabled.value
            ? ColorPalettes.instance.firstText
            : ColorPalettes.instance.firstText.withValues(alpha: 0.3),
      ),
      textAlign: TextAlign.left,
      overflow: TextOverflow.ellipsis,
    );
  }

  /// 构建增益文本
  Widget _buildGainText() {
    return Text(
      '${band.gain.value.toStringAsFixed(1)} dB',
      style: TextStyles.instance.h5(
        color: band.enabled.value
            ? ColorPalettes.instance.firstText
            : ColorPalettes.instance.firstText.withValues(alpha: 0.3),
      ),
      textAlign: TextAlign.left,
      overflow: TextOverflow.ellipsis,
    );
  }

  /// 构建Q值文本
  Widget _buildQText() {
    return Text(
      band.q.value.toStringAsFixed(2),
      style: TextStyles.instance.h5(
        color: band.enabled.value
            ? ColorPalettes.instance.firstText
            : ColorPalettes.instance.firstText.withValues(alpha: 0.3),
      ),
      textAlign: TextAlign.left,
      overflow: TextOverflow.ellipsis,
    );
  }
}
