import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:topping_home/business/peq/ui/controls/parameter_knob.dart';
import 'package:topping_home/common/util/i18n.dart';
import 'package:topping_home/theme/color_palettes.dart';
import '../../../../enums/dx5/dx5_frequency_type.dart';
import '../../peq_controller.dart';

/// 参数旋钮组件
class ParameterKnobs extends StatelessWidget {
  // PEQ控制器
  final PEQController controller;

  // 构造函数
  const ParameterKnobs({super.key, required this.controller});

  @override
  Widget build(BuildContext context) {
    // 使用Obx观察选中波段变化
    return Obx(() {
      // 获取选中的波段ID和索引
      final selectedBandId = controller.selectedBandId.value;
      final bands = controller.currentBands;
      final selectedBandIndex =
          bands.indexWhere((band) => band.id == selectedBandId);

      // 如果没有找到选中的波段，显示默认参数调节器而不是返回空
      bool showDefault = selectedBandIndex == -1;

      // 获取选中的波段或默认使用第一个波段的设置
      final selectedBand = showDefault && bands.isNotEmpty
          ? bands[0] // 如果没有选中波段，默认使用第一个波段的设置
          : showDefault
              ? null
              : bands[selectedBandIndex];

      // 如果没有波段，返回空容器
      if (bands.isEmpty) {
        return const SizedBox();
      }

      // 检查是否选中了前级
      final isPreampSelected = !showDefault && selectedBandIndex == 0;

      // 检查是否是拐点频率（如果是，则需要禁用增益旋钮）
      final isCornerFrequency = selectedBand != null &&
          selectedBand.frequencyType.value == Dx5FrequencyType.cornerFrequency;

      // 判断哪些旋钮可操作（而不是隐藏）
      final canAdjustFrequency = showDefault || !isPreampSelected;
      final canAdjustGain = true; // 增益始终可调整，即使是拐点频率
      final canAdjustQ = showDefault || !isPreampSelected;

      return Container(
        padding:
            const EdgeInsets.symmetric(horizontal: 12, vertical: 4), // 减小内边距
        child: GestureDetector(
          // 阻止滚动事件冒泡
          onVerticalDragUpdate: (_) {},
          // 阻止水平滚动
          onHorizontalDragUpdate: (_) {},
          // 确保事件不会传递到下层滚动视图
          behavior: HitTestBehavior.translucent,
          child: AbsorbPointer(
            // 只有在旋钮被操作时才吸收事件
            absorbing: false,
            child: NotificationListener<ScrollNotification>(
              // 拦截所有滚动通知
              onNotification: (notification) => true,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly, // 均匀分布按钮
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // 频率旋钮 - 始终显示，但前级时禁用
                  Expanded(
                    child: ParameterKnob(
                      paramName: l10n.frequency,
                      color: ColorPalettes.instance.success, // 使用绿色
                      controller: controller,
                      enabled: canAdjustFrequency,
                    ),
                  ),

                  // 增益旋钮 - 始终显示，即使是拐点频率
                  Expanded(
                    child: ParameterKnob(
                      paramName: l10n.gain,
                      color: ColorPalettes.instance.success, // 使用绿色
                      controller: controller,
                      enabled: canAdjustGain &&
                          (!isCornerFrequency || isPreampSelected),
                    ),
                  ),

                  // Q值旋钮 - 始终显示，但前级时禁用
                  Expanded(
                    child: ParameterKnob(
                      paramName: l10n.q,
                      color: ColorPalettes.instance.success, // 使用绿色
                      controller: controller,
                      enabled: canAdjustQ,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      );
    });
  }
}
