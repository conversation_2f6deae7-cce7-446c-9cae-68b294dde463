import 'dart:math';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:topping_home/common/util/i18n.dart';
import 'package:topping_home/theme/color_palettes.dart';
import 'package:topping_home/theme/text_styles.dart';

import '../../core/models/peq_band.dart';
import '../../peq_controller.dart';

/// 参数控制按钮
class ParameterKnob extends StatefulWidget {
  /// 参数名称（Freq/Gain/Q）
  final String paramName;

  /// 按钮颜色
  final Color color;

  /// PEQ控制器
  final PEQController controller;

  /// 是否启用旋钮
  final bool enabled;

  const ParameterKnob({
    super.key,
    required this.paramName,
    required this.color,
    required this.controller,
    this.enabled = true,
  });

  @override
  State<ParameterKnob> createState() => _ParameterKnobState();
}

class _ParameterKnobState extends State<ParameterKnob> {
  // 是否正在调整
  bool _isAdjusting = false;

  // 上次触摸位置
  double? _lastTouchY;

  // 调整灵敏度
  final double _sensitivity = 0.5;

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      // 获取当前选中波段的参数值
      String valueText = '';

      // 默认显示值
      if (widget.controller.selectedBandId.value < 0) {
        if (widget.paramName == l10n.frequency) {
          valueText = '1000.0 Hz';
        } else if (widget.paramName == l10n.gain) {
          valueText = '0.0 dB';
        } else if (widget.paramName == l10n.q) {
          valueText = '1.00';
        }
      } else {
        final bandList = widget.controller.currentBands;
        // 查找与selectedBandId匹配的波段，而不是直接使用selectedBandId作为索引
        final selectedBandIndex = bandList.indexWhere(
            (band) => band.id == widget.controller.selectedBandId.value);

        if (selectedBandIndex >= 0) {
          final selectedBand = bandList[selectedBandIndex];
          _calculateKnobValues(selectedBand, widget.paramName, (text, _) {
            valueText = text;
          });
        }
      }

      return SizedBox(
        // 为整个组件设置一个固定的最小高度，确保有足够空间容纳所有内容
        height: 105, // 略微减小总高度
        width: 90, // 保持宽度
        child: Column(
          mainAxisSize: MainAxisSize.min,
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            // 使用GestureDetector来捕获上下手势
            GestureDetector(
              onVerticalDragStart: widget.enabled ? _onDragStart : null,
              onVerticalDragUpdate: widget.enabled ? _onDragUpdate : null,
              onVerticalDragEnd: widget.enabled ? _onDragEnd : null,
              onTap: widget.enabled ? () {
                widget.controller.setEditParam(widget.paramName);
                HapticFeedback.selectionClick();
              } : null,
              // 确保所有事件都被此组件消费
              behavior: HitTestBehavior.opaque,
              child: Container(
                width: 60,
                height: 60,
                decoration: BoxDecoration(
                  color: ColorPalettes.instance.transparent,
                  shape: BoxShape.circle,
                ),
                child: _buildKnobVisual(),
              ),
            ),
            const SizedBox(height: 2), // 使用固定的小间距，比Spacer更紧凑
            SizedBox(
              height: 16, // 减小固定高度
              width: 90, // 保持宽度
              child: FittedBox(
                fit: BoxFit.scaleDown,
                child: Text(
                  valueText,
                  style: TextStyles.instance.h4(
                    color: widget.enabled ? ColorPalettes.instance.firstText : ColorPalettes.instance.firstText.withValues(alpha: 0.3),
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
            ),
            const SizedBox(height: 1), // 最小间距
            SizedBox(
              height: 16, // 减小固定高度
              width: 90, // 保持宽度
              child: FittedBox(
                fit: BoxFit.scaleDown,
                child: Text(
                  widget.paramName,
                  style: TextStyles.instance.h4(
                    color: !widget.enabled
                        ? ColorPalettes.instance.secondText.withValues(alpha: 0.3)
                        : widget.controller.currentEditParam.value == widget.paramName
                            ? widget.color
                            : ColorPalettes.instance.secondText,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
            ),
          ],
        ),
      );
    });
  }

  // 处理拖动开始事件
  void _onDragStart(DragStartDetails details) {
    setState(() {
      _isAdjusting = true;
      _lastTouchY = details.localPosition.dy;
    });
    // 触觉反馈
    HapticFeedback.lightImpact();
  }

  // 处理拖动更新事件
  void _onDragUpdate(DragUpdateDetails details) {
    if (!_isAdjusting || _lastTouchY == null) return;

    // 计算垂直方向上的移动距离
    final currentY = details.localPosition.dy;
    final deltaY = _lastTouchY! - currentY; // 向上为正，向下为负

    // 避免微小抖动
    if (deltaY.abs() > 1.0) {
      // 更新参数值
      if (widget.controller.selectedBandId.value >= 0) {
        final bandList = widget.controller.currentBands;
        // 查找与selectedBandId匹配的波段，而不是直接使用selectedBandId作为索引
        final selectedBandIndex = bandList.indexWhere(
            (band) => band.id == widget.controller.selectedBandId.value);

        if (selectedBandIndex >= 0) {
          final selectedBand = bandList[selectedBandIndex];
          _updateParameterValue(
              selectedBand, widget.paramName, deltaY * _sensitivity);
        }
      }

      // 更新上次位置
      _lastTouchY = currentY;

      // 大幅度调整时提供触觉反馈
      if (deltaY.abs() > 5.0) {
        HapticFeedback.selectionClick();
      }
    }
  }

  // 处理拖动结束事件
  void _onDragEnd(DragEndDetails details) {
    setState(() {
      _isAdjusting = false;
      _lastTouchY = null;
    });
  }

  /// 计算旋钮显示值
  void _calculateKnobValues(
      PEQBand band, String paramName, Function(String, double) setValues) {
    String valueText = '';
    double normalizedValue = 0.0; // 用于显示，范围在0-1之间

    if (paramName == l10n.frequency) {
      valueText = '${band.frequency.value.toStringAsFixed(1)} Hz';
      // 对数比例，将20-20000映射到0-1
      final minFreq = log(20);
      final maxFreq = log(20000);
      final currentFreq = log(band.frequency.value);
      normalizedValue = (currentFreq - minFreq) / (maxFreq - minFreq);
    } else if (paramName == l10n.gain) {
      valueText = '${band.gain.value.toStringAsFixed(1)} dB';
      // 线性比例，将-12到12映射到0-1
      normalizedValue = (band.gain.value + 12) / 24;
    } else if (paramName == l10n.q) {
      valueText = band.q.value.toStringAsFixed(2);
      // 对数比例，将0.1-10映射到0-1
      final minQ = log(0.1);
      final maxQ = log(10);
      final currentQ = log(band.q.value);
      normalizedValue = (currentQ - minQ) / (maxQ - minQ);
    }

    setValues(valueText, normalizedValue);
  }

  /// 根据垂直拖动更新参数值
  void _updateParameterValue(PEQBand band, String paramName, double deltaY) {
    if (paramName == l10n.frequency) {
      // 频率使用指数变化，使小值变化更精细
      final sensitivity = 0.01;
      final factor = exp(deltaY * sensitivity);
      final newFreq = band.frequency.value * factor;
      final clampedFreq = newFreq.clamp(20.0, 20000.0);
      widget.controller.updateBandFrequency(band.id, clampedFreq);
    } else if (paramName == l10n.gain) {
      // 增益使用线性变化
      final sensitivity = 0.1;
      final newGain = band.gain.value + (deltaY * sensitivity);
      final clampedGain = newGain.clamp(-12.0, 12.0);
      widget.controller.updateBandGain(band.id, clampedGain);
    } else if (paramName == l10n.q) {
      // Q值使用指数变化
      final sensitivity = 0.01;
      final factor = exp(deltaY * sensitivity);
      final newQ = band.q.value * factor;
      final clampedQ = newQ.clamp(0.1, 15.0);
      widget.controller.updateBandQ(band.id, clampedQ);
    }
  }

  /// 构建旋钮的视觉外观
  Widget _buildKnobVisual() {

    // 获取当前参数的标准化值 (0.0-1.0)
    double progressValue = 0.5; // 默认中间位置

    if (widget.controller.selectedBandId.value >= 0) {
      final bandList = widget.controller.currentBands;
      // 查找与selectedBandId匹配的波段，而不是直接使用selectedBandId作为索引
      final selectedBandIndex = bandList.indexWhere(
          (band) => band.id == widget.controller.selectedBandId.value);

      if (selectedBandIndex >= 0) {
        final selectedBand = bandList[selectedBandIndex];
        _calculateKnobValues(selectedBand, widget.paramName, (_, value) {
          progressValue = value;
        });
      }
    }

    // 计算指示器角度 - 从左上135度开始，顺时针方向
    final startAngle = 135 * pi / 180; // 左上方 (135度)
    final maxSweepAngle = 270 * pi / 180; // 最大270度弧（从左上到右上）
    final progressAngle = startAngle + (progressValue * maxSweepAngle);

    // 定义尺寸常量
    const containerSize = 60.0; // 整个容器大小
    const innerCircleSize = 36.0; // 内圆大小
    const outerRingSize = 42.0; // 外环大小
    const progressRingSize = 56.0; // 进度环大小 - 比外环大，留出间距

    // 三角形指示器位置计算
    final indicatorRadius = innerCircleSize / 2 - 2.0; // 使指示器位于内圆边缘
    final indicatorX = indicatorRadius * cos(progressAngle) + containerSize / 2;
    final indicatorY = indicatorRadius * sin(progressAngle) + containerSize / 2;

    return SizedBox(
      width: containerSize,
      height: containerSize,
      child: Stack(
        alignment: Alignment.center,
        children: [
          // 最外层进度弧 - 与外环有间距
          CustomPaint(
            size: Size(progressRingSize, progressRingSize),
            painter: ProgressArcPainter(
              progressValue: progressValue,
              progressColor: widget.enabled ? widget.color : ColorPalettes.instance.secondText.withValues(alpha: 0.3),
              isSelected: (widget.controller.currentEditParam.value == widget.paramName) && widget.enabled,
            ),
          ),

          // 外环 - 紧贴内圆
          Container(
            width: outerRingSize,
            height: outerRingSize,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              color: ColorPalettes.instance.knobOuterRing,
            ),
          ),

          // 内圆
          Container(
            width: innerCircleSize,
            height: innerCircleSize,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              color: widget.enabled ? ColorPalettes.instance.knobInnerCircle : ColorPalettes.instance.knobInnerCircle.withValues(alpha: 0.5),
              boxShadow: _isAdjusting && widget.enabled
                  ? [
                      BoxShadow(
                        color: widget.color.withValues(alpha: 0.3),
                        blurRadius: 8,
                        spreadRadius: 2,
                      ),
                    ]
                  : null,
            ),
          ),

          // 三角形指示器
          Positioned(
            left: indicatorX - 3,
            top: indicatorY - 3,
            child: Transform.rotate(
              angle: progressAngle + pi / 2, // 旋转三角形
              child: CustomPaint(
                size: const Size(6, 6),
                painter: TriangleIndicatorPainter(
                  color: widget.enabled ? ColorPalettes.instance.knobIndicator : ColorPalettes.instance.knobIndicator.withValues(alpha: 0.3),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}

/// 进度弧绘制器
class ProgressArcPainter extends CustomPainter {
  final double progressValue;
  final Color progressColor;
  final bool isSelected;

  ProgressArcPainter({
    required this.progressValue,
    required this.progressColor,
    this.isSelected = false,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final center = Offset(size.width / 2, size.height / 2);
    final radius = size.width / 2 - 3.0; // 稍微缩小半径以避免边缘被裁剪

    // 绘制进度弧
    final startAngle = 135 * pi / 180; // 从左上方开始 (135度)
    final maxSweepAngle = 270 * pi / 180; // 最大270度弧（从左上到右上）
    final sweepAngle = progressValue * maxSweepAngle;

    final paint = Paint()
      ..color = progressColor
      ..style = PaintingStyle.stroke
      ..strokeWidth = 2.0 // 统一使用相同宽度，移除isSelected条件
      ..strokeCap = StrokeCap.round;

    canvas.drawArc(
      Rect.fromCircle(center: center, radius: radius),
      startAngle,
      sweepAngle,
      false,
      paint,
    );
  }

  @override
  bool shouldRepaint(covariant ProgressArcPainter oldDelegate) {
    return oldDelegate.progressValue != progressValue ||
        oldDelegate.isSelected != isSelected;
  }
}

/// 三角形指示器绘制器
class TriangleIndicatorPainter extends CustomPainter {
  final Color color;

  TriangleIndicatorPainter({required this.color});

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = color
      ..style = PaintingStyle.fill;

    final path = Path()
      ..moveTo(size.width / 2, 0)
      ..lineTo(0, size.height)
      ..lineTo(size.width, size.height)
      ..close();

    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(covariant TriangleIndicatorPainter oldDelegate) {
    return oldDelegate.color != color;
  }
}
