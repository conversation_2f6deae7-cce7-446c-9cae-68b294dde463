import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:topping_home/common/util/i18n.dart';
import 'package:topping_home/common/util/log_util.dart';
import 'package:topping_home/theme/color_palettes.dart';
import 'package:topping_home/theme/text_styles.dart';

import '../../core/models/peq_modes.dart';
import '../../peq_controller.dart';

/// PEQ模式选择器
class PEQModeSelector extends StatelessWidget {
  final PEQController controller;

  const PEQModeSelector({super.key, required this.controller});

  /// 构建组件
  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        // 根据可用宽度计算高度，增加高度确保按钮显示完整
        final height = constraints.maxWidth * 0.08;

        return SizedBox(
          height: height.clamp(36.h, 48.h), // 增加最小和最大高度限制
          child: Padding(
            padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 0), // 使用screenutil适配内边距
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(
                  l10n.mode,
                  style: TextStyles.instance.h3(),
                ),
                SizedBox(width: 6.w), // 使用screenutil适配间距
                _buildModeDropdown(),

                // 添加PEQ开关按钮在中间
                const Spacer(flex: 1),

                // 仅当模式为L或R时显示快速切换按钮
                Obx(() => (controller.peqMode.value == PEQModes.leftChannel ||
                        controller.peqMode.value == PEQModes.rightChannel)
                    ? _buildQuickToggleButtons()
                    : const SizedBox.shrink()),
                const Spacer(flex: 1),
                _buildPeqToggleButton(),
              ],
            ),
          ),
        );
      },
    );
  }

  /// 构建模式下拉菜单
  Widget _buildModeDropdown() {
    return LayoutBuilder(
      builder: (context, constraints) {
        final containerHeight = constraints.maxHeight * 0.9; // 使用可用高度的90%

        return Obx(() {
          // 下拉菜单选项使用UI显示模式，而不是处理模式
          final dropdownOptions = PEQModes.uiModes;

          return Container(
            height: containerHeight,
            alignment: Alignment.center, // 垂直居中
            decoration: BoxDecoration(
              color: ColorPalettes.instance.card,
              borderRadius: BorderRadius.circular(4.r),
            ),
            padding: EdgeInsets.symmetric(horizontal: 6.w),
            child: DropdownButton<String>(
              // 正确表示当前模式
              value: (controller.peqMode.value == PEQModes.leftChannel ||
                      controller.peqMode.value == PEQModes.rightChannel)
                  ? PEQModes.separateChannelsDisplay
                  : PEQModes.allChannelsDisplay,
              dropdownColor: ColorPalettes.instance.card,
              underline: Container(),
              // 移除下划线
              icon: Icon(Icons.arrow_drop_down,
                  color: ColorPalettes.instance.firstText, size: 16.sp),
              style: TextStyles.instance.h3(),
              isDense: true, // 使用紧凑模式减小高度
          onChanged: (String? newValue) {
            Log.e('newValue: $newValue');
            if (newValue != null) {
              if (newValue == PEQModes.separateChannelsDisplay) {
                // 当选择L/R时，保持当前L或R模式，或默认为L
                final currentMode = controller.peqMode.value;
                if (currentMode != PEQModes.leftChannel &&
                    currentMode != PEQModes.rightChannel) {
                  controller.switchPEQMode(PEQModes.leftChannel);
                }
              } else {
                // 当选择L+R时，切换到LR模式
                controller.switchPEQMode(PEQModes.allChannels);
              }
            }
          },
          items: dropdownOptions.map<DropdownMenuItem<String>>((String value) {
            return DropdownMenuItem<String>(
              value: value,
              child: Text(value, style: TextStyles.instance.h3()),
            );
          }).toList(),
            ),
          );
        });
      },
    );
  }

  /// 构建快速切换按钮 (仅L和R按钮)
  Widget _buildQuickToggleButtons() {
    return LayoutBuilder(
      builder: (context, constraints) {
        // 计算按钮宽度和高度，确保它们能完全显示
        final buttonWidth = constraints.maxWidth * 0.15;
        final horizontalPadding = buttonWidth * 0.2;
        final buttonHeight = constraints.maxHeight * 0.8; // 使用可用高度的80%

        return Container(
          height: buttonHeight,
          decoration: BoxDecoration(
            color: ColorPalettes.instance.card,
            borderRadius: BorderRadius.circular(4.r),
          ),
          child: Obx(() => Row(
                mainAxisSize: MainAxisSize.min, // 确保按钮只占用必要的空间
                children: [
                  GestureDetector(
                    onTap: () {
                      controller.switchPEQMode(PEQModes.leftChannel);
                    },
                    child: Container(
                      height: buttonHeight,
                      alignment: Alignment.center, // 垂直居中
                      padding: EdgeInsets.symmetric(
                          horizontal: horizontalPadding.clamp(6.0, 10.0),
                          vertical: 0), // 移除垂直内边距，使用容器高度和居中对齐
                      decoration: BoxDecoration(
                        color: controller.peqMode.value == PEQModes.leftChannel
                            ? ColorPalettes.instance.accent
                            : ColorPalettes.instance.transparent,
                        border: controller.peqMode.value != PEQModes.leftChannel
                            ? Border.all(
                                color: ColorPalettes.instance.secondText
                                    .withValues(alpha: 0.5),
                                width: 1)
                            : null,
                        borderRadius: BorderRadius.only(
                          topLeft: Radius.circular(4.r),
                          bottomLeft: Radius.circular(4.r),
                        ),
                      ),
                      child: Text(
                        'L',
                        style: controller.peqMode.value == PEQModes.leftChannel
                            ? TextStyles.instance.h3()
                            : TextStyles.instance.h4(),
                      ),
                    ),
                  ),
                  GestureDetector(
                    onTap: () {
                      controller.switchPEQMode(PEQModes.rightChannel);
                    },
                    child: Container(
                      height: buttonHeight,
                      alignment: Alignment.center, // 垂直居中
                      padding: EdgeInsets.symmetric(
                          horizontal: horizontalPadding.clamp(6.0, 10.0),
                          vertical: 0), // 移除垂直内边距
                      decoration: BoxDecoration(
                        color: controller.peqMode.value == PEQModes.rightChannel
                            ? ColorPalettes.instance.accent
                            : ColorPalettes.instance.transparent,
                        border: controller.peqMode.value != PEQModes.rightChannel
                            ? Border.all(
                                color: ColorPalettes.instance.secondText
                                    .withValues(alpha: 0.5),
                                width: 1)
                            : null,
                        borderRadius: BorderRadius.only(
                          topRight: Radius.circular(4.r),
                          bottomRight: Radius.circular(4.r),
                        ),
                      ),
                      child: Text(
                        'R',
                        style: controller.peqMode.value == PEQModes.rightChannel
                            ? TextStyles.instance.h3()
                            : TextStyles.instance.h4(),
                      ),
                    ),
                  ),
                ],
              )),
        );
      },
    );
  }

  /// 构建PEQ开关按钮
  Widget _buildPeqToggleButton() {
    return LayoutBuilder(
      builder: (context, constraints) {
        // 计算开关的缩放比例，确保它能完全显示
        final scale = (constraints.maxWidth * 0.05).clamp(0.7, 0.9); // 增加缩放比例
        final containerHeight = constraints.maxHeight * 0.9; // 使用可用高度的90%

        return Container(
          height: containerHeight,
          alignment: Alignment.center, // 垂直居中
          child: Obx(() => Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    'PEQ',
                    style: TextStyles.instance.h3(),
                  ),
                  SizedBox(width: 4.w),
                  // 使用Switch组件替代文字按钮
                  Transform.scale(
                    scale: scale, // 动态计算缩放比例
                    child: Switch(
                      value: controller.isEnabled.value,
                      onChanged: (value) => controller.toggleEnabled(),
                      activeColor: ColorPalettes.instance.accent, // 使用强调色替代主题色
                    ),
                  ),
                ],
              )),
        );
      },
    );
  }
}
