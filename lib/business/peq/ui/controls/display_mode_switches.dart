import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:topping_home/common/util/i18n.dart';
import 'package:topping_home/theme/color_palettes.dart';
import 'package:topping_home/theme/text_styles.dart';

import '../../peq_controller.dart';
import '../display/eq_wave_painter.dart';

/// 显示模式切换开关 - 仅文字变色版，居中对齐
class DisplayModeSwitches extends StatelessWidget {
  final PEQController controller;

  const DisplayModeSwitches({super.key, required this.controller});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 0),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // 第一行 - 三个按钮，均匀分布
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              Expanded(
                  child: _buildTextModeSwitch(
                      l10n.target, controller.displayModes[0], EQWavePainter.targetCurveColor)),
              Expanded(
                  child: _buildTextModeSwitch(
                      l10n.sourceFR, controller.displayModes[1], EQWavePainter.sourceFRCurveColor)),
              Expanded(
                  child: _buildTextModeSwitch(
                      l10n.eachFilter, controller.displayModes[2])),
            ],
          ),
          SizedBox(height: 2.h), // 添加响应式间距
          // 第二行 - 三个按钮，均匀分布
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              Expanded(
                  child: _buildTextModeSwitch(
                      l10n.combinedFilter, controller.displayModes[3], EQWavePainter.combinedCurveColor)),
              Expanded(
                  child: _buildTextModeSwitch(
                      l10n.filteredFR, controller.displayModes[4], EQWavePainter.filteredFRCurveColor)),
              Expanded(child: _buildTextRawCompensatedToggle()),
            ],
          ),
        ],
      ),
    );
  }

  /// 构建纯文字模式切换开关，居中对齐
  Widget _buildTextModeSwitch(String displayName, String mode, [Color? activeColor]) {
    return Obx(() {
      final bool isActive = controller.activeDisplayModes.contains(mode);
      return InkWell(
        onTap: () => controller.toggleDisplayMode(mode),
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 4),
          child: Text(
            displayName,
            textAlign: TextAlign.center,
            style: TextStyles.instance.h4(
              color: isActive
                  ? activeColor ?? ColorPalettes.instance.accent // 使用传入的颜色或默认强调色
                  : ColorPalettes.instance.firstText.withValues(alpha: 0.59),
              fontWeight: isActive ? FontWeight.w500 : FontWeight.normal,
            ),
          ),
        ),
      );
    });
  }

  /// 构建纯文字Raw/Compensated切换按钮，居中对齐
  Widget _buildTextRawCompensatedToggle() {
    return Obx(() {
      final bool isCompensated = controller.isCompensatedMode.value;
      final String currentText = isCompensated ? l10n.compensated : l10n.raw;

      return InkWell(
        onTap: () {
          controller.setCompensatedMode(!isCompensated);
        },
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 4),
          child: Text(
            currentText,
            textAlign: TextAlign.center,
            style: TextStyles.instance.h4(
              color: ColorPalettes.instance.success, // 使用绿色
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
      );
    });
  }
}