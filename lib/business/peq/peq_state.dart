import 'package:get/get.dart';
import 'package:topping_home/common/util/i18n.dart';

import 'core/models/imported_file.dart';
import 'core/models/peq_modes.dart';

/// UI状态管理器
class UIStateManager {
  // PEQ开关状态
  final RxBool isEnabled = false.obs;

  // PEQ模式 - 左或右声道
  final Rx<String> peqMode = PEQModes.allChannels.obs;

  // 可用模式
  final List<String> peqModes = PEQModes.processingModes;
  final List<String> realTimeModes = PEQModes.processingModes;

  // 显示模式（允许多选）
  final RxList<String> activeDisplayModes = <String>[].obs;

  // 可用显示模式选项
  final List<String> displayModes = [
    l10n.target,
    l10n.sourceFR,
    l10n.eachFilter,
    l10n.combinedFilter,
    l10n.filteredFR
  ];

  // Raw/Compensated模式切换
  final RxBool isCompensatedMode = false.obs;

  // 当前选中的波段ID
  final RxInt selectedBandId = (-1).obs;

  // 当前编辑参数
  final Rx<String> currentEditParam = 'frequency'.obs;

  // 当前选择的目标文件和源频响文件
  final Rx<ImportedFile?> selectedTargetFile = Rx<ImportedFile?>(null);
  final Rx<ImportedFile?> selectedSourceFRFile = Rx<ImportedFile?>(null);

  // 角度跟踪变量
  double? lastAngle;

  // 重置角度
  void resetAngle() {
    lastAngle = null;
  }

  // 切换PEQ开关
  void toggleEnabled() {
    isEnabled.value = !isEnabled.value;
  }

  // 切换PEQ模式（左/右声道）
  void switchPEQMode(String mode) {
    if (PEQModes.processingModes.contains(mode)) {
      peqMode.value = mode;
    }
  }

  // 切换显示模式（多选）
  void toggleDisplayMode(String mode) {
    if (activeDisplayModes.contains(mode)) {
      activeDisplayModes.remove(mode);
    } else {
      activeDisplayModes.add(mode);
    }
  }

  // 设置Raw/Compensated模式
  void setCompensatedMode(bool isCompensated) {
    isCompensatedMode.value = isCompensated;
  }

  // 检查显示模式是否激活
  bool isDisplayModeActive(String mode) {
    return activeDisplayModes.contains(mode);
  }

  // 设置当前编辑的参数类型
  void setEditParam(String param) {
    currentEditParam.value = param;
  }

  // 选择波段
  void selectBand(int bandId) {
    resetAngle();
    selectedBandId.value = bandId;
  }

  // 选择目标文件
  void selectTargetFile(ImportedFile? file) {
    selectedTargetFile.value = file;
  }

  // 选择源频响文件
  void selectSourceFRFile(ImportedFile? file) {
    selectedSourceFRFile.value = file;
  }
}
