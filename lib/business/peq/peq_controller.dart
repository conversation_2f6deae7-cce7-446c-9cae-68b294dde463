import 'package:get/get.dart';
import 'package:topping_home/router/routers.dart';
import '../../common/util/log_util.dart';

import '../../common/util/debouncer.dart';
import '../../enums/dx5/dx5_filter_type.dart';
import '../../enums/dx5/dx5_frequency_type.dart';
import '../../models/peq_config_model.dart';
import '../../repositories/peq_repository.dart';
import '../../theme/color_palettes.dart';
import 'core/calculator/curve_points_generator.dart';
import 'core/models/imported_file.dart';
import 'core/models/peq_band.dart';
import 'core/models/peq_modes.dart';
import 'core/services/file_dialog_service.dart';
import 'core/services/filter_calculator_wrapper.dart';
import 'core/services/peq_band_service.dart';
import 'core/services/peq_file_service.dart';
import 'peq_state.dart';

/// PEQ页面逻辑类
class PEQController extends GetxController {
  // 设备ID
  final String deviceId;

  // 依赖注入的服务和仓库
  final PEQRepository _peqRepository;
  final PEQBandService _bandService;
  final PEQFileService _fileService;
  final UIStateManager _uiStateManager;
  final FileDialogService _dialogService;

  PEQController({
    required this.deviceId,
    required PEQRepository peqRepository,
    required PEQBandService bandService,
    required FilterCalculatorWrapper filterCalculator,
    required PEQFileService fileService,
    required UIStateManager uiStateManager,
    required FileDialogService dialogService,
  })  : _peqRepository = peqRepository,
        _bandService = bandService,
        _fileService = fileService,
        _uiStateManager = uiStateManager,
        _dialogService = dialogService;

  final _updateDebouncer = Debouncer(milliseconds: 100);

  // 是否启用
  RxBool get isEnabled => _uiStateManager.isEnabled;

  // PEQ模式
  Rx<String> get peqMode => _uiStateManager.peqMode;

  // 激活的显示模式
  RxList<String> get activeDisplayModes => _uiStateManager.activeDisplayModes;

  // 显示模式
  List<String> get displayModes => _uiStateManager.displayModes;

  // 是否补偿模式
  RxBool get isCompensatedMode => _uiStateManager.isCompensatedMode;

  // 滤波器类型
  List<Dx5FilterType> get filterTypes => Dx5FilterType.values;

  // 左声道波段
  RxList<PEQBand> get leftBands => _bandService.leftBands;

  // 右声道波段
  RxList<PEQBand> get rightBands => _bandService.rightBands;

  // 所以波段
  RxList<PEQBand> get allBands => _bandService.allBands;

  // 选中的波段ID
  RxInt get selectedBandId => _uiStateManager.selectedBandId;

  // 当前编辑参数
  Rx<String> get currentEditParam => _uiStateManager.currentEditParam;

  // 目标文件
  RxList<ImportedFile> get targetFiles => _fileService.targetFiles;

  // 源FR文件
  RxList<ImportedFile> get sourceFRFiles => _fileService.sourceFRFiles;

  // 选中的目标文件
  Rx<ImportedFile?> get selectedTargetFile =>
      _uiStateManager.selectedTargetFile;

  // 选中的源FR文件
  Rx<ImportedFile?> get selectedSourceFRFile =>
      _uiStateManager.selectedSourceFRFile;

  // 前级增益
  RxInt get preampGain => _bandService.preampGain;

  // 当前波段
  List<PEQBand> get currentBands {
    final currentMode = peqMode.value;
    if (currentMode == PEQModes.allChannels) {
      return allBands;
    }
    return currentMode == PEQModes.leftChannel ? leftBands : rightBands;
  }

  // 当前前级增益
  int get currentPreampGain => preampGain.value;

  // PEQ模式列表
  List<String> get peqModes => _uiStateManager.peqModes;

  // 实时模式列表
  List<String> get realTimeModes => _uiStateManager.realTimeModes;
  final RxInt updateTrigger = 0.obs;

  // 最大波段数
  int get maxBands => _bandService.maxBands;

  // 所有PEQ配置
  RxList<PEQConfigModel> configs = <PEQConfigModel>[].obs;

  // 当前活跃配置ID
  RxString activeConfigId = ''.obs;

  @override
  void onInit() {
    super.onInit();
    _bandService.ensureBandsInitialized();
    loadPEQSettings();
  }

  // 加载PEQ设置并更新状态
  Future<void> loadPEQSettings() async {
    // 从存储库加载设置
    final settings = await _peqRepository.loadPEQSettings(deviceId);

    if (settings != null) {
      // 更新UI状态
      _uiStateManager.isEnabled.value = settings.isEnabled;
      _uiStateManager.peqMode.value = settings.peqMode;
      _uiStateManager.activeDisplayModes.value = settings.activeDisplayModes;
      _uiStateManager.isCompensatedMode.value = settings.isCompensatedMode;
      _uiStateManager.selectedBandId.value = settings.selectedBandId;
      _uiStateManager.currentEditParam.value = settings.currentEditParam;

      // 更新前级增益
      _bandService.setPreampGain(settings.preampGain);

      // 更新多配置相关状态
      configs.value = settings.peqConfigs;
      activeConfigId.value = settings.activeConfigId;

      // 初始化当前配置的波段数据
      _initializeBandsFromCurrentConfig(settings);

      // 加载文件
      if (settings.targetFiles.isNotEmpty) {
        _fileService.loadTargetFilesFromModels(settings.targetFiles);
      }
      if (settings.sourceFRFiles.isNotEmpty) {
        _fileService.loadSourceFRFilesFromModels(settings.sourceFRFiles);
      }

      // 设置选中的文件
      if (settings.selectedTargetFile != null) {
        _uiStateManager.selectTargetFile(
            ImportedFile.fromModel(settings.selectedTargetFile));
      }
      if (settings.selectedSourceFRFile != null) {
        _uiStateManager.selectSourceFRFile(
            ImportedFile.fromModel(settings.selectedSourceFRFile));
      }
    }

    triggerUpdate();
  }

  // 初始化当前配置的波段数据
  void _initializeBandsFromCurrentConfig(settings) {
    final activeConfig = settings.activeConfig;
    if (activeConfig == null) return;

    // 加载波段数据
    _bandService.loadBandsFromModels(
      activeConfig.modeBands[PEQModes.allChannels] ?? [],
      activeConfig.modeBands[PEQModes.leftChannel] ?? [],
      activeConfig.modeBands[PEQModes.rightChannel] ?? [],
    );
  }

  // 切换启用状态
  void toggleEnabled() {
    _uiStateManager.toggleEnabled();
    _peqRepository.updatePEQEnabled(deviceId, _uiStateManager.isEnabled.value);
    triggerUpdate();
  }

  // 切换PEQ模式
  void switchPEQMode(String mode) {
    _uiStateManager.switchPEQMode(mode);
    _uiStateManager.selectBand(-1); // 取消选中
    _peqRepository.updatePEQMode(deviceId, mode);
    triggerUpdate();
    Log.e(
        '切换模式到 $mode, currentBands.length: ${currentBands.length}, content: ${currentBands.map((b) => b.toString()).join(", ")}');
  }

  // 切换显示模式
  void toggleDisplayMode(String mode) {
    _uiStateManager.toggleDisplayMode(mode);
    _peqRepository.updateActiveDisplayModes(
        deviceId, _uiStateManager.activeDisplayModes);
    triggerUpdate();
  }

  // 切换补偿模式
  void setCompensatedMode(bool isCompensated) {
    _uiStateManager.setCompensatedMode(isCompensated);
    _peqRepository.updateCompensatedMode(deviceId, isCompensated);
    triggerUpdate();
  }

  // 是否显示模式激活
  bool isDisplayModeActive(String mode) {
    return _uiStateManager.isDisplayModeActive(mode);
  }

  // 设置编辑参数
  void setEditParam(String param) {
    _uiStateManager.setEditParam(param);
    _peqRepository.updateCurrentEditParam(deviceId, param);
    triggerUpdate();
  }

  // 选择波段
  void selectBand(int bandId) {
    _uiStateManager.selectBand(bandId);
    _peqRepository.updateSelectedBandId(deviceId, bandId);
    triggerUpdate();
  }

  // 设置前级增益
  void setPreampGain(int value) {
    _bandService.setPreampGain(value);
    _peqRepository.updatePreampGain(deviceId, value);
    triggerUpdate();
  }

  // 切换波段启用状态
  void toggleBandEnabled(int bandId) {
    _bandService.toggleBandEnabled(bandId, peqMode.value);
    // 更新当前模式的所有波段
    _peqRepository.updateBands(deviceId, peqMode.value, currentBands);
    triggerUpdate();
  }

  // 更新波段滤波器类型
  void updateBandFilterType(int bandId, Dx5FilterType filterType) {
    final band = _bandService
        .getCurrentBands(peqMode.value)
        .firstWhere((b) => b.id == bandId);
    band.filterType.value = filterType;

    // 根据滤波器类型设置频率类型
    switch (filterType) {
      case Dx5FilterType.peakingFilter:
      case Dx5FilterType.lowShelfFilter:
      case Dx5FilterType.highShelfFilter:
        band.frequencyType.value = Dx5FrequencyType.centerFrequency;
        break;
      case Dx5FilterType.lowPassFilter:
      case Dx5FilterType.highPassFilter:
        band.frequencyType.value = Dx5FrequencyType.cornerFrequency;
        break;
    }

    // 更新波段属性
    _peqRepository.updateBandProperty(
        deviceId, peqMode.value, bandId, 'filterType', filterType.index);

    triggerUpdate();
  }

  // 更新波段频率
  void updateBandFrequency(int bandId, double frequency) {
    _bandService.updateBandFrequency(bandId, frequency, peqMode.value);
    _peqRepository.updateBandProperty(
        deviceId, peqMode.value, bandId, 'frequency', frequency);
    triggerUpdate();
  }

  // 更新波段增益
  void updateBandGain(int bandId, double gain) {
    _bandService.updateBandGain(bandId, gain, peqMode.value);
    _peqRepository.updateBandProperty(
        deviceId, peqMode.value, bandId, 'gain', gain);
    triggerUpdate();
  }

  // 更新波段Q值
  void updateBandQ(int bandId, double q) {
    _bandService.updateBandQ(bandId, q, peqMode.value);
    _peqRepository.updateBandProperty(deviceId, peqMode.value, bandId, 'q', q);
    triggerUpdate();
  }

  // 添加新波段
  void addNewBand() {
    _bandService.addNewBand(peqMode.value);
    final addedBand = _bandService.getLastAddedBand(peqMode.value);
    if (addedBand != null) {
      _peqRepository.addBand(deviceId, peqMode.value, addedBand);
      selectBand(addedBand.id);
    }
    triggerUpdate();
  }

  // 删除波段
  void removeBand(int bandId) {
    if (bandId == 0) return; // 不允许删除前级波段
    _bandService.removeBand(bandId, peqMode.value);
    _peqRepository.removeBand(deviceId, peqMode.value, bandId);
    _uiStateManager.selectBand(-1);
    triggerUpdate();
  }

  // 重置当前模式所有波段
  void resetBands() {
    _bandService.resetBands(peqMode.value);
    _peqRepository.updateBands(deviceId, peqMode.value, currentBands);
    triggerUpdate();
  }

  // 触发更新
  void triggerUpdate() {
    _updateDebouncer.run(() {
      updateTrigger.value = updateTrigger.value + 1;
    });
  }

  // 添加新的PEQ配置
  Future<void> addNewConfig(String name, [String description = '']) async {
    final newConfig = PEQConfigModel.createDefault(name);
    if (description.isNotEmpty) {
      // 复制然后更新描述
      final configWithDesc = newConfig.copyWith(description: description);
      await _peqRepository.addConfig(deviceId, configWithDesc);
    } else {
      await _peqRepository.addConfig(deviceId, newConfig);
    }

    await loadPEQSettings(); // 重新加载以更新UI
  }

  // 删除PEQ配置
  Future<void> deleteConfig(String configId) async {
    await _peqRepository.removeConfig(deviceId, configId);
    await loadPEQSettings(); // 重新加载以更新UI
  }

  // 切换活跃配置
  Future<void> switchToConfig(String configId) async {
    if (activeConfigId.value == configId) return; // 已经是当前配置

    await _peqRepository.switchActiveConfig(deviceId, configId);
    await loadPEQSettings(); // 重新加载以更新UI和波段数据
  }

  // 更新配置名称或描述
  Future<void> updateConfigInfo(String configId,
      {String? name, String? description}) async {
    await _peqRepository.updateConfigInfo(deviceId, configId,
        name: name, description: description);
    loadPEQSettings();
  }

  // 复制配置
  Future<void> duplicateConfig(String configId) async {
    final config = configs.firstWhere((c) => c.id == configId);
    final newConfig = config.clone(newName: "${config.name} 副本");
    await _peqRepository.addConfig(deviceId, newConfig);
    loadPEQSettings();
  }

  // 获取当前活跃配置
  PEQConfigModel? get activeConfig {
    if (configs.isEmpty) return null;

    return configs.firstWhere((config) => config.id == activeConfigId.value,
        orElse: () => configs.first);
  }

  // 导入目标
  Future<void> importTarget() async {
    try {
      final result = await _dialogService.showOpenFileDialog(
        title: '选择目标响应文件',
        extensions: ['txt', 'csv'],
      );

      if (result != null && result.isNotEmpty) {
        try {
          final file = await _fileService.importTargetFile(result.first);
          if (file != null) {
            _uiStateManager.selectTargetFile(file);

            // 持久化导入的目标文件
            final targetFilesList = _fileService.targetFiles.toList();
            await _peqRepository.updateTargetFiles(
                deviceId, targetFilesList);

            Log.d('导入目标成功: ${result.first}');
            triggerUpdate();
          }
        } catch (e) {
          Log.e('导入目标响应文件失败: $e');
        }
      }
    } catch (e) {
      Log.e('导入目标失败', e);
    }
  }

  // 导入源频响
  Future<void> importSourceFR() async {
    try {
      final result = await _dialogService.showOpenFileDialog(
        title: '选择源频响文件',
        extensions: ['txt', 'csv'],
      );

      if (result != null && result.isNotEmpty) {
        try {
          final file = await _fileService.importSourceFRFile(result.first);
          if (file != null) {
            _uiStateManager.selectSourceFRFile(file);

            // 持久化导入的源频响文件
            final sourceFRFilesList = _fileService.sourceFRFiles.toList();
            await _peqRepository.updateSourceFRFiles(
                deviceId, sourceFRFilesList);

            Log.d('导入源频响成功: ${result.first}');
            triggerUpdate();
          }
        } catch (e) {
          Log.e('导入源频响文件失败: $e');
        }
      }
    } catch (e) {
      Log.e('导入源频响失败', e);
    }
  }

  // 选择目标文件
  void selectTargetFile(ImportedFile? file) {
    _uiStateManager.selectTargetFile(file);

    // 持久化选择的目标文件
    if (file != null) {
      _peqRepository.updateSelectedTargetFile(deviceId, file);
    } else {
      _peqRepository.updateSelectedTargetFile(deviceId, null);
    }

    triggerUpdate();
  }

  // 选择源FR文件
  void selectSourceFRFile(ImportedFile? file) {
    _uiStateManager.selectSourceFRFile(file);

    // 持久化选择的源频响文件
    if (file != null) {
      _peqRepository.updateSelectedSourceFRFile(deviceId, file);
    } else {
      _peqRepository.updateSelectedSourceFRFile(deviceId, null);
    }

    triggerUpdate();
  }

  // 删除目标文件
  Future<void> deleteTargetFile(ImportedFile file) async {
    // 如果删除的是当前选中的文件，先取消选择
    if (selectedTargetFile.value?.id == file.id) {
      selectTargetFile(null);
    }

    // 从文件服务中删除文件
    _fileService.deleteTargetFile(file.id);

    // 持久化更新后的目标文件列表
    final targetFilesList = _fileService.targetFiles.toList();
    await _peqRepository.updateTargetFiles(
        deviceId, targetFilesList);

    triggerUpdate();
    Get.snackbar('提示', '已删除目标文件: ${file.name}');
  }

  // 删除源频响文件
  Future<void> deleteSourceFRFile(ImportedFile file) async {
    // 如果删除的是当前选中的文件，先取消选择
    if (selectedSourceFRFile.value?.id == file.id) {
      selectSourceFRFile(null);
    }

    // 从文件服务中删除文件
    _fileService.deleteSourceFRFile(file.id);

    // 持久化更新后的源频响文件列表
    final sourceFRFilesList = _fileService.sourceFRFiles.toList();
    await _peqRepository.updateSourceFRFiles(
        deviceId, sourceFRFilesList);

    triggerUpdate();
    Get.snackbar('提示', '已删除源频响文件: ${file.name}');
  }

  // 导出合成滤波器响应
  Future<void> exportCombinedFilter() async {
    try {
      Log.i('开始导出合并滤波器');
      if (currentBands.isEmpty) {
        Log.i('没有波段可以导出');
        Get.snackbar('提示', '没有波段可以导出');
        return;
      }

      Log.i('准备导出数据，当前波段数量: ${currentBands.length}');

      // *** 使用精确计算获取数据 ***
      // 可以选择更高的点数以获得更精确的导出文件
      final List<Map<String, double>> data =
          CurvePointsGenerator.generateCombinedCurveRawData(currentBands,
              points: 240); // 例如 240 点

      if (data.isEmpty) {
        Log.w('计算出的组合滤波器数据为空');
        Get.snackbar('错误', '无法计算滤波器数据');
        return;
      }

      Log.i('完成数据计算 (${data.length} 个点)，开始导出文件');
      await _fileService.exportCombinedFilter(data); // 传递精确数据
      Log.i('导出完成');
    } catch (e, s) {
      Log.e('导出合并滤波失败: $e\n$s');
      Get.snackbar('错误', '导出合并滤波失败: $e',
          colorText: ColorPalettes.instance.pure, backgroundColor: ColorPalettes.instance.error);
    }
  }

  // 更新波段频率类型
  void updateBandFrequencyType(int bandId, Dx5FrequencyType frequencyType) {
    final band = _bandService
        .getCurrentBands(peqMode.value)
        .firstWhere((b) => b.id == bandId);
    band.frequencyType.value = frequencyType;

    // 更新波段属性
    _peqRepository.updateBandProperty(
        deviceId, peqMode.value, bandId, 'frequencyType', frequencyType.index);

    triggerUpdate();
  }

  // 打开PEQ设置页面
  void openSettingsPage() {
    Get.toNamed(AppRoutes.peqSettingsPage, arguments: this);
  }

}
