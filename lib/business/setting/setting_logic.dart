import 'package:get/get.dart';
import 'package:flutter/material.dart';
import 'package:topping_home/theme/color_palettes.dart';
import '../../repositories/user_repository.dart';
import '../../router/routers.dart';
import 'setting_state.dart';

/// 设置逻辑控制器
class SettingLogic extends GetxController {
  final SettingState state = SettingState();

  @override
  void onInit() {
    super.onInit();
    // 监听登录状态
    ever(UserRepository.instance.loginEntity, (loginEntity) {
      state.loginInfo.value = loginEntity;
    });

    // 监听用户信息
    ever(UserRepository.instance.userEntity, (userEntity) {
      state.userInfo.value = userEntity;
    });

    // 初始化当前值
    state.loginInfo.value = UserRepository.instance.loginEntity.value;
    state.userInfo.value = UserRepository.instance.userEntity.value;
  }

  @override
  void onReady() {
    super.onReady();
    _updateLoginInfo();
    _updateUserInfo();
  }

  /// 更新用户信息
  void _updateUserInfo() {
    state.loginInfo.value = UserRepository.instance.loginEntity.value;
    state.userInfo.value = UserRepository.instance.userEntity.value;
  }

  /// 更新登录信息
  void _updateLoginInfo() {
    state.loginInfo.value = UserRepository.instance.loginEntity.value;
  }

  /// 切换主题
  void toggleTheme() {
    // 切换暗黑/亮色模式
    if (state.isDarkMode.value) {
      // 当前是暗黑模式，切换到亮色模式
      ColorPalettes.instance.changeTheme(PalettesStyle.lightDefault);
    } else {
      // 当前是亮色模式，切换到暗黑模式
      ColorPalettes.instance.changeTheme(PalettesStyle.dark);
    }

    // 更新状态
    state.isDarkMode.value = ColorPalettes.instance.isDark();

    // 刷新整个应用
    Get.forceAppUpdate();
  }

  /// 检查登录状态并跳转
  void checkLoginAndNavigate(VoidCallback action) {
    if (!UserRepository.instance.isLogin()) {
      AppRoutes.jumpPage(AppRoutes.passwordLoginPage);
    } else {
      action();
    }
  }

  /// 头像点击事件
  void onAvatarTap() {
    if (!UserRepository.instance.isLogin()) {
      AppRoutes.jumpPage(AppRoutes.passwordLoginPage);
    }
  }

  /// 快速开始点击事件
  void onQuickStartTap() {
    AppRoutes.jumpPage(AppRoutes.quickStartPage);
  }
}
