import 'package:get/get.dart';
import 'package:topping_home/theme/color_palettes.dart';
import '../../models/login_entity.dart';
import '../../models/user_entity.dart';

/// 设置状态管理类
class SettingState {
  late Rx<LoginEntity?> loginInfo;
  late Rx<UserEntity?> userInfo;
  late RxBool isDarkMode;

  SettingState() {
    loginInfo = Rx<LoginEntity?>(null);
    userInfo = Rx<UserEntity?>(null);
    isDarkMode = ColorPalettes.instance.isDark().obs;
  }
}
