import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../common/util/i18n.dart';
import '../../repositories/user_repository.dart';
import '../../../models/user_entity.dart';
import '../../router/routers.dart';
import 'package:topping_home/theme/color_palettes.dart';
import 'password_change_state.dart';

/// 修改密码逻辑
class PasswordChangeLogic extends GetxController {
  final PasswordChangeState state = PasswordChangeState();

  @override
  void onClose() {
    state.dispose();
    super.onClose();
  }

  /// 切换旧密码可见性
  void toggleOldPasswordVisibility() {
    state.isOldPasswordVisible.value = !state.isOldPasswordVisible.value;
  }

  /// 切换新密码可见性
  void toggleNewPasswordVisibility() {
    state.isNewPasswordVisible.value = !state.isNewPasswordVisible.value;
  }

  /// 切换确认密码可见性
  void toggleConfirmPasswordVisibility() {
    state.isConfirmPasswordVisible.value =
        !state.isConfirmPasswordVisible.value;
  }

  /// 验证密码格式
  bool _validatePassword(String password) {
    // TODO: 添加更复杂的密码强度验证
    if (password.length < 6) {
      Get.snackbar(
        l10n.error,
        l10n.passwordTooShort,
        snackPosition: SnackPosition.TOP,
        backgroundColor: ColorPalettes.instance.error,
        colorText: ColorPalettes.instance.firstText,
      );
      return false;
    }
    return true;
  }

  /// 验证输入
  bool _validateInputs() {
    final oldPassword = state.oldPasswordController.text;
    final newPassword = state.newPasswordController.text;
    final confirmPassword = state.confirmPasswordController.text;

    if (oldPassword.isEmpty || newPassword.isEmpty || confirmPassword.isEmpty) {
      Get.snackbar(
        l10n.error,
        l10n.passwordEmpty,
        snackPosition: SnackPosition.TOP,
        backgroundColor: ColorPalettes.instance.error,
        colorText: ColorPalettes.instance.firstText,
      );
      return false;
    }

    if (!_validatePassword(newPassword)) return false;

    if (newPassword != confirmPassword) {
      Get.snackbar(
        l10n.error,
        l10n.passwordMismatch,
        snackPosition: SnackPosition.TOP,
        backgroundColor: ColorPalettes.instance.error,
        colorText: ColorPalettes.instance.firstText,
      );
      return false;
    }

    return true;
  }

  /// 修改密码
  Future<void> changePassword() async {
    if (!_validateInputs()) return;

    state.isLoading.value = true;
    try {
      final currentUser = UserRepository.instance.userEntity.value;
      if (currentUser == null) {
        throw Exception(l10n.userNotFound);
      }

      // 验证旧密码
      if (currentUser.password != state.oldPasswordController.text) {
        throw Exception(l10n.oldPasswordError);
      }

      // 创建更新后的用户信息
      final updatedUser = UserEntity()
        ..id = currentUser.id
        ..userPhone = currentUser.userPhone
        ..password = state.newPasswordController.text
        ..nickname = currentUser.nickname
        ..signature = currentUser.signature
        ..avatar = currentUser.avatar
        ..sex = currentUser.sex
        ..birthday = currentUser.birthday;

      // 更新用户信息
      await UserRepository.instance.updateUserEntity(updatedUser);

      // 提示用户修改成功
      await Get.dialog(
        AlertDialog(
          title: Text(l10n.success),
          content: Text(l10n.passwordUpdateSuccessRelogin),
          actions: [
            TextButton(
              onPressed: () {
                // 登出并跳转到登录页
                UserRepository.instance.logout();
                Get.offAllNamed(AppRoutes.passwordLoginPage);
              },
              child: Text(l10n.confirm),
            ),
          ],
        ),
        barrierDismissible: false, // 禁止点击外部关闭
      );
    } catch (e) {
      Get.snackbar(
        l10n.error,
        e.toString(),
        snackPosition: SnackPosition.TOP,
        backgroundColor: ColorPalettes.instance.error,
        colorText: ColorPalettes.instance.firstText,
      );
    } finally {
      state.isLoading.value = false;
    }
  }
}
