import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:topping_home/theme/color_palettes.dart';
import 'package:topping_home/theme/text_styles.dart';

import '../../../common/util/i18n.dart';
import 'password_change_logic.dart';

/// 修改密码页面
class PasswordChangePage extends StatelessWidget {
  PasswordChangePage({super.key});

  final logic = Get.find<PasswordChangeLogic>();
  final state = Get.find<PasswordChangeLogic>().state;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: ColorPalettes.instance.background,
      appBar: AppBar(
        backgroundColor: ColorPalettes.instance.background,
        title: Text(
          l10n.loginPasswordModify,
          style: TextStyles.instance.h2(),
        ),
        centerTitle: true,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Card(
          color: ColorPalettes.instance.card,
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                _buildPasswordField(
                  controller: state.oldPasswordController,
                  label: l10n.oldPassword,
                  isVisible: state.isOldPasswordVisible,
                  onToggleVisibility: logic.toggleOldPasswordVisibility,
                ),
                const SizedBox(height: 16),
                _buildPasswordField(
                  controller: state.newPasswordController,
                  label: l10n.newPassword,
                  isVisible: state.isNewPasswordVisible,
                  onToggleVisibility: logic.toggleNewPasswordVisibility,
                ),
                const SizedBox(height: 16),
                _buildPasswordField(
                  controller: state.confirmPasswordController,
                  label: l10n.confirmPassword,
                  isVisible: state.isConfirmPasswordVisible,
                  onToggleVisibility: logic.toggleConfirmPasswordVisibility,
                ),
                const SizedBox(height: 24),
                _buildSubmitButton(),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// 构建密码输入框
  Widget _buildPasswordField({
    required TextEditingController controller,
    required String label,
    required RxBool isVisible,
    required VoidCallback onToggleVisibility,
  }) {
    return Obx(() => TextField(
          controller: controller,
          obscureText: !isVisible.value,
          style: TextStyles.instance.h2(
            color: ColorPalettes.instance.firstText,
          ),
          decoration: InputDecoration(
            labelText: label,
            labelStyle: TextStyles.instance.h2(
              color: ColorPalettes.instance.secondText,
            ),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: ColorPalettes.instance.divider),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: ColorPalettes.instance.primary),
            ),
            suffixIcon: IconButton(
              icon: Icon(
                isVisible.value ? Icons.visibility_off : Icons.visibility,
                color: ColorPalettes.instance.firstText,
                size: 20,
              ),
              onPressed: onToggleVisibility,
            ),
          ),
        ));
  }

  /// 构建提交按钮
  Widget _buildSubmitButton() {
    return Obx(() => ElevatedButton(
          onPressed: state.isLoading.value ? null : logic.changePassword,
          style: ElevatedButton.styleFrom(
            backgroundColor: ColorPalettes.instance.primary,
            padding: const EdgeInsets.symmetric(vertical: 16),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
          ),
          child: state.isLoading.value
              ? SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    color: ColorPalettes.instance.firstText,
                  ),
                )
              : Text(
                  l10n.confirm,
                  style: TextStyles.instance.h2(
                    fontWeight: FontWeight.bold,
                  ),
                ),
        ));
  }
}
