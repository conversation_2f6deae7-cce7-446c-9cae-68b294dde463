import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:topping_home/common/util/i18n.dart';
import 'package:topping_home/gen/assets.gen.dart';
import 'package:topping_home/theme/color_palettes.dart';
import 'package:topping_home/theme/text_styles.dart';
import 'about_logic.dart';

/// 关于页面 - 展示应用信息、隐私政策、用户协议等
class AboutPage extends GetView<AboutLogic> {
  const AboutPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Obx(() => AnnotatedRegion<SystemUiOverlayStyle>(
          value: SystemUiOverlayStyle(
            statusBarColor: ColorPalettes.instance.transparent,
            statusBarIconBrightness: ColorPalettes.instance.isDark()
                ? Brightness.light
                : Brightness.dark,
            statusBarBrightness: ColorPalettes.instance.isDark()
                ? Brightness.dark
                : Brightness.light,
          ),
          child: Scaffold(
            backgroundColor: ColorPalettes.instance.card,
            appBar: AppBar(
              backgroundColor: ColorPalettes.instance.card,
              elevation: 0,
              title: Text(
                l10n.about,
                style: TextStyles.instance.h2(),
              ),
              centerTitle: true,
              iconTheme: IconThemeData(color: ColorPalettes.instance.firstText),
              actions: [
                // 显示调试模式指示器
                Obx(() => controller.isDebugMode
                    ? IconButton(
                        icon: Icon(Icons.bug_report, color: Colors.red),
                        onPressed: () => _showDebugOptions(context),
                        tooltip: '调试模式选项',
                      )
                    : SizedBox.shrink()),
              ],
            ),
            body: SingleChildScrollView(
              child: Column(
                children: [
                  SizedBox(height: 48.h),
                  // 应用图标 - 添加点击事件触发调试模式
                  GestureDetector(
                    onTap: controller.onDebugTap,
                    child: Image.asset(
                      Assets.icon.icon.path,
                      width: 96.w,
                      height: 96.h,
                    ),
                  ),
                  SizedBox(height: 24.h),
                  // 应用名称和版本信息
                  Obx(() {
                    final info = controller.state.packageInfo.value;
                    return Column(
                      children: [
                        Text(
                          info?.appName ?? '',
                          style: TextStyles.instance.h2(),
                        ),
                        SizedBox(height: 8.h),
                        Text(
                          '${l10n.version} ${info?.version ?? '1.0.0'} (${info?.buildNumber ?? '1'})',
                          style: TextStyles.instance.h3(
                            color: ColorPalettes.instance.secondText,
                          ),
                        ),
                      ],
                    );
                  }),
                  SizedBox(height: 48.h),
                  // 功能链接卡片区域
                  Padding(
                    padding: EdgeInsets.symmetric(horizontal: 24.w),
                    child: Column(
                      children: [
                        // 隐私政策卡片
                        _buildInfoCard(
                          title: l10n.privacyPolicyText,
                          icon: Icons.privacy_tip_outlined,
                          onTap: controller.onPrivacyTap,
                        ),
                        SizedBox(height: 16.h),
                        // 用户协议卡片
                        _buildInfoCard(
                          title: l10n.userAgreement,
                          icon: Icons.description_outlined,
                          onTap: controller.onTermsTap,
                        ),
                        SizedBox(height: 16.h),
                        // 检查更新卡片 - 只在安卓平台显示
                        if (GetPlatform.isAndroid)
                          _buildInfoCard(
                            title: l10n.checkUpdate,
                            icon: Icons.system_update_outlined,
                            onTap: controller.checkUpdate,
                          ),
                      ],
                    ),
                  ),
                  SizedBox(height: 48.h),
                  // 版权信息
                  Text(
                    l10n.copyright,
                    style: TextStyles.instance.body1(
                      color: ColorPalettes.instance.secondText,
                    ),
                  ),
                  SizedBox(height: 24.h),
                ],
              ),
            ),
          ),
        ));
  }

  /// 构建信息卡片
  ///
  /// [title] 卡片标题
  /// [icon] 卡片图标
  /// [onTap] 点击回调
  /// [iconColor] 图标颜色，默认使用主文本颜色
  Widget _buildInfoCard({
    required String title,
    required IconData icon,
    required VoidCallback onTap,
    Color? iconColor,
  }) {
    return Card(
      elevation: 2,
      color: ColorPalettes.instance.background,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12.r),
      ),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12.r),
        child: Padding(
          padding: EdgeInsets.symmetric(
            horizontal: 20.w,
            vertical: 22.h, // 适当增加高度，但不要太高
          ),
          child: Row(
            children: [
              // 使用SizedBox包装图标，确保宽度一致
              SizedBox(
                width: 48.w,
                child: Icon(
                  icon,
                  color: iconColor ?? ColorPalettes.instance.firstText,
                  size: 40.sp, // 稍微减小图标大小
                ),
              ),
              SizedBox(width: 16.w),
              Expanded(
                child: Text(
                  title,
                  style: TextStyles.instance.h3(
                    fontWeight: FontWeight.w500, // 统一字体粗细
                  ),
                  textAlign: TextAlign.left, // 确保文本左对齐
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
              Icon(
                Icons.arrow_forward_ios,
                color: ColorPalettes.instance.firstText,
                size: 30.sp,
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// 显示调试选项菜单
  void _showDebugOptions(BuildContext context) {
    showModalBottomSheet(
      context: context,
      builder: (context) => SafeArea(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: Icon(Icons.bug_report),
              title: Text('查看验证日志'),
              onTap: () {
                Navigator.pop(context);
                controller.showLogViewer();
              },
            ),
            ListTile(
              leading: Icon(Icons.exit_to_app),
              title: Text('退出调试模式'),
              onTap: () {
                Navigator.pop(context);
                controller.exitDebugMode();
              },
            ),
          ],
        ),
      ),
    );
  }
}
