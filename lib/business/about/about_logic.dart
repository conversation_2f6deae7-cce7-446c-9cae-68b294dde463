import 'dart:async';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:package_info_plus/package_info_plus.dart';

import '../../router/routers.dart';
import '../../service/update_service.dart';
import '../../common/util/log_util.dart';
import '../log_viewer/log_viewer_page.dart';
import 'about_state.dart';

/// 关于页面逻辑
class AboutLogic extends GetxController {
  final AboutState state = AboutState();
  final UpdateService _updateService = Get.find<UpdateService>();

  // 调试模式相关变量
  final RxBool _isDebugMode = false.obs;
  final RxInt _tapCount = 0.obs;
  Timer? _tapTimer;
  final int _requiredTaps = 7; // 需要连续点击的次数
  final int _tapTimeoutMs = 3000; // 点击超时时间（毫秒）

  /// 检查是否处于调试模式
  bool get isDebugMode => _isDebugMode.value;

  @override
  void onReady() {
    super.onReady();
    _loadPackageInfo();
  }

  // 加载包信息
  Future<void> _loadPackageInfo() async {
    state.packageInfo.value = await PackageInfo.fromPlatform();
  }

  // 打开隐私政策页面
  void onPrivacyTap() {
    Get.toNamed(AppRoutes.privacyPage);
  }

  // 打开用户协议页面
  void onTermsTap() {
    Get.toNamed(AppRoutes.agreementPage);
  }

  // 检查APP更新
  Future<void> checkUpdate() async {
    await _updateService.checkUpdate();
  }

  @override
  void onClose() {
    _tapTimer?.cancel();
    super.onClose();
  }

  /// 触发调试模式的点击事件
  void onDebugTap() {
    // 如果已经在调试模式，则不做任何处理
    if (_isDebugMode.value) return;

    // 增加点击计数
    _tapCount.value++;
    Log.d('调试模式点击: ${_tapCount.value}/$_requiredTaps');

    // 取消之前的定时器
    _tapTimer?.cancel();

    // 如果达到所需的点击次数，则进入调试模式
    if (_tapCount.value >= _requiredTaps) {
      _enterDebugMode();
      return;
    }

    // 设置超时定时器，如果超时则重置点击计数
    _tapTimer = Timer(Duration(milliseconds: _tapTimeoutMs), () {
      Log.d('调试模式点击超时，重置计数');
      _tapCount.value = 0;
    });
  }

  /// 进入调试模式
  void _enterDebugMode() {
    _isDebugMode.value = true;
    _tapCount.value = 0;
    Log.i('已进入调试模式');

    // 初始化验证日志帮手
    // VerifyLogHelper.init();

    // 显示提示信息
    Get.snackbar(
      '调试模式',
      '已进入调试模式，日志记录已启用',
      snackPosition: SnackPosition.BOTTOM,
      duration: Duration(seconds: 3),
    );
  }

  /// 退出调试模式
  void exitDebugMode() {
    if (!_isDebugMode.value) return;

    _isDebugMode.value = false;
    Log.i('已退出调试模式');

    // 显示提示信息
    Get.snackbar(
      '调试模式',
      '已退出调试模式，日志记录已停止',
      snackPosition: SnackPosition.BOTTOM,
      duration: Duration(seconds: 3),
    );
  }

  /// 显示日志查看器
  void showLogViewer() {
    if (!_isDebugMode.value) return;

    try {
      Get.to(() => const VerifyLogViewerPage());
    } catch (e) {
      Log.e('跳转到日志查看器页面失败: $e');

      // 显示错误提示
      Get.snackbar(
        '错误',
        '无法显示日志查看器，请检查是否已正确集成插件',
        snackPosition: SnackPosition.BOTTOM,
        duration: Duration(seconds: 3),
      );
    }
  }
}
