import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:topping_home/router/routers.dart';
import 'package:topping_home/theme/color_palettes.dart';
import 'package:topping_home/theme/text_styles.dart';

import '../../../common/util/i18n.dart';
import '../../../gen/assets.gen.dart';
import 'password_logic.dart';

/// 密码登录页面
class LoginPasswordPage extends GetView<LoginPasswordLogic> {
  const LoginPasswordPage({super.key});

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () => FocusScope.of(context).unfocus(),
      child: Scaffold(
        appBar: _buildAppBar(),
        body: _buildBody(context),
      ),
    );
  }

  /// 构建AppBar
  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      title: Text(l10n.login),
      centerTitle: true,
      elevation: 0,
      backgroundColor: ColorPalettes.instance.transparent,
    );
  }

  /// 构建Body
  Widget _buildBody(BuildContext context) {
    return SingleChildScrollView(
      child: Padding(
        padding: const EdgeInsets.all(24.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            _buildLogo(),
            const SizedBox(height: 48),
            _buildLoginForm(),
            const SizedBox(height: 24),
            _buildLoginButton(),
            const SizedBox(height: 16), // 添加间距
            _buildRegisterEntry(), // 添加注册入口
          ],
        ),
      ),
    );
  }

  /// 添加注册入口组件
  Widget _buildRegisterEntry() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Text(
          l10n.noAccount,
          style: TextStyles.instance.h2(
            color: ColorPalettes.instance.firstText.withAlpha(60),
          ),
        ),
        TextButton(
          onPressed: () => AppRoutes.jumpPage(AppRoutes.registerPage),
          child: Text(
            l10n.registerNow,
            style: TextStyles.instance.h2(
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
      ],
    );
  }

  /// 构建Logo
  Widget _buildLogo() {
    return Column(
      children: [
        const SizedBox(height: 40),
        Hero(
          tag: 'logo',
          child: Container(
            width: 100,
            height: 100,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              boxShadow: [
                BoxShadow(
                  color: ColorPalettes.instance.firstText.withAlpha(10),
                  spreadRadius: 1,
                  blurRadius: 10,
                  offset: const Offset(0, 4),
                ),
              ],
              image: DecorationImage(
                image: Assets.icon.icon.provider(),
                fit: BoxFit.cover,
              ),
            ),
          ),
        ),
      ],
    );
  }

  /// 构建登录表单
  Widget _buildLoginForm() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          children: [
            _buildPhoneInput(),
            const SizedBox(height: 20),
            _buildPasswordInput(),
          ],
        ),
      ),
    );
  }

  /// 构建手机号输入框
  Widget _buildPhoneInput() {
    return TextField(
      controller: controller.state.phoneController,
      decoration: InputDecoration(
        labelText: l10n.phone,
        prefixIcon: const Icon(Icons.phone_android),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide:
              BorderSide(color: ColorPalettes.instance.firstText.withAlpha(30)),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: ColorPalettes.instance.firstText),
        ),
        contentPadding: const EdgeInsets.symmetric(
          horizontal: 16,
          vertical: 16,
        ),
      ),
      keyboardType: TextInputType.phone,
    );
  }

  /// 构建密码输入框
  Widget _buildPasswordInput() {
    return Obx(() => TextField(
          controller: controller.state.passwordController,
          decoration: InputDecoration(
            labelText: l10n.loginPassword,
            prefixIcon: const Icon(Icons.lock_outline),
            suffixIcon: IconButton(
              icon: Icon(
                controller.state.isPasswordVisible.value
                    ? Icons.visibility_off
                    : Icons.visibility,
              ),
              onPressed: controller.togglePasswordVisibility,
            ),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(
                  color: ColorPalettes.instance.firstText.withAlpha(30)),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide:
                  BorderSide(color: Theme.of(Get.context!).primaryColor),
            ),
            contentPadding: const EdgeInsets.symmetric(
              horizontal: 16,
              vertical: 16,
            ),
          ),
          obscureText: !controller.state.isPasswordVisible.value,
        ));
  }

  /// 构建登录按钮
  Widget _buildLoginButton() {
    return Obx(() => ElevatedButton(
          onPressed: controller.state.isLoading.value
              ? null
              : () => controller.login(
                    controller.state.phoneController.text,
                    controller.state.passwordController.text,
                  ),
          style: ElevatedButton.styleFrom(
            padding: const EdgeInsets.symmetric(vertical: 16),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
            elevation: 2,
          ),
          child: controller.state.isLoading.value
              ? SizedBox(
                  width: 24,
                  height: 24,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    color: ColorPalettes.instance.firstText,
                  ),
                )
              : Text(
                  l10n.login,
                  style: TextStyles.instance.h2(
                    fontWeight: FontWeight.bold,
                  ),
                ),
        ));
  }
}
