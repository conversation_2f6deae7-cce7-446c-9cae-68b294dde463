import 'package:get/get.dart';
import 'package:topping_home/theme/color_palettes.dart';

import '../../../common/util/i18n.dart';
import '../../../models/login_entity.dart';
import '../../../repositories/user_repository.dart';
import 'password_state.dart';

/// 密码登录逻辑
class LoginPasswordLogic extends GetxController {
  final LoginPasswordState state = LoginPasswordState();

  /// 切换密码可见性
  void togglePasswordVisibility() {
    state.isPasswordVisible.value = !state.isPasswordVisible.value;
  }

  /// 登录
  void login(String phone, String password) async {
    if (!_validateInputs(phone, password)) return;

    _startLoading();
    try {
      await _performLogin(phone, password);
      _handleLoginSuccess();
    } catch (e) {
      _handleLoginError(e);
    }
    _stopLoading();
  }

  /// 验证输入
  bool _validateInputs(String phone, String password) {
    if (phone.isEmpty || password.isEmpty) {
      Get.snackbar(
        l10n.inputError,
        l10n.inputCannotBeEmpty,
        snackPosition: SnackPosition.TOP,
        backgroundColor: ColorPalettes.instance.error,
        colorText: ColorPalettes.instance.firstText,
      );
      return false;
    }
    return true;
  }

  /// 执行登录
  Future<void> _performLogin(String phone, String password) async {
    // 获取已注册用户信息
    final existingUser = UserRepository.instance.userEntity.value;

    if (existingUser == null || existingUser.userPhone != phone) {
      // todo 为了测试，临时注册
      await UserRepository.instance.register(phone, password);
      return;
    }

    if (existingUser.password != password) {
      throw Exception(l10n.passwordError);
    }

    // 登录成功，创建新的LoginEntity
    final loginEntity = LoginEntity()
      ..token = 'login_token_${DateTime.now().millisecondsSinceEpoch}'
      ..type = 'user'
      ..userId = existingUser.id;

    // 更新登录信息
    await UserRepository.instance.updateLoginEntity(loginEntity);
    await UserRepository.instance.updateUserEntity(existingUser);
  }

  /// 处理登录成功
  void _handleLoginSuccess() {
    Get.back();
  }

  /// 处理登录失败
  void _handleLoginError(dynamic error) {
    Get.snackbar(
      l10n.loginFail,
      error.toString(),
      snackPosition: SnackPosition.TOP,
      backgroundColor: ColorPalettes.instance.error,
      colorText: ColorPalettes.instance.firstText,
    );
  }

  /// 开始加载
  void _startLoading() {
    state.isLoading.value = true;
  }

  /// 停止加载
  void _stopLoading() {
    state.isLoading.value = false;
  }
}
