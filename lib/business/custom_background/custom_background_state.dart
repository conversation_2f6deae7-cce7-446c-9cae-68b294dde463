import 'package:get/get.dart';
import '../../repositories/background_repository.dart';

/// 自定义背景页面状态
class CustomBackgroundState {
  // 背景管理器
  final backgroundManager = Get.find<BackgroundRepository>();
  final RxBool isLoading = false.obs;
  final RxList<String> backgrounds = <String>[].obs;

  CustomBackgroundState() {
    _loadBackgrounds();
  }

  Future<void> _loadBackgrounds() async {
    isLoading.value = true;
    try {
      backgrounds.value = await backgroundManager.getAllBackgrounds();
    } finally {
      isLoading.value = false;
    }
  }
}
