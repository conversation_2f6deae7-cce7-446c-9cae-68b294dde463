import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:topping_home/common/util/i18n.dart';
import 'package:topping_home/theme/color_palettes.dart';
import 'package:topping_home/theme/text_styles.dart';
import '../../common/widget/background_wrapper.dart';
import 'custom_background_logic.dart';
import 'custom_background_state.dart';

/// 自定义背景页面
class CustomBackgroundPage extends StatefulWidget {
  const CustomBackgroundPage({super.key});

  @override
  State<CustomBackgroundPage> createState() => _CustomBackgroundPageState();
}

/// 自定义背景页面的状态类
class _CustomBackgroundPageState extends State<CustomBackgroundPage> {
  // 业务逻辑控制器
  final CustomBackgroundLogic logic = Get.put(CustomBackgroundLogic());
  // 页面状态
  final CustomBackgroundState state = Get.find<CustomBackgroundLogic>().state;

  @override
  Widget build(BuildContext context) {
    // 直接使用自适应单位，无需再次初始化 ScreenUtil
    return BackgroundWrapper(
      useDarkOverlay: true,
      darkOverlayOpacity: 0.3,
      backgroundColor: ColorPalettes.instance.background,
      child: Obx(() => AnnotatedRegion<SystemUiOverlayStyle>(
            value: SystemUiOverlayStyle(
              statusBarColor: ColorPalettes.instance.transparent,
              statusBarIconBrightness: ColorPalettes.instance.isDark()
                  ? Brightness.light
                  : Brightness.dark,
              statusBarBrightness: ColorPalettes.instance.isDark()
                  ? Brightness.dark
                  : Brightness.light,
            ),
            child: Scaffold(
              backgroundColor: ColorPalettes.instance.transparent,
              appBar: AppBar(
                backgroundColor: ColorPalettes.instance.card,
                elevation: 0,
                title: Text(
                  l10n.customBackground,
                  style: TextStyles.instance.h2(),
                ),
                centerTitle: false,
                iconTheme:
                    IconThemeData(color: ColorPalettes.instance.firstText),
              ),
              body: Column(
                children: [
                  Expanded(
                    child: Obx(() {
                      // 加载中状态
                      if (state.isLoading.value) {
                        return Center(
                          child: CircularProgressIndicator(
                            valueColor: AlwaysStoppedAnimation<Color>(
                              ColorPalettes.instance.accent,
                            ),
                          ),
                        );
                      }
                      // 背景图片列表
                      return GridView.builder(
                        padding: EdgeInsets.all(16.w),
                        gridDelegate:
                            SliverGridDelegateWithFixedCrossAxisCount(
                          crossAxisCount: 2,
                          mainAxisSpacing: 16.w,
                          crossAxisSpacing: 16.w,
                          childAspectRatio: 16 / 9,
                        ),
                        itemCount: state.backgrounds.length + 1,
                        itemBuilder: (context, index) {
                          if (index == 0) {
                            return _buildAddButton();
                          }
                          return _buildBackgroundItem(
                              state.backgrounds[index - 1]);
                        },
                      );
                    }),
                  ),
                  _buildSettingsPanel(),
                ],
              ),
            ),
          )),
    );
  }

  /// 添加按钮
  Widget _buildAddButton() {
    return InkWell(
      onTap: _showImageSourceDialog,
      child: Container(
        decoration: BoxDecoration(
          color: ColorPalettes.instance.card,
          borderRadius: BorderRadius.circular(12.r),
          boxShadow: [
            BoxShadow(
              color: ColorPalettes.instance.shadow.withAlpha((0.1 * 255).toInt()),
              blurRadius: 4.r,
              offset: Offset(0, 2.h),
            ),
          ],
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.add_photo_alternate,
                color: ColorPalettes.instance.secondText, size: 32.sp),
            SizedBox(height: 8.h),
            Text(
              l10n.addBackground,
              style: TextStyles.instance.h3(
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 背景图片项
  Widget _buildBackgroundItem(String path) {
    return Obx(() {
      // 判断是否为当前背景
      final isCurrentBackground =
          state.backgroundManager.currentBackground.value == path;
      return Stack(
        children: [
          // 背景图片
          Positioned.fill(
            child: Container(
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(12.r),
                boxShadow: [
                  BoxShadow(
                    color: ColorPalettes.instance.shadow.withAlpha((0.15 * 255).toInt()),
                    blurRadius: 6.r,
                    offset: Offset(0, 3.h),
                  ),
                ],
                image: DecorationImage(
                  image: FileImage(File(path)),
                  fit: BoxFit.cover,
                ),
              ),
            ),
          ),
          // 点击切换背景
          Positioned.fill(
            child: Material(
              color: ColorPalettes.instance.transparent,
              child: InkWell(
                onTap: () => logic.setCurrentBackground(path),
                borderRadius: BorderRadius.circular(12.r),
              ),
            ),
          ),
          // 删除按钮
          Positioned(
            top: 8.h,
            right: 8.w,
            child: GestureDetector(
              onTap: () => _showDeleteConfirmDialog(path),
              child: Container(
                padding: EdgeInsets.all(4.w),
                decoration: BoxDecoration(
                  color: ColorPalettes.instance.errorLightest,
                  shape: BoxShape.circle,
                  border: Border.all(
                    color: ColorPalettes.instance.error,
                    width: 1.w,
                  ),
                ),
                child: Icon(Icons.delete,
                    color: ColorPalettes.instance.error, size: 18.sp),
              ),
            ),
          ),
          // 当前背景标记
          if (isCurrentBackground)
            Positioned(
              bottom: 8.h,
              right: 8.w,
              child: Container(
                padding: EdgeInsets.all(4.w),
                decoration: BoxDecoration(
                  color: ColorPalettes.instance.success,
                  shape: BoxShape.circle,
                  boxShadow: [
                    BoxShadow(
                      color: ColorPalettes.instance.shadow.withAlpha((0.2 * 255).toInt()),
                      blurRadius: 4.r,
                      offset: Offset(0, 1.h),
                    ),
                  ],
                ),
                child: Icon(Icons.check,
                    color: ColorPalettes.instance.pure, size: 16.sp),
              ),
            ),
        ],
      );
    });
  }

  /// 显示删除确认对话框
  void _showDeleteConfirmDialog(String path) {
    Get.dialog(
      AlertDialog(
        backgroundColor: ColorPalettes.instance.card,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12.r),
        ),
        title: Text(
          l10n.confirmDelete,
          style: TextStyles.instance.h2(
            color: ColorPalettes.instance.firstText,
          ),
        ),
        content: Text(
          l10n.confirmDeleteHint,
          style: TextStyles.instance.body1(),
        ),
        actions: [
          TextButton(
            child: Text(
              l10n.cancel,
              style: TextStyles.instance.button(
                color: ColorPalettes.instance.secondText,
              ),
            ),
            onPressed: () => Get.back(),
          ),
          TextButton(
            child: Text(
              l10n.delete,
              style: TextStyles.instance.button(
                color: ColorPalettes.instance.error,
                fontWeight: FontWeight.bold,
              ),
            ),
            onPressed: () {
              Get.back();
              logic.deleteBackground(path);
            },
          ),
        ],
      ),
    );
  }

  /// 设置面板
  Widget _buildSettingsPanel() {
    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: ColorPalettes.instance.card,
        borderRadius: BorderRadius.vertical(top: Radius.circular(16.r)),
        boxShadow: [
          BoxShadow(
            color: ColorPalettes.instance.shadow.withAlpha((0.1 * 255).toInt()),
            blurRadius: 4.r,
            offset: Offset(0, -2.h),
          ),
        ],
      ),
      child: Column(
        children: [
          Obx(() => _buildSliderSetting(
                title: l10n.opacity,
                value: state.backgroundManager.backgroundOpacity.value,
                onChanged: logic.setBackgroundOpacity,
              )),
          SizedBox(height: 16.h),
          Obx(() => _buildSliderSetting(
                title: l10n.blur,
                value: state.backgroundManager.backgroundBlur.value,
                onChanged: logic.setBackgroundBlur,
                max: 20,
              )),
        ],
      ),
    );
  }

  /// 构建滑块设置
  Widget _buildSliderSetting({
    required String title,
    required double value,
    required ValueChanged<double> onChanged,
    double max = 1.0,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: TextStyles.instance.h3(
            fontWeight: FontWeight.w600,
            color: ColorPalettes.instance.firstText,
          ),
        ),
        SizedBox(height: 8.h),
        Row(
          children: [
            Expanded(
              child: Slider(
                value: value,
                min: 0.0,
                max: max,
                onChanged: onChanged,
                activeColor: ColorPalettes.instance.accent,
                inactiveColor: ColorPalettes.instance.accentLightest,
              ),
            ),
            SizedBox(width: 8.w),
            Container(
              padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 4.h),
              decoration: BoxDecoration(
                color: ColorPalettes.instance.accentLightest,
                borderRadius: BorderRadius.circular(12.r),
              ),
              child: Text(
                max == 1.0
                    ? '${(value * 100).toInt()}%'
                    : value.toStringAsFixed(1),
                style: TextStyles.instance.body2(
                  color: ColorPalettes.instance.accent,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }

  /// 显示图片来源对话框
  void _showImageSourceDialog() {
    Get.bottomSheet(
      Container(
        padding: EdgeInsets.all(16.w),
        decoration: BoxDecoration(
          color: ColorPalettes.instance.card,
          borderRadius: BorderRadius.vertical(top: Radius.circular(16.r)),
          boxShadow: [
            BoxShadow(
              color: ColorPalettes.instance.shadow.withAlpha((0.1 * 255).toInt()),
              blurRadius: 4.r,
              offset: Offset(0, -2.h),
            ),
          ],
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              width: 40.w,
              height: 4.h,
              margin: EdgeInsets.only(bottom: 16.h),
              decoration: BoxDecoration(
                color: ColorPalettes.instance.secondText.withAlpha((0.3 * 255).toInt()),
                borderRadius: BorderRadius.circular(2.r),
              ),
            ),
            ListTile(
              leading: Icon(
                Icons.photo_library,
                color: ColorPalettes.instance.secondText,
              ),
              title: Text(
                l10n.selectFromAlbum,
                style: TextStyles.instance.body1(
                  fontWeight: FontWeight.w600,
                ),
              ),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12.r),
              ),
              onTap: () {
                Get.back();
                logic.pickImageFromGallery();
              },
            ),
            const Divider(),
            ListTile(
              leading: Icon(
                Icons.camera_alt,
                color: ColorPalettes.instance.secondText,
              ),
              title: Text(
                l10n.takePhoto,
                style: TextStyles.instance.body1(
                  fontWeight: FontWeight.w600,
                ),
              ),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12.r),
              ),
              onTap: () {
                Get.back();
                logic.pickImageFromCamera();
              },
            ),
          ],
        ),
      ),
    );
  }

  @override
  void dispose() {
    // 页面销毁时释放逻辑控制器
    Get.delete<CustomBackgroundLogic>();
    super.dispose();
  }
}
