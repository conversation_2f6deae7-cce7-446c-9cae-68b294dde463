import 'dart:io';

import 'package:get/get.dart';
import 'package:topping_home/common/util/i18n.dart';
import 'package:topping_home/common/util/log_util.dart';

import 'custom_background_state.dart';

/// 自定义背景页面逻辑类
class CustomBackgroundLogic extends GetxController {
  final CustomBackgroundState state = CustomBackgroundState();

  // 从相册选择图片
  Future<void> pickImageFromGallery() async {
    final file = await state.backgroundManager.pickImageFromGallery();
    if (file != null) {
      await _processImage(file);
    }
  }

  // 拍照获取图片
  Future<void> pickImageFromCamera() async {
    final file = await state.backgroundManager.pickImageFromCamera();
    if (file != null) {
      await _processImage(file);
    }
  }

  // 处理选择的图片
  Future<void> _processImage(File file) async {
    final savedPath = await state.backgroundManager.saveBackground(file);
    if (savedPath != null) {
      state.backgrounds.add(savedPath);
      state.backgroundManager.setCurrentBackground(savedPath);
      Log.i('保存背景图片成功: $savedPath');
    } else {
      Get.snackbar(l10n.error, l10n.saveBackgroundFailed,
          snackPosition: SnackPosition.BOTTOM);
      Log.e('保存背景图片失败');
    }
  }

  /// 删除背景图片
  Future<void> deleteBackground(String path) async {
    if (await state.backgroundManager.deleteBackground(path)) {
      state.backgrounds.remove(path);
    } else {
      Get.snackbar(l10n.error, l10n.deleteBackgroundFailed,
          snackPosition: SnackPosition.BOTTOM);
    }
  }

  /// 设置当前背景
  Future<void> setCurrentBackground(String path) async {
    await state.backgroundManager.setCurrentBackground(path);
  }

  /// 设置背景透明度
  void setBackgroundOpacity(double opacity) {
    state.backgroundManager.setBackgroundOpacity(opacity);
  }

  /// 设置背景模糊度
  void setBackgroundBlur(double blur) {
    state.backgroundManager.setBackgroundBlur(blur);
  }
}
