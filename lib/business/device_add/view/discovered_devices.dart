import 'package:flutter/material.dart';
import 'package:topping_home/theme/text_styles.dart';

import '../../../common/util/i18n.dart';
import '../../../models/device_entity.dart';
import 'device_item.dart';

/// 发现的设备列表
class DiscoveredDevices extends StatelessWidget {
  final List<DeviceEntity> devices;
  final Function(DeviceEntity) onDeviceSelected;

  const DiscoveredDevices({
    super.key,
    required this.devices,
    required this.onDeviceSelected,
  });

  @override
  Widget build(BuildContext context) {
    return SliverToBoxAdapter(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          // 发现设备
          Padding(
            padding: const EdgeInsets.fromLTRB(16, 24, 16, 16),
            child: Text(
              devices.length == 1
                  ? l10n.discoveredDevices
                  : l10n.discoveredDevicesHint,
              style: TextStyles.instance.h2(),
              textAlign: TextAlign.center,
            ),
          ),
          // 轻触连接
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: Text(
              l10n.tapToConnect,
              style: TextStyles.instance.h3(),
              textAlign: TextAlign.center,
            ),
          ),
          const SizedBox(height: 10),
          // 设备列表
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: devices.length == 1
                ? SizedBox(
                    width: double.infinity,
                    child: DeviceItem(
                      device: devices[0],
                      horizontal: false,
                      onTap: () => onDeviceSelected(devices[0]),
                    ),
                  )
                : GridView.builder(
                    shrinkWrap: true,
                    physics: const NeverScrollableScrollPhysics(),
                    gridDelegate:
                        const SliverGridDelegateWithFixedCrossAxisCount(
                      crossAxisCount: 2,
                      crossAxisSpacing: 12,
                      mainAxisSpacing: 12,
                      childAspectRatio: 0.75,
                    ),
                    itemCount: devices.length,
                    itemBuilder: (context, index) => DeviceItem(
                      device: devices[index],
                      horizontal: false,
                      onTap: () => onDeviceSelected(devices[index]),
                    ),
                  ),
          ),
        ],
      ),
    );
  }
}
