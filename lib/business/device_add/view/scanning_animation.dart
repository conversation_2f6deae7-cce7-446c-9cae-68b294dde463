import 'package:flutter/material.dart';
import 'package:topping_home/theme/color_palettes.dart';

/// 雷达搜索动画 - 优化版本
class ScanningAnimation extends StatelessWidget {
  final Animation<double> animation;

  const ScanningAnimation({super.key, required this.animation});

  // 预先创建静态元素
  static final _centerDotDecoration = BoxDecoration(
    shape: BoxShape.circle,
    color: ColorPalettes.instance.primary,
    boxShadow: [
      BoxShadow(
        color: ColorPalettes.instance.primary.withAlpha(128),
        blurRadius: 10,
        spreadRadius: 0,
      ),
    ],
  );

  // 波纹装饰 - 带填充的波纹
  static BoxDecoration _getRippleDecoration(double opacity) => BoxDecoration(
    shape: BoxShape.circle,
    color: ColorPalettes.instance.primary.withAlpha((opacity * 0.15 * 255).toInt()),
    border: Border.all(
      color: ColorPalettes.instance.primary.withAlpha((opacity * 0.6 * 255).toInt()),
      width: 2.0,
    ),
  );

  @override
  Widget build(BuildContext context) {
    // 预先计算好中心点部分，避免每次都重新构建
    final centerDot = Container(
      width: 50, // 进一步增大中心点大小
      height: 50, // 进一步增大中心点大小
      decoration: _centerDotDecoration,
    );

    return RepaintBoundary(
      child: SizedBox(
        height: 300, // 从200增加到300
        width: 300, // 从200增加到300
        child: Stack(
          alignment: Alignment.center,
          children: [
            // 多条波纹效果 - 使用不同的时间间隔
            _buildRadarRipple(
              animation.drive(CurveTween(
                curve: const Interval(0.0, 0.7, curve: Curves.easeOutQuad),
              )),
              0.95,
            ),
            _buildRadarRipple(
              animation.drive(CurveTween(
                curve: const Interval(0.2, 0.9, curve: Curves.easeOutQuad),
              )),
              0.85,
            ),
            _buildRadarRipple(
              animation.drive(CurveTween(
                curve: const Interval(0.4, 1.0, curve: Curves.easeOutQuad),
              )),
              0.75,
            ),
            _buildRadarRipple(
              animation.drive(CurveTween(
                curve: const Interval(0.6, 1.0, curve: Curves.easeOutQuad),
              )),
              0.65,
            ),

            // 中心点
            centerDot,
          ],
        ),
      ),
    );
  }

  /// 构建雷达波纹 - 带填充效果
  Widget _buildRadarRipple(Animation<double> animation, double opacityFactor) {
    return RepaintBoundary(
      child: AnimatedBuilder(
        animation: animation,
        builder: (context, child) {
          final double animationValue = animation.value;
          final double opacity = (1 - animationValue).clamp(0.0, 1.0) * opacityFactor;
          final double scale = animationValue;

          return Opacity(
            opacity: opacity,
            child: Transform.scale(
              scale: scale,
              child: Container(
                width: 300, // 从200增加到300
                height: 300, // 从200增加到300
                decoration: _getRippleDecoration(opacity),
              ),
            ),
          );
        },
      ),
    );
  }
}
