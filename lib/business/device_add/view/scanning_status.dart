import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:topping_home/common/util/log_util.dart';
import 'package:topping_home/theme/color_palettes.dart';
import 'package:topping_home/theme/text_styles.dart';

import '../../../common/util/i18n.dart';
import '../../../repositories/device_repository.dart';
import '../device_add_logic.dart';

/// 扫描状态
class ScanningStatus extends StatefulWidget {
  final bool isScanning;
  final int deviceCount;

  const ScanningStatus({
    super.key,
    required this.isScanning,
    required this.deviceCount,
  });

  @override
  State<ScanningStatus> createState() => _ScanningStatusState();
}

class _ScanningStatusState extends State<ScanningStatus> {
  // 使用状态变量跟踪是否是第一次构建
  bool isFirstBuild = true;

  @override
  Widget build(BuildContext context) {
    // 记录实际传入的状态参数
    Log.i('进入 ScanningStatus build isScanning: ${widget.isScanning}, deviceCount: ${widget.deviceCount}, isFirstBuild: $isFirstBuild');

    // 如果是第一次构建，返回一个空的Container而不是无设备UI
    // 这样可以避免初始化时闪现"无设备"提示
    if (isFirstBuild) {
      // 设置为false，确保之后的构建能正常显示
      WidgetsBinding.instance.addPostFrameCallback((_) {
        setState(() {
          isFirstBuild = false;
        });
        Log.i('已将isFirstBuild设置为false，将在下一帧显示实际内容');
      });

      Log.i('第一次构建ScanningStatus，返回空容器');
      return const SizedBox.shrink();
    }

    // 这里保留原始的Log.e，方便与之前的日志对比
    Log.e('进入 ScanningStatus build isScanning: ${widget.isScanning}, deviceCount: ${widget.deviceCount}');
    return SingleChildScrollView(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(
                widget.isScanning
                    ? l10n.scanning
                    : widget.deviceCount > 0
                        ? '${l10n.foundDevice} ${widget.deviceCount} ${l10n.devices}'
                        : l10n.deviceNotFoundTitle,
                style: TextStyles.instance.h2(),
                textAlign: TextAlign.center,
              ),
              if (widget.isScanning) ...[
                const SizedBox(width: 8),
                SizedBox(
                  width: 16,
                  height: 16,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    valueColor: AlwaysStoppedAnimation<Color>(
                      ColorPalettes.instance.primary,
                    ),
                  ),
                ),
              ],
              if (!widget.isScanning) ...[
                const SizedBox(width: 8),
                IconButton(
                  icon: Icon(Icons.refresh,
                      color: ColorPalettes.instance.firstText),
                  onPressed: () => Get.find<DeviceAddLogic>().refreshScanning(),
                ),
              ],
            ],
          ),
          if (widget.deviceCount == 0 && !widget.isScanning) ...[
            const SizedBox(height: 16),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(l10n.deviceNotFoundHint1,
                      style: TextStyles.instance.h3()),
                  const SizedBox(height: 8),
                  Text(l10n.deviceNotFoundHint2,
                      style: TextStyles.instance.h3()),
                  const SizedBox(height: 8),
                  Text(l10n.deviceNotFoundHint3,
                      style: TextStyles.instance.h3()),
                  const SizedBox(height: 8),
                  Text(l10n.deviceNotFoundHint4,
                      style: TextStyles.instance.h3()),
                  const SizedBox(height: 24),
                  if (Get.find<DeviceRepository>().getAllDevices().isNotEmpty)
                    Container(
                      height: 1,
                      color: ColorPalettes.instance.divider,
                      margin: const EdgeInsets.only(bottom: 16),
                    ),
                  ...Get.find<DeviceRepository>()
                      .getAllDevices()
                      .map((device) => Obx(() {
                            final logic = Get.find<DeviceAddLogic>();
                            final isConnecting =
                                logic.state.isDeviceConnecting(device.id);
                            final isConnected = device.connected == 1;
                            return Card(
                              margin: const EdgeInsets.only(bottom: 8),
                              color: ColorPalettes.instance.card,
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(8),
                                side: BorderSide(
                                    color: ColorPalettes.instance.divider),
                              ),
                              clipBehavior: Clip.antiAlias,
                              child: InkWell(
                                splashColor: ColorPalettes.instance.primary.withAlpha(26),
                                highlightColor: ColorPalettes.instance.primary.withAlpha(13),
                                onTap: isConnecting
                                    ? null
                                    : () => logic.onDeviceSelected(device),
                                child: ListTile(
                                  title: Text(
                                    device.name,
                                    style: TextStyles.instance.h2(),
                                  ),
                                  subtitle: Text(
                                    device.macAddress,
                                    style: TextStyles.instance.h3(),
                                  ),
                                  trailing: isConnecting
                                      ? SizedBox(
                                          width: 20,
                                          height: 20,
                                          child: CircularProgressIndicator(
                                            strokeWidth: 2,
                                            valueColor: AlwaysStoppedAnimation<
                                                    Color>(
                                                ColorPalettes.instance.primary),
                                          ),
                                        )
                                      : isConnected
                                          ? Icon(Icons.check_circle,
                                              color: ColorPalettes
                                                  .instance.success)
                                          : Icon(Icons.bluetooth,
                                              color: ColorPalettes
                                                  .instance.firstText),
                                ),
                              ),
                            );
                          })),
                ],
              ),
            ),
          ],
        ],
      ),
    );
  }
}
