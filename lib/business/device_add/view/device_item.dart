import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:topping_home/common/util/i18n.dart';
import 'package:topping_home/theme/color_palettes.dart';
import 'package:topping_home/theme/text_styles.dart';

import '../../../models/device_entity.dart';
import '../device_add_logic.dart';

/// 设备列表项
class DeviceItem extends StatelessWidget {
  final DeviceEntity device;
  final bool horizontal;
  final VoidCallback? onTap;

  const DeviceItem({
    super.key,
    required this.device,
    this.horizontal = false,
    this.onTap,
  });

  // 预先创建装饰对象，避免重复创建
  static BoxDecoration _getCardDecoration() => BoxDecoration(
    // 使用浅色背景，增强对比度
    color: ColorPalettes.instance.background,
    borderRadius: BorderRadius.circular(12),
    border: Border.all(
      color: ColorPalettes.instance.primary.withValues(alpha: 0.12),
      width: 1,
    ),
    // 添加阴影增强立体感
    boxShadow: [
      BoxShadow(
        color: ColorPalettes.instance.shadow.withValues(alpha: 0.06),
        blurRadius: 4,
        offset: const Offset(0, 2),
      ),
    ],
  );

  static BoxDecoration _getOverlayDecoration() => BoxDecoration(
    // 半透明背景
    color: ColorPalettes.instance.background.withValues(alpha: 0.9),
    borderRadius: BorderRadius.circular(12),
  );

  // 预先创建连接中的进度指示器
  static Widget _getConnectingIndicator() => SizedBox(
    width: 24.w,
    height: 24.h,
    child: CircularProgressIndicator(
      strokeWidth: 2.w,
      valueColor: AlwaysStoppedAnimation<Color>(
        ColorPalettes.instance.primary,
      ),
    ),
  );

  @override
  Widget build(BuildContext context) {
    // 获取设备连接状态
    final logic = Get.find<DeviceAddLogic>();
    final state = logic.state;

    return Obx(() {
      final isConnecting = state.isDeviceConnecting(device.id);

      return Material(
        color: ColorPalettes.instance.transparent,
        child: Ink(
          decoration: _getCardDecoration(),
          child: InkWell(
            onTap: isConnecting ? null : onTap, // 连接中时禁用点击
            borderRadius: BorderRadius.circular(8.r),
            splashColor: ColorPalettes.instance.primary.withValues(alpha: 0.1),
            highlightColor: ColorPalettes.instance.primary.withValues(alpha: 0.05),
            child: Padding(
              padding: EdgeInsets.all(8.w),
              child: Stack(
                children: [
                  horizontal
                      ? _buildHorizontalLayout()
                      : _buildVerticalLayout(),
                  // 连接中状态指示器
                  if (isConnecting)
                    Positioned.fill(
                      child: Container(
                        decoration: _getOverlayDecoration(),
                        child: Center(
                          child: Column(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              _getConnectingIndicator(),
                              SizedBox(height: 8.h),
                              Text(
                                l10n.connecting,
                                style: TextStyles.instance.h3(),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                ],
              ),
            ),
          ),
        ),
      );
    });
  }

  /// 水平布局
  Widget _buildHorizontalLayout() {
    return Row(
      children: [
        _buildDeviceImage(),
        SizedBox(width: 12.w),
        Expanded(child: _buildDeviceName()),
      ],
    );
  }

  /// 垂直布局
  Widget _buildVerticalLayout() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      mainAxisSize: MainAxisSize.min,
      children: [
        Flexible(
          fit: FlexFit.loose,
          child: _buildDeviceImage(),
        ),
        SizedBox(height: 8.h), // 减小间距
        _buildDeviceName(),
      ],
    );
  }

  /// 设备图片 - 使用缓存图片
  Widget _buildDeviceImage() {
    // 如果是单个设备视图，使用更大的图标
    final double imageSize = horizontal ? 80 : 140;

    return SizedBox(
      width: horizontal ? imageSize : double.infinity,
      height: horizontal ? imageSize : imageSize,
      child: AspectRatio(
          aspectRatio: 1,
          child: Image.asset(
            device.image,
            fit: BoxFit.contain,
            cacheWidth: 200, // 指定缓存宽度，减少内存使用
            cacheHeight: 200, // 指定缓存高度
            errorBuilder: (context, error, stackTrace) {
              return Icon(Icons.broken_image,
                  color: ColorPalettes.instance.secondText);
            },
          )),
    );
  }

  /// 设备名称
  Widget _buildDeviceName() {
    return Column(
      children: [
        // 设备名称
        Text(
          device.name.toUpperCase(),
          style: TextStyles.instance.h2(
            // 如果是单个设备视图，使用更大的字体
            fontSize: horizontal ? null : 20,
            fontWeight: FontWeight.bold,
          ),
          textAlign: horizontal ? TextAlign.left : TextAlign.center,
          maxLines: 2,
          overflow: TextOverflow.ellipsis,
        ),
        // 如果是单个设备视图，显示设备类型
        if (!horizontal) ...[
          const SizedBox(height: 8),
          Text(
            l10n.devices, // 使用国际化字符串“设备”
            style: TextStyles.instance.body1(
              color: ColorPalettes.instance.secondText,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ],
    );
  }
}
