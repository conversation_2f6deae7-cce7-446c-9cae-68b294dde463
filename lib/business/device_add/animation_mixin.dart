import 'package:flutter/material.dart';

/// 设备添加页面动画混入
mixin DeviceAddAnimationMixin<T extends StatefulWidget>
    on State<T>, TickerProvider {
  late AnimationController animationController;
  late Animation<double> animation;

  @override
  void initState() {
    super.initState();
    _initAnimation();
  }

  void _initAnimation() {
    animationController = AnimationController(
      duration: const Duration(milliseconds: 2500), // 增加动画时长，减少刷新频率
      vsync: this,
    );
    animation = CurvedAnimation(
      parent: Tween<double>(begin: 0.0, end: 1.0).animate(animationController),
      curve: Curves.linear,
    );
  }

  void startAnimation() {
    if (mounted && !animationController.isAnimating) {
      // 使用微任务确保动画不与UI更新冲突
      Future.microtask(() {
        if (mounted) {
          animationController.repeat();
        }
      });
    }
  }

  void stopAnimation() {
    if (animationController.isAnimating) {
      animationController.stop();
    }
  }

  @override
  void dispose() {
    // 确保在dispose时不再操作动画控制器
    if (animationController.isAnimating) {
      animationController.stop();
    }
    animationController.dispose();
    super.dispose();
  }
}
