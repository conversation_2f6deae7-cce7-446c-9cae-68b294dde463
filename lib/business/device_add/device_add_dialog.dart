import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:topping_home/common/util/log_util.dart';
import 'package:topping_home/theme/color_palettes.dart';
import 'package:topping_home/theme/text_styles.dart';
import '../../common/util/i18n.dart';
import 'device_add_logic.dart';
import 'animation_mixin.dart';
import 'view/scanning_animation.dart';
import 'view/scanning_status.dart' as status_widget;
import 'view/device_item.dart';
import 'device_add_state.dart';

/// 设备添加对话框
class DeviceAddDialog extends StatefulWidget {
  DeviceAddDialog({super.key}) {
    Get.put(DeviceAddLogic());
  }

  @override
  State<DeviceAddDialog> createState() => DeviceAddPageState();
}

class DeviceAddPageState extends State<DeviceAddDialog>
    with SingleTickerProviderStateMixin, DeviceAddAnimationMixin {
  DeviceAddPageState() {
    Get.put(this);
  }
  late final logic = Get.find<DeviceAddLogic>();
  late final state = Get.find<DeviceAddLogic>().state;

  // 简化标志 - 只使用一个标志控制初始加载
  bool _initialRenderComplete = false;

  // 设置初始渲染完成标志
  void setInitialRenderComplete(bool value) {
    if (_initialRenderComplete != value) {
      setState(() {
        _initialRenderComplete = value;
      });
    }
  }

  // 重置扫描状态
  void resetScanState() {
    Log.i("===== 重置扫描状态 =====");
    _initialRenderComplete = false;
  }

  @override
  void initState() {
    super.initState();
    Log.i("===== 初始化开始 =====");

    // 初始化时设置扫描状态
    if (state.selectedTabIndex.value == 0) {
      state.scanningStatus.value = ScanningStatus.scanning;
      Log.i("初始化: 设置扫描状态为扫描中");
      startAnimation();
    }

    // 监听扫描状态变化
    ever(state.scanningStatus, (status) {
      Log.i("扫描状态变化: $status, 初始渲染完成=$_initialRenderComplete");

      if (status == ScanningStatus.scanning) {
        startAnimation();
      } else {
        stopAnimation();

        // 当完成第一次扫描时，标记初始渲染已完成
        if (!_initialRenderComplete) {
          _initialRenderComplete = true;
          Log.i("第一次扫描完成，标记初始渲染完成");
        }
      }
    });

    ever(state.connectionSuccess, (success) {
      if (success) {
        Log.i('连接成功');
        if (state.selectedDevice.value != null) {
          Log.i('连接设备: ${state.selectedDevice.value!.name}');
        }
      }
    });

    // 延迟100毫秒后再开始扫描，确保UI已经稳定
    Future.delayed(const Duration(milliseconds: 100), () {
      if (state.selectedTabIndex.value == 0 && mounted) {
        Log.i("延迟后开始扫描...");
        // 启动扫描
        state.scanningStatus.value = ScanningStatus.scanning;
        logic.startScanning();
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    Log.i("构建对话框 initialRenderComplete=$_initialRenderComplete");

    return Container(
      height: MediaQuery.of(context).size.height * 0.5,
      decoration: BoxDecoration(
        color: ColorPalettes.instance.card,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(24.r),
          topRight: Radius.circular(24.r),
        ),
      ),
      child: Column(
        children: [
          // 添加顶部间距
          SizedBox(height: 12),
          _buildHeader(),
          Expanded(
            child: Obx(() => _buildContent()),
          ),
        ],
      ),
    );
  }

  /// 构建标题栏
  Widget _buildHeader() {
    return Container(
      height: 60,
      padding: EdgeInsets.symmetric(horizontal: 16.w),
      decoration: BoxDecoration(
        color: ColorPalettes.instance.card,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(24.r),
          topRight: Radius.circular(24.r),
        ),
      ),
      child: Stack(
        alignment: Alignment.center,
        children: [
          Align(
            alignment: Alignment.centerLeft,
            child: IconButton(
              visualDensity: VisualDensity.compact,
              padding: EdgeInsets.zero,
              constraints: const BoxConstraints(),
              icon: Icon(Icons.close, color: ColorPalettes.instance.firstText),
              onPressed: () {
                state.connectionSuccess.value = false;
                // 先停止扫描
                logic.stopScanning();
                // 清空扫描数据
                state.resetScanningState();
                // 关闭弹窗
                Get.back();
              },
            ),
          ),
          Text(
            l10n.addDevice,
            style: TextStyles.instance.h2(),
          ),
        ],
      ),
    );
  }

  /// 构建内容
  Widget _buildContent() {
    Log.i("构建内容: 扫描状态=${state.scanningStatus.value}, 初始渲染完成=$_initialRenderComplete, 设备数量=${state.discoveredDevices.length}");

    // 如果连接成功，显示连接成功UI
    if (state.connectionSuccess.value) {
      Log.i("显示连接成功UI");
      return Container(
        color: ColorPalettes.instance.card,
        child: Center(
          child: SingleChildScrollView(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              mainAxisSize: MainAxisSize.min, // 使用最小空间
              children: [
                if (state.selectedDevice.value?.image != null)
                  Image.asset(
                    state.selectedDevice.value!.image,
                    width: 180.w, // 减小尺寸
                    height: 180.h, // 减小尺寸
                  ),
                SizedBox(height: 16.h), // 减小间距
                Text(
                  state.selectedDevice.value?.name ?? '',
                  style: TextStyles.instance.h2(),
                ),
                SizedBox(height: 8.h), // 减小间距
                Text(
                  l10n.connected,
                  style: TextStyles.instance.h2(),
                ),
              ],
            ),
          ),
        ),
      );
    }

    // 基于扫描状态决定显示内容
    switch (state.scanningStatus.value) {
      case ScanningStatus.scanning:
        // 如果有设备，直接显示设备列表，不再使用_initialRenderComplete标志
        if (state.discoveredDevices.isNotEmpty) {
          // 确保初始渲染完成标志设置为true
          if (!_initialRenderComplete) {
            WidgetsBinding.instance.addPostFrameCallback((_) {
              setInitialRenderComplete(true);
            });
          }
          return _buildDeviceList();
        }
        // 否则显示扫描动画
        return Column(
          children: [
            Expanded(
              child: ScanningAnimation(
                animation: animation,
              ),
            ),
          ],
        );

      case ScanningStatus.completed:
      case ScanningStatus.error:
        // 完成扫描 - 显示设备列表或空状态
        return state.discoveredDevices.isEmpty
            ? const status_widget.ScanningStatus(
                isScanning: false,
                deviceCount: 0,
              )
            : _buildDeviceList();

      case ScanningStatus.initial:
      // 初始状态 - 显示扫描状态
        return const status_widget.ScanningStatus(
          isScanning: true,
          deviceCount: 0,
        );
    }
  }

  /// 构建设备列表
  Widget _buildDeviceList() {
    // 单个设备时使用特殊布局
    if (state.discoveredDevices.length == 1) {
      return _buildSingleDeviceView(state.discoveredDevices[0]);
    }

    // 多个设备时使用网格布局
    final gridDelegate = SliverGridDelegateWithFixedCrossAxisCount(
      crossAxisCount: 2,
      crossAxisSpacing: 16, // 增加间距
      mainAxisSpacing: 16, // 增加间距
      childAspectRatio: 0.8, // 调整宽高比
    );

    return GridView.builder(
      shrinkWrap: true,
      physics: const ClampingScrollPhysics(),
      gridDelegate: gridDelegate,
      itemCount: state.discoveredDevices.length,
      // 使用缓存构建器来减少重建
      itemBuilder: (context, index) {
        final device = state.discoveredDevices[index];
        return DeviceItem(
          key: ValueKey(device.id), // 添加key以便Flutter可以识别和重用元素
          device: device,
          horizontal: false,
          onTap: () => logic.onDeviceSelected(device),
        );
      },
      // 使用缓存范围来减少重建
      cacheExtent: 500, // 增加缓存范围，减少重建
    );
  }

  /// 为单个设备构建特殊布局
  Widget _buildSingleDeviceView(device) {
    return Column(
      children: [
        // 标题区域，显示状态
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(
                l10n.discoveredDevices,
                style: TextStyles.instance.h2(),
                textAlign: TextAlign.center,
              ),
              if (state.scanningStatus.value == ScanningStatus.scanning) ...[
                const SizedBox(width: 8),
                SizedBox(
                  width: 16,
                  height: 16,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    valueColor: AlwaysStoppedAnimation<Color>(
                      ColorPalettes.instance.primary,
                    ),
                  ),
                ),
              ],
            ],
          ),
        ),
        // 轻触连接提示
        Padding(
          padding: const EdgeInsets.only(bottom: 8),
          child: Text(
            l10n.tapToConnect,
            style: TextStyles.instance.body1(
              color: ColorPalettes.instance.secondText,
            ),
            textAlign: TextAlign.center,
          ),
        ),
        // 单个设备显示 - 居中大图显示
        Expanded(
          child: Center(
            child: Padding(
              padding: const EdgeInsets.all(24),
              child: DeviceItem(
                device: device,
                horizontal: false, // 使用竖直布局
                onTap: () => logic.onDeviceSelected(device),
              ),
            ),
          ),
        ),
      ],
    );
  }

  @override
  void reassemble() {
    super.reassemble();

    if (state.selectedTabIndex.value == 0) {
      // 重置首次加载标志，避免热重载时闪烁
      _initialRenderComplete = false;

      // 先设置扫描状态为true避免闪烁
      state.scanningStatus.value = ScanningStatus.scanning;
      startAnimation();

      // 停止当前扫描并重新开始
      logic.stopScanning();
      logic.startScanning();
    }
  }

  @override
  void dispose() {
    logic.stopScanning();
    Get.delete<DeviceAddPageState>();
    super.dispose();
  }
}
