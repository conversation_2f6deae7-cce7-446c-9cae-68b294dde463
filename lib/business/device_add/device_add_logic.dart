import 'dart:async';

import 'package:get/get.dart';
import 'package:topping_ble_control/device/device_factory.dart';
import 'package:topping_ble_control/event/connection_state_event.dart';
import 'package:topping_ble_control/model/bluetooth/ble_device.dart';
import 'package:topping_ble_control/model/enums/ble_connection_state.dart';
import 'package:topping_ble_control/registry/device_data_manager.dart';
import 'package:topping_home/models/device_entity.dart';
import 'package:topping_home/router/routers.dart';

import '../../common/converters/ble_device_converter.dart';
import '../../common/util/log_util.dart';
import '../../repositories/device_repository.dart';
import '../../utils/device_sync_util.dart';
import 'device_add_state.dart';
import 'device_add_dialog.dart';

/// 设备添加逻辑
class DeviceAddLogic extends GetxController {
  final String tag = "DeviceAddLogic";

  // 设备仓库
  final DeviceRepository storage = Get.find<DeviceRepository>();

  // 设备添加状态
  final DeviceAddState state = DeviceAddState();

  // 使用 DeviceFactory
  final DeviceFactory _deviceFactory = DeviceFactory();

  late StreamSubscription<ConnectionStateEvent> _connectionSubscription;
  late StreamSubscription<BleDiscoveryState> _discoverySubscription;

  @override
  void onInit() {
    super.onInit();

    // 新增：监听合并后的发现状态流
    _discoverySubscription = _deviceFactory.discoveryState
        .listen(_onDiscoveryStateChanged, onError: (error) {
      Log.e("$tag 发现状态流错误: $error");
    });

    // 使用 DeviceFactory 监听连接状态
    _connectionSubscription = _deviceFactory.connectionState.listen(
      _onConnectionStateChanged,
    );
  }

  /// 新增: 处理合并后的发现状态
  void _onDiscoveryStateChanged(BleDiscoveryState discoveryState) {
    Log.d(
        "$tag Discovery state changed: scanning=${discoveryState.isScanning}, devices=${discoveryState.devices.length}");
    // 更新扫描状态枚举
    state.scanningStatus.value = discoveryState.isScanning
        ? ScanningStatus.scanning
        : ScanningStatus.completed;

    // 处理扫描结果
    _onScanResults(discoveryState.devices);
  }

  /// 处理扫描结果 (现在由 _onDiscoveryStateChanged 调用)
  void _onScanResults(List<BleDevice> devices) {
    if (devices.isEmpty) return;

    // 简单记录扫描到的设备
    for (final device in devices) {
      Log.i("$tag 扫描到设备: ${device.name} - ${device.nativeHandle} - ${device.deviceType.name}");
    }

    Log.i("$tag 处理扫描结果: ${devices.length} 个设备");

    // 使用BleDeviceConverter将BleDevice转换为DeviceEntity
    final List<DeviceEntity> processedDevices = BleDeviceConverter.fromBleDevices(devices);
    
    if (processedDevices.isEmpty) return;
    
    // 直接更新UI
    _updateUI(processedDevices);
  }

  /// 直接更新UI
  void _updateUI(List<DeviceEntity> devices) {
    // 获取动画控制器
    DeviceAddPageState? pageState;
    try {
      pageState = Get.find<DeviceAddPageState>();
    } catch (e) {
      Log.e("$tag 无法获取DeviceAddPageState: $e");
      return;
    }

    // 按信号强度排序并限制数量
    devices.sort((a, b) => (b.rssi ?? -100).compareTo(a.rssi ?? -100));
    final limitedDevices = devices.take(8).toList(); // 限制为8个设备

    // 直接更新UI
    if (!Get.isRegistered<DeviceAddPageState>()) return;

    // 更新状态中的设备列表
    state.discoveredDevices.value = limitedDevices;

    // 确保初始渲染完成标志设置为true
    pageState.setInitialRenderComplete(true);

    // 恢复动画
    if (state.scanningStatus.value == ScanningStatus.scanning &&
        pageState.animationController.isAnimating == false) {
      pageState.startAnimation();
    }
  }

  /// 开始扫描
  void startScanning() {
    state.clearDiscoveredDevices();
    _deviceFactory.startScan();
    Log.i("$tag 开始扫描蓝牙设备");
  }

  /// 停止扫描
  void stopScanning() {
    _deviceFactory.stopScan();
    Log.i("$tag 停止扫描蓝牙设备");
  }

  /// 刷新扫描
  void refreshScanning() {
    stopScanning(); // 先停止扫描
    state.clearDiscoveredDevices(); // 清空设备列表
    startScanning(); // 重新开始扫描
  }

  /// 连接状态变更处理
  void _onConnectionStateChanged(ConnectionStateEvent stateEvent) {
    final deviceHandle = stateEvent.deviceHandle;
    if (deviceHandle == null) {
      Log.e("$tag 连接状态事件缺少设备句柄");
      state.connectingDeviceId.value = null; // 清空连接状态
      return;
    }

    // 尝试通过 handle 从 state 中找到设备 ID
    final deviceEntity = state.discoveredDevices
            .firstWhereOrNull((d) => d.bleDeviceId == deviceHandle) ??
        state.selectedDevice.value; // 也可能是当前选中的设备

    final deviceId = deviceEntity?.id;
    if (deviceId == null) {
      Log.w("$tag 收到句柄 $deviceHandle 的状态更新，但在已知设备中找不到对应 ID");
      // 如果是断开连接，也清空全局状态
      if (stateEvent.state == BleConnectionState.disconnected) {
        state.connectingDeviceId.value = null;
      }
      return;
    }

    Log.i("$tag 设备 $deviceId (句柄: $deviceHandle) 连接状态变为: ${stateEvent.state}");

    switch (stateEvent.state) {
      case BleConnectionState.connecting:
        state.connectingDeviceId.value = deviceId;
        break;
      case BleConnectionState.connected:
        state.connectingDeviceId.value = null; // 清空连接状态
        if (deviceEntity != null) {
          Log.i("$tag 设备 ${deviceEntity.name} 连接成功，准备同步数据并跳转。deviceModel: ${deviceEntity.deviceModel}");
          // 标记设备为已连接
          deviceEntity.connected = 1;
          // 保存设备信息到仓库
          storage.saveDevice(deviceEntity);

          // 关闭任何现有的弹窗
          Get.back(closeOverlays: true);

          // === 添加数据同步和跳转逻辑 ===
          DeviceSyncUtil.syncDeviceAndNavigate(
            device: deviceEntity,
            deviceFactory: _deviceFactory, // 传递 DeviceFactory 实例
            onSyncComplete: () {
              // 可选：同步完成后的回调，例如显示提示
              Log.i("$tag 数据同步完成，准备跳转到首页或设备详情页");
              Get.toNamed(AppRoutes.deviceDetailPage, arguments: deviceEntity);
            },
          );
          // 确保在同步和导航后返回，避免执行后续的 disconnect 逻辑
          return;
        } else {
          Log.e("$tag 连接成功但找不到对应的 DeviceEntity");
        }
        break;
      case BleConnectionState.disconnected:
        state.connectingDeviceId.value = null; // 清空连接状态
        if (deviceEntity != null) {
          // 更新 UI 和存储中的状态为未连接
          deviceEntity.connected = 0;
          storage.saveDevice(deviceEntity);
          state.discoveredDevices.refresh(); // 刷新列表UI
          Log.i("$tag 设备 ${deviceEntity.name} 已断开连接");
        }
        break;
      default:
        // 其他状态（如失败）也应清空连接状态
        state.connectingDeviceId.value = null;
        break;
    }

    // 触发 UI 更新
    state.discoveredDevices.refresh();
  }

  /// 连接设备
  void connectToDevice(DeviceEntity device) async {
    Log.i(
      "$tag 准备连接设备: ${device.name} (句柄: ${device.bleDeviceId})",
    );

    // 更新正在连接的设备ID
    state.connectingDeviceId.value = device.id;

    // 查找最新的 BleDevice 对象 (优先使用MAC)
    BleDevice? bleDeviceToConnect;
    if (device.macAddress.isNotEmpty) {
      // 使用工厂构造函数获取实例
      bleDeviceToConnect =
          DeviceDataManager().findDevice(macAddress: device.macAddress);
      Log.i("$tag 通过 MAC 地址查找 BleDevice: ${bleDeviceToConnect?.name}");
    }
    // 如果 MAC 查找失败或没有 MAC，尝试用句柄查找 (作为后备)
    if (bleDeviceToConnect == null && device.bleDeviceId != null) {
      Log.w("$tag MAC查找失败，尝试通过句柄 ${device.bleDeviceId} 查找 BleDevice");
      bleDeviceToConnect =
          await _deviceFactory.findDeviceByHandle(device.bleDeviceId!);
    }

    if (bleDeviceToConnect == null) {
      Log.e(
        "$tag 无法找到用于连接的 BleDevice 对象 (Name: ${device.name}, MAC: ${device.macAddress}, Handle: ${device.bleDeviceId}). 连接失败。",
      );
      state.connectingDeviceId.value = null; // 清空连接状态
      Get.snackbar("连接失败", "找不到设备信息，请刷新或重新扫描");
      return;
    }

    // (可选) 更新 DeviceEntity 中的 handle，以防万一
    device.bleDeviceId = bleDeviceToConnect.nativeHandle;
    state.selectedDevice.value = device; // 更新选中的设备状态

    try {
      await _deviceFactory.connectDevice(bleDeviceToConnect);
      Log.i("$tag 连接命令已发送: ${bleDeviceToConnect.name}");
      // 连接状态由 _onConnectionStateChanged 处理
    } catch (e) {
      Log.e("$tag 连接设备 ${bleDeviceToConnect.name} 时出错: $e");
      state.connectingDeviceId.value = null; // 清空连接状态
      Get.snackbar("连接错误", "连接时发生错误: $e");
      // 尝试断开以清理状态
      await _deviceFactory.disconnectCurrentDevice();
    }
  }

  /// 根据设备句柄查找设备ID
  String? findDeviceIdByHandle(int handle) {
    final device = state.discoveredDevices.firstWhere(
        (device) => device.bleDeviceId == handle,
        orElse: () => DeviceEntity.empty());

    if (device.id.isEmpty) {
      return null;
    }

    return device.id;
  }

  /// 设备选择处理 - 更新为使用 connectingDeviceId
  void onDeviceSelected(DeviceEntity device) {
    // 如果设备正在连接中，则阻止重复连接
    if (state.isDeviceConnecting(device.id)) {
      Log.w("$tag 设备 ${device.name} 正在连接中，请稍后再试");
      return;
    }
    // 如果当前有其他设备正在连接
    if (state.connectingDeviceId.value != null &&
        state.connectingDeviceId.value != device.id) {
      Log.w("$tag 正在连接其他设备 (${state.connectingDeviceId.value})，请稍后");
      return;
    }

    stopScanning();

    // 设置当前选中的设备
    state.selectedDevice.value = device;

    if (device.connected == 1) {
      // 如果设备已连接，直接跳转到设备详情页
      Get.back();
      Get.toNamed(AppRoutes.deviceDetailPage, arguments: device);
    } else {
      // 如果设备未连接，开始连接
      if (device.bleDeviceId != null) {
        Log.i("$tag 选中未连接设备 ${device.name}，开始连接");
        // 使用 connectToDevice，它内部会处理 connectingDeviceId
        connectToDevice(device);
      } else {
        Log.e("$tag 设备 ${device.name} 没有有效的句柄，无法连接");
        Get.snackbar("错误", "设备 ${device.name} 缺少必要信息，无法连接");
      }
    }
  }

  /// 用户选择设备进行连接或断开
  void onTapDiscoveredDevice(DeviceEntity device) {
    // 检查是否正在连接此设备
    if (state.isDeviceConnecting(device.id)) {
      Log.i("$tag 设备 ${device.name} 正在连接中，请稍候");
      return; // 防止重复点击
    }

    // 如果设备已连接（理论上添加页面不应该出现已连接设备，但做个保险）
    if (device.connected == 1) {
      Log.w("$tag 发现列表中出现已连接设备 ${device.name}，尝试断开");
      // 暂无断开逻辑，可以考虑添加或移除
      // disconnectDevice(device);
      return;
    }

    // 如果当前有其他设备正在连接，则忽略本次点击
    if (state.connectingDeviceId.value != null &&
        state.connectingDeviceId.value != device.id) {
      Log.w("$tag 正在连接其他设备 (${state.connectingDeviceId.value})，忽略本次点击");
      return;
    }

    // 如果点击的是当前已选中的设备，且未连接，则开始连接
    if (state.selectedDevice.value?.id == device.id) {
      if (device.bleDeviceId != null) {
        Log.i("$tag 用户再次点击已选中的设备 ${device.name}，开始连接");
        // 使用 connectToDevice 方法，它内部会设置 connectingDeviceId
        connectToDevice(device);
      } else {
        Log.e("$tag 设备 ${device.name} 没有有效的句柄，无法连接");
      }
    } else {
      // 否则，仅选中设备，不立即连接
      Log.i("$tag 用户选中设备: ${device.name}");
      state.selectedDevice.value = device;
    }
  }

  /// 标签切换时完全重置状态
  void onTabSelected(int index) {
    if (state.selectedTabIndex.value != index) {
      // 如果切换到蓝牙标签，先设置扫描状态
      if (index == 0) {
        // 重置状态
        try {
          Get.find<DeviceAddPageState>().resetScanState();
        } catch (e) {
          Log.e("$tag 无法重置扫描状态: $e");
        }

        state.scanningStatus.value = ScanningStatus.scanning;
      } else {
        stopScanning(); // 如果切换到其他标签，停止扫描
      }

      state.selectedTabIndex.value = index;

      // 如果切换到蓝牙标签,自动开始扫描
      if (index == 0) {
        startScanning();
      }
    }
  }

  @override
  void onClose() {
    stopScanning();
    _connectionSubscription.cancel();
    _discoverySubscription.cancel();
    _deviceFactory.dispose();
    // 清理连接状态
    state.connectingDeviceId.value = null;
    state.dispose();
    super.onClose();
  }
}
