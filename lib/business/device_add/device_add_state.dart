import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../gen/assets.gen.dart';
import '../../models/device_entity.dart';

/// 扫描状态枚举
enum ScanningStatus {
  initial, // 初始状态
  scanning, // 正在扫描
  completed, // 扫描完成
  error // 扫描错误
}

/// 设备添加状态
class DeviceAddState {
  // UI状态
  final selectedTabIndex = 0.obs;

  // 使用枚举替代多个布尔变量
  final Rx<ScanningStatus> scanningStatus = ScanningStatus.initial.obs;

  // 正在连接的设备 ID (替换 RxMap<String, bool> connectingDevices)
  final RxnString connectingDeviceId = RxnString(null);

  // 简化的UI状态
  final isLoading = false.obs;
  final connectionSuccess = false.obs;
  final selectedDevice = Rxn<DeviceEntity>();

  // 输入控制
  final ipController = TextEditingController();

  // 统一的设备集合 - 只保留一个设备列表
  final discoveredDevices = <DeviceEntity>[].obs;

  // 检查设备是否正在连接 (使用新的状态变量)
  bool isDeviceConnecting(String deviceId) =>
      connectingDeviceId.value == deviceId;

  // 根据设备名称获取图片
  String getDeviceImage(String deviceName) {
    if (deviceName.contains("DX5")) {
      return Assets.image.icDx5ii.path;
    }
    // 可以添加更多设备的图片匹配
    return '';
  }

  // 添加发现的设备
  void addDiscoveredDevice(DeviceEntity device) {
    if (!discoveredDevices.contains(device)) {
      discoveredDevices.add(device);
    }
  }

  // 清除发现的设备
  void clearDiscoveredDevices() {
    discoveredDevices.clear();
  }

  // 重置所有扫描相关的状态
  void resetScanningState() {
    scanningStatus.value = ScanningStatus.initial;
    clearDiscoveredDevices();
  }

  // 退出时清理所有状态
  void dispose() {
    clearDiscoveredDevices();
    ipController.dispose();
    discoveredDevices.clear();
  }
}
