import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:topping_home/theme/color_palettes.dart';
import 'package:topping_home/theme/text_styles.dart';
import 'register_logic.dart';
import '../../../common/util/i18n.dart';
import '../../../gen/assets.gen.dart';
import 'view/captcha_painter.dart';

/// 注册页面
class RegisterPage extends StatefulWidget {
  const RegisterPage({super.key});

  @override
  State<RegisterPage> createState() => _RegisterPageState();
}

class _RegisterPageState extends State<RegisterPage> {
  final logic = Get.find<RegisterLogic>();
  final state = Get.find<RegisterLogic>().state;

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () => FocusScope.of(context).unfocus(),
      child: Scaffold(
        appBar: AppBar(
          title: Text(l10n.register),
          centerTitle: true,
          elevation: 0,
          backgroundColor: ColorPalettes.instance.transparent,
          leading: IconButton(
            icon: const Icon(Icons.arrow_back),
            onPressed: logic.previousStep,
          ),
        ),
        body: _buildBody(),
      ),
    );
  }

  Widget _buildBody() {
    return SingleChildScrollView(
      child: Padding(
        padding: const EdgeInsets.all(24.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            _buildLogo(),
            const SizedBox(height: 32),
            _buildCurrentStep(),
          ],
        ),
      ),
    );
  }

  Widget _buildLogo() {
    return Column(
      children: [
        const SizedBox(height: 16), // 添加顶部间距
        Hero(
          tag: 'logo',
          child: Container(
            width: 100,
            height: 100,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              boxShadow: [
                BoxShadow(
                  color: ColorPalettes.instance.firstText
                      .withAlpha((0.1 * 255).toInt()),
                  spreadRadius: 1,
                  blurRadius: 10,
                  offset: const Offset(0, 4),
                ),
              ],
              image: DecorationImage(
                image: Assets.icon.icon.provider(),
                fit: BoxFit.cover,
              ),
            ),
          ),
        ),
      ],
    );
  }

  // 根据当前步骤构建不同的内容
  Widget _buildCurrentStep() {
    return Obx(() {
      switch (state.currentStep.value) {
        case 0:
          // 手机号注册步骤
          return _buildPhoneStep();
        case 1:
          // 验证码验证步骤
          return _buildVerificationStep();
        case 2:
          // 注册完成步骤
          return _buildFinalStep();
        default:
          return Container();
      }
    });
  }

  // 构建手机号注册步骤
  Widget _buildPhoneStep() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            Text(
              l10n.register,
              style: TextStyle(
                fontSize: TextStyles.instance.h2().fontSize,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 32),
            _buildPhoneInput(),
            const SizedBox(height: 16),
            _buildCaptchaInput(),
            const SizedBox(height: 32),
            _buildTermsCheckbox(),
            const SizedBox(height: 32),
            _buildNextButton(
              onPressed: logic.requestVerificationCode,
              text: l10n.getVerificationCode,
            ),
          ],
        ),
      ),
    );
  }

  // 构建手机号输入框
  Widget _buildPhoneInput() {
    return TextField(
      controller: state.phoneController,
      decoration: InputDecoration(
        labelText: l10n.phone,
        prefixIcon: const Icon(Icons.phone_android),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide:
              BorderSide(color: ColorPalettes.instance.firstText.withAlpha(30)),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: Theme.of(Get.context!).primaryColor),
        ),
        contentPadding: const EdgeInsets.symmetric(
          horizontal: 16,
          vertical: 16,
        ),
      ),
      keyboardType: TextInputType.phone,
    );
  }

  // 构建验证码输入框
  Widget _buildCaptchaInput() {
    return Row(
      children: [
        Expanded(
          child: TextField(
            controller: state.captchaController,
            decoration: InputDecoration(
              labelText: l10n.verifyCode,
              prefixIcon: const Icon(Icons.security),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(
                    color: ColorPalettes.instance.firstText.withAlpha(30)),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide:
                    BorderSide(color: Theme.of(Get.context!).primaryColor),
              ),
              contentPadding: const EdgeInsets.symmetric(
                horizontal: 16,
                vertical: 16,
              ),
            ),
          ),
        ),
        const SizedBox(width: 16),
        GestureDetector(
          onTap: logic.generateCaptchaCode,
          child: Container(
            height: 48,
            width: 120,
            decoration: BoxDecoration(
              border: Border.all(
                  color: ColorPalettes.instance.firstText.withAlpha(30)),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Obx(() => CustomPaint(
                  painter: CaptchaPainter(state.captchaCode.value),
                  size: const Size(120, 48),
                )),
          ),
        ),
      ],
    );
  }

  // 构建协议复选框
  Widget _buildTermsCheckbox() {
    return Wrap(
      alignment: WrapAlignment.center,
      crossAxisAlignment: WrapCrossAlignment.center,
      children: [
        Obx(() => Checkbox(
              value: state.agreeToTerms.value,
              onChanged: (value) => logic.toggleTermsAgreement(),
            )),
        Text(l10n.agreement),
        TextButton(
          onPressed: () {},
          child: Text(l10n.agreementLink),
        ),
        Text('和'),
        TextButton(
          onPressed: () {},
          child: Text(l10n.privacyPolicy),
        ),
      ],
    );
  }

  // 构建下一步按钮
  Widget _buildVerificationStep() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            Text(
              l10n.verificationCodeHint,
              style: TextStyles.instance.h2(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            Text(
              '${l10n.verificationCodeSent} ${state.phoneController.text}',
              style: TextStyles.instance.h2(
                color: ColorPalettes.instance.firstText.withAlpha(60),
              ),
            ),
            const SizedBox(height: 32),
            _buildVerificationCodeInput(),
            const SizedBox(height: 16),
            _buildResendTimer(),
            const SizedBox(height: 32),
            _buildNextButton(
              onPressed: logic.verifyCode,
              text: l10n.next,
            ),
          ],
        ),
      ),
    );
  }

  // 构建验证码输入框
  Widget _buildVerificationCodeInput() {
    return TextField(
      controller: state.verificationCodeController,
      decoration: InputDecoration(
        labelText: l10n.verifyCode,
        prefixIcon: const Icon(Icons.lock_outline),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide:
              BorderSide(color: ColorPalettes.instance.firstText.withAlpha(30)),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: Theme.of(Get.context!).primaryColor),
        ),
        contentPadding: const EdgeInsets.symmetric(
          horizontal: 16,
          vertical: 16,
        ),
      ),
      keyboardType: TextInputType.number,
    );
  }

  // 构建重新发送验证码倒计时
  Widget _buildResendTimer() {
    return Obx(() => Center(
          child: Text(
            '${state.remainingSeconds}${l10n.secondResend}',
            style: TextStyles.instance.h2(
              color: ColorPalettes.instance.firstText.withAlpha(60),
            ),
          ),
        ));
  }

  // 构建注册完成步骤
  Widget _buildFinalStep() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            Text(
              l10n.settingAccount,
              style: TextStyles.instance.h2(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 32),
            _buildUsernameInput(),
            const SizedBox(height: 16),
            _buildPasswordInput(),
            const SizedBox(height: 16),
            _buildConfirmPasswordInput(),
            const SizedBox(height: 32),
            _buildNextButton(
              onPressed: logic.completeRegistration,
              text: l10n.registerComplete,
            ),
          ],
        ),
      ),
    );
  }

  // 构建用户名输入框
  Widget _buildUsernameInput() {
    return TextField(
      controller: state.usernameController,
      decoration: InputDecoration(
        labelText: l10n.username,
        prefixIcon: const Icon(Icons.person_outline),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide:
              BorderSide(color: ColorPalettes.instance.firstText.withAlpha(30)),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: Theme.of(Get.context!).primaryColor),
        ),
        contentPadding: const EdgeInsets.symmetric(
          horizontal: 16,
          vertical: 16,
        ),
      ),
    );
  }

  // 构建密码输入框
  Widget _buildPasswordInput() {
    return Obx(() => TextField(
          controller: state.passwordController,
          decoration: InputDecoration(
            labelText: l10n.password,
            prefixIcon: const Icon(Icons.lock_outline),
            suffixIcon: IconButton(
              icon: Icon(
                state.isPasswordVisible.value
                    ? Icons.visibility_off
                    : Icons.visibility,
              ),
              onPressed: logic.togglePasswordVisibility,
            ),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(
                  color: ColorPalettes.instance.firstText.withAlpha(30)),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide:
                  BorderSide(color: Theme.of(Get.context!).primaryColor),
            ),
            contentPadding: const EdgeInsets.symmetric(
              horizontal: 16,
              vertical: 16,
            ),
          ),
          obscureText: !state.isPasswordVisible.value,
        ));
  }

  // 构建确认密码输入框
  Widget _buildConfirmPasswordInput() {
    return Obx(() => TextField(
          controller: state.confirmPasswordController,
          decoration: InputDecoration(
            labelText: l10n.confirmPassword,
            prefixIcon: const Icon(Icons.lock_outline),
            suffixIcon: IconButton(
              icon: Icon(
                state.isConfirmPasswordVisible.value
                    ? Icons.visibility_off
                    : Icons.visibility,
              ),
              onPressed: logic.toggleConfirmPasswordVisibility,
            ),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(
                  color: ColorPalettes.instance.firstText.withAlpha(30)),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide:
                  BorderSide(color: Theme.of(Get.context!).primaryColor),
            ),
            contentPadding: const EdgeInsets.symmetric(
              horizontal: 16,
              vertical: 16,
            ),
          ),
          obscureText: !state.isConfirmPasswordVisible.value,
        ));
  }

  // 构建下一步按钮
  Widget _buildNextButton({
    required VoidCallback onPressed,
    required String text,
  }) {
    return Obx(() => ElevatedButton(
          onPressed: state.isLoading.value ? null : onPressed,
          style: ElevatedButton.styleFrom(
            padding: const EdgeInsets.symmetric(vertical: 16),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
            elevation: 2,
          ),
          child: state.isLoading.value
              ? SizedBox(
                  width: 24,
                  height: 24,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    color: ColorPalettes.instance.firstText,
                  ),
                )
              : Text(
                  text,
                  style: TextStyles.instance.h2(
                    fontWeight: FontWeight.bold,
                  ),
                ),
        ));
  }

  @override
  void dispose() {
    Get.delete<RegisterLogic>();
    super.dispose();
  }
}
