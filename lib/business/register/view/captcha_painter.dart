import 'dart:math';
import 'package:flutter/material.dart';
import 'package:topping_home/theme/color_palettes.dart';
import 'package:topping_home/theme/text_styles.dart';

/// 验证码绘制类
class CaptchaPainter extends CustomPainter {
  final String code;

  // 为随机元素存储固定的seed
  final Random random;

  // 存储随机生成的干扰元素
  final List<Map<String, double>> noiseLines;
  final List<Map<String, double>> noiseDots;

  CaptchaPainter(this.code)
      : random = Random(),
        noiseLines = List.generate(
            5,
            (_) => {
                  'startX': Random().nextDouble(),
                  'startY': Random().nextDouble(),
                  'endX': Random().nextDouble(),
                  'endY': Random().nextDouble(),
                }),
        noiseDots = List.generate(
            50,
            (_) => {
                  'x': Random().nextDouble(),
                  'y': Random().nextDouble(),
                });

  @override
  void paint(Canvas canvas, Size size) {
    // 绘制背景
    canvas.drawRect(
      Rect.fromLTWH(0, 0, size.width, size.height),
      Paint()..color = ColorPalettes.instance.firstText.withAlpha(10),
    );

    // 使用预生成的干扰线
    for (var line in noiseLines) {
      canvas.drawLine(
        Offset(line['startX']! * size.width, line['startY']! * size.height),
        Offset(line['endX']! * size.width, line['endY']! * size.height),
        Paint()
          ..color = ColorPalettes.instance.firstText.withAlpha(40)
          ..strokeWidth = 1.0,
      );
    }

    // 绘制文字
    final textStyle = TextStyles.instance.h2(
      color: ColorPalettes.instance.firstText,
      fontWeight: FontWeight.bold,
    );

    final charWidth = size.width / (code.length + 2);

    for (var i = 0; i < code.length; i++) {
      canvas.save();
      final centerX = charWidth * (i + 1);
      final centerY = size.height / 2;
      canvas.translate(centerX, centerY);
      // 使用固定的随机旋转角度
      canvas.rotate(noiseLines[i % noiseLines.length]['startX']! * 0.5 - 0.25);

      final textSpan = TextSpan(text: code[i], style: textStyle);
      final textPainter = TextPainter(
        text: textSpan,
        textDirection: TextDirection.ltr,
      );

      textPainter.layout();
      textPainter.paint(
        canvas,
        Offset(-textPainter.width / 2, -textPainter.height / 2),
      );
      canvas.restore();
    }

    // 使用预生成的噪点
    for (var dot in noiseDots) {
      canvas.drawCircle(
        Offset(dot['x']! * size.width, dot['y']! * size.height),
        1,
        Paint()..color = ColorPalettes.instance.firstText.withAlpha(40),
      );
    }
  }

  @override
  bool shouldRepaint(covariant CaptchaPainter oldDelegate) {
    return code != oldDelegate.code; // 只在验证码改变时重绘
  }
}
