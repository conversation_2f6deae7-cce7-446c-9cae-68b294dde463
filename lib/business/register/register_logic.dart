import 'dart:async';
import 'dart:math';

import 'package:get/get.dart';
import 'package:topping_home/common/util/i18n.dart';
import 'package:topping_home/theme/color_palettes.dart';
import '../../repositories/user_repository.dart';
import '../../models/login_entity.dart';
import '../../models/user_entity.dart';
import 'register_state.dart';

/// 注册逻辑控制器
class RegisterLogic extends GetxController {
  final RegisterState state = RegisterState();

  // 计时器用于验证码倒计时
  Timer? _timer;

  @override
  void onClose() {
    state.dispose();
    _timer?.cancel();
    super.onClose();
  }

  @override
  void onInit() {
    super.onInit();
    generateCaptchaCode();
  }

  /// 生成验证码
  /// @param length 验证码长度，默认为4位
  void generateCaptchaCode() {
    const chars = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ';
    final random = Random();
    state.captchaCode.value =
        List.generate(4, (_) => chars[random.nextInt(chars.length)]).join();
  }

  /// 切换密码可见性
  void togglePasswordVisibility() {
    state.isPasswordVisible.value = !state.isPasswordVisible.value;
  }

  /// 切换确认密码可见性
  void toggleConfirmPasswordVisibility() {
    state.isConfirmPasswordVisible.value =
        !state.isConfirmPasswordVisible.value;
  }

  /// 切换协议同意状态
  void toggleTermsAgreement() {
    state.agreeToTerms.value = !state.agreeToTerms.value;
  }

  /// 下一步
  void nextStep() {
    if (state.currentStep.value < 2) {
      state.currentStep.value++;
      if (state.currentStep.value == 1) {
        startVerificationTimer();
      }
    }
  }

  /// 上一步
  void previousStep() {
    if (state.currentStep.value > 0) {
      state.currentStep.value--;
    } else {
      Get.back();
    }
  }

  /// 开始验证码倒计时
  void startVerificationTimer() {
    state.remainingSeconds.value = 30;
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (state.remainingSeconds.value > 0) {
        state.remainingSeconds.value--;
      } else {
        timer.cancel();
      }
    });
  }

  /// 请求验证码
  Future<void> requestVerificationCode() async {
    if (!_validatePhoneStep()) return;

    state.isLoading.value = true;
    try {
      // 模拟网络请求
      await Future.delayed(const Duration(seconds: 1));
      nextStep();
    } catch (e) {
      Get.snackbar(
        l10n.error,
        e.toString(),
        snackPosition: SnackPosition.TOP,
        backgroundColor: ColorPalettes.instance.error,
        colorText: ColorPalettes.instance.firstText,
      );
    } finally {
      state.isLoading.value = false;
    }
  }

  /// 验证手机号和图形验证码
  bool _validatePhoneStep() {
    if (state.phoneController.text.isEmpty) {
      Get.snackbar(l10n.tips, l10n.phoneHint);
      return false;
    }
    if (state.captchaController.text.isEmpty) {
      Get.snackbar(l10n.tips, l10n.graphicCodeHint);
      return false;
    }
    if (state.captchaController.text.toUpperCase() != state.captchaCode.value) {
      Get.snackbar(l10n.tips, l10n.invalidCaptcha);
      generateCaptchaCode(); // Generate new code if validation fails
      return false;
    }
    if (!state.agreeToTerms.value) {
      Get.snackbar(l10n.tips, l10n.agreeToTermsHint);
      return false;
    }
    return true;
  }

  /// 验证验证码
  Future<void> verifyCode() async {
    if (state.verificationCodeController.text.isEmpty) {
      Get.snackbar(l10n.tips, l10n.verificationCodeHint);
      return;
    }

    state.isLoading.value = true;
    try {
      // 模拟网络请求
      await Future.delayed(const Duration(seconds: 1));
      nextStep();
    } catch (e) {
      Get.snackbar(l10n.error, e.toString());
    } finally {
      state.isLoading.value = false;
    }
  }

  /// 验证用户名、密码和确认密码
  bool _validateFinalStep() {
    if (state.usernameController.text.isEmpty) {
      Get.snackbar(l10n.tips, l10n.usernameHint);
      return false;
    }
    if (state.passwordController.text.isEmpty) {
      Get.snackbar(l10n.tips, l10n.passwordHint);
      return false;
    }
    if (state.confirmPasswordController.text.isEmpty) {
      Get.snackbar(l10n.tips, l10n.confirmPasswordHint);
      return false;
    }
    if (state.passwordController.text != state.confirmPasswordController.text) {
      Get.snackbar(l10n.tips, l10n.passwordMismatch);
      return false;
    }
    return true;
  }

  /// 完成注册
  Future<void> completeRegistration() async {
    if (!_validateFinalStep()) return;

    state.isLoading.value = true;
    try {
      final userId = DateTime.now().millisecondsSinceEpoch; // 使用时间戳作为临时ID

      // 创建LoginEntity
      final loginEntity = LoginEntity()
        ..token = 'register_token_$userId'
        ..type = 'user'
        ..userId = userId;

      // 创建UserEntity
      final userEntity = UserEntity()
        ..id = userId
        ..userPhone = state.phoneController.text
        ..nickname = state.usernameController.text
        ..avatar =
            'https://51img2.51txapp.com/2024/06/12/ed458ba6ea0e4afc8a931455750f246e!400x400.jpeg'
        ..signature = '这是一条个性签名～'
        ..sex = '男'
        ..birthday = '2000-01-01'
        ..password = state.passwordController.text;

      // 更新用户信息
      await UserRepository.instance.updateLoginEntity(loginEntity);
      await UserRepository.instance.updateUserEntity(userEntity);

      Get.back(); // 返回登录页
      Get.snackbar(
        l10n.registerSuccess,
        l10n.registerSuccessHint,
        snackPosition: SnackPosition.TOP,
        backgroundColor: ColorPalettes.instance.success,
        colorText: ColorPalettes.instance.firstText,
      );
    } catch (e) {
      Get.snackbar(
        l10n.registerFailed,
        e.toString(),
        snackPosition: SnackPosition.TOP,
        backgroundColor: ColorPalettes.instance.error,
        colorText: ColorPalettes.instance.firstText,
      );
    } finally {
      state.isLoading.value = false;
    }
  }
}
