import 'package:flutter/material.dart';
import 'package:get/get.dart';

/// 注册状态管理类
class RegisterState {
  final captchaCode = ''.obs;
  final currentStep = 0.obs;
  final isLoading = false.obs;

  // 手机号验证步骤
  final phoneController = TextEditingController();
  final captchaController = TextEditingController();
  final agreeToTerms = false.obs;

  // 验证码步骤
  final verificationCodeController = TextEditingController();
  final remainingSeconds = 30.obs;

  // 用户名密码步骤
  final usernameController = TextEditingController();
  final passwordController = TextEditingController();
  final confirmPasswordController = TextEditingController();
  final isPasswordVisible = false.obs;
  final isConfirmPasswordVisible = false.obs;

  void dispose() {
    phoneController.dispose();
    captchaController.dispose();
    verificationCodeController.dispose();
    usernameController.dispose();
    passwordController.dispose();
    confirmPasswordController.dispose();
  }
}
