import 'package:topping_ble_control/model/enums/device_mode_type.dart';

import '../models/d900_device_settings.dart';
import '../models/dx5_device_settings.dart';

/// 设备设置工厂类
class DeviceSettingsFactory {
  
  /// 根据设备类型创建默认设置
  static dynamic createDefaultSettings(String deviceModel) {
    // 将字符串转换为枚举
    final deviceType = _getDeviceType(deviceModel);
    
    switch (deviceType) {
      case DeviceModeType.dx5:
        return Dx5DeviceSettings();
      case DeviceModeType.dx9:
        return D900DeviceSettings();
      default:
        // 默认返回DX5设置
        return Dx5DeviceSettings();
    }
  }

  /// 判断设备是否为D900
  static bool isD900Device(String deviceModel) {
    return _getDeviceType(deviceModel) == DeviceModeType.dx9;
  }

  /// 判断设备是否为DX5
  static bool isDX5Device(String deviceModel) {
    return _getDeviceType(deviceModel) == DeviceModeType.dx5;
  }
  
  /// 私有辅助方法：将设备型号字符串转换为枚举类型
  static DeviceModeType _getDeviceType(String deviceModel) {
    final upperModel = deviceModel.toUpperCase();
    
    if (upperModel == 'DX5') return DeviceModeType.dx5;
    if (upperModel == 'DX9') return DeviceModeType.dx9;
    
    return DeviceModeType.unknown;
  }
}
