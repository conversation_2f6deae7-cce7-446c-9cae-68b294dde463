import 'dart:ffi';
import 'dart:io';

import 'package:ffi/ffi.dart';
import 'package:topping_ble_control/utils/log_util.dart';

// --- C Struct Definitions ---

/// Corresponds to `controller_scan_result_t` in C.
final class FFIControllerScanResult extends Struct {
  external Pointer<Utf8> name;
  @Int64()
  external int device; // Corresponds to long device
  @Int32()
  external int rssi;
}

/// Corresponds to `controller_scanner_callback_t` in C.
final class FFIControllerScannerCallback extends Struct {
  external Pointer<
          NativeFunction<
              Void Function(Int64, Pointer<FFIControllerScanResult>, IntPtr)>>
      on_scan_results;
  external Pointer<NativeFunction<Void Function(Int64, Int32)>> on_scan_failed;
}

// --- Function Typedefs ---
typedef NativeControllerScannerCreate = Int64 Function(
    Int64 flutterObject, Pointer<FFIControllerScannerCallback> callback);
typedef DartControllerScannerCreate = int Function(
    int flutterObject, Pointer<FFIControllerScannerCallback> callback);

typedef NativeControllerScannerDestroy = Void Function(Int64 nativeObject);
typedef DartControllerScannerDestroy = void Function(int nativeObject);

typedef NativeControllerScannerStartScan = Void Function(Int64 nativeObject);
typedef DartControllerScannerStartScan = void Function(int nativeObject);

typedef NativeControllerScannerStopScan = Void Function(Int64 nativeObject);
typedef DartControllerScannerStopScan = void Function(int nativeObject);

/// Bindings for the C++ ControllerScanner.
class ControllerScannerBindings {
  static ControllerScannerBindings? _instance;
  static ControllerScannerBindings get instance {
    _instance ??= ControllerScannerBindings._();
    return _instance!;
  }

  late final DynamicLibrary _dylib;

  // Function pointers
  late final DartControllerScannerCreate _controllerScannerCreate;
  late final DartControllerScannerDestroy _controllerScannerDestroy;
  late final DartControllerScannerStartScan _controllerScannerStartScan;
  late final DartControllerScannerStopScan _controllerScannerStopScan;

  ControllerScannerBindings._() {
    _dylib = Platform.isAndroid
        ? DynamicLibrary.open("libtopping_controller.so")
        : DynamicLibrary.process();
    _loadFunctions();
  }

  void _loadFunctions() {
    _controllerScannerCreate = _dylib
        .lookup<NativeFunction<NativeControllerScannerCreate>>(
            'controller_scanner_create')
        .asFunction<DartControllerScannerCreate>();

    _controllerScannerDestroy = _dylib
        .lookup<NativeFunction<NativeControllerScannerDestroy>>(
            'controller_scanner_destory')
        .asFunction<DartControllerScannerDestroy>();

    _controllerScannerStartScan = _dylib
        .lookup<NativeFunction<NativeControllerScannerStartScan>>(
            'controller_scanner_startScan')
        .asFunction<DartControllerScannerStartScan>();

    _controllerScannerStopScan = _dylib
        .lookup<NativeFunction<NativeControllerScannerStopScan>>(
            'controller_scanner_stopScan')
        .asFunction<DartControllerScannerStopScan>();
  }

  int createScanner(
      int flutterObject, Pointer<FFIControllerScannerCallback> callback) {
    Log.d(
        "ControllerScannerBindings: Creating native scanner, flutterObject: \$flutterObject");
    return _controllerScannerCreate(flutterObject, callback);
  }

  void destroyScanner(int nativeObject) {
    Log.d(
        "ControllerScannerBindings: Destroying native scanner, nativeObject: \$nativeObject");
    _controllerScannerDestroy(nativeObject);
  }

  void startScan(int nativeObject) {
    Log.d(
        "ControllerScannerBindings: Starting scan, nativeObject: \$nativeObject");
    _controllerScannerStartScan(nativeObject);
  }

  void stopScan(int nativeObject) {
    Log.d(
        "ControllerScannerBindings: Stopping scan, nativeObject: \$nativeObject");
    _controllerScannerStopScan(nativeObject);
  }
}
