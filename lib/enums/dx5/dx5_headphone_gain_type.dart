import 'package:flutter/cupertino.dart';
import 'package:hive_ce/hive.dart';
import 'package:topping_home/enums/interfaces/convert.dart';
import 'package:topping_home/models/hive_adapters.dart';

import '../../l10n/app_localizations.dart';
import '../interfaces/localizable.dart';


part 'dx5_headphone_gain_type.g.dart';

/// 耳放增益等级
@HiveType(typeId: HiveAdapterIds.headphoneGainTypeId)
enum Dx5HeadphoneGainType implements Localizable, Convert {
  // 低增益
  @HiveField(0)
  lowGain,
  // 高增益
  @HiveField(1)
  highGain;

  @override
  String localized(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;
    switch (this) {
      case Dx5HeadphoneGainType.highGain:
        return l10n.headphoneGainHigh;
      case Dx5HeadphoneGainType.lowGain:
        return l10n.headphoneGainLow;
    }
  }

  @override
  int get value => index;

  static Dx5HeadphoneGainType fromValue(int value) {
    return Convert.fromValue(
        Dx5HeadphoneGainType.values, value, Dx5HeadphoneGainType.highGain);
  }
}

// dx5_headphone_gain_type.dart 的扩展
extension HeadphoneGainTypeLocalization on Dx5HeadphoneGainType {
  String localized(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;
    switch (this) {
      case Dx5HeadphoneGainType.highGain:
        return l10n.headphoneGainHigh;
      case Dx5HeadphoneGainType.lowGain:
        return l10n.headphoneGainLow;
    }
  }

  static List<Dx5HeadphoneGainType> get options => Dx5HeadphoneGainType.values;

  static List<String> getLocalizedOptions(BuildContext context) {
    return options.map((type) => type.localized(context)).toList();
  }

  static List<MapEntry<Dx5HeadphoneGainType, String>> getLocalizedEntries(
      BuildContext context) {
    return options
        .map((type) => MapEntry(type, type.localized(context)))
        .toList();
  }
}
