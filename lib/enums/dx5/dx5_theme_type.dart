import 'package:flutter/cupertino.dart';
import 'package:hive_ce/hive.dart';
import 'package:topping_home/enums/interfaces/convert.dart';
import 'package:topping_home/models/hive_adapters.dart';

import '../../l10n/app_localizations.dart';
import '../interfaces/localizable.dart';

part 'dx5_theme_type.g.dart';

/// 主机主题(极光，橙色，秘鲁色，豆绿色，深卡其色，玫瑰棕色，蓝色，幻紫色，白色)
@HiveType(typeId: HiveAdapterIds.themeTypeId)
enum Dx5ThemeType implements Localizable, Convert {
  // 极光
  @HiveField(0)
  aurora,
  // 橙色
  @HiveField(1)
  orange,
  // 秘鲁色
  @HiveField(2)
  peru,
  // 豆绿色
  @HiveField(3)
  green,
  // 深卡其色
  @HiveField(4)
  khaki,
  // 玫瑰棕色
  @HiveField(5)
  rose,
  // 蓝色
  @HiveField(6)
  blue,
  // 幻紫色
  @HiveField(7)
  purple,
  // 白色
  @HiveField(8)
  white;

  @override
  String localized(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;
    switch (this) {
      case Dx5ThemeType.aurora:
        return l10n.themeAurora;
      case Dx5ThemeType.orange:
        return l10n.themeOrange;
      case Dx5ThemeType.peru:
        return l10n.themePeru;
      case Dx5ThemeType.green:
        return l10n.themeGreen;
      case Dx5ThemeType.khaki:
        return l10n.themeKhaki;
      case Dx5ThemeType.rose:
        return l10n.themeRose;
      case Dx5ThemeType.blue:
        return l10n.themeBlue;
      case Dx5ThemeType.purple:
        return l10n.themePurple;
      case Dx5ThemeType.white:
        return l10n.themeWhite;
    }
  }

  @override
  int get value => index;

  static Dx5ThemeType fromValue(int value) {
    return Convert.fromValue(Dx5ThemeType.values, value, Dx5ThemeType.aurora);
  }
}

// dx5_theme_type.dart 的扩展
extension ThemeTypeLocalization on Dx5ThemeType {
  String localized(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;
    switch (this) {
      case Dx5ThemeType.aurora:
        return l10n.themeAurora;
      case Dx5ThemeType.orange:
        return l10n.themeOrange;
      case Dx5ThemeType.peru:
        return l10n.themePeru;
      case Dx5ThemeType.green:
        return l10n.themeGreen;
      case Dx5ThemeType.khaki:
        return l10n.themeKhaki;
      case Dx5ThemeType.rose:
        return l10n.themeRose;
      case Dx5ThemeType.blue:
        return l10n.themeBlue;
      case Dx5ThemeType.purple:
        return l10n.themePurple;
      case Dx5ThemeType.white:
        return l10n.themeWhite;
    }
  }

  static List<Dx5ThemeType> get options => Dx5ThemeType.values;

  static List<String> getLocalizedOptions(BuildContext context) {
    return options.map((type) => type.localized(context)).toList();
  }

  static List<MapEntry<Dx5ThemeType, String>> getLocalizedEntries(
      BuildContext context) {
    return options
        .map((type) => MapEntry(type, type.localized(context)))
        .toList();
  }
}
