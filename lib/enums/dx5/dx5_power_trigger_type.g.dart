// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'dx5_power_trigger_type.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class Dx5PowerTriggerTypeAdapter extends TypeAdapter<Dx5PowerTriggerType> {
  @override
  final int typeId = 110;

  @override
  Dx5PowerTriggerType read(BinaryReader reader) {
    switch (reader.readByte()) {
      case 0:
        return Dx5PowerTriggerType.signal;
      case 1:
        return Dx5PowerTriggerType.voltage;
      case 2:
        return Dx5PowerTriggerType.close;
      default:
        return Dx5PowerTriggerType.signal;
    }
  }

  @override
  void write(BinaryWriter writer, Dx5PowerTriggerType obj) {
    switch (obj) {
      case Dx5PowerTriggerType.signal:
        writer.writeByte(0);
      case Dx5PowerTriggerType.voltage:
        writer.writeByte(1);
      case Dx5PowerTriggerType.close:
        writer.writeByte(2);
    }
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is Dx5PowerTriggerTypeAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}
