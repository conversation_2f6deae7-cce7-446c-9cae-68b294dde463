import 'package:flutter/cupertino.dart';
import 'package:hive_ce/hive.dart';
import 'package:topping_home/enums/interfaces/convert.dart';
import 'package:topping_home/models/hive_adapters.dart';
import '../../l10n/app_localizations.dart';
import '../interfaces/localizable.dart';

part 'dx5_display_type.g.dart';

/// 显示界面(VU、FFT)
@HiveType(typeId: HiveAdapterIds.displayTypeId)
enum Dx5DisplayType implements Localizable, Convert {
  // 正常
  @HiveField(0)
  normal,
  // VU
  @HiveField(1)
  vu,
  // FFT
  @HiveField(2)
  fft;

  @override
  String localized(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;
    switch (this) {
      case Dx5DisplayType.normal:
        return l10n.displayNormal;
      case Dx5DisplayType.vu:
        return l10n.displayVu;
      case Dx5DisplayType.fft:
        return l10n.displayFft;
    }
  }

  @override
  int get value => index;

  static Dx5DisplayType fromValue(int value) {
    return Convert.fromValue(Dx5DisplayType.values, value, Dx5DisplayType.vu);
  }
}

// dx5_display_type.dart 的扩展
extension DisplayTypeLocalization on Dx5DisplayType {
  String localized(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;
    switch (this) {
      case Dx5DisplayType.normal:
        return l10n.displayNormal;
      case Dx5DisplayType.vu:
        return l10n.displayVu;
      case Dx5DisplayType.fft:
        return l10n.displayFft;
    }
  }

  static List<Dx5DisplayType> get options => Dx5DisplayType.values;

  static List<String> getLocalizedOptions(BuildContext context) {
    return options.map((type) => type.localized(context)).toList();
  }

  static List<MapEntry<Dx5DisplayType, String>> getLocalizedEntries(
      BuildContext context) {
    return options
        .map((type) => MapEntry(type, type.localized(context)))
        .toList();
  }
}
