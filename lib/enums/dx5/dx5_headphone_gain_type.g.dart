// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'dx5_headphone_gain_type.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class Dx5HeadphoneGainTypeAdapter extends TypeAdapter<Dx5HeadphoneGainType> {
  @override
  final int typeId = 105;

  @override
  Dx5HeadphoneGainType read(BinaryReader reader) {
    switch (reader.readByte()) {
      case 0:
        return Dx5HeadphoneGainType.lowGain;
      case 1:
        return Dx5HeadphoneGainType.highGain;
      default:
        return Dx5HeadphoneGainType.lowGain;
    }
  }

  @override
  void write(BinaryWriter writer, Dx5HeadphoneGainType obj) {
    switch (obj) {
      case Dx5HeadphoneGainType.lowGain:
        writer.writeByte(0);
      case Dx5HeadphoneGainType.highGain:
        writer.writeByte(1);
    }
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is Dx5HeadphoneGainTypeAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}
