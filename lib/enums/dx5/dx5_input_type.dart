import 'package:flutter/cupertino.dart';
import 'package:hive_ce/hive.dart';
import 'package:topping_home/models/hive_adapters.dart';

import '../../l10n/app_localizations.dart';
import '../interfaces/convert.dart';
import '../interfaces/localizable.dart';

part 'dx5_input_type.g.dart';

/// 输入枚举
@HiveType(typeId: HiveAdapterIds.inputTypeId)
enum Dx5InputType implements Localizable, Convert {
  // usb
  @HiveField(0)
  usb,
  // 光纤
  @HiveField(1)
  optical,
  // 同轴
  @HiveField(2)
  coaxial,
  // 蓝牙
  @HiveField(3)
  bluetooth;

  @override
  String localized(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;
    switch (this) {
      case Dx5InputType.usb:
        return l10n.inputUsb;
      case Dx5InputType.optical:
        return l10n.inputOptical;
      case Dx5InputType.coaxial:
        return l10n.inputCoaxial;
      case Dx5InputType.bluetooth:
        return l10n.inputBluetooth;
    }
  }

  @override
  int get value => index;

  static Dx5InputType fromValue(int value) {
    return Convert.fromValue(Dx5InputType.values, value, Dx5InputType.usb);
  }
}

// InputType 扩展
extension InputTypeLocalization on Dx5InputType {
  String localized(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;
    switch (this) {
      case Dx5InputType.usb:
        return l10n.inputUsb;
      case Dx5InputType.optical:
        return l10n.inputOptical;
      case Dx5InputType.coaxial:
        return l10n.inputCoaxial;
      case Dx5InputType.bluetooth:
        return l10n.inputBluetooth;
    }
  }

  // 添加以下静态方法
  static List<Dx5InputType> get options => Dx5InputType.values;

  static List<String> getLocalizedOptions(BuildContext context) {
    return options.map((type) => type.localized(context)).toList();
  }

  static List<MapEntry<Dx5InputType, String>> getLocalizedEntries(
      BuildContext context) {
    return options
        .map((type) => MapEntry(type, type.localized(context)))
        .toList();
  }
}
