import 'package:flutter/cupertino.dart';
import 'package:hive_ce/hive.dart';
import 'package:topping_home/models/hive_adapters.dart';

import '../../l10n/app_localizations.dart';
import '../interfaces/convert.dart';
import '../interfaces/localizable.dart';

part 'dx5_power_trigger_type.g.dart';

/// 开关机触发（信号，12V，关闭）
@HiveType(typeId: HiveAdapterIds.powerTriggerTypeId)
enum Dx5PowerTriggerType implements Localizable, Convert {
  // 信号
  @HiveField(0)
  signal,
  // 12V
  @HiveField(1)
  voltage,
  // 关闭
  @HiveField(2)
  close;

  @override
  String localized(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;
    switch (this) {
      case Dx5PowerTriggerType.signal:
        return l10n.powerTriggerSignal;
      case Dx5PowerTriggerType.voltage:
        return l10n.powerTriggerVoltage;
      case Dx5PowerTriggerType.close:
        return l10n.powerTriggerClose;
    }
  }

  @override
  int get value => index;

  static Dx5PowerTriggerType fromValue(int value) {
    return Convert.fromValue(
        Dx5PowerTriggerType.values, value, Dx5PowerTriggerType.signal);
  }
}

// dx5_power_trigger_type.dart 的扩展
extension PowerTriggerTypeLocalization on Dx5PowerTriggerType {
  String localized(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;
    switch (this) {
      case Dx5PowerTriggerType.signal:
        return l10n.powerTriggerSignal;
      case Dx5PowerTriggerType.voltage:
        return l10n.powerTriggerVoltage;
      case Dx5PowerTriggerType.close:
        return l10n.powerTriggerClose;
    }
  }

  static List<Dx5PowerTriggerType> get options => Dx5PowerTriggerType.values;

  static List<String> getLocalizedOptions(BuildContext context) {
    return options.map((type) => type.localized(context)).toList();
  }

  static List<MapEntry<Dx5PowerTriggerType, String>> getLocalizedEntries(
      BuildContext context) {
    return options
        .map((type) => MapEntry(type, type.localized(context)))
        .toList();
  }
}
