import 'package:flutter/cupertino.dart';
import 'package:hive_ce/hive.dart';
import 'package:topping_home/enums/interfaces/convert.dart';
import 'package:topping_home/models/hive_adapters.dart';

import '../../l10n/app_localizations.dart';
import '../interfaces/localizable.dart';

// 这一行是必须的，用于Hive代码生成
part 'dx5_decode_mode_type.g.dart';

/// 解码模式（前缀，DAC）
@HiveType(typeId: HiveAdapterIds.decodeModeTypeId, adapterName: 'Dx5DecodeModeTypeAdapter')
enum Dx5DecodeModeType implements Localizable, Convert {
  // 前缀
  @HiveField(0)
  prefix,
  // DAC
  @HiveField(1)
  dac;

  @override
  String localized(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;
    switch (this) {
      case Dx5DecodeModeType.prefix:
        return l10n.preamplifier;
      case Dx5DecodeModeType.dac:
        return l10n.decodeModeDac;
    }
  }

  @override
  int get value => index;

  static Dx5DecodeModeType fromValue(int value) {
    return Convert.fromValue(
        Dx5DecodeModeType.values, value, Dx5DecodeModeType.prefix);
  }
}

// dx5_decode_mode_type.dart 的扩展
extension DecodeModeTypeLocalization on Dx5DecodeModeType {
  String localized(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;
    switch (this) {
      case Dx5DecodeModeType.prefix:
        return l10n.preamplifier;
      case Dx5DecodeModeType.dac:
        return l10n.decodeModeDac;
    }
  }

  static List<Dx5DecodeModeType> get options => Dx5DecodeModeType.values;

  static List<String> getLocalizedOptions(BuildContext context) {
    return options.map((type) => type.localized(context)).toList();
  }

  static List<MapEntry<Dx5DecodeModeType, String>> getLocalizedEntries(
      BuildContext context) {
    return options
        .map((type) => MapEntry(type, type.localized(context)))
        .toList();
  }
}
