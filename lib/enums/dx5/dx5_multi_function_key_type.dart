import 'package:flutter/cupertino.dart';
import 'package:hive_ce/hive.dart';
import 'package:topping_home/enums/interfaces/convert.dart';
import 'package:topping_home/models/hive_adapters.dart';

import '../../l10n/app_localizations.dart';
import '../interfaces/localizable.dart';

part 'dx5_multi_function_key_type.g.dart';

/// 多功能按键（输入选择，线路输出选择，耳放输出选择，主页选择，亮度选择，息屏，PCM滤波器选择，静音）
@HiveType(typeId: HiveAdapterIds.multiFunctionKeyTypeId)
enum Dx5MultiFunctionKeyType implements Localizable, Convert {
  // 静音
  @HiveField(0)
  mute,
  // 输入选择
  @HiveField(1)
  inputSelect,
  // 线路输出选择
  @HiveField(2)
  lineOutSelect,
  // 耳放输出选择
  @HiveField(3)
  headphoneOutSelect,
  // 主页选择
  @HiveField(4)
  homeSelect,
  // 亮度选择
  @HiveField(5)
  brightnessSelect,
  // 息屏
  @HiveField(6)
  sleep,
  // PCM滤波器选择
  @HiveField(7)
  pcmFilterSelect,
  // PEQ选择
  @HiveField(8)
  peqSelect;

  @override
  String localized(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;
    switch (this) {
      case Dx5MultiFunctionKeyType.inputSelect:
        return l10n.multiFunctionKeyInputSelect;
      case Dx5MultiFunctionKeyType.lineOutSelect:
        return l10n.multiFunctionKeyLineOutSelect;
      case Dx5MultiFunctionKeyType.headphoneOutSelect:
        return l10n.multiFunctionKeyHeadphoneOutSelect;
      case Dx5MultiFunctionKeyType.homeSelect:
        return l10n.multiFunctionKeyHomeSelect;
      case Dx5MultiFunctionKeyType.brightnessSelect:
        return l10n.multiFunctionKeyBrightnessSelect;
      case Dx5MultiFunctionKeyType.sleep:
        return l10n.multiFunctionKeySleep;
      case Dx5MultiFunctionKeyType.pcmFilterSelect:
        return l10n.multiFunctionKeyPcmFilterSelect;
      case Dx5MultiFunctionKeyType.mute:
        return l10n.multiFunctionKeyMute;
      case Dx5MultiFunctionKeyType.peqSelect:
        return l10n.multiFunctionKeyPeqSelect;
    }
  }

  @override
  int get value => index;

  static Dx5MultiFunctionKeyType fromValue(int value) {
    return Convert.fromValue(
        Dx5MultiFunctionKeyType.values, value, Dx5MultiFunctionKeyType.inputSelect);
  }
}

// dx5_multi_function_key_type.dart 的扩展
extension MultiFunctionKeyTypeLocalization on Dx5MultiFunctionKeyType {
  String localized(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;
    switch (this) {
      case Dx5MultiFunctionKeyType.inputSelect:
        return l10n.multiFunctionKeyInputSelect;
      case Dx5MultiFunctionKeyType.lineOutSelect:
        return l10n.multiFunctionKeyLineOutSelect;
      case Dx5MultiFunctionKeyType.headphoneOutSelect:
        return l10n.multiFunctionKeyHeadphoneOutSelect;
      case Dx5MultiFunctionKeyType.homeSelect:
        return l10n.multiFunctionKeyHomeSelect;
      case Dx5MultiFunctionKeyType.brightnessSelect:
        return l10n.multiFunctionKeyBrightnessSelect;
      case Dx5MultiFunctionKeyType.sleep:
        return l10n.multiFunctionKeySleep;
      case Dx5MultiFunctionKeyType.pcmFilterSelect:
        return l10n.multiFunctionKeyPcmFilterSelect;
      case Dx5MultiFunctionKeyType.mute:
        return l10n.multiFunctionKeyMute;
      case Dx5MultiFunctionKeyType.peqSelect:
        return l10n.multiFunctionKeyPeqSelect;
    }
  }

  static List<Dx5MultiFunctionKeyType> get options => Dx5MultiFunctionKeyType.values;

  static List<String> getLocalizedOptions(BuildContext context) {
    return options.map((type) => type.localized(context)).toList();
  }

  static List<MapEntry<Dx5MultiFunctionKeyType, String>> getLocalizedEntries(
      BuildContext context) {
    return options
        .map((type) => MapEntry(type, type.localized(context)))
        .toList();
  }
}
