// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'dx5_filter_parameter_type.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class Dx5FilterParameterTypeAdapter
    extends TypeAdapter<Dx5FilterParameterType> {
  @override
  final int typeId = 104;

  @override
  Dx5FilterParameterType read(BinaryReader reader) {
    switch (reader.readByte()) {
      case 0:
        return Dx5FilterParameterType.minimumPhase;
      case 1:
        return Dx5FilterParameterType.linearPhaseApodizing;
      case 2:
        return Dx5FilterParameterType.linearPhaseFast;
      case 3:
        return Dx5FilterParameterType.linearPhaseFastLowRipple;
      case 4:
        return Dx5FilterParameterType.linearPhaseSlow;
      case 5:
        return Dx5FilterParameterType.minimumPhaseFast;
      case 6:
        return Dx5FilterParameterType.minimumPhaseSlow;
      case 7:
        return Dx5FilterParameterType.minimumPhaseSlowLowDispersion;
      default:
        return Dx5FilterParameterType.minimumPhase;
    }
  }

  @override
  void write(BinaryWriter writer, Dx5FilterParameterType obj) {
    switch (obj) {
      case Dx5FilterParameterType.minimumPhase:
        writer.writeByte(0);
      case Dx5FilterParameterType.linearPhaseApodizing:
        writer.writeByte(1);
      case Dx5FilterParameterType.linearPhaseFast:
        writer.writeByte(2);
      case Dx5FilterParameterType.linearPhaseFastLowRipple:
        writer.writeByte(3);
      case Dx5FilterParameterType.linearPhaseSlow:
        writer.writeByte(4);
      case Dx5FilterParameterType.minimumPhaseFast:
        writer.writeByte(5);
      case Dx5FilterParameterType.minimumPhaseSlow:
        writer.writeByte(6);
      case Dx5FilterParameterType.minimumPhaseSlowLowDispersion:
        writer.writeByte(7);
    }
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is Dx5FilterParameterTypeAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}
