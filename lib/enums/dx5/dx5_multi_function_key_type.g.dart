// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'dx5_multi_function_key_type.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class Dx5MultiFunctionKeyTypeAdapter
    extends TypeAdapter<Dx5MultiFunctionKeyType> {
  @override
  final int typeId = 108;

  @override
  Dx5MultiFunctionKeyType read(BinaryReader reader) {
    switch (reader.readByte()) {
      case 0:
        return Dx5MultiFunctionKeyType.mute;
      case 1:
        return Dx5MultiFunctionKeyType.inputSelect;
      case 2:
        return Dx5MultiFunctionKeyType.lineOutSelect;
      case 3:
        return Dx5MultiFunctionKeyType.headphoneOutSelect;
      case 4:
        return Dx5MultiFunctionKeyType.homeSelect;
      case 5:
        return Dx5MultiFunctionKeyType.brightnessSelect;
      case 6:
        return Dx5MultiFunctionKeyType.sleep;
      case 7:
        return Dx5MultiFunctionKeyType.pcmFilterSelect;
      case 8:
        return Dx5MultiFunctionKeyType.peqSelect;
      default:
        return Dx5MultiFunctionKeyType.mute;
    }
  }

  @override
  void write(BinaryWriter writer, Dx5MultiFunctionKeyType obj) {
    switch (obj) {
      case Dx5MultiFunctionKeyType.mute:
        writer.writeByte(0);
      case Dx5MultiFunctionKeyType.inputSelect:
        writer.writeByte(1);
      case Dx5MultiFunctionKeyType.lineOutSelect:
        writer.writeByte(2);
      case Dx5MultiFunctionKeyType.headphoneOutSelect:
        writer.writeByte(3);
      case Dx5MultiFunctionKeyType.homeSelect:
        writer.writeByte(4);
      case Dx5MultiFunctionKeyType.brightnessSelect:
        writer.writeByte(5);
      case Dx5MultiFunctionKeyType.sleep:
        writer.writeByte(6);
      case Dx5MultiFunctionKeyType.pcmFilterSelect:
        writer.writeByte(7);
      case Dx5MultiFunctionKeyType.peqSelect:
        writer.writeByte(8);
    }
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is Dx5MultiFunctionKeyTypeAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}
