import 'package:flutter/cupertino.dart';
import 'package:hive_ce/hive.dart';
import 'package:topping_home/models/hive_adapters.dart';

import '../../l10n/app_localizations.dart';
import '../interfaces/convert.dart';
import '../interfaces/localizable.dart';

part 'dx5_screen_brightness_type.g.dart';

/// 屏幕亮度（H,M,L,AUTO）
@HiveType(typeId: HiveAdapterIds.screenBrightnessTypeId)
enum Dx5ScreenBrightnessType implements Localizable, Convert {
  // L
  @HiveField(0)
  low,
  // M
  @HiveField(1)
  medium,
  // H
  @HiveField(2)
  high,
  @HiveField(3)
  // AUTO
  auto;

  @override
  String localized(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;
    switch (this) {
      case Dx5ScreenBrightnessType.high:
        return l10n.screenBrightnessHigh;
      case Dx5ScreenBrightnessType.medium:
        return l10n.screenBrightnessMedium;
      case Dx5ScreenBrightnessType.low:
        return l10n.screenBrightnessLow;
      case Dx5ScreenBrightnessType.auto:
        return l10n.screenBrightnessAuto;
    }
  }

  @override
  int get value => index;

  static Dx5ScreenBrightnessType fromValue(int value) {
    return Convert.fromValue(
        Dx5ScreenBrightnessType.values, value, Dx5ScreenBrightnessType.high);
  }
}

// dx5_screen_brightness_type.dart 的扩展
extension ScreenBrightnessTypeLocalization on Dx5ScreenBrightnessType {
  String localized(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;
    switch (this) {
      case Dx5ScreenBrightnessType.high:
        return l10n.screenBrightnessHigh;
      case Dx5ScreenBrightnessType.medium:
        return l10n.screenBrightnessMedium;
      case Dx5ScreenBrightnessType.low:
        return l10n.screenBrightnessLow;
      case Dx5ScreenBrightnessType.auto:
        return l10n.screenBrightnessAuto;
    }
  }

  static List<Dx5ScreenBrightnessType> get options => Dx5ScreenBrightnessType.values;

  static List<String> getLocalizedOptions(BuildContext context) {
    return options.map((type) => type.localized(context)).toList();
  }

  static List<MapEntry<Dx5ScreenBrightnessType, String>> getLocalizedEntries(
      BuildContext context) {
    return options
        .map((type) => MapEntry(type, type.localized(context)))
        .toList();
  }
}
