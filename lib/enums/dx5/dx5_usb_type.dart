import 'package:flutter/cupertino.dart';
import 'package:hive_ce/hive.dart';
import 'package:topping_home/enums/interfaces/convert.dart';
import 'package:topping_home/models/hive_adapters.dart';

import '../../l10n/app_localizations.dart';
import '../interfaces/localizable.dart';

part 'dx5_usb_type.g.dart';

/// UsbType(UAC2.0,UAC1.0)
@HiveType(typeId: HiveAdapterIds.usbTypeId)
enum Dx5UsbType implements Localizable, Convert {
  // uac1.0
  @HiveField(0)
  uac1,
  // uac2.0
  @HiveField(1)
  uac2;

  @override
  String localized(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;
    switch (this) {
      case Dx5UsbType.uac2:
        return l10n.usbTypeUac2;
      case Dx5UsbType.uac1:
        return l10n.usbTypeUac1;
    }
  }

  @override
  int get value => index;

  static Dx5UsbType fromValue(int value) {
    return Convert.fromValue(Dx5UsbType.values, value, Dx5UsbType.uac2);
  }
}

// dx5_usb_type.dart 的扩展
extension UsbTypeLocalization on Dx5UsbType {
  String localized(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;
    switch (this) {
      case Dx5UsbType.uac2:
        return l10n.usbTypeUac2;
      case Dx5UsbType.uac1:
        return l10n.usbTypeUac1;
    }
  }

  static List<Dx5UsbType> get options => Dx5UsbType.values;

  static List<String> getLocalizedOptions(BuildContext context) {
    return options.map((type) => type.localized(context)).toList();
  }

  static List<MapEntry<Dx5UsbType, String>> getLocalizedEntries(
      BuildContext context) {
    return options
        .map((type) => MapEntry(type, type.localized(context)))
        .toList();
  }
}
