// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'dx5_frequency_type.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class Dx5FrequencyTypeAdapter extends TypeAdapter<Dx5FrequencyType> {
  @override
  final int typeId = 101;

  @override
  Dx5FrequencyType read(BinaryReader reader) {
    switch (reader.readByte()) {
      case 0:
        return Dx5FrequencyType.centerFrequency;
      case 1:
        return Dx5FrequencyType.cornerFrequency;
      default:
        return Dx5FrequencyType.centerFrequency;
    }
  }

  @override
  void write(BinaryWriter writer, Dx5FrequencyType obj) {
    switch (obj) {
      case Dx5FrequencyType.centerFrequency:
        writer.writeByte(0);
      case Dx5FrequencyType.cornerFrequency:
        writer.writeByte(1);
    }
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is Dx5FrequencyTypeAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}
