// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'dx5_device_type.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class Dx5DeviceTypeAdapter extends TypeAdapter<Dx5DeviceType> {
  @override
  final int typeId = 2;

  @override
  Dx5DeviceType read(BinaryReader reader) {
    switch (reader.readByte()) {
      case 0:
        return Dx5DeviceType.bluetooth;
      case 1:
        return Dx5DeviceType.dac;
      case 2:
        return Dx5DeviceType.headphone;
      case 3:
        return Dx5DeviceType.player;
      default:
        return Dx5DeviceType.bluetooth;
    }
  }

  @override
  void write(BinaryWriter writer, Dx5DeviceType obj) {
    switch (obj) {
      case Dx5DeviceType.bluetooth:
        writer.writeByte(0);
      case Dx5DeviceType.dac:
        writer.writeByte(1);
      case Dx5DeviceType.headphone:
        writer.writeByte(2);
      case Dx5DeviceType.player:
        writer.writeByte(3);
    }
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is Dx5DeviceTypeAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}
