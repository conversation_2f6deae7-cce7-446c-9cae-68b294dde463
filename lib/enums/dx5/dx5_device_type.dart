import 'package:flutter/cupertino.dart';
import 'package:hive_ce/hive.dart';
import 'package:topping_home/enums/interfaces/convert.dart';

import '../../l10n/app_localizations.dart';
import '../../models/hive_adapters.dart';
import '../interfaces/localizable.dart';

part 'dx5_device_type.g.dart';

/// 设备类型枚举
@HiveType(typeId: HiveAdapterIds.deviceTypeId)
enum Dx5DeviceType implements Localizable, Convert {
  // 蓝牙耳放
  @HiveField(0)
  bluetooth,
  // 解码耳放
  @HiveField(1)
  dac,
  // 耳机
  @HiveField(2)
  headphone,
  // 播放器
  @HiveField(3)
  player;

  @override
  String localized(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;
    switch (this) {
      case Dx5DeviceType.bluetooth:
        return l10n.deviceTypeBluetooth;
      case Dx5DeviceType.dac:
        return l10n.deviceTypeDac;
      case Dx5DeviceType.headphone:
        return l10n.deviceTypeHeadphone;
      case Dx5DeviceType.player:
        return l10n.deviceTypePlayer;
    }
  }

  @override
  int get value => index;

  static Dx5DeviceType fromValue(int value) {
    return Convert.fromValue(Dx5DeviceType.values, value, Dx5DeviceType.bluetooth);
  }
}

// device_type_entity.dart 的扩展
extension DeviceTypeLocalization on Dx5DeviceType {
  String localized(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;
    switch (this) {
      case Dx5DeviceType.bluetooth:
        return l10n.deviceTypeBluetooth;
      case Dx5DeviceType.dac:
        return l10n.deviceTypeDac;
      case Dx5DeviceType.headphone:
        return l10n.deviceTypeHeadphone;
      case Dx5DeviceType.player:
        return l10n.deviceTypePlayer;
    }
  }

  static List<Dx5DeviceType> get options => Dx5DeviceType.values;

  static List<String> getLocalizedOptions(BuildContext context) {
    return options.map((type) => type.localized(context)).toList();
  }

  static List<MapEntry<Dx5DeviceType, String>> getLocalizedEntries(
      BuildContext context) {
    return options
        .map((type) => MapEntry(type, type.localized(context)))
        .toList();
  }
}
