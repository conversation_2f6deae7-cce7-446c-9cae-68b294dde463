// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'dx5_theme_type.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class Dx5ThemeTypeAdapter extends TypeAdapter<Dx5ThemeType> {
  @override
  final int typeId = 112;

  @override
  Dx5ThemeType read(BinaryReader reader) {
    switch (reader.readByte()) {
      case 0:
        return Dx5ThemeType.aurora;
      case 1:
        return Dx5ThemeType.orange;
      case 2:
        return Dx5ThemeType.peru;
      case 3:
        return Dx5ThemeType.green;
      case 4:
        return Dx5ThemeType.khaki;
      case 5:
        return Dx5ThemeType.rose;
      case 6:
        return Dx5ThemeType.blue;
      case 7:
        return Dx5ThemeType.purple;
      case 8:
        return Dx5ThemeType.white;
      default:
        return Dx5ThemeType.aurora;
    }
  }

  @override
  void write(BinaryWriter writer, Dx5ThemeType obj) {
    switch (obj) {
      case Dx5ThemeType.aurora:
        writer.writeByte(0);
      case Dx5ThemeType.orange:
        writer.writeByte(1);
      case Dx5ThemeType.peru:
        writer.writeByte(2);
      case Dx5ThemeType.green:
        writer.writeByte(3);
      case Dx5ThemeType.khaki:
        writer.writeByte(4);
      case Dx5ThemeType.rose:
        writer.writeByte(5);
      case Dx5ThemeType.blue:
        writer.writeByte(6);
      case Dx5ThemeType.purple:
        writer.writeByte(7);
      case Dx5ThemeType.white:
        writer.writeByte(8);
    }
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is Dx5ThemeTypeAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}
