import 'package:flutter/cupertino.dart';
import 'package:hive_ce/hive.dart';
import 'package:topping_home/enums/interfaces/convert.dart';
import 'package:topping_home/models/hive_adapters.dart';

import '../interfaces/localizable.dart';


part 'dx5_filter_parameter_type.g.dart';

/// 滤波器类型
@HiveType(typeId: HiveAdapterIds.filterParameterTypeId)
enum Dx5FilterParameterType implements Localizable, Convert {
  // 最小相位
  @HiveField(0)
  minimumPhase,
  // 线性相位快速衰减平滑型
  @HiveField(1)
  linearPhaseApodizing,
  // 线性相位快速衰减
  @HiveField(2)
  linearPhaseFast,
  // 线性相位快速衰减低纹波
  @HiveField(3)
  linearPhaseFastLowRipple,
  // 线性相位慢速衰减
  @HiveField(4)
  linearPhaseSlow,
  // 最小相位快速衰减
  @HiveField(5)
  minimumPhaseFast,
  // 最小相位慢速衰减
  @HiveField(6)
  minimumPhaseSlow,
  // 最小相位慢速衰减低色散
  @HiveField(7)
  minimumPhaseSlowLowDispersion;

  @override
  String localized(BuildContext context) {
    switch (this) {
      case Dx5FilterParameterType.minimumPhase:
        return 'F-1 Minimum Phase';
      case Dx5FilterParameterType.linearPhaseApodizing:
        return 'F-2 Liner Phase Fast Roll-off Apodizing';
      case Dx5FilterParameterType.linearPhaseFast:
        return 'F-3 Linear Phase Fast Roll-off';
      case Dx5FilterParameterType.linearPhaseFastLowRipple:
        return 'F-4 Linear Phase Fast Roll-off Low Ripple';
      case Dx5FilterParameterType.linearPhaseSlow:
        return 'F-5 Linear Phase Slow Roll-off';
      case Dx5FilterParameterType.minimumPhaseFast:
        return 'F-6 Minimum Phase Fast Roll-off';
      case Dx5FilterParameterType.minimumPhaseSlow:
        return 'F-7 Minimum Phase Slow Roll-off';
      case Dx5FilterParameterType.minimumPhaseSlowLowDispersion:
        return 'F-8 Minimum Phase Slow Roll-off Low Dispersion';
    }
  }

  @override
  int get value => index;

  static Dx5FilterParameterType fromValue(int value) {
    return Convert.fromValue(
        Dx5FilterParameterType.values, value, Dx5FilterParameterType.minimumPhase);
  }
}

// dx5_filter_parameter_type.dart 的扩展
extension FilterParameterTypeLocalization on Dx5FilterParameterType {
  String localized(BuildContext context) {
    switch (this) {
      case Dx5FilterParameterType.minimumPhase:
        return 'F-1 Minimum Phase';
      case Dx5FilterParameterType.linearPhaseApodizing:
        return 'F-2 Liner Phase Fast Roll-off Apodizing';
      case Dx5FilterParameterType.linearPhaseFast:
        return 'F-3 Linear Phase Fast Roll-off';
      case Dx5FilterParameterType.linearPhaseFastLowRipple:
        return 'F-4 Linear Phase Fast Roll-off Low Ripple';
      case Dx5FilterParameterType.linearPhaseSlow:
        return 'F-5 Linear Phase Slow Roll-off';
      case Dx5FilterParameterType.minimumPhaseFast:
        return 'F-6 Minimum Phase Fast Roll-off';
      case Dx5FilterParameterType.minimumPhaseSlow:
        return 'F-7 Minimum Phase Slow Roll-off';
      case Dx5FilterParameterType.minimumPhaseSlowLowDispersion:
        return 'F-8 Minimum Phase Slow Roll-off Low Dispersion';
    }
  }

  static List<Dx5FilterParameterType> get options => Dx5FilterParameterType.values;

  static List<String> getLocalizedOptions(BuildContext context) {
    return options.map((type) => type.localized(context)).toList();
  }

  static List<MapEntry<Dx5FilterParameterType, String>> getLocalizedEntries(
      BuildContext context) {
    return options
        .map((type) => MapEntry(type, type.localized(context)))
        .toList();
  }
}
