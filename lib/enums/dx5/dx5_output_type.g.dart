// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'dx5_output_type.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class Dx5OutputTypeAdapter extends TypeAdapter<Dx5OutputType> {
  @override
  final int typeId = 109;

  @override
  Dx5OutputType read(BinaryReader reader) {
    switch (reader.readByte()) {
      case 0:
        return Dx5OutputType.singleEnded;
      case 1:
        return Dx5OutputType.balanced;
      case 2:
        return Dx5OutputType.singleEndedAndBalanced;
      case 3:
        return Dx5OutputType.close;
      default:
        return Dx5OutputType.singleEnded;
    }
  }

  @override
  void write(BinaryWriter writer, Dx5OutputType obj) {
    switch (obj) {
      case Dx5OutputType.singleEnded:
        writer.writeByte(0);
      case Dx5OutputType.balanced:
        writer.writeByte(1);
      case Dx5OutputType.singleEndedAndBalanced:
        writer.writeByte(2);
      case Dx5OutputType.close:
        writer.writeByte(3);
    }
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is Dx5OutputTypeAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}
