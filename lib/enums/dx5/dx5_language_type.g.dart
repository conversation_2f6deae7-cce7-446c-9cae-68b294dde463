// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'dx5_language_type.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class Dx5LanguageTypeAdapter extends TypeAdapter<Dx5LanguageType> {
  @override
  final int typeId = 107;

  @override
  Dx5LanguageType read(BinaryReader reader) {
    switch (reader.readByte()) {
      case 0:
        return Dx5LanguageType.en;
      case 1:
        return Dx5LanguageType.zh;
      default:
        return Dx5LanguageType.en;
    }
  }

  @override
  void write(BinaryWriter writer, Dx5LanguageType obj) {
    switch (obj) {
      case Dx5LanguageType.en:
        writer.writeByte(0);
      case Dx5LanguageType.zh:
        writer.writeByte(1);
    }
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is Dx5LanguageTypeAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}
