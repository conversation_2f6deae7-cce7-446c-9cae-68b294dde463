import 'package:flutter/cupertino.dart';
import 'package:hive_ce/hive.dart';
import 'package:topping_home/enums/interfaces/convert.dart';
import 'package:topping_home/models/hive_adapters.dart';

import '../../l10n/app_localizations.dart';
import '../interfaces/localizable.dart';


part 'dx5_language_type.g.dart';

/// 语言（中文，英文）
@HiveType(typeId: HiveAdapterIds.languageTypeId)
enum Dx5LanguageType implements Localizable, Convert {
  // en
  @HiveField(0)
  en,
  // zh
  @HiveField(1)
  zh;

  @override
  String localized(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;
    switch (this) {
      case Dx5LanguageType.zh:
        return l10n.languageZh;
      case Dx5LanguageType.en:
        return l10n.languageEn;
    }
  }

  @override
  int get value => index;

  static Dx5LanguageType fromValue(int value) {
    return Convert.fromValue(Dx5LanguageType.values, value, Dx5LanguageType.zh);
  }
}

// dx5_language_type.dart 的扩展
extension LanguageTypeLocalization on Dx5LanguageType {
  String localized(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;
    switch (this) {
      case Dx5LanguageType.zh:
        return l10n.languageZh;
      case Dx5LanguageType.en:
        return l10n.languageEn;
    }
  }

  static List<Dx5LanguageType> get options => Dx5LanguageType.values;

  static List<String> getLocalizedOptions(BuildContext context) {
    return options.map((type) => type.localized(context)).toList();
  }

  static List<MapEntry<Dx5LanguageType, String>> getLocalizedEntries(
      BuildContext context) {
    return options
        .map((type) => MapEntry(type, type.localized(context)))
        .toList();
  }
}
