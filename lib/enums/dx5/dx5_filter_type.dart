import 'package:flutter/cupertino.dart';
import 'package:hive_ce/hive.dart';
import 'package:topping_home/enums/interfaces/convert.dart';
import 'package:topping_home/models/hive_adapters.dart';

import '../../l10n/app_localizations.dart';
import '../interfaces/localizable.dart';


part 'dx5_filter_type.g.dart';

/// 滤波器类型
@HiveType(typeId: HiveAdapterIds.filterTypeId)
enum Dx5FilterType implements Localizable, Convert {
  // 峰值滤波器
  @HiveField(0)
  peakingFilter,
  // 低通滤波器
  @HiveField(1)
  lowPassFilter,
  // 高通滤波器
  @HiveField(2)
  highPassFilter,
  // 低架滤波器
  @HiveField(3)
  lowShelfFilter,
  // 高架滤波器
  @HiveField(4)
  highShelfFilter;

  @override
  String localized(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;
    switch (this) {
      case Dx5FilterType.peakingFilter:
        return l10n.peakingFilter;
      case Dx5FilterType.lowPassFilter:
        return l10n.lowPassFilter;
      case Dx5FilterType.highPassFilter:
        return l10n.highPassFilter;
      case Dx5FilterType.lowShelfFilter:
        return l10n.lowShelfFilter;
      case Dx5FilterType.highShelfFilter:
        return l10n.highShelfFilter;
    }
  }

  @override
  int get value => index;

  static Dx5FilterType fromValue(int value) {
    return Convert.fromValue(
        Dx5FilterType.values, value, Dx5FilterType.peakingFilter);
  }
}

// dx5_filter_parameter_type.dart 的扩展
extension FilterParameterTypeLocalization on Dx5FilterType {
  String localized(BuildContext context) {
    return localized(context);
  }

  static List<Dx5FilterType> get options => Dx5FilterType.values;

  static List<String> getLocalizedOptions(BuildContext context) {
    return options.map((type) => type.localized(context)).toList();
  }

  static List<MapEntry<Dx5FilterType, String>> getLocalizedEntries(
      BuildContext context) {
    return options
        .map((type) => MapEntry(type, type.localized(context)))
        .toList();
  }

  // 根据string获取FilterType
  static Dx5FilterType? fromString(BuildContext context, String? value) {
    if (value == null) return null;
    for (var type in Dx5FilterType.values) {
      if (type.localized(context) == value) {
        return type;
      }
    }
    return null;
  }
}
