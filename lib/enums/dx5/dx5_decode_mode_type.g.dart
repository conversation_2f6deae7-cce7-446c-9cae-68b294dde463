// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'dx5_decode_mode_type.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class Dx5DecodeModeTypeAdapter extends TypeAdapter<Dx5DecodeModeType> {
  @override
  final int typeId = 102;

  @override
  Dx5DecodeModeType read(BinaryReader reader) {
    switch (reader.readByte()) {
      case 0:
        return Dx5DecodeModeType.prefix;
      case 1:
        return Dx5DecodeModeType.dac;
      default:
        return Dx5DecodeModeType.prefix;
    }
  }

  @override
  void write(BinaryWriter writer, Dx5DecodeModeType obj) {
    switch (obj) {
      case Dx5DecodeModeType.prefix:
        writer.writeByte(0);
      case Dx5DecodeModeType.dac:
        writer.writeByte(1);
    }
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is Dx5DecodeModeTypeAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}
