import 'package:flutter/material.dart';
import 'package:hive_ce/hive.dart';
import 'package:topping_home/enums/interfaces/convert.dart';
import 'package:topping_home/models/hive_adapters.dart';

import '../../l10n/app_localizations.dart';
import '../interfaces/localizable.dart';


part 'dx5_frequency_type.g.dart';

/// 频率类型
@HiveType(typeId: HiveAdapterIds.frequencyTypeId)
enum Dx5FrequencyType implements Localizable, Convert {
  @HiveField(0)
  centerFrequency, // 中心频率
  @HiveField(1)
  cornerFrequency; // 拐点频率

  @override
  String localized(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;
    switch (this) {
      case Dx5FrequencyType.centerFrequency:
        return l10n.centerFrequency; // 中心频率
      case Dx5FrequencyType.cornerFrequency:
        return l10n.connerFrequency; // 拐点频率
    }
  }

  @override
  int get value => index;

  static Dx5FrequencyType fromValue(int value) {
    return Convert.fromValue(
        Dx5FrequencyType.values, value, Dx5FrequencyType.centerFrequency);
  }
}
