// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'dx5_display_type.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class Dx5DisplayTypeAdapter extends TypeAdapter<Dx5DisplayType> {
  @override
  final int typeId = 103;

  @override
  Dx5DisplayType read(BinaryReader reader) {
    switch (reader.readByte()) {
      case 0:
        return Dx5DisplayType.normal;
      case 1:
        return Dx5DisplayType.vu;
      case 2:
        return Dx5DisplayType.fft;
      default:
        return Dx5DisplayType.normal;
    }
  }

  @override
  void write(BinaryWriter writer, Dx5DisplayType obj) {
    switch (obj) {
      case Dx5DisplayType.normal:
        writer.writeByte(0);
      case Dx5DisplayType.vu:
        writer.writeByte(1);
      case Dx5DisplayType.fft:
        writer.writeByte(2);
    }
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is Dx5DisplayTypeAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}
