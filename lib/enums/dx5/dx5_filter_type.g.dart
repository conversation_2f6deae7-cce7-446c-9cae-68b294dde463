// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'dx5_filter_type.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class Dx5FilterTypeAdapter extends TypeAdapter<Dx5FilterType> {
  @override
  final int typeId = 100;

  @override
  Dx5FilterType read(BinaryReader reader) {
    switch (reader.readByte()) {
      case 0:
        return Dx5FilterType.peakingFilter;
      case 1:
        return Dx5FilterType.lowPassFilter;
      case 2:
        return Dx5FilterType.highPassFilter;
      case 3:
        return Dx5FilterType.lowShelfFilter;
      case 4:
        return Dx5FilterType.highShelfFilter;
      default:
        return Dx5FilterType.peakingFilter;
    }
  }

  @override
  void write(BinaryWriter writer, Dx5FilterType obj) {
    switch (obj) {
      case Dx5FilterType.peakingFilter:
        writer.writeByte(0);
      case Dx5FilterType.lowPassFilter:
        writer.writeByte(1);
      case Dx5FilterType.highPassFilter:
        writer.writeByte(2);
      case Dx5FilterType.lowShelfFilter:
        writer.writeByte(3);
      case Dx5FilterType.highShelfFilter:
        writer.writeByte(4);
    }
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is Dx5FilterTypeAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}
