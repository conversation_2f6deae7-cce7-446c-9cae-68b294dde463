// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'dx5_input_type.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class Dx5InputTypeAdapter extends TypeAdapter<Dx5InputType> {
  @override
  final int typeId = 106;

  @override
  Dx5InputType read(BinaryReader reader) {
    switch (reader.readByte()) {
      case 0:
        return Dx5InputType.usb;
      case 1:
        return Dx5InputType.optical;
      case 2:
        return Dx5InputType.coaxial;
      case 3:
        return Dx5InputType.bluetooth;
      default:
        return Dx5InputType.usb;
    }
  }

  @override
  void write(BinaryWriter writer, Dx5InputType obj) {
    switch (obj) {
      case Dx5InputType.usb:
        writer.writeByte(0);
      case Dx5InputType.optical:
        writer.writeByte(1);
      case Dx5InputType.coaxial:
        writer.writeByte(2);
      case Dx5InputType.bluetooth:
        writer.writeByte(3);
    }
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is Dx5InputTypeAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}
