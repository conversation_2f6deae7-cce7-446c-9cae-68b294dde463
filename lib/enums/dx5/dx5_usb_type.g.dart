// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'dx5_usb_type.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class Dx5UsbTypeAdapter extends TypeAdapter<Dx5UsbType> {
  @override
  final int typeId = 113;

  @override
  Dx5UsbType read(BinaryReader reader) {
    switch (reader.readByte()) {
      case 0:
        return Dx5UsbType.uac1;
      case 1:
        return Dx5UsbType.uac2;
      default:
        return Dx5UsbType.uac1;
    }
  }

  @override
  void write(BinaryWriter writer, Dx5UsbType obj) {
    switch (obj) {
      case Dx5UsbType.uac1:
        writer.writeByte(0);
      case Dx5UsbType.uac2:
        writer.writeByte(1);
    }
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is Dx5UsbTypeAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}
