// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'dx5_screen_brightness_type.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class Dx5ScreenBrightnessTypeAdapter
    extends TypeAdapter<Dx5ScreenBrightnessType> {
  @override
  final int typeId = 111;

  @override
  Dx5ScreenBrightnessType read(BinaryReader reader) {
    switch (reader.readByte()) {
      case 0:
        return Dx5ScreenBrightnessType.low;
      case 1:
        return Dx5ScreenBrightnessType.medium;
      case 2:
        return Dx5ScreenBrightnessType.high;
      case 3:
        return Dx5ScreenBrightnessType.auto;
      default:
        return Dx5ScreenBrightnessType.low;
    }
  }

  @override
  void write(BinaryWriter writer, Dx5ScreenBrightnessType obj) {
    switch (obj) {
      case Dx5ScreenBrightnessType.low:
        writer.writeByte(0);
      case Dx5ScreenBrightnessType.medium:
        writer.writeByte(1);
      case Dx5ScreenBrightnessType.high:
        writer.writeByte(2);
      case Dx5ScreenBrightnessType.auto:
        writer.writeByte(3);
    }
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is Dx5ScreenBrightnessTypeAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}
