import 'package:flutter/cupertino.dart';
import 'package:hive_ce/hive.dart';
import 'package:topping_home/models/hive_adapters.dart';

import '../../l10n/app_localizations.dart';
import '../interfaces/convert.dart';
import '../interfaces/localizable.dart';

part 'dx5_output_type.g.dart';

/// 输出枚举(单端/平衡/RCA+XLR/Close)
@HiveType(typeId: HiveAdapterIds.outputTypeId)
enum Dx5OutputType implements Localizable, Convert {
  // RCA
  @HiveField(0)
  singleEnded,
  // XLR
  @HiveField(1)
  balanced,
  // RCA+XLR
  @HiveField(2)
  singleEndedAndBalanced,
  // Close
  @HiveField(3)
  close;

  @override
  String localized(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;
    switch (this) {
      case Dx5OutputType.close:
        return l10n.outputClose;
      case Dx5OutputType.singleEnded:
        return l10n.outputSingleEnded;
      case Dx5OutputType.balanced:
        return l10n.outputBalanced;
      case Dx5OutputType.singleEndedAndBalanced:
        return l10n.outputSingleEndedAndBalanced;
    }
  }

  @override
  int get value => index;

  static Dx5OutputType fromValue(int value) {
    return Convert.fromValue(Dx5OutputType.values, value, Dx5OutputType.close);
  }
}

// OutputType 扩展
extension OutputTypeLocalization on Dx5OutputType {
  String localized(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;
    switch (this) {
      case Dx5OutputType.close:
        return l10n.outputClose;
      case Dx5OutputType.singleEnded:
        return l10n.outputSingleEnded;
      case Dx5OutputType.balanced:
        return l10n.outputBalanced;
      case Dx5OutputType.singleEndedAndBalanced:
        return l10n.outputSingleEndedAndBalanced;
    }
  }

  static List<Dx5OutputType> get options => Dx5OutputType.values;

  static List<String> getLocalizedOptions(BuildContext context) {
    return options.map((type) => type.localized(context)).toList();
  }

  static List<MapEntry<Dx5OutputType, String>> getLocalizedEntries(
      BuildContext context) {
    return options
        .map((type) => MapEntry(type, type.localized(context)))
        .toList();
  }
}
