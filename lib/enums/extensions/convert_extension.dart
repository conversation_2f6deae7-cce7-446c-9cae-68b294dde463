/// 为枚举类型提供转换功能的扩展
extension ConvertExtension<T extends Enum> on T {
  /// 获取枚举的值（默认为index）
  int get value => (this as dynamic).index;
}

/// 为枚举类型列表提供的扩展
extension EnumListExtension<T extends Enum> on List<T> {
  /// 从数值转换为枚举
  T fromValue(int value, T defaultValue) {
    try {
      return firstWhere(
            (type) => (type as dynamic).index == value,
        orElse: () => defaultValue,
      );
    } catch (e) {
      return defaultValue;
    }
  }
}