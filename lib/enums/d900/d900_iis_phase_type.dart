import 'package:flutter/cupertino.dart';
import 'package:hive_ce/hive.dart';
import 'package:topping_home/models/hive_adapters.dart';

import '../../l10n/app_localizations.dart';
import '../interfaces/convert.dart';
import '../interfaces/localizable.dart';

part 'd900_iis_phase_type.g.dart';

/// D900 IIS相位枚举(标准, 反向)
@HiveType(typeId: HiveAdapterIds.d900IisPhaseTypeId)
enum D900IisPhaseType implements Localizable, Convert {
  // 标准
  @HiveField(0)
  standard,
  // 反向
  @HiveField(1)
  inverted;

  @override
  String localized(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;
    switch (this) {
      case D900IisPhaseType.standard:
        return l10n.standard;
      case D900IisPhaseType.inverted:
        return l10n.inverted;
    }
  }

  @override
  int get value => index;

  static D900IisPhaseType fromValue(int value) {
    return Convert.fromValue(D900IisPhaseType.values, value, D900IisPhaseType.standard);
  }
}

// D900IisPhaseType 扩展
extension D900IisPhaseTypeLocalization on D900IisPhaseType {
  String localized(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;
    switch (this) {
      case D900IisPhaseType.standard:
        return l10n.standard;
      case D900IisPhaseType.inverted:
        return l10n.inverted;
    }
  }

  static List<D900IisPhaseType> get options => D900IisPhaseType.values;

  static List<String> getLocalizedOptions(BuildContext context) {
    return options.map((type) => type.localized(context)).toList();
  }

  static List<MapEntry<D900IisPhaseType, String>> getLocalizedEntries(
      BuildContext context) {
    return options
        .map((type) => MapEntry(type, type.localized(context)))
        .toList();
  }
}
