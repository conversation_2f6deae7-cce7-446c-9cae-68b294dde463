import 'package:flutter/cupertino.dart';
import 'package:hive_ce/hive.dart';
import 'package:topping_home/models/hive_adapters.dart';

import '../../l10n/app_localizations.dart';
import '../interfaces/convert.dart';
import '../interfaces/localizable.dart';

part 'd900_output_type.g.dart';

/// D900 输出枚举(DAC, 前级, 全部)
@HiveType(typeId: HiveAdapterIds.d900OutputTypeId)
enum D900OutputType implements Localizable, Convert {
  // DAC
  @HiveField(0)
  dac,
  // 前级
  @HiveField(1)
  preamp,
  // 全部
  @HiveField(2)
  all;

  @override
  String localized(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;
    switch (this) {
      case D900OutputType.dac:
        return 'DAC';
      case D900OutputType.preamp:
        return l10n.outputPreamp;
      case D900OutputType.all:
        return l10n.outputAll;
    }
  }

  @override
  int get value => index;

  static D900OutputType fromValue(int value) {
    return Convert.fromValue(D900OutputType.values, value, D900OutputType.dac);
  }
}

// D900OutputType 扩展
extension D900OutputTypeLocalization on D900OutputType {
  String localized(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;
    switch (this) {
      case D900OutputType.dac:
        return 'DAC';
      case D900OutputType.preamp:
        return l10n.outputPreamp;
      case D900OutputType.all:
        return l10n.outputAll;
    }
  }

  static List<D900OutputType> get options => D900OutputType.values;

  static List<String> getLocalizedOptions(BuildContext context) {
    return options.map((type) => type.localized(context)).toList();
  }

  static List<MapEntry<D900OutputType, String>> getLocalizedEntries(
      BuildContext context) {
    return options
        .map((type) => MapEntry(type, type.localized(context)))
        .toList();
  }
}
