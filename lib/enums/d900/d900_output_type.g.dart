// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'd900_output_type.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class D900OutputTypeAdapter extends TypeAdapter<D900OutputType> {
  @override
  final int typeId = 201;

  @override
  D900OutputType read(BinaryReader reader) {
    switch (reader.readByte()) {
      case 0:
        return D900OutputType.dac;
      case 1:
        return D900OutputType.preamp;
      case 2:
        return D900OutputType.all;
      default:
        return D900OutputType.dac;
    }
  }

  @override
  void write(BinaryWriter writer, D900OutputType obj) {
    switch (obj) {
      case D900OutputType.dac:
        writer.writeByte(0);
      case D900OutputType.preamp:
        writer.writeByte(1);
      case D900OutputType.all:
        writer.writeByte(2);
    }
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is D900OutputTypeAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}
