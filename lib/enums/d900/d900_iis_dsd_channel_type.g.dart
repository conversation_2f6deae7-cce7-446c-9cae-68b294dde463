// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'd900_iis_dsd_channel_type.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class D900IisDsdChannelTypeAdapter extends TypeAdapter<D900IisDsdChannelType> {
  @override
  final int typeId = 206;

  @override
  D900IisDsdChannelType read(BinaryReader reader) {
    switch (reader.readByte()) {
      case 0:
        return D900IisDsdChannelType.standard;
      case 1:
        return D900IisDsdChannelType.swapped;
      default:
        return D900IisDsdChannelType.standard;
    }
  }

  @override
  void write(BinaryWriter writer, D900IisDsdChannelType obj) {
    switch (obj) {
      case D900IisDsdChannelType.standard:
        writer.writeByte(0);
      case D900IisDsdChannelType.swapped:
        writer.writeByte(1);
    }
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is D900IisDsdChannelTypeAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}
