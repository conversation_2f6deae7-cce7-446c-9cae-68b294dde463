import 'package:flutter/cupertino.dart';
import 'package:hive_ce/hive.dart';
import 'package:topping_home/models/hive_adapters.dart';

import '../../l10n/app_localizations.dart';
import '../interfaces/convert.dart';
import '../interfaces/localizable.dart';

part 'd900_multi_function_key_type.g.dart';

/// D900 多功能按键枚举(输入选择, 输出选择, 主页选择, 亮度选择, 息屏, PEQ选择, 静音)
@HiveType(typeId: HiveAdapterIds.d900MultiFunctionKeyTypeId)
enum D900MultiFunctionKeyType implements Localizable, Convert {
  // 输入选择
  @HiveField(0)
  inputSelect,
  // 输出选择
  @HiveField(1)
  outputSelect,
  // 主页选择
  @HiveField(2)
  homeSelect,
  // 亮度选择
  @HiveField(3)
  brightnessSelect,
  // 息屏
  @HiveField(4)
  screenOff,
  // PEQ选择
  @HiveField(5)
  peqSelect,
  // 静音
  @HiveField(6)
  mute;

  @override
  String localized(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;
    switch (this) {
      case D900MultiFunctionKeyType.inputSelect:
        return l10n.multiFunctionKeyInputSelect;
      case D900MultiFunctionKeyType.outputSelect:
        return l10n.multiFunctionKeyOutputSelect;
      case D900MultiFunctionKeyType.homeSelect:
        return l10n.multiFunctionKeyHomeSelect;
      case D900MultiFunctionKeyType.brightnessSelect:
        return l10n.multiFunctionKeyBrightnessSelect;
      case D900MultiFunctionKeyType.screenOff:
        return l10n.multiFunctionKeyScreenOff;
      case D900MultiFunctionKeyType.peqSelect:
        return l10n.multiFunctionKeyPeqSelect;
      case D900MultiFunctionKeyType.mute:
        return l10n.multiFunctionKeyMute;
    }
  }

  @override
  int get value => index;

  static D900MultiFunctionKeyType fromValue(int value) {
    return Convert.fromValue(D900MultiFunctionKeyType.values, value, D900MultiFunctionKeyType.inputSelect);
  }
}

// D900MultiFunctionKeyType 扩展
extension D900MultiFunctionKeyTypeLocalization on D900MultiFunctionKeyType {
  String localized(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;
    switch (this) {
      case D900MultiFunctionKeyType.inputSelect:
        return l10n.multiFunctionKeyInputSelect;
      case D900MultiFunctionKeyType.outputSelect:
        return l10n.multiFunctionKeyOutputSelect;
      case D900MultiFunctionKeyType.homeSelect:
        return l10n.multiFunctionKeyHomeSelect;
      case D900MultiFunctionKeyType.brightnessSelect:
        return l10n.multiFunctionKeyBrightnessSelect;
      case D900MultiFunctionKeyType.screenOff:
        return l10n.multiFunctionKeyScreenOff;
      case D900MultiFunctionKeyType.peqSelect:
        return l10n.multiFunctionKeyPeqSelect;
      case D900MultiFunctionKeyType.mute:
        return l10n.multiFunctionKeyMute;
    }
  }

  static List<D900MultiFunctionKeyType> get options => D900MultiFunctionKeyType.values;

  static List<String> getLocalizedOptions(BuildContext context) {
    return options.map((type) => type.localized(context)).toList();
  }

  static List<MapEntry<D900MultiFunctionKeyType, String>> getLocalizedEntries(
      BuildContext context) {
    return options
        .map((type) => MapEntry(type, type.localized(context)))
        .toList();
  }
}
