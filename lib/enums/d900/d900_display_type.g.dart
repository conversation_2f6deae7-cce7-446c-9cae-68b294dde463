// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'd900_display_type.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class D900DisplayTypeAdapter extends TypeAdapter<D900DisplayType> {
  @override
  final int typeId = 212;

  @override
  D900DisplayType read(BinaryReader reader) {
    switch (reader.readByte()) {
      case 0:
        return D900DisplayType.normal;
      case 1:
        return D900DisplayType.vu;
      case 2:
        return D900DisplayType.fft;
      default:
        return D900DisplayType.normal;
    }
  }

  @override
  void write(BinaryWriter writer, D900DisplayType obj) {
    switch (obj) {
      case D900DisplayType.normal:
        writer.writeByte(0);
      case D900DisplayType.vu:
        writer.writeByte(1);
      case D900DisplayType.fft:
        writer.writeByte(2);
    }
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is D900DisplayTypeAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}
