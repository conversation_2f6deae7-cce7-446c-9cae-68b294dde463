import 'package:flutter/cupertino.dart';
import 'package:hive_ce/hive.dart';
import 'package:topping_home/models/hive_adapters.dart';

import '../../l10n/app_localizations.dart';
import '../interfaces/convert.dart';
import '../interfaces/localizable.dart';

part 'd900_screen_brightness_type.g.dart';

/// D900 屏幕亮度枚举(H, M, L, AUTO)
@HiveType(typeId: HiveAdapterIds.d900ScreenBrightnessTypeId)
enum D900ScreenBrightnessType implements Localizable, Convert {
  // L
  @HiveField(0)
  low,
  // M
  @HiveField(1)
  medium,
  // H
  @HiveField(2)
  high,
  @HiveField(3)
  // AUTO
  auto;

  @override
  String localized(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;
    switch (this) {
      case D900ScreenBrightnessType.high:
        return l10n.screenBrightnessHigh;
      case D900ScreenBrightnessType.medium:
        return l10n.screenBrightnessMedium;
      case D900ScreenBrightnessType.low:
        return l10n.screenBrightnessLow;
      case D900ScreenBrightnessType.auto:
        return l10n.screenBrightnessAuto;
    }
  }

  @override
  int get value => index;

  static D900ScreenBrightnessType fromValue(int value) {
    return Convert.fromValue(D900ScreenBrightnessType.values, value, D900ScreenBrightnessType.auto);
  }
}

// D900ScreenBrightnessType 扩展
extension D900ScreenBrightnessTypeLocalization on D900ScreenBrightnessType {
  String localized(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;
    switch (this) {
      case D900ScreenBrightnessType.high:
        return l10n.screenBrightnessHigh;
      case D900ScreenBrightnessType.medium:
        return l10n.screenBrightnessMedium;
      case D900ScreenBrightnessType.low:
        return l10n.screenBrightnessLow;
      case D900ScreenBrightnessType.auto:
        return l10n.screenBrightnessAuto;
    }
  }

  static List<D900ScreenBrightnessType> get options => D900ScreenBrightnessType.values;

  static List<String> getLocalizedOptions(BuildContext context) {
    return options.map((type) => type.localized(context)).toList();
  }

  static List<MapEntry<D900ScreenBrightnessType, String>> getLocalizedEntries(
      BuildContext context) {
    return options
        .map((type) => MapEntry(type, type.localized(context)))
        .toList();
  }
}
