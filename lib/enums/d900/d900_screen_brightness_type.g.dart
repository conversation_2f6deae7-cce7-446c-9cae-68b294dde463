// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'd900_screen_brightness_type.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class D900ScreenBrightnessTypeAdapter
    extends TypeAdapter<D900ScreenBrightnessType> {
  @override
  final int typeId = 209;

  @override
  D900ScreenBrightnessType read(BinaryReader reader) {
    switch (reader.readByte()) {
      case 0:
        return D900ScreenBrightnessType.low;
      case 1:
        return D900ScreenBrightnessType.medium;
      case 2:
        return D900ScreenBrightnessType.high;
      case 3:
        return D900ScreenBrightnessType.auto;
      default:
        return D900ScreenBrightnessType.low;
    }
  }

  @override
  void write(BinaryWriter writer, D900ScreenBrightnessType obj) {
    switch (obj) {
      case D900ScreenBrightnessType.low:
        writer.writeByte(0);
      case D900ScreenBrightnessType.medium:
        writer.writeByte(1);
      case D900ScreenBrightnessType.high:
        writer.writeByte(2);
      case D900ScreenBrightnessType.auto:
        writer.writeByte(3);
    }
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is D900ScreenBrightnessTypeAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}
