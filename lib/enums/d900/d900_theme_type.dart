import 'package:flutter/cupertino.dart';
import 'package:hive_ce/hive.dart';
import 'package:topping_home/models/hive_adapters.dart';

import '../../l10n/app_localizations.dart';
import '../interfaces/convert.dart';
import '../interfaces/localizable.dart';

part 'd900_theme_type.g.dart';

/// D900 主题枚举(极光, 橙色, 秘鲁色, 豆绿色, 深卡其色, 玫瑰棕色, 蓝色, 幻紫色, 白色)
@HiveType(typeId: HiveAdapterIds.d900ThemeTypeId)
enum D900ThemeType implements Localizable, Convert {
  // 极光
  @HiveField(0)
  aurora,
  // 橙色
  @HiveField(1)
  orange,
  // 秘鲁色
  @HiveField(2)
  peru,
  // 豆绿色
  @HiveField(3)
  green,
  // 深卡其色
  @HiveField(4)
  khaki,
  // 玫瑰棕色
  @HiveField(5)
  rose,
  // 蓝色
  @HiveField(6)
  blue,
  // 幻紫色
  @HiveField(7)
  purple,
  // 白色
  @HiveField(8)
  white;

  @override
  String localized(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;
    switch (this) {
      case D900ThemeType.aurora:
        return l10n.themeAurora;
      case D900ThemeType.orange:
        return l10n.themeOrange;
      case D900ThemeType.peru:
        return l10n.themePeru;
      case D900ThemeType.green:
        return l10n.themeGreen;
      case D900ThemeType.khaki:
        return l10n.themeKhaki;
      case D900ThemeType.rose:
        return l10n.themeRose;
      case D900ThemeType.blue:
        return l10n.themeBlue;
      case D900ThemeType.purple:
        return l10n.themePurple;
      case D900ThemeType.white:
        return l10n.themeWhite;
    }
  }

  @override
  int get value => index;

  static D900ThemeType fromValue(int value) {
    return Convert.fromValue(D900ThemeType.values, value, D900ThemeType.aurora);
  }
}

// D900ThemeType 扩展
extension D900ThemeTypeLocalization on D900ThemeType {
  String localized(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;
    switch (this) {
      case D900ThemeType.aurora:
        return l10n.themeAurora;
      case D900ThemeType.orange:
        return l10n.themeOrange;
      case D900ThemeType.peru:
        return l10n.themePeru;
      case D900ThemeType.green:
        return l10n.themeGreen;
      case D900ThemeType.khaki:
        return l10n.themeKhaki;
      case D900ThemeType.rose:
        return l10n.themeRose;
      case D900ThemeType.blue:
        return l10n.themeBlue;
      case D900ThemeType.purple:
        return l10n.themePurple;
      case D900ThemeType.white:
        return l10n.themeWhite;
    }
  }

  static List<D900ThemeType> get options => D900ThemeType.values;

  static List<String> getLocalizedOptions(BuildContext context) {
    return options.map((type) => type.localized(context)).toList();
  }

  static List<MapEntry<D900ThemeType, String>> getLocalizedEntries(
      BuildContext context) {
    return options
        .map((type) => MapEntry(type, type.localized(context)))
        .toList();
  }
}
