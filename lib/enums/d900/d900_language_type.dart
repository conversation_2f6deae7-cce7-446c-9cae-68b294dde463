import 'package:flutter/cupertino.dart';
import 'package:hive_ce/hive.dart';
import 'package:topping_home/models/hive_adapters.dart';

import '../../l10n/app_localizations.dart';
import '../interfaces/convert.dart';
import '../interfaces/localizable.dart';

part 'd900_language_type.g.dart';

/// D900 语言枚举(中文, 英文)
@HiveType(typeId: HiveAdapterIds.d900LanguageTypeId)
enum D900LanguageType implements Localizable, Convert {
  // en
  @HiveField(0)
  en,
  // zh
  @HiveField(1)
  zh;

  @override
  String localized(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;
    switch (this) {
      case D900LanguageType.zh:
        return l10n.languageZh;
      case D900LanguageType.en:
        return l10n.languageEn;
    }
  }

  @override
  int get value => index;

  static D900LanguageType fromValue(int value) {
    return Convert.fromValue(D900LanguageType.values, value, D900LanguageType.zh);
  }
}

// D900LanguageType 扩展
extension D900LanguageTypeLocalization on D900LanguageType {
  String localized(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;
    switch (this) {
      case D900LanguageType.zh:
        return l10n.languageZh;
      case D900LanguageType.en:
        return l10n.languageEn;
    }
  }

  static List<D900LanguageType> get options => D900LanguageType.values;

  static List<String> getLocalizedOptions(BuildContext context) {
    return options.map((type) => type.localized(context)).toList();
  }

  static List<MapEntry<D900LanguageType, String>> getLocalizedEntries(
      BuildContext context) {
    return options
        .map((type) => MapEntry(type, type.localized(context)))
        .toList();
  }
}
