// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'd900_iis_phase_type.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class D900IisPhaseTypeAdapter extends TypeAdapter<D900IisPhaseType> {
  @override
  final int typeId = 205;

  @override
  D900IisPhaseType read(BinaryReader reader) {
    switch (reader.readByte()) {
      case 0:
        return D900IisPhaseType.standard;
      case 1:
        return D900IisPhaseType.inverted;
      default:
        return D900IisPhaseType.standard;
    }
  }

  @override
  void write(BinaryWriter writer, D900IisPhaseType obj) {
    switch (obj) {
      case D900IisPhaseType.standard:
        writer.writeByte(0);
      case D900IisPhaseType.inverted:
        writer.writeByte(1);
    }
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is D900IisPhaseTypeAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}
