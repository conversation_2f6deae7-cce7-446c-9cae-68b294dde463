// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'd900_multi_function_key_type.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class D900MultiFunctionKeyTypeAdapter
    extends TypeAdapter<D900MultiFunctionKeyType> {
  @override
  final int typeId = 211;

  @override
  D900MultiFunctionKeyType read(BinaryReader reader) {
    switch (reader.readByte()) {
      case 0:
        return D900MultiFunctionKeyType.inputSelect;
      case 1:
        return D900MultiFunctionKeyType.outputSelect;
      case 2:
        return D900MultiFunctionKeyType.homeSelect;
      case 3:
        return D900MultiFunctionKeyType.brightnessSelect;
      case 4:
        return D900MultiFunctionKeyType.screenOff;
      case 5:
        return D900MultiFunctionKeyType.peqSelect;
      case 6:
        return D900MultiFunctionKeyType.mute;
      default:
        return D900MultiFunctionKeyType.inputSelect;
    }
  }

  @override
  void write(BinaryWriter writer, D900MultiFunctionKeyType obj) {
    switch (obj) {
      case D900MultiFunctionKeyType.inputSelect:
        writer.writeByte(0);
      case D900MultiFunctionKeyType.outputSelect:
        writer.writeByte(1);
      case D900MultiFunctionKeyType.homeSelect:
        writer.writeByte(2);
      case D900MultiFunctionKeyType.brightnessSelect:
        writer.writeByte(3);
      case D900MultiFunctionKeyType.screenOff:
        writer.writeByte(4);
      case D900MultiFunctionKeyType.peqSelect:
        writer.writeByte(5);
      case D900MultiFunctionKeyType.mute:
        writer.writeByte(6);
    }
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is D900MultiFunctionKeyTypeAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}
