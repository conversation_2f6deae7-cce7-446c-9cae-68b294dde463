// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'd900_theme_type.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class D900ThemeTypeAdapter extends TypeAdapter<D900ThemeType> {
  @override
  final int typeId = 207;

  @override
  D900ThemeType read(BinaryReader reader) {
    switch (reader.readByte()) {
      case 0:
        return D900ThemeType.aurora;
      case 1:
        return D900ThemeType.orange;
      case 2:
        return D900ThemeType.peru;
      case 3:
        return D900ThemeType.green;
      case 4:
        return D900ThemeType.khaki;
      case 5:
        return D900ThemeType.rose;
      case 6:
        return D900ThemeType.blue;
      case 7:
        return D900ThemeType.purple;
      case 8:
        return D900ThemeType.white;
      default:
        return D900ThemeType.aurora;
    }
  }

  @override
  void write(BinaryWriter writer, D900ThemeType obj) {
    switch (obj) {
      case D900ThemeType.aurora:
        writer.writeByte(0);
      case D900ThemeType.orange:
        writer.writeByte(1);
      case D900ThemeType.peru:
        writer.writeByte(2);
      case D900ThemeType.green:
        writer.writeByte(3);
      case D900ThemeType.khaki:
        writer.writeByte(4);
      case D900ThemeType.rose:
        writer.writeByte(5);
      case D900ThemeType.blue:
        writer.writeByte(6);
      case D900ThemeType.purple:
        writer.writeByte(7);
      case D900ThemeType.white:
        writer.writeByte(8);
    }
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is D900ThemeTypeAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}
