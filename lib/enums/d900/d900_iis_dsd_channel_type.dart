import 'package:flutter/cupertino.dart';
import 'package:hive_ce/hive.dart';
import 'package:topping_home/models/hive_adapters.dart';

import '../../l10n/app_localizations.dart';
import '../interfaces/convert.dart';
import '../interfaces/localizable.dart';

part 'd900_iis_dsd_channel_type.g.dart';

/// D900 IIS DSD通道枚举(标准, 交换)
@HiveType(typeId: HiveAdapterIds.d900IisDsdChannelTypeId)
enum D900IisDsdChannelType implements Localizable, Convert {
  // 标准
  @HiveField(0)
  standard,
  // 交换
  @HiveField(1)
  swapped;

  @override
  String localized(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;
    switch (this) {
      case D900IisDsdChannelType.standard:
        return l10n.standard;
      case D900IisDsdChannelType.swapped:
        return l10n.swapped;
    }
  }

  @override
  int get value => index;

  static D900IisDsdChannelType fromValue(int value) {
    return Convert.fromValue(D900IisDsdChannelType.values, value, D900IisDsdChannelType.standard);
  }
}

// D900IisDsdChannelType 扩展
extension D900IisDsdChannelTypeLocalization on D900IisDsdChannelType {
  String localized(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;
    switch (this) {
      case D900IisDsdChannelType.standard:
        return l10n.standard;
      case D900IisDsdChannelType.swapped:
        return l10n.swapped;
    }
  }

  static List<D900IisDsdChannelType> get options => D900IisDsdChannelType.values;

  static List<String> getLocalizedOptions(BuildContext context) {
    return options.map((type) => type.localized(context)).toList();
  }

  static List<MapEntry<D900IisDsdChannelType, String>> getLocalizedEntries(
      BuildContext context) {
    return options
        .map((type) => MapEntry(type, type.localized(context)))
        .toList();
  }
}
