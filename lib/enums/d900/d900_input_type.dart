import 'package:flutter/cupertino.dart';
import 'package:hive_ce/hive.dart';
import 'package:topping_home/models/hive_adapters.dart';

import '../../l10n/app_localizations.dart';
import '../interfaces/convert.dart';
import '../interfaces/localizable.dart';

part 'd900_input_type.g.dart';

/// D900 输入枚举
@HiveType(typeId: HiveAdapterIds.d900InputTypeId)
enum D900InputType implements Localizable, Convert {
  // usb
  @HiveField(0)
  usb,
  // 光纤1
  @HiveField(1)
  optical1,
  // 光纤2
  @HiveField(2)
  optical2,
  // 同轴1
  @HiveField(3)
  coaxial1,
  // 同轴2
  @HiveField(4)
  coaxial2,
  // AES
  @HiveField(5)
  aes,
  // IIS
  @HiveField(6)
  iis,
  // 蓝牙
  @HiveField(7)
  bluetooth;

  @override
  String localized(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;
    switch (this) {
      case D900InputType.usb:
        return l10n.inputUsb;
      case D900InputType.optical1:
        return '${l10n.inputOptical}1';
      case D900InputType.optical2:
        return '${l10n.inputOptical}2';
      case D900InputType.coaxial1:
        return '${l10n.inputCoaxial}1';
      case D900InputType.coaxial2:
        return '${l10n.inputCoaxial}2';
      case D900InputType.aes:
        return 'AES';
      case D900InputType.iis:
        return 'IIS';
      case D900InputType.bluetooth:
        return l10n.inputBluetooth;
    }
  }

  @override
  int get value => index;

  static D900InputType fromValue(int value) {
    return Convert.fromValue(D900InputType.values, value, D900InputType.usb);
  }
}

// D900InputType 扩展
extension D900InputTypeLocalization on D900InputType {
  String localized(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;
    switch (this) {
      case D900InputType.usb:
        return l10n.inputUsb;
      case D900InputType.optical1:
        return '${l10n.inputOptical}1';
      case D900InputType.optical2:
        return '${l10n.inputOptical}2';
      case D900InputType.coaxial1:
        return '${l10n.inputCoaxial}1';
      case D900InputType.coaxial2:
        return '${l10n.inputCoaxial}2';
      case D900InputType.aes:
        return 'AES';
      case D900InputType.iis:
        return 'IIS';
      case D900InputType.bluetooth:
        return l10n.inputBluetooth;
    }
  }

  static List<D900InputType> get options => D900InputType.values;

  static List<String> getLocalizedOptions(BuildContext context) {
    return options.map((type) => type.localized(context)).toList();
  }

  static List<MapEntry<D900InputType, String>> getLocalizedEntries(
      BuildContext context) {
    return options
        .map((type) => MapEntry(type, type.localized(context)))
        .toList();
  }
}
