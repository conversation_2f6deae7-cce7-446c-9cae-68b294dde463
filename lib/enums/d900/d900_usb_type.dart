import 'package:flutter/cupertino.dart';
import 'package:hive_ce/hive.dart';
import 'package:topping_home/models/hive_adapters.dart';

import '../../l10n/app_localizations.dart';
import '../interfaces/convert.dart';
import '../interfaces/localizable.dart';

part 'd900_usb_type.g.dart';

/// D900 USB类型枚举(UAC2.0, UAC1.0)
@HiveType(typeId: HiveAdapterIds.d900UsbTypeId)
enum D900UsbType implements Localizable, Convert {
  // UAC1.0
  @HiveField(0)
  uac1,
  // UAC2.0
  @HiveField(1)
  uac2;

  @override
  String localized(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;
    switch (this) {
      case D900UsbType.uac1:
        return l10n.usbTypeUac1;
      case D900UsbType.uac2:
        return l10n.usbTypeUac2;
    }
  }

  @override
  int get value => index;

  static D900UsbType fromValue(int value) {
    return Convert.fromValue(D900UsbType.values, value, D900UsbType.uac2);
  }
}

// D900UsbType 扩展
extension D900UsbTypeLocalization on D900UsbType {
  String localized(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;
    switch (this) {
      case D900UsbType.uac1:
        return l10n.usbTypeUac1;
      case D900UsbType.uac2:
        return l10n.usbTypeUac2;
    }
  }

  static List<D900UsbType> get options => D900UsbType.values;

  static List<String> getLocalizedOptions(BuildContext context) {
    return options.map((type) => type.localized(context)).toList();
  }

  static List<MapEntry<D900UsbType, String>> getLocalizedEntries(
      BuildContext context) {
    return options
        .map((type) => MapEntry(type, type.localized(context)))
        .toList();
  }
}
