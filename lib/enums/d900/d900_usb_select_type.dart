import 'package:flutter/cupertino.dart';
import 'package:hive_ce/hive.dart';
import 'package:topping_home/models/hive_adapters.dart';

import '../../l10n/app_localizations.dart';
import '../interfaces/convert.dart';
import '../interfaces/localizable.dart';

part 'd900_usb_select_type.g.dart';

/// D900 USB选择枚举(Type-C, Type-B, 自动)
@HiveType(typeId: HiveAdapterIds.d900UsbSelectTypeId)
enum D900UsbSelectType implements Localizable, Convert {
  // Type-C
  @HiveField(0)
  typeB,
  // Type-B
  @HiveField(1)
  typeC,
  // 自动
  @HiveField(2)
  auto;

  @override
  String localized(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;
    switch (this) {
      case D900UsbSelectType.typeC:
        return 'Type-C';
      case D900UsbSelectType.typeB:
        return 'Type-B';
      case D900UsbSelectType.auto:
        return l10n.auto;
    }
  }

  @override
  int get value => index;

  static D900UsbSelectType fromValue(int value) {
    return Convert.fromValue(D900UsbSelectType.values, value, D900UsbSelectType.auto);
  }
}

// D900UsbSelectType 扩展
extension D900UsbSelectTypeLocalization on D900UsbSelectType {
  String localized(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;
    switch (this) {
      case D900UsbSelectType.typeC:
        return 'Type-C';
      case D900UsbSelectType.typeB:
        return 'Type-B';
      case D900UsbSelectType.auto:
        return l10n.auto;
    }
  }

  static List<D900UsbSelectType> get options => D900UsbSelectType.values;

  static List<String> getLocalizedOptions(BuildContext context) {
    return options.map((type) => type.localized(context)).toList();
  }

  static List<MapEntry<D900UsbSelectType, String>> getLocalizedEntries(
      BuildContext context) {
    return options
        .map((type) => MapEntry(type, type.localized(context)))
        .toList();
  }
}
