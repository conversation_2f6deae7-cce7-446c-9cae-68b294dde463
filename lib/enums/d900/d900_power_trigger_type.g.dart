// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'd900_power_trigger_type.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class D900PowerTriggerTypeAdapter extends TypeAdapter<D900PowerTriggerType> {
  @override
  final int typeId = 208;

  @override
  D900PowerTriggerType read(BinaryReader reader) {
    switch (reader.readByte()) {
      case 0:
        return D900PowerTriggerType.signal;
      case 1:
        return D900PowerTriggerType.voltage12V;
      case 2:
        return D900PowerTriggerType.off;
      default:
        return D900PowerTriggerType.signal;
    }
  }

  @override
  void write(BinaryWriter writer, D900PowerTriggerType obj) {
    switch (obj) {
      case D900PowerTriggerType.signal:
        writer.writeByte(0);
      case D900PowerTriggerType.voltage12V:
        writer.writeByte(1);
      case D900PowerTriggerType.off:
        writer.writeByte(2);
    }
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is D900PowerTriggerTypeAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}
