import 'package:flutter/cupertino.dart';
import 'package:hive_ce/hive.dart';
import 'package:topping_home/models/hive_adapters.dart';

import '../../l10n/app_localizations.dart';
import '../interfaces/convert.dart';
import '../interfaces/localizable.dart';

part 'd900_power_trigger_type.g.dart';

/// D900 开关机触发枚举(信号, 12V, 关闭)
@HiveType(typeId: HiveAdapterIds.d900PowerTriggerTypeId)
enum D900PowerTriggerType implements Localizable, Convert {
  // 信号
  @HiveField(0)
  signal,
  // 12V
  @HiveField(1)
  voltage12V,
  // 关闭
  @HiveField(2)
  off;

  @override
  String localized(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;
    switch (this) {
      case D900PowerTriggerType.signal:
        return l10n.powerTriggerSignal;
      case D900PowerTriggerType.voltage12V:
        return l10n.powerTrigger12V;
      case D900PowerTriggerType.off:
        return l10n.powerTriggerOff;
    }
  }

  @override
  int get value => index;

  static D900PowerTriggerType fromValue(int value) {
    return Convert.fromValue(D900PowerTriggerType.values, value, D900PowerTriggerType.signal);
  }
}

// D900PowerTriggerType 扩展
extension D900PowerTriggerTypeLocalization on D900PowerTriggerType {
  String localized(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;
    switch (this) {
      case D900PowerTriggerType.signal:
        return l10n.powerTriggerSignal;
      case D900PowerTriggerType.voltage12V:
        return l10n.powerTrigger12V;
      case D900PowerTriggerType.off:
        return l10n.powerTriggerOff;
    }
  }

  static List<D900PowerTriggerType> get options => D900PowerTriggerType.values;

  static List<String> getLocalizedOptions(BuildContext context) {
    return options.map((type) => type.localized(context)).toList();
  }

  static List<MapEntry<D900PowerTriggerType, String>> getLocalizedEntries(
      BuildContext context) {
    return options
        .map((type) => MapEntry(type, type.localized(context)))
        .toList();
  }
}
