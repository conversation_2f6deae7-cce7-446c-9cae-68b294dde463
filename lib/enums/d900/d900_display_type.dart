import 'package:flutter/cupertino.dart';
import 'package:hive_ce/hive.dart';
import 'package:topping_home/models/hive_adapters.dart';

import '../../l10n/app_localizations.dart';
import '../interfaces/convert.dart';
import '../interfaces/localizable.dart';

part 'd900_display_type.g.dart';

/// D900 显示界面枚举(VU, FFT, 常规)
@HiveType(typeId: HiveAdapterIds.d900DisplayTypeId)
enum D900DisplayType implements Localizable, Convert {
  // 正常
  @HiveField(0)
  normal,
  // VU
  @HiveField(1)
  vu,
  // FFT
  @HiveField(2)
  fft;

  @override
  String localized(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;
    switch (this) {
      case D900DisplayType.vu:
        return l10n.displayVu;
      case D900DisplayType.fft:
        return l10n.displayFft;
      case D900DisplayType.normal:
        return l10n.displayNormal;
    }
  }

  @override
  int get value => index;

  static D900DisplayType fromValue(int value) {
    return Convert.fromValue(D900DisplayType.values, value, D900DisplayType.vu);
  }
}

// D900DisplayType 扩展
extension D900DisplayTypeLocalization on D900DisplayType {
  String localized(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;
    switch (this) {
      case D900DisplayType.vu:
        return l10n.displayVu;
      case D900DisplayType.fft:
        return l10n.displayFft;
      case D900DisplayType.normal:
        return l10n.displayNormal;
    }
  }

  static List<D900DisplayType> get options => D900DisplayType.values;

  static List<String> getLocalizedOptions(BuildContext context) {
    return options.map((type) => type.localized(context)).toList();
  }

  static List<MapEntry<D900DisplayType, String>> getLocalizedEntries(
      BuildContext context) {
    return options
        .map((type) => MapEntry(type, type.localized(context)))
        .toList();
  }
}
