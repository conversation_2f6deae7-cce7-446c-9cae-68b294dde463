// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'd900_input_type.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class D900InputTypeAdapter extends TypeAdapter<D900InputType> {
  @override
  final int typeId = 200;

  @override
  D900InputType read(BinaryReader reader) {
    switch (reader.readByte()) {
      case 0:
        return D900InputType.usb;
      case 1:
        return D900InputType.optical1;
      case 2:
        return D900InputType.optical2;
      case 3:
        return D900InputType.coaxial1;
      case 4:
        return D900InputType.coaxial2;
      case 5:
        return D900InputType.aes;
      case 6:
        return D900InputType.iis;
      case 7:
        return D900InputType.bluetooth;
      default:
        return D900InputType.usb;
    }
  }

  @override
  void write(BinaryWriter writer, D900InputType obj) {
    switch (obj) {
      case D900InputType.usb:
        writer.writeByte(0);
      case D900InputType.optical1:
        writer.writeByte(1);
      case D900InputType.optical2:
        writer.writeByte(2);
      case D900InputType.coaxial1:
        writer.writeByte(3);
      case D900InputType.coaxial2:
        writer.writeByte(4);
      case D900InputType.aes:
        writer.writeByte(5);
      case D900InputType.iis:
        writer.writeByte(6);
      case D900InputType.bluetooth:
        writer.writeByte(7);
    }
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is D900InputTypeAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}
