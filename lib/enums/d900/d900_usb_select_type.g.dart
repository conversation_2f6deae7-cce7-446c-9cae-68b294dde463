// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'd900_usb_select_type.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class D900UsbSelectTypeAdapter extends TypeAdapter<D900UsbSelectType> {
  @override
  final int typeId = 202;

  @override
  D900UsbSelectType read(BinaryReader reader) {
    switch (reader.readByte()) {
      case 0:
        return D900UsbSelectType.typeB;
      case 1:
        return D900UsbSelectType.typeC;
      case 2:
        return D900UsbSelectType.auto;
      default:
        return D900UsbSelectType.typeB;
    }
  }

  @override
  void write(BinaryWriter writer, D900UsbSelectType obj) {
    switch (obj) {
      case D900UsbSelectType.typeB:
        writer.writeByte(0);
      case D900UsbSelectType.typeC:
        writer.writeByte(1);
      case D900UsbSelectType.auto:
        writer.writeByte(2);
    }
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is D900UsbSelectTypeAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}
