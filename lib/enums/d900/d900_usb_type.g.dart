// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'd900_usb_type.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class D900UsbTypeAdapter extends TypeAdapter<D900UsbType> {
  @override
  final int typeId = 203;

  @override
  D900UsbType read(BinaryReader reader) {
    switch (reader.readByte()) {
      case 0:
        return D900UsbType.uac1;
      case 1:
        return D900UsbType.uac2;
      default:
        return D900UsbType.uac1;
    }
  }

  @override
  void write(BinaryWriter writer, D900UsbType obj) {
    switch (obj) {
      case D900UsbType.uac1:
        writer.writeByte(0);
      case D900UsbType.uac2:
        writer.writeByte(1);
    }
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is D900UsbTypeAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}
