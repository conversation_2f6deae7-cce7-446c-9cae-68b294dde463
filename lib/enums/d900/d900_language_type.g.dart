// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'd900_language_type.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class D900LanguageTypeAdapter extends TypeAdapter<D900LanguageType> {
  @override
  final int typeId = 210;

  @override
  D900LanguageType read(BinaryReader reader) {
    switch (reader.readByte()) {
      case 0:
        return D900LanguageType.en;
      case 1:
        return D900LanguageType.zh;
      default:
        return D900LanguageType.en;
    }
  }

  @override
  void write(BinaryWriter writer, D900LanguageType obj) {
    switch (obj) {
      case D900LanguageType.en:
        writer.writeByte(0);
      case D900LanguageType.zh:
        writer.writeByte(1);
    }
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is D900LanguageTypeAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}
