/// GENERATED CODE - DO NOT MODIFY BY HAND
/// *****************************************************
///  FlutterGen
/// *****************************************************

// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: directives_ordering,unnecessary_import,implicit_dynamic_list_literal,deprecated_member_use

import 'package:flutter/widgets.dart';

class $AssetsIconGen {
  const $AssetsIconGen();

  /// File path: assets/icon/icon.png
  AssetGenImage get icon => const AssetGenImage('assets/icon/icon.png');

  /// File path: assets/icon/icon_small.png
  AssetGenImage get iconSmall =>
      const AssetGenImage('assets/icon/icon_small.png');

  /// List of all assets
  List<AssetGenImage> get values => [icon, iconSmall];
}

class $AssetsImageGen {
  const $AssetsImageGen();

  /// File path: assets/image/ic_dx5ii.png
  AssetGenImage get icDx5ii => const AssetGenImage('assets/image/ic_dx5ii.png');

  /// File path: assets/image/view_guide_1.png
  AssetGenImage get viewGuide1 =>
      const AssetGenImage('assets/image/view_guide_1.png');

  /// File path: assets/image/view_guide_2.png
  AssetGenImage get viewGuide2 =>
      const AssetGenImage('assets/image/view_guide_2.png');

  /// File path: assets/image/view_guide_3.png
  AssetGenImage get viewGuide3 =>
      const AssetGenImage('assets/image/view_guide_3.png');

  /// File path: assets/image/view_guide_4.png
  AssetGenImage get viewGuide4 =>
      const AssetGenImage('assets/image/view_guide_4.png');

  /// File path: assets/image/view_guide_5.png
  AssetGenImage get viewGuide5 =>
      const AssetGenImage('assets/image/view_guide_5.png');

  /// List of all assets
  List<AssetGenImage> get values => [
    icDx5ii,
    viewGuide1,
    viewGuide2,
    viewGuide3,
    viewGuide4,
    viewGuide5,
  ];
}

class $AssetsLegalGen {
  const $AssetsLegalGen();

  /// File path: assets/legal/agreement_en.json
  String get agreementEn => 'assets/legal/agreement_en.json';

  /// File path: assets/legal/agreement_ja.json
  String get agreementJa => 'assets/legal/agreement_ja.json';

  /// File path: assets/legal/agreement_ko.json
  String get agreementKo => 'assets/legal/agreement_ko.json';

  /// File path: assets/legal/agreement_zh.json
  String get agreementZh => 'assets/legal/agreement_zh.json';

  /// File path: assets/legal/agreement_zh_TW.json
  String get agreementZhTW => 'assets/legal/agreement_zh_TW.json';

  /// File path: assets/legal/privacy_en.json
  String get privacyEn => 'assets/legal/privacy_en.json';

  /// File path: assets/legal/privacy_ja.json
  String get privacyJa => 'assets/legal/privacy_ja.json';

  /// File path: assets/legal/privacy_ko.json
  String get privacyKo => 'assets/legal/privacy_ko.json';

  /// File path: assets/legal/privacy_zh.json
  String get privacyZh => 'assets/legal/privacy_zh.json';

  /// File path: assets/legal/privacy_zh_TW.json
  String get privacyZhTW => 'assets/legal/privacy_zh_TW.json';

  /// List of all assets
  List<String> get values => [
    agreementEn,
    agreementJa,
    agreementKo,
    agreementZh,
    agreementZhTW,
    privacyEn,
    privacyJa,
    privacyKo,
    privacyZh,
    privacyZhTW,
  ];
}

class $AssetsLottieGen {
  const $AssetsLottieGen();

  /// File path: assets/lottie/view_loading.json
  String get viewLoading => 'assets/lottie/view_loading.json';

  /// List of all assets
  List<String> get values => [viewLoading];
}

class $AssetsPdfsGen {
  const $AssetsPdfsGen();

  /// File path: assets/pdfs/operation_guide.pdf
  String get operationGuide => 'assets/pdfs/operation_guide.pdf';

  /// List of all assets
  List<String> get values => [operationGuide];
}

class Assets {
  const Assets._();

  static const $AssetsIconGen icon = $AssetsIconGen();
  static const $AssetsImageGen image = $AssetsImageGen();
  static const $AssetsLegalGen legal = $AssetsLegalGen();
  static const $AssetsLottieGen lottie = $AssetsLottieGen();
  static const $AssetsPdfsGen pdfs = $AssetsPdfsGen();
}

class AssetGenImage {
  const AssetGenImage(this._assetName, {this.size, this.flavors = const {}});

  final String _assetName;

  final Size? size;
  final Set<String> flavors;

  Image image({
    Key? key,
    AssetBundle? bundle,
    ImageFrameBuilder? frameBuilder,
    ImageErrorWidgetBuilder? errorBuilder,
    String? semanticLabel,
    bool excludeFromSemantics = false,
    double? scale,
    double? width,
    double? height,
    Color? color,
    Animation<double>? opacity,
    BlendMode? colorBlendMode,
    BoxFit? fit,
    AlignmentGeometry alignment = Alignment.center,
    ImageRepeat repeat = ImageRepeat.noRepeat,
    Rect? centerSlice,
    bool matchTextDirection = false,
    bool gaplessPlayback = true,
    bool isAntiAlias = false,
    String? package,
    FilterQuality filterQuality = FilterQuality.medium,
    int? cacheWidth,
    int? cacheHeight,
  }) {
    return Image.asset(
      _assetName,
      key: key,
      bundle: bundle,
      frameBuilder: frameBuilder,
      errorBuilder: errorBuilder,
      semanticLabel: semanticLabel,
      excludeFromSemantics: excludeFromSemantics,
      scale: scale,
      width: width,
      height: height,
      color: color,
      opacity: opacity,
      colorBlendMode: colorBlendMode,
      fit: fit,
      alignment: alignment,
      repeat: repeat,
      centerSlice: centerSlice,
      matchTextDirection: matchTextDirection,
      gaplessPlayback: gaplessPlayback,
      isAntiAlias: isAntiAlias,
      package: package,
      filterQuality: filterQuality,
      cacheWidth: cacheWidth,
      cacheHeight: cacheHeight,
    );
  }

  ImageProvider provider({AssetBundle? bundle, String? package}) {
    return AssetImage(_assetName, bundle: bundle, package: package);
  }

  String get path => _assetName;

  String get keyName => _assetName;
}
