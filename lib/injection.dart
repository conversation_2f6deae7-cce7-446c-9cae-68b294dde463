import 'package:get/get.dart';
import 'package:hive_ce_flutter/hive_flutter.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:topping_ble_control/service/ota_server.dart';
import 'package:topping_home/models/hive_registrar.dart';
import 'package:topping_home/theme/color_palettes.dart';

import 'common/util/flavor_config.dart';
import 'common/util/log_util.dart';
import 'environment_config.dart';
import 'http/api/api_config.dart';
import 'http/api/api_service.dart';
import 'http/dio_client.dart';
import 'repositories/background_repository.dart';
import 'repositories/device_repository.dart';
import 'service/firmware_service.dart';
import 'service/update_service.dart';

///初始化注入对象
class Injection extends GetxService {
  Future<void> init() async {
    // ===== 第一部分: 基础配置初始化 =====
    // 初始化环境配置
    await EnvironmentConfig().init();

    // 初始化Flavor配置
    await FlavorConfig().init();

    // 初始化SharedPreferences
    await Get.putAsync(() => SharedPreferences.getInstance());

    // 初始化颜色主题
    ColorPalettes.instance.init();

    // ===== 第二部分: 数据存储初始化 =====
    // 初始化Hive
    await Hive.initFlutter();

    // 注册Hive适配器
    Log.i('开始注册 Hive 适配器');
    HiveRegistrar.registerAdapters();

    // 验证关键适配器是否注册成功
    bool adaptersVerified = await HiveRegistrar.verifyKeyAdapters();
    Log.i('关键 Hive 适配器验证结果: $adaptersVerified');

    if (!adaptersVerified) {
      Log.w('关键 Hive 适配器验证失败，尝试重新注册');
      // 尝试再次注册
      HiveRegistrar.registerAdapters();

      // 再次验证
      adaptersVerified = await HiveRegistrar.verifyKeyAdapters();
      Log.i('重新注册后的适配器验证结果: $adaptersVerified');

      if (!adaptersVerified) {
        Log.e('即使重新注册后，适配器仍然验证失败，这可能会导致数据存储问题');
      }
    }

    // 初始化设备存储
    final deviceStorage = DeviceRepository();
    await deviceStorage.init();
    Get.put(deviceStorage, permanent: true);

    // 初始化背景存储
    Get.put(BackgroundRepository());

    // ===== 第三部分: 网络服务初始化 =====
    // 初始化DioClient
    final baseUrl = ApiConfig().getBaseUrl();
    Log.i('初始化API服务，使用基础URL: $baseUrl');
    DioClient().init(baseUrl: baseUrl);

    // 添加API服务
    Get.put(ApiService(baseUrl: baseUrl));

    // ===== 第四部分: 功能服务初始化 =====
    // 添加更新服务
    Get.put(UpdateService());

    // 添加固件服务
    Get.put(FirmwareService());

    // 添加OTA服务
    Get.put(OtaServer());

    Log.i('依赖注入初始化完成');
  }

}
