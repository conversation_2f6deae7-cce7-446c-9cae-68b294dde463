import 'dart:ffi';
import 'dart:io';

import 'package:ffi/ffi.dart';
import '../../model/d900/d900_callback.dart';
import '../../model/ffi/ffi_d900_device_callback.dart';
import '../../model/ffi/ffi_dx5ii_scan_result.dart';
import '../../model/ffi/ffi_d900_settings.dart';
import '../../utils/log_util.dart';

/// D900设备绑定类
class D900DeviceBindings {
  static const String tag = "D900DeviceBindings";

  /// 单例实例
  static D900DeviceBindings? _instance;

  /// 获取单例实例
  static D900DeviceBindings get instance {
    _instance ??= D900DeviceBindings._();
    return _instance!;
  }

  late final DynamicLibrary _dylib;

  /// 原生函数指针
  late final int Function(int) _d900DeviceCreate;
  late final void Function(int) _d900DeviceDestroy;
  late final void Function(int, Pointer<FFID900DeviceCallback>)
  _d900DeviceRegisterCallback;
  late final void Function(int, int) _d900DeviceConnect;
  late final void Function(int) _d900DeviceDisconnect;
  late final void Function(int) _d900DeviceVerify;
  late final void Function(int, int) _d900DevicePowerOn;
  late final void Function(int, int) _d900DeviceSetVolume;
  late final void Function(int, int) _d900DeviceSetMute;
  late final void Function(int, int) _d900DeviceSetInputType;
  late final void Function(int, int) _d900DeviceSetOutputType;
  late final void Function(int, int) _d900DeviceSetDisplayMode;
  late final void Function(int, int) _d900DeviceSetTheme;
  late final void Function(int, int) _d900DeviceSetPowerTrigger;
  late final void Function(int, int) _d900DeviceSetBalance;
  late final void Function(int, int) _d900DeviceEnableAudioBluetooth;
  late final void Function(int, int) _d900DeviceEnableBluetoothAptx;
  late final void Function(int, int) _d900DeviceEnableRelay;
  late final void Function(int, int) _d900DeviceSetMultifunctionKey;
  late final void Function(int, int) _d900DeviceSetUsbMode;
  late final void Function(int, int) _d900DeviceSetScreenBrightness;
  late final void Function(int, int) _d900DeviceSetLanguage;
  late final void Function(int) _d900DeviceRestoreFactorySettings;
  late final void Function(int) _d900DeviceRequestSettings;

  // D900特有功能
  late final void Function(int, int) _d900DeviceSetUsbSelect;
  late final void Function(int, int) _d900DeviceEnableUsbDsd;
  late final void Function(int, int) _d900DeviceSetIisPhase;
  late final void Function(int, int) _d900DeviceSetIisChannel;

  // --- 新增函数指针 (D900新版功能) ---
  late final void Function(int, int) _d900DeviceSetVuMeterLevel;
  late final void Function(int, int) _d900DeviceSetVuMeterDisplayMode;
  late final void Function(int, int) _d900DeviceSetInputOptions;
  late final void Function(int, int) _d900DeviceSetOutputOptions;
  late final void Function(int, int) _d900DeviceSetUsbPortSelect;
  late final void Function(int, int) _d900DeviceSetDsdMute;
  late final void Function(int, int) _d900DeviceSetOutputLevel;
  late final void Function(int, int) _d900DeviceSetVolumeStep;
  late final void Function(int, int) _d900DeviceSetPolarity;
  late final void Function(int, int) _d900DeviceEnableDsdDirect;
  late final void Function(int, int) _d900DeviceSetVolumeMemoryMode;
  late final void Function(int, int) _d900DeviceSetPeqMemoryMode;
  late final void Function(int, int) _d900DeviceSetMainKeyFunction;
  late final void Function(int, int) _d900DeviceSetRemoteAKeyFunction;
  late final void Function(int, int) _d900DeviceSetRemoteBKeyFunction;

  /// 回调实例
  D900Callback? _callback;

  /// 原生对象指针
  int? _nativeObject;

  /// Flutter对象ID
  int? _flutterObjectId;

  /// 私有构造函数
  D900DeviceBindings._() {
    _dylib = _nativeLib;
    _loadFunctions();
  }

  /// 加载适合当前平台的动态库
  final DynamicLibrary _nativeLib =
      Platform.isAndroid
          ? DynamicLibrary.open("libtopping_controller.so")
          : DynamicLibrary.process();

  /// 加载原生函数
  void _loadFunctions() {
    _d900DeviceCreate =
        _dylib
            .lookup<NativeFunction<Int64 Function(Int64)>>('d900_device_create')
            .asFunction();
    _d900DeviceDestroy =
        _dylib
            .lookup<NativeFunction<Void Function(Int64)>>('d900_device_destory')
            .asFunction();
    _d900DeviceRegisterCallback =
        _dylib
            .lookup<
              NativeFunction<
                Void Function(Int64, Pointer<FFID900DeviceCallback>)
              >
            >('d900_device_register_callback')
            .asFunction();
    _d900DeviceConnect =
        _dylib
            .lookup<NativeFunction<Void Function(Int64, Int64)>>(
              'd900_device_connect',
            )
            .asFunction();
    _d900DeviceDisconnect =
        _dylib
            .lookup<NativeFunction<Void Function(Int64)>>(
              'd900_device_disconnect',
            )
            .asFunction();
    _d900DeviceVerify =
        _dylib
            .lookup<NativeFunction<Void Function(Int64)>>('d900_device_verify')
            .asFunction();
    _d900DevicePowerOn =
        _dylib
            .lookup<NativeFunction<Void Function(Int64, Int32)>>(
              'd900_device_power_on',
            )
            .asFunction();
    _d900DeviceSetVolume =
        _dylib
            .lookup<NativeFunction<Void Function(Int64, Int32)>>(
              'd900_device_set_volume',
            )
            .asFunction();
    _d900DeviceSetMute =
        _dylib
            .lookup<NativeFunction<Void Function(Int64, Int32)>>(
              'd900_device_set_mute',
            )
            .asFunction();
    _d900DeviceSetInputType =
        _dylib
            .lookup<NativeFunction<Void Function(Int64, Int32)>>(
              'd900_device_set_input_type',
            )
            .asFunction();
    _d900DeviceSetOutputType =
        _dylib
            .lookup<NativeFunction<Void Function(Int64, Int32)>>(
              'd900_device_set_output_type',
            )
            .asFunction();
    _d900DeviceSetDisplayMode =
        _dylib
            .lookup<NativeFunction<Void Function(Int64, Int32)>>(
              'd900_device_set_diaplay_mode',
            )
            .asFunction();
    _d900DeviceSetTheme =
        _dylib
            .lookup<NativeFunction<Void Function(Int64, Int32)>>(
              'd900_device_set_theme',
            )
            .asFunction();
    _d900DeviceSetPowerTrigger =
        _dylib
            .lookup<NativeFunction<Void Function(Int64, Int32)>>(
              'd900_device_set_power_trigger',
            )
            .asFunction();
    _d900DeviceSetBalance =
        _dylib
            .lookup<NativeFunction<Void Function(Int64, Int32)>>(
              'd900_device_set_balance',
            )
            .asFunction();
    _d900DeviceEnableAudioBluetooth =
        _dylib
            .lookup<NativeFunction<Void Function(Int64, Int32)>>(
              'd900_device_enable_audio_bluetooth',
            )
            .asFunction();
    _d900DeviceEnableBluetoothAptx =
        _dylib
            .lookup<NativeFunction<Void Function(Int64, Int32)>>(
              'd900_device_enable_bluetooth_aptx',
            )
            .asFunction();
    _d900DeviceEnableRelay =
        _dylib
            .lookup<NativeFunction<Void Function(Int64, Int32)>>(
              'd900_device_enable_remote',
            )
            .asFunction();
    _d900DeviceSetMultifunctionKey =
        _dylib
            .lookup<NativeFunction<Void Function(Int64, Int32)>>(
              'd900_device_set_multifunction_key',
            )
            .asFunction();
    _d900DeviceSetUsbMode =
        _dylib
            .lookup<NativeFunction<Void Function(Int64, Int32)>>(
              'd900_device_set_usb_mode',
            )
            .asFunction();
    _d900DeviceSetScreenBrightness =
        _dylib
            .lookup<NativeFunction<Void Function(Int64, Int32)>>(
              'd900_device_set_screen_brightness',
            )
            .asFunction();
    _d900DeviceSetLanguage =
        _dylib
            .lookup<NativeFunction<Void Function(Int64, Int32)>>(
              'd900_device_set_language',
            )
            .asFunction();
    _d900DeviceRestoreFactorySettings =
        _dylib
            .lookup<NativeFunction<Void Function(Int64)>>(
              'd900_device_restore_factory_settings',
            )
            .asFunction();
    _d900DeviceRequestSettings =
        _dylib
            .lookup<NativeFunction<Void Function(Int64)>>(
              'd900_device_request_settings',
            )
            .asFunction();

    // 添加D900特有的功能
    _d900DeviceSetUsbSelect =
        _dylib
            .lookup<NativeFunction<Void Function(Int64, Int32)>>(
              'd900_device_set_usb_select',
            )
            .asFunction();

    _d900DeviceEnableUsbDsd =
        _dylib
            .lookup<NativeFunction<Void Function(Int64, Int32)>>(
              'd900_device_enebla_usb_dsd',
            )
            .asFunction();

    _d900DeviceSetIisPhase =
        _dylib
            .lookup<NativeFunction<Void Function(Int64, Int32)>>(
              'd900_device_set_iis_phase',
            )
            .asFunction();

    _d900DeviceSetIisChannel =
        _dylib
            .lookup<NativeFunction<Void Function(Int64, Int32)>>(
              'd900_device_set_iis_channel',
            )
            .asFunction();

    // --- 加载新增函数 (D900新版功能) ---
    _d900DeviceSetVuMeterLevel =
        _dylib
            .lookup<NativeFunction<Void Function(Int64, Int32)>>(
              'd900_device_set_vu_meter_level',
            )
            .asFunction();
    _d900DeviceSetVuMeterDisplayMode =
        _dylib
            .lookup<NativeFunction<Void Function(Int64, Int32)>>(
              'd900_device_set_vu_meter_display_mode',
            )
            .asFunction();
    _d900DeviceSetInputOptions =
        _dylib
            .lookup<NativeFunction<Void Function(Int64, Int32)>>(
              'd900_device_set_input_options',
            )
            .asFunction();
    _d900DeviceSetOutputOptions =
        _dylib
            .lookup<NativeFunction<Void Function(Int64, Int32)>>(
              'd900_device_set_output_options',
            )
            .asFunction();
    _d900DeviceSetUsbPortSelect =
        _dylib
            .lookup<NativeFunction<Void Function(Int64, Int32)>>(
              'd900_device_set_usb_port_select',
            )
            .asFunction();
    _d900DeviceSetDsdMute =
        _dylib
            .lookup<NativeFunction<Void Function(Int64, Int32)>>(
              'd900_device_set_dsd_mute',
            )
            .asFunction();
    _d900DeviceSetOutputLevel =
        _dylib
            .lookup<NativeFunction<Void Function(Int64, Int32)>>(
              'd900_device_set_output_level',
            )
            .asFunction();
    _d900DeviceSetVolumeStep =
        _dylib
            .lookup<NativeFunction<Void Function(Int64, Int32)>>(
              'd900_device_set_volume_step',
            )
            .asFunction();
    _d900DeviceSetPolarity =
        _dylib
            .lookup<NativeFunction<Void Function(Int64, Int32)>>(
              'd900_device_set_polarity',
            )
            .asFunction();
    _d900DeviceEnableDsdDirect =
        _dylib
            .lookup<NativeFunction<Void Function(Int64, Int32)>>(
              'd900_device_enable_dsd_direct',
            )
            .asFunction();
    _d900DeviceSetVolumeMemoryMode =
        _dylib
            .lookup<NativeFunction<Void Function(Int64, Int32)>>(
              'd900_device_set_volume_memory_mode',
            )
            .asFunction();
    _d900DeviceSetPeqMemoryMode =
        _dylib
            .lookup<NativeFunction<Void Function(Int64, Int32)>>(
              'd900_device_set_peq_memory_mode',
            )
            .asFunction();
    _d900DeviceSetMainKeyFunction =
        _dylib
            .lookup<NativeFunction<Void Function(Int64, Int32)>>(
              'd900_device_set_main_key_function',
            )
            .asFunction();
    _d900DeviceSetRemoteAKeyFunction =
        _dylib
            .lookup<NativeFunction<Void Function(Int64, Int32)>>(
              'd900_device_set_remote_a_key_function',
            )
            .asFunction();
    _d900DeviceSetRemoteBKeyFunction =
        _dylib
            .lookup<NativeFunction<Void Function(Int64, Int32)>>(
              'd900_device_set_remote_b_key_function',
            )
            .asFunction();
  }

  /// C回调函数实现
  static void _onScanResults(
    int flutterObject,
    Pointer<FFIDx5iiScanResult> results,
    int count,
  ) {
    Log.i(
      "Flutter回调: onScanResults, flutterObject: $flutterObject, count: $count",
    );
    final instance = D900DeviceBindings.instance;
    if (instance._callback == null) return;

    final scanResults = <dynamic>[];
    for (var i = 0; i < count; i++) {
      final result = results[i];
      final name = result.name.cast<Utf8>().toDartString();
      final deviceHandle = result.device;
      final rssi = result.rssi;
      scanResults.add(
        _ScanResultWrapper(name: name, device: deviceHandle, rssi: rssi),
      );
      Log.i("添加扫描结果: name=$name, device=$deviceHandle, rssi=$rssi");
    }
    instance._callback!.onScanResults(scanResults);
  }

  static void _onScanFailed(int flutterObject, int errorCode) {
    final instance = D900DeviceBindings.instance;
    if (instance._callback != null) {
      instance._callback!.onScanFailed(errorCode);
    }
  }

  static void _onStateChange(int flutterObject, int state) {
    Log.i(
      "Flutter回调: onStateChange, flutterObject: $flutterObject, state: $state",
    );
    final instance = D900DeviceBindings.instance;
    if (instance._callback != null) {
      instance._callback!.onStateChange(state);
    }
  }

  static void _onVerifyResult(int flutterObject, int type) {
    Log.i("Flutter回调: onVerifyResult, type: $type");
    final instance = D900DeviceBindings.instance;
    if (instance._callback != null) {
      instance._callback!.onVerifyResult(type);
    }
  }

  static void _onPowerChange(int flutterObject, int isOn) {
    final instance = D900DeviceBindings.instance;
    if (instance._callback != null) {
      instance._callback!.onPowerChange(isOn != 0);
    }
  }

  static void _onDeviceVolumeChange(int flutterObject, int volume) {
    Log.i("Flutter回调: onDeviceVolumeChange, volume: $volume");
    final instance = D900DeviceBindings.instance;
    if (instance._callback != null) {
      instance._callback!.onVolumeChange(volume);
    }
  }

  static void _onDeviceMuteChange(int flutterObject, int isMute) {
    Log.i("Flutter回调: onDeviceMuteChange, isMute: $isMute");
    final instance = D900DeviceBindings.instance;
    if (instance._callback != null) {
      instance._callback!.onMuteChange(isMute != 0);
    }
  }

  // 其他回调函数类似，暂未在 Dx5iiCallback 中定义，可根据需要扩展
  static void _onDeviceInputTypeChange(int flutterObject, int inputType) {
    Log.i("onDeviceInputTypeChange: $inputType");
    final instance = D900DeviceBindings.instance;
    if (instance._callback != null) {
      instance._callback!.onInputTypeChange(inputType);
    }
  }

  static void _onDeviceOutputTypeChange(int flutterObject, int outputType) {
    Log.i("onDeviceOutputTypeChange: $outputType");
    final instance = D900DeviceBindings.instance;
    if (instance._callback != null) {
      instance._callback!.onOutputTypeChange(outputType);
    }
  }

  /// 设备蓝牙音频启用状态变化回调
  static void _onDeviceEnableAudioBluetooth(int flutterObject, int enable) {
    Log.i("Flutter回调: onDeviceEnableAudioBluetooth, enable: $enable");
    final instance = D900DeviceBindings.instance;
    if (instance._flutterObjectId == flutterObject &&
        instance._callback != null) {
      instance._callback!.onAudioBluetoothChange(enable != 0);
    }
  }

  /// 设备蓝牙APTX启用状态变化回调
  static void _onDeviceEnableBluetoothAptx(int flutterObject, int enable) {
    Log.i("Flutter回调: onDeviceEnableBluetoothAptx, enable: $enable");
    final instance = D900DeviceBindings.instance;
    if (instance._flutterObjectId == flutterObject &&
        instance._callback != null) {
      instance._callback!.onBluetoothAptxChange(enable != 0);
    }
  }

  /// 设备遥控启用状态变化回调
  static void _onDeviceEnableRelay(int flutterObject, int enable) {
    Log.i("Flutter回调: onDeviceEnableRelay, enable: $enable");
    final instance = D900DeviceBindings.instance;
    if (instance._flutterObjectId == flutterObject &&
        instance._callback != null) {
      instance._callback!.onRemoteEnabledChange(enable != 0);
    }
  }

  /// 设备设置响应
  static void _onDeviceSettingsResponse(
    int flutterObject,
    Pointer<FFID900Settings> settings,
  ) {
    Log.i("Flutter回调: onDeviceSettingsResponse, pointer: ${settings.address}");

    final instance = D900DeviceBindings.instance;
    if (instance._flutterObjectId == flutterObject &&
        instance._callback != null) {
      instance._callback!.onDeviceSettingsResponse(settings);
    }
  }

  /// 初始化设备并注册回调
  void initialize(D900Callback callback) {
    _callback = callback;
    _flutterObjectId = DateTime.now().millisecondsSinceEpoch;
    _nativeObject = _d900DeviceCreate(_flutterObjectId!);

    final nativeCallbacks = calloc<FFID900DeviceCallback>();

    // 共享回调函数
    nativeCallbacks.ref.on_scan_results = Pointer.fromFunction<
      Void Function(Int64, Pointer<FFIDx5iiScanResult>, Size)
    >(_onScanResults);
    nativeCallbacks.ref.on_scan_failed =
        Pointer.fromFunction<Void Function(Int64, Int32)>(_onScanFailed);
    nativeCallbacks.ref.on_state_change =
        Pointer.fromFunction<Void Function(Int64, Int32)>(_onStateChange);
    nativeCallbacks.ref.on_verify_result =
        Pointer.fromFunction<Void Function(Int64, Int32)>(_onVerifyResult);
    nativeCallbacks.ref.on_power_change =
        Pointer.fromFunction<Void Function(Int64, Int32)>(_onPowerChange);
    nativeCallbacks.ref.on_device_volume_change =
        Pointer.fromFunction<Void Function(Int64, Int32)>(
          _onDeviceVolumeChange,
        );
    nativeCallbacks.ref.on_device_mute_change =
        Pointer.fromFunction<Void Function(Int64, Int32)>(_onDeviceMuteChange);
    nativeCallbacks.ref.on_device_input_type_change =
        Pointer.fromFunction<Void Function(Int64, Int32)>(
          _onDeviceInputTypeChange,
        );
    nativeCallbacks.ref.on_device_output_type_change =
        Pointer.fromFunction<Void Function(Int64, Int32)>(
          _onDeviceOutputTypeChange,
        );
    nativeCallbacks.ref.on_device_display_mode_change =
        Pointer.fromFunction<Void Function(Int64, Int32)>(
          _onDeviceDisplayModeChange,
        );
    nativeCallbacks.ref.on_device_theme_change =
        Pointer.fromFunction<Void Function(Int64, Int32)>(_onDeviceThemeChange);
    nativeCallbacks.ref.on_device_power_trigger_change =
        Pointer.fromFunction<Void Function(Int64, Int32)>(
          _onDevicePowerTriggerChange,
        );
    nativeCallbacks.ref.on_device_balance_change =
        Pointer.fromFunction<Void Function(Int64, Int32)>(
          _onDeviceBalanceChange,
        );
    nativeCallbacks.ref.on_device_enable_audio_bluetooth =
        Pointer.fromFunction<Void Function(Int64, Int32)>(
          _onDeviceEnableAudioBluetooth,
        );
    nativeCallbacks.ref.on_device_enable_bluetooth_aptx =
        Pointer.fromFunction<Void Function(Int64, Int32)>(
          _onDeviceEnableBluetoothAptx,
        );
    nativeCallbacks.ref.on_device_enable_remote =
        Pointer.fromFunction<Void Function(Int64, Int32)>(_onDeviceEnableRelay);
    nativeCallbacks.ref.on_device_multifunction_key_change =
        Pointer.fromFunction<Void Function(Int64, Int32)>(
          _onDeviceMultifunctionKeyChange,
        );
    nativeCallbacks.ref.on_device_usb_mode_change =
        Pointer.fromFunction<Void Function(Int64, Int32)>(
          _onDeviceUsbModeChange,
        );
    nativeCallbacks.ref.on_device_screen_brightness_change =
        Pointer.fromFunction<Void Function(Int64, Int32)>(
          _onDeviceScreenBrightnessChange,
        );
    nativeCallbacks.ref.on_device_language_change =
        Pointer.fromFunction<Void Function(Int64, Int32)>(
          _onDeviceLanguageChange,
        );
    nativeCallbacks.ref.on_device_restore_factory_settings =
        Pointer.fromFunction<Void Function(Int64)>(
          _onDeviceRestoreFactorySettings,
        );
    nativeCallbacks.ref.on_device_settings_response =
        Pointer.fromFunction<Void Function(Int64, Pointer<FFID900Settings>)>(
          _onDeviceSettingsResponse,
        );

    // D900特有回调
    nativeCallbacks.ref.on_device_usb_select_change =
        Pointer.fromFunction<Void Function(Int64, Int32)>(
          _onDeviceUsbSelectChange,
        );
    nativeCallbacks.ref.on_device_enable_usb_dsd =
        Pointer.fromFunction<Void Function(Int64, Int32)>(
          _onDeviceUsbDsdEnabledChange,
        );
    nativeCallbacks.ref.on_device_iis_phase_change =
        Pointer.fromFunction<Void Function(Int64, Int32)>(
          _onDeviceIisPhaseChange,
        );
    nativeCallbacks.ref.on_device_iis_channel_change =
        Pointer.fromFunction<Void Function(Int64, Int32)>(
          _onDeviceIisChannelChange,
        );

    _d900DeviceRegisterCallback(_nativeObject!, nativeCallbacks);
    calloc.free(nativeCallbacks);
  }

  /// 连接设备
  void connect(int device) {
    if (_nativeObject != null) {
      Log.i("$tag: D900DeviceBindings: 连接设备, 句柄: $device");
      _d900DeviceConnect(_nativeObject!, device);
    }
  }

  /// 断开连接
  void disconnect() {
    if (_nativeObject != null) _d900DeviceDisconnect(_nativeObject!);
  }

  /// 验证设备
  void verify() {
    if (_nativeObject != null) _d900DeviceVerify(_nativeObject!);
  }

  /// 设置设备电源状态
  void powerOn(bool isOn) {
    if (_nativeObject != null) {
      _d900DevicePowerOn(_nativeObject!, isOn ? 1 : 0);
    }
  }

  /// 设置音量
  void setVolume(int volume) {
    if (_nativeObject != null) _d900DeviceSetVolume(_nativeObject!, volume);
  }

  /// 设置静音
  void setMute(bool isMute) {
    if (_nativeObject != null) {
      _d900DeviceSetMute(_nativeObject!, isMute ? 1 : 0);
    }
  }

  /// 设置输入类型
  void setInputType(int inputType) {
    if (_nativeObject != null) {
      _d900DeviceSetInputType(_nativeObject!, inputType);
    }
  }

  /// 设置输出类型
  void setOutputType(int outputType) {
    if (_nativeObject != null) {
      _d900DeviceSetOutputType(_nativeObject!, outputType);
    }
  }

  /// 设置显示模式
  void setDisplayMode(int displayMode) {
    if (_nativeObject != null) {
      _d900DeviceSetDisplayMode(_nativeObject!, displayMode);
    }
  }

  /// 设置主题
  void setTheme(int theme) {
    if (_nativeObject != null) _d900DeviceSetTheme(_nativeObject!, theme);
  }

  /// 设置电源触发器
  void setPowerTrigger(int triggerType) {
    if (_nativeObject != null) {
      _d900DeviceSetPowerTrigger(_nativeObject!, triggerType);
    }
  }

  /// 设置声道平衡
  void setRightBalance(int balance) {
    if (_nativeObject != null) {
      _d900DeviceSetBalance(_nativeObject!, balance);
    }
  }

  /// 启用蓝牙音频
  void enableAudioBluetooth(bool enable) {
    if (_nativeObject != null) {
      _d900DeviceEnableAudioBluetooth(_nativeObject!, enable ? 1 : 0);
    }
  }

  /// 启用蓝牙APTX
  void enableBluetoothAptx(bool enable) {
    if (_nativeObject != null) {
      _d900DeviceEnableBluetoothAptx(_nativeObject!, enable ? 1 : 0);
    }
  }

  /// 启用继电器
  void enableRelay(bool enable) {
    if (_nativeObject != null) {
      _d900DeviceEnableRelay(_nativeObject!, enable ? 1 : 0);
    }
  }

  /// 设置多功能键
  void setMultifunctionKey(int keyType) {
    if (_nativeObject != null) {
      _d900DeviceSetMultifunctionKey(_nativeObject!, keyType);
    }
  }

  /// 设置USB模式
  void setUsbMode(int usbMode) {
    if (_nativeObject != null) _d900DeviceSetUsbMode(_nativeObject!, usbMode);
  }

  /// 设置屏幕亮度
  void setScreenBrightness(int brightnessType) {
    if (_nativeObject != null) {
      _d900DeviceSetScreenBrightness(_nativeObject!, brightnessType);
    }
  }

  /// 设置语言
  void setLanguage(int language) {
    if (_nativeObject != null) {
      _d900DeviceSetLanguage(_nativeObject!, language);
    }
  }

  /// 恢复出厂设置
  void restoreFactorySettings() {
    if (_nativeObject != null) {
      _d900DeviceRestoreFactorySettings(_nativeObject!);
    }
  }

  /// 请求设备设置
  void requestSettings() {
    if (_nativeObject != null) _d900DeviceRequestSettings(_nativeObject!);
  }

  /// 设置USB类型
  void setUsbSelect(int type) {
    if (_nativeObject == null) return;
    _d900DeviceSetUsbSelect(_nativeObject!, type);
  }

  /// 启用/禁用USB DSD直通
  void enableUsbDsd(bool enable) {
    if (_nativeObject == null) return;
    _d900DeviceEnableUsbDsd(_nativeObject!, enable ? 1 : 0);
  }

  /// 设置IIS相位
  void setIisPhase(int phase) {
    if (_nativeObject == null) return;
    _d900DeviceSetIisPhase(_nativeObject!, phase);
  }

  /// 设置IIS DSD通道
  void setIisChannel(int channel) {
    if (_nativeObject == null) return;
    _d900DeviceSetIisChannel(_nativeObject!, channel);
  }

  // --- 新增方法 (D900新版功能) ---
  /// 设置VU表0dDB幅值
  void setVuMeterLevel(int level) {
    if (_nativeObject != null) _d900DeviceSetVuMeterLevel(_nativeObject!, level);
  }

  /// 设置VU条显示模式
  void setVuMeterDisplayMode(int mode) {
    if (_nativeObject != null) _d900DeviceSetVuMeterDisplayMode(_nativeObject!, mode);
  }

  /// 设置输入选项
  void setInputOptions(int options) {
    if (_nativeObject != null) _d900DeviceSetInputOptions(_nativeObject!, options);
  }

  /// 设置输出选项
  void setOutputOptions(int options) {
    if (_nativeObject != null) _d900DeviceSetOutputOptions(_nativeObject!, options);
  }

  /// 设置USB接口选择
  void setUsbPortSelect(int port) {
    if (_nativeObject != null) _d900DeviceSetUsbPortSelect(_nativeObject!, port);
  }

  /// 设置DSD MUTE模式
  void setDsdMute(int mode) {
    if (_nativeObject != null) _d900DeviceSetDsdMute(_nativeObject!, mode);
  }

  /// 设置输出幅值
  void setOutputLevel(int level) {
    if (_nativeObject != null) _d900DeviceSetOutputLevel(_nativeObject!, level);
  }

  /// 设置音量步进
  void setVolumeStep(int step) {
    if (_nativeObject != null) _d900DeviceSetVolumeStep(_nativeObject!, step);
  }

  /// 设置极性
  void setPolarity(int polarity) {
    if (_nativeObject != null) _d900DeviceSetPolarity(_nativeObject!, polarity);
  }

  /// 启用/禁用DSD直通
  void enableDsdDirect(bool enable) {
    if (_nativeObject != null) _d900DeviceEnableDsdDirect(_nativeObject!, enable ? 1 : 0);
  }

  /// 设置音量记忆方式
  void setVolumeMemoryMode(int mode) {
    if (_nativeObject != null) _d900DeviceSetVolumeMemoryMode(_nativeObject!, mode);
  }

  /// 设置PEQ记忆方式
  void setPeqMemoryMode(int mode) {
    if (_nativeObject != null) _d900DeviceSetPeqMemoryMode(_nativeObject!, mode);
  }

  /// 设置主按键功能
  void setMainKeyFunction(int function) {
    if (_nativeObject != null) _d900DeviceSetMainKeyFunction(_nativeObject!, function);
  }

  /// 设置遥控A键功能
  void setRemoteAKeyFunction(int function) {
    if (_nativeObject != null) _d900DeviceSetRemoteAKeyFunction(_nativeObject!, function);
  }

  /// 设置遥控B键功能
  void setRemoteBKeyFunction(int function) {
    if (_nativeObject != null) _d900DeviceSetRemoteBKeyFunction(_nativeObject!, function);
  }

  /// 释放资源
  void dispose() {
    if (_nativeObject != null) {
      _d900DeviceDestroy(_nativeObject!);
      _nativeObject = null;
    }
    _callback = null;
  }
}

/// 扫描结果包装类
class _ScanResultWrapper {
  final String name;
  final int device;
  final int rssi;

  _ScanResultWrapper({
    required this.name,
    required this.device,
    required this.rssi,
  });

  @override
  String toString() => '{name: $name, device: $device, rssi: $rssi}';
}

void _onDeviceDisplayModeChange(int flutterObject, int mode) {
  Log.i("onDeviceDisplayModeChange: $mode");
  final instance = D900DeviceBindings.instance;
  if (instance._callback != null) {
    instance._callback!.onDisplayModeChange(mode);
  }
}

void _onDeviceThemeChange(int flutterObject, int theme) {
  Log.i("onDeviceThemeChange: $theme");
  final instance = D900DeviceBindings.instance;
  if (instance._callback != null) {
    instance._callback!.onThemeChange(theme);
  }
}

void _onDevicePowerTriggerChange(int flutterObject, int trigger) {
  Log.i("onDevicePowerTriggerChange: $trigger");
  final instance = D900DeviceBindings.instance;
  if (instance._callback != null) {
    instance._callback!.onPowerTriggerChange(trigger);
  }
}

void _onDeviceBalanceChange(int flutterObject, int balance) {
  Log.i("onDeviceRightBalanceChange: $balance");
  final instance = D900DeviceBindings.instance;
  if (instance._callback != null) {
    instance._callback!.onBalanceChange(balance);
  }
}

void _onDeviceMultifunctionKeyChange(int flutterObject, int key) {
  Log.i("onDeviceMultifunctionKeyChange: $key");
  final instance = D900DeviceBindings.instance;
  if (instance._callback != null) {
    instance._callback!.onMultifunctionKeyChange(key);
  }
}

void _onDeviceUsbModeChange(int flutterObject, int mode) {
  Log.i("onDeviceUsbModeChange: $mode");
  final instance = D900DeviceBindings.instance;
  if (instance._callback != null) {
    instance._callback!.onUsbModeChange(mode);
  }
}

void _onDeviceScreenBrightnessChange(int flutterObject, int brightness) {
  Log.i("onDeviceScreenBrightnessChange: $brightness");
  final instance = D900DeviceBindings.instance;
  if (instance._callback != null) {
    instance._callback!.onScreenBrightnessChange(brightness);
  }
}

void _onDeviceLanguageChange(int flutterObject, int language) {
  Log.i("onDeviceLanguageChange: $language");
  final instance = D900DeviceBindings.instance;
  if (instance._callback != null) {
    instance._callback!.onLanguageChange(language);
  }
}

void _onDeviceRestoreFactorySettings(int flutterObject) =>
    Log.i("onDeviceRestoreFactorySettings");

/// 设备USB选择变化 (D900特有)
void _onDeviceUsbSelectChange(int flutterObject, int usbSelect) {
  Log.i("Flutter回调: onDeviceUsbSelectChange, usbSelect: $usbSelect");
  final instance = D900DeviceBindings.instance;
  if (instance._flutterObjectId == flutterObject &&
      instance._callback != null) {
    instance._callback!.onUsbSelectChange(usbSelect);
  }
}

/// 设备USB DSD启用状态变化 (D900特有)
void _onDeviceUsbDsdEnabledChange(int flutterObject, int isEnabled) {
  Log.i("Flutter回调: onDeviceUsbDsdEnabledChange, isEnabled: $isEnabled");
  final instance = D900DeviceBindings.instance;
  if (instance._flutterObjectId == flutterObject &&
      instance._callback != null) {
    instance._callback!.onUsbDsdEnabledChange(isEnabled == 1);
  }
}

/// 设备IIS相位变化 (D900特有)
void _onDeviceIisPhaseChange(int flutterObject, int phase) {
  Log.i("Flutter回调: onDeviceIisPhaseChange, phase: $phase");
  final instance = D900DeviceBindings.instance;
  if (instance._flutterObjectId == flutterObject &&
      instance._callback != null) {
    instance._callback!.onIisPhaseChange(phase);
  }
}

/// 设备IIS通道变化 (D900特有)
void _onDeviceIisChannelChange(int flutterObject, int channel) {
  Log.i("Flutter回调: onDeviceIisChannelChange, channel: $channel");
  final instance = D900DeviceBindings.instance;
  if (instance._flutterObjectId == flutterObject &&
      instance._callback != null) {
    instance._callback!.onIisChannelChange(channel);
  }
}
