import 'dart:io';
import 'package:dio/dio.dart';
import 'package:get/get.dart';
import 'package:path_provider/path_provider.dart';
import '../common/util/log_util.dart';
import '../http/api/api_service.dart';
import '../models/bluetooth_firmware_entity.dart';

/// 蓝牙固件升级服务
class FirmwareService extends GetxService {
  static FirmwareService get to => Get.find();
  final ApiService _apiService = Get.find<ApiService>();

  // 当前固件信息
  final Rx<BluetoothFirmwareEntity?> latestFirmware =
      Rx<BluetoothFirmwareEntity?>(null);

  // 是否有可用更新
  final RxBool hasUpdate = false.obs;

  // 当前设备型号
  final deviceModel = "".obs;

  // 当前设备MAC地址
  final macAddress = "".obs;

  // 当前版本号
  final versionCode = 0.obs;

  // 下载进度
  final downloadProgress = 0.0.obs;

  // 下载状态
  final isDownloading = false.obs;

  @override
  void onInit() {
    super.onInit();
    // 如果需要，可以从持久化存储中加载设备信息
  }

  /// 设置设备信息
  void setDeviceInfo(String model, String mac) {
    deviceModel.value = model;
    macAddress.value = mac;
    Log.i('设置设备信息: model: $model, mac: $mac');
  }

  /// 检查固件更新
  Future<bool> checkUpdate() async {
    Log.i('检查固件更新');
    if (deviceModel.value.isEmpty || macAddress.value.isEmpty) {
      Log.i('设备信息不完整，无法检查更新');
      return false;
    }

    Log.i('开始检查固件更新 deviceModel: ${deviceModel.value}  macAddress: ${macAddress.value}');
    try {
      final response = await _apiService.checkFirmwareUpdate(
        deviceModel.value,
        macAddress.value,
      );

      Log.i('检查固件更新结果: $response');
      if (response.success && response.data != null) {
        latestFirmware.value = response.data;
        hasUpdate.value = true;
        return true;
      }

      hasUpdate.value = false;
      return false;
    } catch (e) {
      Log.i('检查固件更新失败: $e');
      hasUpdate.value = false;
      return false;
    }
  }

  /// 下载固件
  Future<String?> downloadFirmware() async {
    if (latestFirmware.value == null || latestFirmware.value!.fileUrl.isEmpty) {
      return null;
    }

    isDownloading.value = true;
    downloadProgress.value = 0.0;

    try {
      // 获取应用文档目录
      final appDocDir = await getApplicationDocumentsDirectory();
      final filePath = '${appDocDir.path}/1.bin'; // OtaServer 期望的路径

      // 使用Dio直接下载
      final dio = Dio();
      await dio.download(
        latestFirmware.value!.fileUrl,
        filePath,
        onReceiveProgress: (received, total) {
          if (total != -1) {
            downloadProgress.value = received / total;
          }
        },
      );

      isDownloading.value = false;
      return filePath;
    } catch (e) {
      Log.i('固件下载失败: $e');
      isDownloading.value = false;
      return null;
    }
  }

  /// 报告升级成功
  Future<bool> reportUpgradeSuccess(String firmwareVersion) async {
    if (macAddress.value.isEmpty) {
      return false;
    }

    try {
      final response = await _apiService.reportFirmwareUpgradeSuccess(
        macAddress.value,
        firmwareVersion,
      );

      return response.success;
    } catch (e) {
      Log.i('报告升级成功失败: $e');
      return false;
    }
  }

  /// 获取固件信息
  Future<BluetoothFirmwareEntity?> getLatestFirmware() async {
    if (deviceModel.value.isEmpty) {
      return null;
    }

    try {
      final response = await _apiService.getLatestFirmware(deviceModel.value);

      if (response.success && response.data != null) {
        latestFirmware.value = response.data;
        return response.data;
      }

      return null;
    } catch (e) {
      Log.i('获取最新固件信息失败: $e');
      return null;
    }
  }
}
