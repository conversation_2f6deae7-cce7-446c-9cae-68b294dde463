import 'package:get/get.dart';
import 'package:topping_home/environment_config.dart';

/// 环境配置服务，用于初始化和管理应用环境
class EnvironmentService extends GetxService {
  /// 初始化环境配置
  Future<EnvironmentService> init() async {
    // 初始化环境配置
    await EnvironmentConfig().init();

    // 打印当前环境信息
    final env = EnvironmentConfig();
    print('环境服务初始化完成');
    print('当前环境: ${env.environment}');
    print('是否开发环境: ${env.isDev}');
    print('是否测试环境: ${env.isStaging}');
    print('是否生产环境: ${env.isProd}');
    print('应用版本: ${env.displayVersion}');

    return this;
  }
}
