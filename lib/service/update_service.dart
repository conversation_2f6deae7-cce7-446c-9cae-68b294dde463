import 'dart:io';

import 'package:device_info_plus/device_info_plus.dart';
import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:open_file/open_file.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:path_provider/path_provider.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:topping_home/common/util/i18n.dart';
import 'package:topping_home/common/util/log_util.dart';
import 'package:topping_home/http/api/api_service.dart';
import 'package:topping_home/http/retrofit_client.dart';
import 'package:topping_home/models/app_version_entity.dart';
import 'package:topping_home/theme/color_palettes.dart';
import 'package:topping_home/theme/text_styles.dart';

class UpdateService extends GetxService {
  final ApiService _apiService = RetrofitClient.instance.apiService;

  // 状态变量
  final isCheckingUpdate = false.obs;
  final downloadProgress = 0.0.obs;
  final isDownloading = false.obs;
  final newVersion = Rx<AppVersionEntity?>(null);

  final _lastCheckKey = 'last_update_check_time';

  // 检查更新
  Future<bool> checkUpdate({bool silent = false}) async {
    if (isCheckingUpdate.value) return false;

    isCheckingUpdate.value = true;

    if (!silent) {
      Get.dialog(
        const Center(child: CircularProgressIndicator()),
        barrierDismissible: false,
      );
    }

    try {
      final packageInfo = await PackageInfo.fromPlatform();

      final result = await _apiService.checkAppUpdate(packageInfo.packageName,
          int.parse(packageInfo.buildNumber), null // 可以根据设备类型添加
          );

      if (!silent) Get.back();
      isCheckingUpdate.value = false;

      if (result.success && result.data != null) {
        newVersion.value = result.data;
        if (!silent) _showUpdateDialog(result.data!, packageInfo.version);
        return true;
      } else {
        if (!silent) {
          Get.snackbar(
            '',
            l10n.alreadyLatestVersion,
            backgroundColor: ColorPalettes.instance.successLightest,
            colorText: ColorPalettes.instance.success,
            snackPosition: SnackPosition.BOTTOM,
            borderRadius: 8,
            margin: const EdgeInsets.all(16),
          );
        }
        return false;
      }
    } catch (e) {
      if (!silent) Get.back();
      isCheckingUpdate.value = false;
      if (!silent) {
        Get.snackbar(
          '',
          l10n.checkUpdateFailed,
          backgroundColor: ColorPalettes.instance.errorLightest,
          colorText: ColorPalettes.instance.error,
          snackPosition: SnackPosition.BOTTOM,
          borderRadius: 8,
          margin: const EdgeInsets.all(16),
        );
      }
      return false;
    }
  }

  // 显示更新对话框
  void _showUpdateDialog(AppVersionEntity version, String currentVersion) {
    Get.dialog(
      AlertDialog(
        backgroundColor: ColorPalettes.instance.card,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        title: Text(
          l10n.foundNewVersion,
          style: TextStyles.instance.h2(
            color: ColorPalettes.instance.firstText,
          ),
        ),
        content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                '${l10n.currentVersion}: $currentVersion',
                style: TextStyles.instance.body1(),
              ),
              Text(
                '${l10n.newVersion}: ${version.versionName}',
                style: TextStyles.instance.body1(
                  color: ColorPalettes.instance.secondText,
                  fontWeight: FontWeight.w600,
                ),
              ),
              const SizedBox(height: 8),
              Divider(color: ColorPalettes.instance.divider),
              const SizedBox(height: 8),
              Text(
                l10n.updateContent,
                style: TextStyles.instance.h3(
                  fontWeight: FontWeight.w600,
                ),
              ),
              const SizedBox(height: 4),
              Container(
                constraints: const BoxConstraints(maxHeight: 200),
                child: SingleChildScrollView(
                  child: Text(
                    version.description,
                    style: TextStyles.instance.body1(),
                  ),
                ),
              )
            ]),
        actions: [
          if (!version.forceUpdate)
            TextButton(
              onPressed: () => Get.back(),
              child: Text(
                l10n.later,
                style: TextStyles.instance.button(
                  color: ColorPalettes.instance.secondText,
                ),
              ),
            ),
          TextButton(
            onPressed: () => downloadUpdate(version.fileUrl),
            child: Text(
              l10n.updateNow,
              style: TextStyles.instance.button(
                color: ColorPalettes.instance.primary,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ],
      ),
      barrierDismissible: !version.forceUpdate,
    );
  }

  // 下载更新
  Future<void> downloadUpdate(String url) async {
    // 先关闭对话框
    Get.back();

    // 检查权限
    if (!await _checkAndRequestPermissions()) {
      return;
    }

    // 获取下载路径
    final dir = await getApplicationDocumentsDirectory();

    // 创建下载文件路径
    final savePath = '${dir.path}/topping_update.apk';
    final file = File(savePath);
    if (file.existsSync()) {
      await file.delete();
    }

    // 显示下载进度对话框
    Get.dialog(
      AlertDialog(
        backgroundColor: ColorPalettes.instance.card,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        title: Text(
          l10n.downloading,
          style: TextStyles.instance.h2(),
        ),
        content: Obx(() => Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                LinearProgressIndicator(
                  value: downloadProgress.value / 100,
                  backgroundColor: ColorPalettes.instance.primaryLightest,
                  valueColor: AlwaysStoppedAnimation<Color>(
                      ColorPalettes.instance.primary),
                ),
                const SizedBox(height: 8),
                Text(
                  '${downloadProgress.value.toStringAsFixed(1)}%',
                  style: TextStyles.instance.body1(
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            )),
        actions: [
          TextButton(
            onPressed: () {
              isDownloading.value = false;
              Get.back();
            },
            child: Text(
              l10n.cancel,
              style: TextStyles.instance.button(
                color: ColorPalettes.instance.error,
              ),
            ),
          ),
        ],
      ),
      barrierDismissible: false,
    );

    // 开始下载
    isDownloading.value = true;
    downloadProgress.value = 0;

    try {
      await Dio().download(
        url,
        savePath,
        onReceiveProgress: (received, total) {
          if (total != -1) {
            downloadProgress.value = received / total * 100;
          }
        },
      );

      // 下载完成，关闭对话框
      Get.back();
      isDownloading.value = false;

      // 使用open_file安装APK
      if (Platform.isAndroid) {
        final result = await OpenFile.open(savePath);
        if (result.type == ResultType.done) {
          Get.snackbar(
            '',
            '正在安装...',
            backgroundColor: ColorPalettes.instance.successLightest,
            colorText: ColorPalettes.instance.success,
            snackPosition: SnackPosition.BOTTOM,
            borderRadius: 8,
            margin: const EdgeInsets.all(16),
          );
        } else {
          Get.snackbar(
            '',
            '安装失败: ${result.message}',
            backgroundColor: ColorPalettes.instance.errorLightest,
            colorText: ColorPalettes.instance.error,
            snackPosition: SnackPosition.BOTTOM,
            borderRadius: 8,
            margin: const EdgeInsets.all(16),
          );
        }
      }
    } catch (e) {
      Get.back();
      isDownloading.value = false;
      Get.snackbar(
        '',
        '下载出错: ${e.toString()}',
        backgroundColor: ColorPalettes.instance.errorLightest,
        colorText: ColorPalettes.instance.error,
        snackPosition: SnackPosition.BOTTOM,
        borderRadius: 8,
        margin: const EdgeInsets.all(16),
      );
    }
  }

  // 下载更新时的权限检查
  Future<bool> _checkAndRequestPermissions() async {
    if (Platform.isAndroid) {
      final deviceInfo = DeviceInfoPlugin();
      final androidInfo = await deviceInfo.androidInfo;
      final androidVersion =
          int.tryParse(androidInfo.version.release.split('.')[0]) ?? 0;

      Log.e(
          'Android版本: ${androidInfo.version.release}, SDK: ${androidInfo.version.sdkInt}');

      if (androidVersion < 13) {
        var storage = await Permission.storage.status;
        Log.e('存储权限状态: $storage');

        if (!storage.isGranted) {
          storage = await Permission.storage.request();
          if (!storage.isGranted) {
            _showPermissionDeniedMessage('存储');
            return false;
          }
        }
      }

      if (androidVersion >= 8) {
        bool hasInstallPermission =
            await Permission.requestInstallPackages.status.isGranted;
        Log.e('安装权限状态: $hasInstallPermission');

        if (!hasInstallPermission) {
          hasInstallPermission =
              await Permission.requestInstallPackages.request().isGranted;
          if (!hasInstallPermission) {
            _showPermissionDeniedMessage('安装应用');
            return false;
          }
        }
      }
    }
    return true;
  }

  void _showPermissionDeniedMessage(String permissionType) {
    Get.dialog(
      AlertDialog(
        backgroundColor: ColorPalettes.instance.card,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        title: Text(
          '权限请求',
          style: TextStyles.instance.h2(),
        ),
        content: Text(
          '需要$permissionType权限来下载和安装更新，请在设置中授予权限。',
          style: TextStyles.instance.body1(),
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: Text(
              '取消',
              style: TextStyles.instance.button(
                color: ColorPalettes.instance.secondText,
              ),
            ),
          ),
          TextButton(
            onPressed: () {
              Get.back();
              openAppSettings();
            },
            child: Text(
              '去设置',
              style: TextStyles.instance.button(
                color: ColorPalettes.instance.primary,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ],
      ),
    );
  }

  // 自动检测更新（带间隔控制）
  Future<bool> autoCheckUpdate() async {
    // 获取上次检查时间
    final prefs = Get.find<SharedPreferences>();
    final now = DateTime.now().millisecondsSinceEpoch;
    // 静默检查更新
    final hasUpdate = await checkUpdate(silent: true);

    // 记录检查时间
    await prefs.setInt(_lastCheckKey, now);

    // 如果有更新，显示更新对话框
    if (hasUpdate) {
      final packageInfo = await PackageInfo.fromPlatform();
      _showUpdateDialog(newVersion.value!, packageInfo.version);
    }

    return hasUpdate;
  }
}
