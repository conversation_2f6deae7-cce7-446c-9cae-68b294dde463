import 'dart:ui';

import '../ipalette.dart';

/// 暗色主题调色板
class DarkPalette extends IPalette {
  @override
  Color statusBar = const Color(0xFF121212);
  @override
  Color pure = const Color(0xFF000000);
  @override
  Color primary = const Color(0xFF7B6EE6); // 调整为与浅色主题相协调的紫蓝色
  @override
  Color primaryVariant = const Color(0xFF5B4CE0); // 与浅色主题的primary保持一致
  @override
  Color secondary = const Color(0xFF29B6F6); // 调整为更亮的蓝色
  @override
  Color background = const Color(0xFF121212);
  @override
  Color firstText = const Color(0xFFE0E0E0);
  @override
  Color secondText = const Color(0xFFAAAAAA);
  @override
  Color thirdText = const Color(0xFF8A8A8A); // 调整为更亮的灰色
  @override
  Color firstIcon = const Color(0xFFE0E0E0);
  @override
  Color secondIcon = const Color(0xFFAAAAAA);
  @override
  Color thirdIcon = const Color(0xFF8A8A8A); // 调整为更亮的灰色
  @override
  Color card = const Color(0xFF1E1E1E);
  @override
  Color divider = const Color(0xFF2C2C2C);
  @override
  Color separator = const Color(0xFF323232);
  @override
  Color inputBackground = const Color(0xFF252525); // 与card区分
  @override
  Color transparent = const Color(0x00000000);
  @override
  Color success = const Color(0xFF66BB6A); // 调整为更亮的绿色
  @override
  Color error = const Color(0xFFEF5350); // 调整为更亮的红色
  @override
  Color warning = const Color(0xFFFFB74D); // 调整为更亮的橙色
  @override
  Color info = const Color(0xFF42A5F5); // 调整为更亮的蓝色
  @override
  Color overlay = const Color(0x80000000);
  @override
  Color shadow = const Color(0x80000000);
  @override
  Color accent = const Color(0xFF66BB6A); // 使用更亮的绿色作为暗色主题的强调色
}
