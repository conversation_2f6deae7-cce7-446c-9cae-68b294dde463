import 'dart:ui';

import '../ipalette.dart';

/// 浅色主题调色板 - 默认主题
class DefaultPalette extends IPalette {
  @override
  Color statusBar = const Color(0xFFF2F2F2);
  @override
  Color pure = const Color(0xFFFFFFFF);
  @override
  Color primary = const Color(0xFF5B4CE0); // 使用蓝色作为主题色
  @override
  Color primaryVariant = const Color(0xFF1565C0);
  @override
  Color secondary = const Color(0xFF03A9F4);
  @override
  Color background = const Color(0xFFFAFAFA);
  @override
  Color firstText = const Color(0xFF212121);
  @override
  Color secondText = const Color(0xFF616161);
  @override
  Color thirdText = const Color(0xFF9E9E9E);
  @override
  Color firstIcon = const Color(0xFF212121);
  @override
  Color secondIcon = const Color(0xFF616161);
  @override
  Color thirdIcon = const Color(0xFF9E9E9E);
  @override
  Color card = const Color(0xFFFFFFFF);
  @override
  Color divider = const Color(0xFFE0E0E0);
  @override
  Color separator = const Color(0xFFEEEEEE);
  @override
  Color inputBackground = const Color(0xFFF5F5F5);
  @override
  Color transparent = const Color(0x00000000);
  @override
  Color success = const Color(0xFF4CAF50);
  @override
  Color error = const Color(0xFFF44336);
  @override
  Color warning = const Color(0xFFFF9800); // 橙色警告
  @override
  Color info = const Color(0xFF2196F3); // 蓝色信息
  @override
  Color overlay = const Color(0x80000000);
  @override
  Color shadow = const Color(0x80000000);
  @override
  Color accent = const Color(0xFF4CAF50); // 使用绿色作为强调色
}
