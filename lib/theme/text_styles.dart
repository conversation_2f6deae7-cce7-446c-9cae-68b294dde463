import 'package:flutter/material.dart';
import 'package:topping_home/theme/color_palettes.dart';

class TextStyles {
  TextStyles._();

  static TextStyles get instance => _getInstance();
  static TextStyles? _instance;

  static TextStyles _getInstance() {
    _instance ??= TextStyles._();
    return _instance!;
  }

  // 标题样式 - 大标题
  TextStyle h1({
    Color? color,
    double? fontSize,
    FontWeight? fontWeight,
    double? letterSpacing,
    double? height,
    TextDecoration? decoration,
    FontStyle? fontStyle,
  }) =>
      TextStyle(
        fontSize: fontSize ?? 22,
        fontWeight: fontWeight ?? FontWeight.bold,
        color: color ?? ColorPalettes.instance.firstText,
        letterSpacing: letterSpacing,
        height: height ?? 1.3,
        decoration: decoration,
        fontStyle: fontStyle,
      );

  // 标题样式 - 中标题
  TextStyle h2({
    Color? color,
    double? fontSize,
    FontWeight? fontWeight,
    double? letterSpacing,
    double? height,
    TextDecoration? decoration,
    FontStyle? fontStyle,
  }) =>
      TextStyle(
        fontSize: fontSize ?? 18,
        fontWeight: fontWeight ?? FontWeight.bold,
        color: color ?? ColorPalettes.instance.firstText,
        letterSpacing: letterSpacing,
        height: height ?? 1.3,
        decoration: decoration,
        fontStyle: fontStyle,
      );

  // 标题样式 - 小标题
  TextStyle h3({
    Color? color,
    double? fontSize,
    FontWeight? fontWeight,
    double? letterSpacing,
    double? height,
    TextDecoration? decoration,
    FontStyle? fontStyle,
  }) =>
      TextStyle(
        fontSize: fontSize ?? 16,
        fontWeight: fontWeight ?? FontWeight.w500,
        color: color ?? ColorPalettes.instance.firstText,
        letterSpacing: letterSpacing,
        height: height ?? 1.3,
        decoration: decoration,
        fontStyle: fontStyle,
      );

  // 标题样式 - 极小标题
  TextStyle h4({
    Color? color,
    double? fontSize,
    FontWeight? fontWeight,
    double? letterSpacing,
    double? height,
    TextDecoration? decoration,
    FontStyle? fontStyle,
  }) =>
      TextStyle(
        fontSize: fontSize ?? 14,
        fontWeight: fontWeight ?? FontWeight.w500,
        color: color ?? ColorPalettes.instance.firstText,
        letterSpacing: letterSpacing,
        height: height ?? 1.3,
        decoration: decoration,
        fontStyle: fontStyle,
      );

  // 标题样式 - 微小标题
  TextStyle h5({
    Color? color,
    double? fontSize,
    FontWeight? fontWeight,
    double? letterSpacing,
    double? height,
    TextDecoration? decoration,
    FontStyle? fontStyle,
  }) =>
      TextStyle(
        fontSize: fontSize ?? 12,
        fontWeight: fontWeight ?? FontWeight.w500,
        color: color ?? ColorPalettes.instance.firstText,
        letterSpacing: letterSpacing,
        height: height ?? 1.3,
        decoration: decoration,
        fontStyle: fontStyle,
      );

  // 正文样式 - 主要正文
  TextStyle body1({
    Color? color,
    double? fontSize,
    FontWeight? fontWeight,
    double? letterSpacing,
    double? height,
    TextDecoration? decoration,
    FontStyle? fontStyle,
  }) =>
      TextStyle(
        fontSize: fontSize ?? 14,
        fontWeight: fontWeight ?? FontWeight.normal,
        color: color ?? ColorPalettes.instance.firstText,
        letterSpacing: letterSpacing,
        height: height ?? 1.5,
        decoration: decoration,
        fontStyle: fontStyle,
      );

  // 正文样式 - 次要文本
  TextStyle body2({
    Color? color,
    double? fontSize,
    FontWeight? fontWeight,
    double? letterSpacing,
    double? height,
    TextDecoration? decoration,
    FontStyle? fontStyle,
  }) =>
      TextStyle(
        fontSize: fontSize ?? 12,
        fontWeight: fontWeight ?? FontWeight.normal,
        color: color ?? ColorPalettes.instance.secondText,
        letterSpacing: letterSpacing,
        height: height ?? 1.5,
        decoration: decoration,
        fontStyle: fontStyle,
      );

  // 按钮文字样式
  TextStyle button({
    Color? color,
    double? fontSize,
    FontWeight? fontWeight,
    double? letterSpacing,
    double? height,
    TextDecoration? decoration,
    FontStyle? fontStyle,
  }) =>
      TextStyle(
        fontSize: fontSize ?? 14,
        fontWeight: fontWeight ?? FontWeight.bold,
        letterSpacing: letterSpacing ?? 0.5,
        color: color ?? ColorPalettes.instance.primary,
        height: height ?? 1.2,
        decoration: decoration,
        fontStyle: fontStyle,
      );

  // 辅助文字样式
  TextStyle caption({
    Color? color,
    double? fontSize,
    FontWeight? fontWeight,
    double? letterSpacing,
    double? height,
    TextDecoration? decoration,
    FontStyle? fontStyle,
  }) =>
      TextStyle(
        fontSize: fontSize ?? 10,
        color: color ?? ColorPalettes.instance.thirdText,
        letterSpacing: letterSpacing,
        fontWeight: fontWeight,
        height: height ?? 1.4,
        decoration: decoration,
        fontStyle: fontStyle,
      );

  // 上标文字样式
  TextStyle overline({
    Color? color,
    double? fontSize,
    FontWeight? fontWeight,
    double? letterSpacing,
    double? height,
    TextDecoration? decoration,
    FontStyle? fontStyle,
  }) =>
      TextStyle(
        fontSize: fontSize ?? 10,
        letterSpacing: letterSpacing ?? 1.5,
        color: color ?? ColorPalettes.instance.thirdText,
        fontWeight: fontWeight,
        height: height ?? 1.2,
        decoration: decoration,
        fontStyle: fontStyle,
      );

  // 链接文字样式
  TextStyle link({
    Color? color,
    double? fontSize,
    FontWeight? fontWeight,
    double? letterSpacing,
    double? height,
    TextDecoration? decoration,
    FontStyle? fontStyle,
  }) =>
      TextStyle(
        fontSize: fontSize ?? 14,
        color: color ?? ColorPalettes.instance.primary,
        fontWeight: fontWeight ?? FontWeight.w500,
        letterSpacing: letterSpacing,
        height: height ?? 1.5,
        decoration: decoration ?? TextDecoration.underline,
        fontStyle: fontStyle,
      );

  // 强调文字样式
  TextStyle emphasis({
    Color? color,
    double? fontSize,
    FontWeight? fontWeight,
    double? letterSpacing,
    double? height,
    TextDecoration? decoration,
    FontStyle? fontStyle,
  }) =>
      TextStyle(
        fontSize: fontSize ?? 14,
        color: color ?? ColorPalettes.instance.primary,
        fontWeight: fontWeight ?? FontWeight.bold,
        letterSpacing: letterSpacing,
        height: height ?? 1.5,
        decoration: decoration,
        fontStyle: fontStyle,
      );

  // 表单标签样式
  TextStyle formLabel({
    Color? color,
    double? fontSize,
    FontWeight? fontWeight,
    double? letterSpacing,
    double? height,
    TextDecoration? decoration,
    FontStyle? fontStyle,
  }) =>
      TextStyle(
        fontSize: fontSize ?? 14,
        color: color ?? ColorPalettes.instance.secondText,
        fontWeight: fontWeight ?? FontWeight.w500,
        letterSpacing: letterSpacing,
        height: height ?? 1.3,
        decoration: decoration,
        fontStyle: fontStyle,
      );

  // 一些常用的快捷方法
  TextStyle get h1Bold => h1(fontWeight: FontWeight.w900);
  TextStyle get h1Primary => h1(color: ColorPalettes.instance.primary);
  TextStyle get h2Primary => h2(color: ColorPalettes.instance.primary);
  TextStyle get h3Primary => h3(color: ColorPalettes.instance.primary);
  TextStyle get body1Primary => body1(color: ColorPalettes.instance.primary);
  TextStyle get body1Secondary =>
      body1(color: ColorPalettes.instance.secondary);
  TextStyle get body1Success => body1(color: ColorPalettes.instance.success);
  TextStyle get body1Error => body1(color: ColorPalettes.instance.error);
}
