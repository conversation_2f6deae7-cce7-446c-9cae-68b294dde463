import 'dart:ui';

import 'package:get/get.dart';
import 'palette/dark/dark_palette.dart';
import 'palette/ipalette.dart';
import 'palette/light/default_palette.dart';

import '../common/util/preference_utils.dart';

class ColorPalettes {
  ColorPalettes._();

  final String key = "keyPalettesIndex";

  static ColorPalettes get instance => _getInstance();
  static ColorPalettes? _instance;

  static ColorPalettes _getInstance() {
    _instance ??= ColorPalettes._();
    return _instance!;
  }

  /// App主题色集合
  final Map<PalettesStyle, IPalette> palettes = {
    PalettesStyle.dark: DarkPalette(),
    PalettesStyle.lightDefault: DefaultPalette(),
  };

  /// 当前主题色类型
  late Rx<PalettesStyle> palettesStyle;

  /// 初始化--使用上一次的主题色，默认使用亮色主题
  void init() {
    // 修改默认值为亮色主题的索引(1)
    int curPalettesIndex = PreferenceUtils.instance
        .getInteger(key, PalettesStyle.lightDefault.index);
    PalettesStyle curPalettes = palettes.keys
        .where((element) => element.index == curPalettesIndex)
        .first;
    palettesStyle = curPalettes.obs;
  }

  /// 改变主题色
  void changeTheme(PalettesStyle style) {
    palettesStyle.value = style;
    PreferenceUtils.instance.putInteger(key, style.index);
  }

  /// 是否是暗黑模式
  bool isDark() => palettesStyle.value == PalettesStyle.dark;

  // 系统颜色
  Color get statusBar => palettes[palettesStyle.value]!.statusBar;
  Color get pure => palettes[palettesStyle.value]!.pure;
  Color get transparent => palettes[palettesStyle.value]!.transparent;

  // 主题色系
  Color get primary => palettes[palettesStyle.value]!.primary;
  Color get primaryVariant => palettes[palettesStyle.value]!.primaryVariant;
  Color get primaryLight => primary.withValues(alpha: 0.7);
  Color get primaryLighter => primary.withValues(alpha: 0.3);
  Color get primaryLightest => primary.withValues(alpha: 0.1);

  // 次要主题色
  Color get secondary => palettes[palettesStyle.value]!.secondary;
  Color get secondaryLight => secondary.withValues(alpha: 0.7);
  Color get secondaryLighter => secondary.withValues(alpha: 0.3);
  Color get secondaryLightest => secondary.withValues(alpha: 0.1);

  // 背景色系
  Color get background => palettes[palettesStyle.value]!.background;
  Color get card => palettes[palettesStyle.value]!.card;
  Color get inputBackground => palettes[palettesStyle.value]!.inputBackground;

  // 文字颜色
  Color get firstText => palettes[palettesStyle.value]!.firstText;
  Color get secondText => palettes[palettesStyle.value]!.secondText;
  Color get thirdText => palettes[palettesStyle.value]!.thirdText;

  // 图标颜色
  Color get firstIcon => palettes[palettesStyle.value]!.firstIcon;
  Color get secondIcon => palettes[palettesStyle.value]!.secondIcon;
  Color get thirdIcon => palettes[palettesStyle.value]!.thirdIcon;

  // 分割线颜色
  Color get divider => palettes[palettesStyle.value]!.divider;
  Color get separator => palettes[palettesStyle.value]!.separator;

  // 功能色
  Color get success => palettes[palettesStyle.value]!.success;
  Color get successLight => success.withValues(alpha: 0.7);
  Color get successLighter => success.withValues(alpha: 0.3);
  Color get successLightest => success.withValues(alpha: 0.1);

  Color get error => palettes[palettesStyle.value]!.error;
  Color get errorLight => error.withValues(alpha: 0.7);
  Color get errorLighter => error.withValues(alpha: 0.3);
  Color get errorLightest => error.withValues(alpha: 0.1);

  // 新增功能色
  Color get warning => palettes[palettesStyle.value]!.warning; // 橙色警告
  Color get warningLight => warning.withValues(alpha: 0.7);
  Color get warningLighter => warning.withValues(alpha: 0.3);
  Color get warningLightest => warning.withValues(alpha: 0.1);

  Color get info => palettes[palettesStyle.value]!.info; // 蓝色信息
  Color get infoLight => info.withValues(alpha: 0.7);
  Color get infoLighter => info.withValues(alpha: 0.3);
  Color get infoLightest => info.withValues(alpha: 0.1);

  // 强调色系
  Color get accent => palettes[palettesStyle.value]!.accent;
  Color get accentLight => accent.withValues(alpha: 0.7);
  Color get accentLighter => accent.withValues(alpha: 0.3);
  Color get accentLightest => accent.withValues(alpha: 0.1);

  // 其他功能色
  Color get overlay => palettes[palettesStyle.value]!.overlay;
  Color get shadow => palettes[palettesStyle.value]!.shadow;

  // 旋钮控件颜色
  Color get knobOuterRing =>
      isDark() ? const Color(0xFF2A2A2A) : const Color(0xFFE0E0E0);
  Color get knobInnerCircle =>
      isDark() ? const Color(0xFF1A1A1A) : const Color(0xFFF5F5F5);
  Color get knobIndicator =>
      isDark() ? const Color(0xFFDADADA) : const Color(0xFF505050);
}

/// 主题色类型
enum PalettesStyle { dark, lightDefault, blue, green, orange, purple, yellow }
