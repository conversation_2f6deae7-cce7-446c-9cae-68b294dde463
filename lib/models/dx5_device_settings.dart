import 'package:hive_ce/hive.dart';
import 'package:json_annotation/json_annotation.dart';

import '../enums/dx5/dx5_decode_mode_type.dart';
import '../enums/dx5/dx5_display_type.dart';
import '../enums/dx5/dx5_filter_parameter_type.dart';
import '../enums/dx5/dx5_headphone_gain_type.dart';
import '../enums/dx5/dx5_input_type.dart';
import '../enums/dx5/dx5_language_type.dart';
import '../enums/dx5/dx5_multi_function_key_type.dart';
import '../enums/dx5/dx5_output_type.dart';
import '../enums/dx5/dx5_power_trigger_type.dart';
import '../enums/dx5/dx5_screen_brightness_type.dart';
import '../enums/dx5/dx5_theme_type.dart';
import '../enums/dx5/dx5_usb_type.dart';
import 'hive_adapters.dart';
import 'peq_settings_model.dart';

part 'dx5_device_settings.g.dart';

/// 设备设置类
/// 存储和管理设备的各项可配置参数
@JsonSerializable()
@HiveType(typeId: HiveAdapterIds.deviceSettingsTypeId)
class Dx5DeviceSettings extends HiveObject {
  // id
  @HiveField(0)
  String? id;

  // 关联id (设备id)
  @HiveField(1)
  String? deviceId;

  /// 基础设置

  // 标题栏
  @HiveField(2)
  String? title;

  // 开关机
  @HiveField(3)
  bool power;

  // 音量大小 (-99到0db, 0为最大))
  @HiveField(4)
  int volume;

  // 静音
  @HiveField(5)
  bool mute;

  // 当前选择的输入源（USB,光纤，同轴，蓝牙）
  Dx5InputType selectedInput;

  // 存储输入源的索引值
  @HiveField(6)
  int get selectedInputIndex => selectedInput.index;
  set selectedInputIndex(int value) =>
      selectedInput = Dx5InputType.values[value];

  // 当前选择的输出方式（关闭，单端，平衡，单端+平衡）
  Dx5OutputType selectedOutput;

  // 存储输出方式的索引值
  @HiveField(7)
  int get selectedOutputIndex => selectedOutput.index;
  set selectedOutputIndex(int value) =>
      selectedOutput = Dx5OutputType.values[value];

  // 耳放输出开关
  @HiveField(8)
  bool headphoneEnabled;

  // 耳放增益等级 ('高增益', '低增益')
  Dx5HeadphoneGainType headphoneGainLevel;

  // 存储耳放增益等级的索引值
  @HiveField(9)
  int get headphoneGainLevelIndex => headphoneGainLevel.index;
  set headphoneGainLevelIndex(int value) =>
      headphoneGainLevel = Dx5HeadphoneGainType.values[value];

  // 显示界面(VU,FFT)
  Dx5DisplayType displayType;

  // 存储显示界面的索引值
  @HiveField(10)
  int get displayTypeIndex => displayType.index;
  set displayTypeIndex(int value) => displayType = Dx5DisplayType.values[value];

  /// 高级设置

  // 主机主题(极光，橙色，秘鲁色，豆绿色，深卡其色，玫瑰棕色，蓝色，幻紫色，白色)
  @HiveField(11)
  Dx5ThemeType theme;

  //开关机触发(信号，12V，关闭)
  @HiveField(12)
  Dx5PowerTriggerType powerTrigger;

  // 声道平衡(L9.5-C-R9.5)
  @HiveField(13)
  int channelBalance;

  // 滤波器(F1-F8)
  @HiveField(14)
  Dx5FilterParameterType filter;

  // 解码模式(前缀，DAC)
  @HiveField(15)
  Dx5DecodeModeType decodeMode;

  // 音频蓝牙
  @HiveField(16)
  bool audioBluetooth;

  // 蓝牙APTX
  @HiveField(17)
  bool bluetoothAPTX;

  // 遥控器
  @HiveField(18)
  bool remoteControl;

  // 多功能按键（输入选择，线路输出选择，耳放输出选择，主页选择，亮度选择，息屏，PCM滤波器选择，静音）
  @HiveField(19)
  Dx5MultiFunctionKeyType multiFunctionKey;

  // USB(UAC2.0,UAC1.0)
  @HiveField(20)
  Dx5UsbType usbType;

  // 屏幕亮度（H,M,L,AUTO）
  @HiveField(21)
  Dx5ScreenBrightnessType screenBrightness;

  // 语言（中文，英文）
  @HiveField(22)
  Dx5LanguageType language;

  // 恢复出厂设置
  @HiveField(23)
  bool reset;

  // 采样率
  @HiveField(24)
  int sampleRate;

  // 添加PEQ设置字段
  @HiveField(25)
  PEQSettingsModel? peqSettings;

  Dx5DeviceSettings({
    this.id,
    this.deviceId,
    this.title,
    this.power = true,
    this.volume = 0,
    this.mute = false,
    this.selectedInput = Dx5InputType.usb,
    this.selectedOutput = Dx5OutputType.close,
    this.headphoneEnabled = false,
    this.headphoneGainLevel = Dx5HeadphoneGainType.highGain,
    this.displayType = Dx5DisplayType.vu,
    this.theme = Dx5ThemeType.aurora,
    this.powerTrigger = Dx5PowerTriggerType.signal,
    this.channelBalance = 0,
    this.filter = Dx5FilterParameterType.minimumPhase,
    this.decodeMode = Dx5DecodeModeType.prefix,
    this.audioBluetooth = false,
    this.bluetoothAPTX = false,
    this.remoteControl = false,
    this.multiFunctionKey = Dx5MultiFunctionKeyType.inputSelect,
    this.usbType = Dx5UsbType.uac2,
    this.screenBrightness = Dx5ScreenBrightnessType.auto,
    this.language = Dx5LanguageType.zh,
    this.reset = false,
    this.sampleRate =
        0, // Default to 0 instead of 384, will be updated from actual device value
    this.peqSettings,
  });

  // 从JSON创建实例
  factory Dx5DeviceSettings.fromJson(Map<String, dynamic> json) =>
      _$Dx5DeviceSettingsFromJson(json);

  // 转换为JSON
  Map<String, dynamic> toJson() => _$Dx5DeviceSettingsToJson(this);

  // toString方法
  @override
  String toString() {
    return 'Dx5DeviceSettings{'
        'id: $id, '
        'deviceId: $deviceId, '
        'title: $title, '
        'power: $power, '
        'volume: $volume, '
        'mute: $mute, '
        'selectedInput: $selectedInput, '
        'selectedOutput: $selectedOutput, '
        'headphoneEnabled: $headphoneEnabled, '
        'headphoneGainLevel: $headphoneGainLevel, '
        'displayType: $displayType, '
        'theme: $theme, '
        'powerTrigger: $powerTrigger, '
        'channelBalance: $channelBalance, '
        'filter: $filter, '
        'decodeMode: $decodeMode, '
        'audioBluetooth: $audioBluetooth, '
        'bluetoothAPTX: $bluetoothAPTX, '
        'remoteControl: $remoteControl, '
        'multiFunctionKey: $multiFunctionKey, '
        'usbType: $usbType, '
        'screenBrightness: $screenBrightness, '
        'language: $language, '
        'reset: $reset, '
        'sampleRate: $sampleRate, '
        'peqSettings: $peqSettings}';
  }
}
