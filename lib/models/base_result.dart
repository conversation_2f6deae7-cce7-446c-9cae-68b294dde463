/// 通用 API 响应数据封装
///
/// T 是响应数据的类型。
class BaseResult<T> {
  /// 请求是否成功
  final bool success;

  /// 提示信息或错误描述
  final String? message;

  /// errCode 错误码
  final int? code;

  /// 返回的数据
  final T? data;

  BaseResult({
    required this.success,
    this.message,
    this.data,
    this.code,
  });

  /// 手动实现的 fromJson 方法，要求传入一个将动态数据转换为 T 类型的函数
  factory BaseResult.fromJson(
      Map<String, dynamic> json, T Function(dynamic) fromJsonT) {
    return BaseResult(
      success: json['success'] as bool,
      message: json['message'] as String?,
      data: json['data'] != null ? fromJsonT(json['data']) : null,
    );
  }

  /// 手动实现的 toJson 方法，要求传入一个将 T 类型转换为 Map 的函数
  Map<String, dynamic> toJson(Object Function(T value) toJsonT) {
    return {
      'success': success,
      'message': message,
      'data': data != null ? toJsonT(data as T) : null,
    };
  }

  bool isSuccess() => success;

  bool isEmpty() {
    if (data == null) {
      return true;
    } else {
      if (data is List) {
        return (data as List).isEmpty;
      }
      return false;
    }
  }

  bool noMoreData(int pageSize) {
    if (data == null) {
      return true;
    } else {
      if (data is List) {
        return (data as List).length < pageSize;
      }
      return false;
    }
  }
}
