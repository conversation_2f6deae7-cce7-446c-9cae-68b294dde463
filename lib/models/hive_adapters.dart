/// Hive适配器类型ID
class HiveAdapterIds {
  // 基础模型ID
  static const int deviceEntityTypeId = 0;
  static const int deviceTypeId = 2;
  static const int peqBandTypeId = 3;
  static const int peqConfigTypeId = 4;
  static const int importedFileTypeId = 5;
  static const int deviceSettingsTypeId = 8;
  static const int peqSettingsTypeId = 17;

  // DX5设备枚举ID (100-199)
  static const int filterTypeId = 100;
  static const int frequencyTypeId = 101;
  static const int decodeModeTypeId = 102;
  static const int displayTypeId = 103;
  static const int filterParameterTypeId = 104;
  static const int headphoneGainTypeId = 105;
  static const int inputTypeId = 106;
  static const int languageTypeId = 107;
  static const int multiFunctionKeyTypeId = 108;
  static const int outputTypeId = 109;
  static const int powerTriggerTypeId = 110;
  static const int screenBrightnessTypeId = 111;
  static const int themeTypeId = 112;
  static const int usbTypeId = 113;

  // D900设备枚举ID (200-299)
  static const int d900InputTypeId = 200;
  static const int d900OutputTypeId = 201;
  static const int d900UsbSelectTypeId = 202;
  static const int d900UsbTypeId = 203;
  static const int d900UsbDsdPassthroughTypeId = 204;
  static const int d900IisPhaseTypeId = 205;
  static const int d900IisDsdChannelTypeId = 206;
  static const int d900ThemeTypeId = 207;
  static const int d900PowerTriggerTypeId = 208;
  static const int d900ScreenBrightnessTypeId = 209;
  static const int d900LanguageTypeId = 210;
  static const int d900MultiFunctionKeyTypeId = 211;
  static const int d900DisplayTypeId = 212;
  static const int d900DecodeModeTypeId = 213;

  static const int d900DeviceSettingsTypeId = 214;
}
