/// 设备类型实体
class DeviceTypeEntity {
  final String code;
  final String name;

  DeviceTypeEntity({required this.code, required this.name});

  factory DeviceTypeEntity.fromJson(Map<String, dynamic> json) {
    return DeviceTypeEntity(
      code: json['code'] as String,
      name: json['name'] as String,
    );
  }

  Map<String, dynamic> toJson() => {
        'code': code,
        'name': name,
      };
}
