// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'peq_config_model.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class PEQConfigModelAdapter extends TypeAdapter<PEQConfigModel> {
  @override
  final int typeId = 4;

  @override
  PEQConfigModel read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return PEQConfigModel(
      id: fields[0] as String,
      name: fields[1] as String,
      description: fields[2] == null ? '' : fields[2] as String,
      mode: fields[5] == null ? 'all' : fields[5] as String,
      allBands: fields[6] == null
          ? const []
          : (fields[6] as List).cast<PEQBandModel>(),
      leftBands: fields[7] == null
          ? const []
          : (fields[7] as List).cast<PEQBandModel>(),
      rightBands: fields[8] == null
          ? const []
          : (fields[8] as List).cast<PEQBandModel>(),
      targetFile: fields[9] as ImportedFileModel?,
      sourceFRFile: fields[10] as ImportedFileModel?,
      modeBands: (fields[11] as Map?)?.map((dynamic k, dynamic v) =>
          MapEntry(k as String, (v as List).cast<PEQBandModel>())),
      createdAt: fields[3] as DateTime?,
      updatedAt: fields[4] as DateTime?,
    );
  }

  @override
  void write(BinaryWriter writer, PEQConfigModel obj) {
    writer
      ..writeByte(12)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.name)
      ..writeByte(2)
      ..write(obj.description)
      ..writeByte(3)
      ..write(obj.createdAt)
      ..writeByte(4)
      ..write(obj.updatedAt)
      ..writeByte(5)
      ..write(obj.mode)
      ..writeByte(6)
      ..write(obj.allBands)
      ..writeByte(7)
      ..write(obj.leftBands)
      ..writeByte(8)
      ..write(obj.rightBands)
      ..writeByte(9)
      ..write(obj.targetFile)
      ..writeByte(10)
      ..write(obj.sourceFRFile)
      ..writeByte(11)
      ..write(obj.modeBands);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is PEQConfigModelAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

PEQConfigModel _$PEQConfigModelFromJson(Map<String, dynamic> json) =>
    PEQConfigModel(
      id: json['id'] as String,
      name: json['name'] as String,
      description: json['description'] as String? ?? '',
      mode: json['mode'] as String? ?? 'all',
      allBands: (json['allBands'] as List<dynamic>?)
              ?.map((e) => PEQBandModel.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
      leftBands: (json['leftBands'] as List<dynamic>?)
              ?.map((e) => PEQBandModel.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
      rightBands: (json['rightBands'] as List<dynamic>?)
              ?.map((e) => PEQBandModel.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
      targetFile: json['targetFile'] == null
          ? null
          : ImportedFileModel.fromJson(
              json['targetFile'] as Map<String, dynamic>),
      sourceFRFile: json['sourceFRFile'] == null
          ? null
          : ImportedFileModel.fromJson(
              json['sourceFRFile'] as Map<String, dynamic>),
      modeBands: (json['modeBands'] as Map<String, dynamic>?)?.map(
        (k, e) => MapEntry(
            k,
            (e as List<dynamic>)
                .map((e) => PEQBandModel.fromJson(e as Map<String, dynamic>))
                .toList()),
      ),
      createdAt: json['createdAt'] == null
          ? null
          : DateTime.parse(json['createdAt'] as String),
      updatedAt: json['updatedAt'] == null
          ? null
          : DateTime.parse(json['updatedAt'] as String),
    );

Map<String, dynamic> _$PEQConfigModelToJson(PEQConfigModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'description': instance.description,
      'createdAt': instance.createdAt.toIso8601String(),
      'updatedAt': instance.updatedAt.toIso8601String(),
      'mode': instance.mode,
      'allBands': instance.allBands,
      'leftBands': instance.leftBands,
      'rightBands': instance.rightBands,
      'targetFile': instance.targetFile,
      'sourceFRFile': instance.sourceFRFile,
      'modeBands': instance.modeBands,
    };
