import 'package:hive_ce/hive.dart';
import 'package:json_annotation/json_annotation.dart';

import '../business/peq/core/models/imported_file.dart';
import 'hive_adapters.dart';

part 'imported_file_model.g.dart';

/// 用于保存到Hive的导入文件模型
@JsonSerializable()
@HiveType(typeId: HiveAdapterIds.importedFileTypeId)
class ImportedFileModel extends HiveObject {

  @HiveField(0)
  final String id;

  @HiveField(1)
  final String name;

  @HiveField(2)
  final List<Map<String, double>> data;

  @HiveField(3)
  final String type;

  @HiveField(4)
  final DateTime importTime;

  ImportedFileModel({
    required this.id,
    required this.name,
    required this.data,
    required this.type,
    required this.importTime,
  });

  // 从ImportedFile转换
  factory ImportedFileModel.fromImportedFile(ImportedFile file) {
    return ImportedFileModel(
      id: file.id,
      name: file.name,
      data: file.data,
      type: file.type,
      importTime: file.importTime,
    );
  }

  // 转回ImportedFile
  ImportedFile toImportedFile() {
    return ImportedFile(
      id: id,
      name: name,
      data: data,
      type: type,
      importTime: importTime,
    );
  }

  // 从JSON创建实例
  factory ImportedFileModel.fromJson(Map<String, dynamic> json) =>
      _$ImportedFileModelFromJson(json);

  // 转换为JSON
  Map<String, dynamic> toJson() => _$ImportedFileModelToJson(this);

  // toString
  @override
  String toString() {
    return 'ImportedFileModel{id: $id, name: $name, data: $data, type: $type, importTime: $importTime}';
  }

  /// 创建当前对象的副本
  ImportedFileModel copy() {
    return ImportedFileModel(
      id: id,
      name: name,
      data: List.from(data.map((item) => Map<String, double>.from(item))),
      type: type,
      importTime: importTime,
    );
  }
}
