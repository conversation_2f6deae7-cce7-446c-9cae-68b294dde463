// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'peq_band_model.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class PEQBandModelAdapter extends TypeAdapter<PEQBandModel> {
  @override
  final int typeId = 3;

  @override
  PEQBandModel read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return PEQBandModel(
      id: (fields[0] as num).toInt(),
      enabled: fields[1] as bool,
      gain: (fields[2] as num).toDouble(),
      frequency: (fields[3] as num).toDouble(),
      filterType: fields[4] as Dx5FilterType,
      q: (fields[5] as num).toDouble(),
      name: fields[6] as String,
    );
  }

  @override
  void write(BinaryWriter writer, PEQBandModel obj) {
    writer
      ..writeByte(7)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.enabled)
      ..writeByte(2)
      ..write(obj.gain)
      ..writeByte(3)
      ..write(obj.frequency)
      ..writeByte(4)
      ..write(obj.filterType)
      ..writeByte(5)
      ..write(obj.q)
      ..writeByte(6)
      ..write(obj.name);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is PEQBandModelAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

PEQBandModel _$PEQBandModelFromJson(Map<String, dynamic> json) => PEQBandModel(
      id: (json['id'] as num).toInt(),
      enabled: json['enabled'] as bool,
      gain: (json['gain'] as num).toDouble(),
      frequency: (json['frequency'] as num).toDouble(),
      filterType: $enumDecode(_$Dx5FilterTypeEnumMap, json['filterType']),
      q: (json['q'] as num).toDouble(),
      name: json['name'] as String,
    );

Map<String, dynamic> _$PEQBandModelToJson(PEQBandModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'enabled': instance.enabled,
      'gain': instance.gain,
      'frequency': instance.frequency,
      'filterType': _$Dx5FilterTypeEnumMap[instance.filterType]!,
      'q': instance.q,
      'name': instance.name,
    };

const _$Dx5FilterTypeEnumMap = {
  Dx5FilterType.peakingFilter: 'peakingFilter',
  Dx5FilterType.lowPassFilter: 'lowPassFilter',
  Dx5FilterType.highPassFilter: 'highPassFilter',
  Dx5FilterType.lowShelfFilter: 'lowShelfFilter',
  Dx5FilterType.highShelfFilter: 'highShelfFilter',
};
