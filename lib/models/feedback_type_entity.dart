/// 反馈类型实体
class FeedbackTypeEntity {
  final int id;
  final String itemName;
  final String itemValue;

  FeedbackTypeEntity(
      {required this.id, required this.itemName, required this.itemValue});

  factory FeedbackTypeEntity.fromJson(Map<String, dynamic> json) {
    return FeedbackTypeEntity(
      id: json['id'] as int,
      itemName: json['itemName'] as String,
      itemValue: json['itemValue'] as String,
    );
  }

  Map<String, dynamic> toJson() => {
        'id': id,
        'itemName': itemName,
        'itemValue': itemValue,
      };
}
