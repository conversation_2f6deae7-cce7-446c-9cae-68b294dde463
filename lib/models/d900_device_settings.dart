import 'package:json_annotation/json_annotation.dart';
import 'package:hive_ce/hive.dart';
import 'package:topping_ble_control/model/d900/d900_settings.dart';
import 'package:topping_ble_control/utils/log_util.dart';

import '../enums/d900/d900_display_type.dart';
import '../enums/d900/d900_iis_dsd_channel_type.dart';
import '../enums/d900/d900_iis_phase_type.dart';
import '../enums/d900/d900_input_type.dart';
import '../enums/d900/d900_language_type.dart';
import '../enums/d900/d900_multi_function_key_type.dart';
import '../enums/d900/d900_output_type.dart';
import '../enums/d900/d900_power_trigger_type.dart';
import '../enums/d900/d900_screen_brightness_type.dart';
import '../enums/d900/d900_theme_type.dart';
import '../enums/d900/d900_usb_select_type.dart';
import '../enums/d900/d900_usb_type.dart';
import 'hive_adapters.dart';
import 'peq_settings_model.dart';

part 'd900_device_settings.g.dart';

/// D900设备设置模型
@JsonSerializable()
@HiveType(typeId: HiveAdapterIds.d900DeviceSettingsTypeId)
class D900DeviceSettings extends HiveObject {
  // 设置ID
  @HiveField(0)
  String? id;

  // 设备ID
  @HiveField(1)
  String? deviceId;

  // 设置标题
  @HiveField(2)
  String? title;

  // 电源开关
  @HiveField(3)
  bool power;

  // 音量(-99--8)dB
  @HiveField(4)
  int volume;

  // 静音
  @HiveField(5)
  bool mute;

  // 当前选择的输入源
  D900InputType selectedInput;

  // 存储输入源的索引值
  @HiveField(6)
  int get selectedInputIndex => selectedInput.index;
  set selectedInputIndex(int value) =>
      selectedInput = D900InputType.values[value];

  // 当前选择的输出方式(DAC, 前级, 全部)
  D900OutputType selectedOutput;

  // 存储输出方式的索引值
  @HiveField(7)
  int get selectedOutputIndex => selectedOutput.index;
  set selectedOutputIndex(int value) =>
      selectedOutput = D900OutputType.values[value];

  // 显示界面(信号, 12V, 关闭)
  D900DisplayType displayType;

  // 存储显示界面的索引值
  @HiveField(8)
  int get displayTypeIndex => displayType.index;
  set displayTypeIndex(int value) =>
      displayType = D900DisplayType.values[value];

  /// 高级设置

  // 主机主题(极光，橙色，秘鲁色，豆绿色，深卡其色，玫瑰棕色，蓝色，幻紫色，白色)
  @HiveField(9)
  D900ThemeType theme;

  // 开关机触发(信号，12V，关闭)
  @HiveField(10)
  D900PowerTriggerType powerTrigger;

  // 声道平衡(L9.5-C-R9.5)
  @HiveField(11)
  int channelBalance;

  // USB选择(Type-C, Type-B, 自动)
  @HiveField(12)
  D900UsbSelectType usbSelect;

  // 音频蓝牙
  @HiveField(13)
  bool audioBluetooth;

  // 蓝牙APTX
  @HiveField(14)
  bool bluetoothAPTX;

  // 遥控器
  @HiveField(15)
  bool remoteControl;

  // 多功能按键(输入选择, 输出选择, 主页选择, 亮度选择, 息屏, PEQ选择, 静音)
  @HiveField(16)
  D900MultiFunctionKeyType multiFunctionKey;

  // USB DSD直通(启用, 禁用)
  @HiveField(17)
  bool usbDsdPassthrough;

  // USB(UAC2.0, UAC1.0)
  @HiveField(18)
  D900UsbType usbType;

  // IIS相位(标准, 反向)
  @HiveField(19)
  D900IisPhaseType iisPhase;

  // IIS DSD通道(标准, 交换)
  @HiveField(20)
  D900IisDsdChannelType iisDsdChannel;

  // 屏幕亮度(H, M, L, AUTO)
  @HiveField(21)
  D900ScreenBrightnessType screenBrightness;

  // 语言(中文, 英文)
  @HiveField(22)
  D900LanguageType language;

  // 恢复出厂设置
  @HiveField(23)
  bool reset;

  // 采样率
  @HiveField(24)
  int sampleRate;

  // 获取采样率显示文本
  String get sampleRateDisplayText {
    // 使用D900Settings类的转换函数
    Log.i("D900DeviceSettings: 获取采样率显示文本, 原始值: $sampleRate");

    // 当采样率为0(错误)时，返回暂无信息而不是"错误"
    if (sampleRate == 0) {
      Log.i("D900DeviceSettings: 采样率为0，显示'--'");
      return "--";
    }

    String result = D900Settings.convertSamplingRateToDisplayText(sampleRate);
    Log.i("D900DeviceSettings: 采样率显示结果: '$result'");
    return result;
  }

  // PEQ设置
  @HiveField(25)
  PEQSettingsModel? peqSettings;

  D900DeviceSettings({
    this.id,
    this.deviceId,
    this.title,
    this.power = true,
    this.volume = 0,
    this.mute = false,
    this.selectedInput = D900InputType.usb,
    this.selectedOutput = D900OutputType.dac,
    this.displayType = D900DisplayType.vu,
    this.theme = D900ThemeType.aurora,
    this.powerTrigger = D900PowerTriggerType.signal,
    this.channelBalance = 0,
    this.usbSelect = D900UsbSelectType.auto,
    this.audioBluetooth = false,
    this.bluetoothAPTX = false,
    this.remoteControl = false,
    this.multiFunctionKey = D900MultiFunctionKeyType.inputSelect,
    this.usbDsdPassthrough = false,
    this.usbType = D900UsbType.uac2,
    this.iisPhase = D900IisPhaseType.standard,
    this.iisDsdChannel = D900IisDsdChannelType.standard,
    this.screenBrightness = D900ScreenBrightnessType.auto,
    this.language = D900LanguageType.zh,
    this.reset = false,
    this.sampleRate =
        0, // Default to 0 instead of 384, will be updated from actual device value
    this.peqSettings,
  });

  // 从JSON创建实例
  factory D900DeviceSettings.fromJson(Map<String, dynamic> json) =>
      _$D900DeviceSettingsFromJson(json);

  // 转换为JSON
  Map<String, dynamic> toJson() => _$D900DeviceSettingsToJson(this);

  // 创建副本
  D900DeviceSettings copy() {
    return D900DeviceSettings(
      id: id,
      deviceId: deviceId,
      title: title,
      power: power,
      volume: volume,
      mute: mute,
      selectedInput: selectedInput,
      selectedOutput: selectedOutput,
      displayType: displayType,
      theme: theme,
      powerTrigger: powerTrigger,
      channelBalance: channelBalance,
      usbSelect: usbSelect,
      audioBluetooth: audioBluetooth,
      bluetoothAPTX: bluetoothAPTX,
      remoteControl: remoteControl,
      multiFunctionKey: multiFunctionKey,
      usbDsdPassthrough: usbDsdPassthrough,
      usbType: usbType,
      iisPhase: iisPhase,
      iisDsdChannel: iisDsdChannel,
      screenBrightness: screenBrightness,
      language: language,
      reset: reset,
      sampleRate: sampleRate,
      peqSettings: peqSettings?.copyWith(),
    );
  }
}
