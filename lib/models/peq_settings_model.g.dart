// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'peq_settings_model.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class PEQSettingsModelAdapter extends TypeAdapter<PEQSettingsModel> {
  @override
  final int typeId = 17;

  @override
  PEQSettingsModel read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return PEQSettingsModel(
      isEnabled: fields[0] as bool,
      peqMode: fields[1] as String,
      activeDisplayModes: (fields[2] as List).cast<String>(),
      isCompensatedMode: fields[3] as bool,
      activeConfigId: fields[15] as String,
      peqConfigs: (fields[16] as List).cast<PEQConfigModel>(),
      preampGain: (fields[7] as num).toInt(),
      selectedBandId: (fields[8] as num).toInt(),
      currentEditParam: fields[9] as String,
      selectedTargetFile: fields[10] as ImportedFileModel?,
      selectedSourceFRFile: fields[11] as ImportedFileModel?,
      targetFiles: (fields[12] as List).cast<ImportedFileModel>(),
      sourceFRFiles: (fields[13] as List).cast<ImportedFileModel>(),
    );
  }

  @override
  void write(BinaryWriter writer, PEQSettingsModel obj) {
    writer
      ..writeByte(13)
      ..writeByte(0)
      ..write(obj.isEnabled)
      ..writeByte(1)
      ..write(obj.peqMode)
      ..writeByte(2)
      ..write(obj.activeDisplayModes)
      ..writeByte(3)
      ..write(obj.isCompensatedMode)
      ..writeByte(7)
      ..write(obj.preampGain)
      ..writeByte(8)
      ..write(obj.selectedBandId)
      ..writeByte(9)
      ..write(obj.currentEditParam)
      ..writeByte(10)
      ..write(obj.selectedTargetFile)
      ..writeByte(11)
      ..write(obj.selectedSourceFRFile)
      ..writeByte(12)
      ..write(obj.targetFiles)
      ..writeByte(13)
      ..write(obj.sourceFRFiles)
      ..writeByte(15)
      ..write(obj.activeConfigId)
      ..writeByte(16)
      ..write(obj.peqConfigs);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is PEQSettingsModelAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

PEQSettingsModel _$PEQSettingsModelFromJson(Map<String, dynamic> json) =>
    PEQSettingsModel(
      isEnabled: json['isEnabled'] as bool,
      peqMode: json['peqMode'] as String,
      activeDisplayModes: (json['activeDisplayModes'] as List<dynamic>)
          .map((e) => e as String)
          .toList(),
      isCompensatedMode: json['isCompensatedMode'] as bool,
      activeConfigId: json['activeConfigId'] as String,
      peqConfigs: (json['peqConfigs'] as List<dynamic>)
          .map((e) => PEQConfigModel.fromJson(e as Map<String, dynamic>))
          .toList(),
      preampGain: (json['preampGain'] as num).toInt(),
      selectedBandId: (json['selectedBandId'] as num).toInt(),
      currentEditParam: json['currentEditParam'] as String,
      selectedTargetFile: json['selectedTargetFile'] == null
          ? null
          : ImportedFileModel.fromJson(
              json['selectedTargetFile'] as Map<String, dynamic>),
      selectedSourceFRFile: json['selectedSourceFRFile'] == null
          ? null
          : ImportedFileModel.fromJson(
              json['selectedSourceFRFile'] as Map<String, dynamic>),
      targetFiles: (json['targetFiles'] as List<dynamic>)
          .map((e) => ImportedFileModel.fromJson(e as Map<String, dynamic>))
          .toList(),
      sourceFRFiles: (json['sourceFRFiles'] as List<dynamic>)
          .map((e) => ImportedFileModel.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$PEQSettingsModelToJson(PEQSettingsModel instance) =>
    <String, dynamic>{
      'isEnabled': instance.isEnabled,
      'peqMode': instance.peqMode,
      'activeDisplayModes': instance.activeDisplayModes,
      'isCompensatedMode': instance.isCompensatedMode,
      'activeConfigId': instance.activeConfigId,
      'peqConfigs': instance.peqConfigs,
      'preampGain': instance.preampGain,
      'selectedBandId': instance.selectedBandId,
      'currentEditParam': instance.currentEditParam,
      'selectedTargetFile': instance.selectedTargetFile,
      'selectedSourceFRFile': instance.selectedSourceFRFile,
      'targetFiles': instance.targetFiles,
      'sourceFRFiles': instance.sourceFRFiles,
    };
