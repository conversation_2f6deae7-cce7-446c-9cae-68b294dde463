// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'device_entity.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class DeviceEntityAdapter extends TypeAdapter<DeviceEntity> {
  @override
  final int typeId = 0;

  @override
  DeviceEntity read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return DeviceEntity(
      fields[0] as String,
      fields[1] as String,
      fields[2] as String,
      fields[3] as String,
      connected: (fields[6] as num?)?.toInt(),
      deviceModel: fields[5] == null ? '' : fields[5] as String,
    )..lastConnectedState = fields[4] as bool?;
  }

  @override
  void write(BinaryWriter writer, DeviceEntity obj) {
    writer
      ..writeByte(7)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.name)
      ..writeByte(2)
      ..write(obj.image)
      ..writeByte(3)
      ..write(obj.macAddress)
      ..writeByte(4)
      ..write(obj.lastConnectedState)
      ..writeByte(5)
      ..write(obj.deviceModel)
      ..writeByte(6)
      ..write(obj.connected);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is DeviceEntityAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

DeviceEntity _$DeviceEntityFromJson(Map<String, dynamic> json) => DeviceEntity(
      json['id'] as String,
      json['name'] as String,
      json['image'] as String,
      json['macAddress'] as String,
      connected: (json['connected'] as num?)?.toInt(),
      deviceModel: json['deviceModel'] as String? ?? '',
    )
      ..lastConnectedState = json['lastConnectedState'] as bool?
      ..bleDeviceId = (json['bleDeviceId'] as num?)?.toInt()
      ..rssi = (json['rssi'] as num?)?.toInt();

Map<String, dynamic> _$DeviceEntityToJson(DeviceEntity instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'image': instance.image,
      'macAddress': instance.macAddress,
      'deviceModel': instance.deviceModel,
      'lastConnectedState': instance.lastConnectedState,
      'connected': instance.connected,
      'bleDeviceId': instance.bleDeviceId,
      'rssi': instance.rssi,
    };
