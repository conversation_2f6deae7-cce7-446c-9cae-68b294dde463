import 'package:hive_ce/hive.dart';
import 'package:json_annotation/json_annotation.dart';
import 'package:topping_home/models/peq_band_model.dart';

import '../business/peq/core/models/peq_modes.dart';
import 'hive_adapters.dart';
import 'imported_file_model.dart';
import 'peq_config_model.dart';

part 'peq_settings_model.g.dart';

/// PEQ设置模型
@JsonSerializable()
@HiveType(typeId: HiveAdapterIds.peqSettingsTypeId)
class PEQSettingsModel extends HiveObject {

  @HiveField(0)
  final bool isEnabled;

  @HiveField(1)
  final String peqMode;

  @HiveField(2)
  final List<String> activeDisplayModes;

  @HiveField(3)
  final bool isCompensatedMode;

  // 当前选中的配置ID
  @HiveField(15)
  final String activeConfigId;

  // 所有PEQ配置集合
  @HiveField(16)
  final List<PEQConfigModel> peqConfigs;

  @HiveField(7)
  final int preampGain;

  @HiveField(8)
  final int selectedBandId;

  @HiveField(9)
  final String currentEditParam;

  @HiveField(10)
  final ImportedFileModel? selectedTargetFile;

  @HiveField(11)
  final ImportedFileModel? selectedSourceFRFile;

  @HiveField(12)
  final List<ImportedFileModel> targetFiles;

  @HiveField(13)
  final List<ImportedFileModel> sourceFRFiles;

  PEQSettingsModel({
    required this.isEnabled,
    required this.peqMode,
    required this.activeDisplayModes,
    required this.isCompensatedMode,
    required this.activeConfigId,
    required this.peqConfigs,
    required this.preampGain,
    required this.selectedBandId,
    required this.currentEditParam,
    this.selectedTargetFile,
    this.selectedSourceFRFile,
    required this.targetFiles,
    required this.sourceFRFiles,
  });

  // 便捷方法获取当前活跃配置
  PEQConfigModel? get activeConfig {
    return peqConfigs.isNotEmpty
        ? peqConfigs.firstWhere((config) => config.id == activeConfigId,
            orElse: () => peqConfigs.first)
        : null;
  }

  // 便捷方法从配置中获取特定模式的波段
  List<PEQBandModel> getBandsForMode(String mode) {
    final config = activeConfig;
    if (config == null) return [];
    return config.modeBands[mode] ?? [];
  }

  // 从JSON创建实例
  factory PEQSettingsModel.fromJson(Map<String, dynamic> json) =>
      _$PEQSettingsModelFromJson(json);

  // 转换为JSON
  Map<String, dynamic> toJson() => _$PEQSettingsModelToJson(this);

  // toString方法
  @override
  String toString() {
    return 'PEQSettingsModel{isEnabled: $isEnabled, peqMode: $peqMode, activeDisplayModes: $activeDisplayModes, isCompensatedMode: $isCompensatedMode, activeConfigId: $activeConfigId, peqConfigs: $peqConfigs, preampGain: $preampGain, selectedBandId: $selectedBandId, currentEditParam: $currentEditParam, selectedTargetFile: $selectedTargetFile, selectedSourceFRFile: $selectedSourceFRFile, targetFiles: $targetFiles, sourceFRFiles: $sourceFRFiles}';
  }

  PEQSettingsModel copyWith({
    bool? isEnabled,
    String? peqMode,
    List<String>? activeDisplayModes,
    bool? isCompensatedMode,
    String? activeConfigId,
    List<PEQConfigModel>? peqConfigs,
    int? preampGain,
    int? selectedBandId,
    String? currentEditParam,
    ImportedFileModel? selectedTargetFile,
    ImportedFileModel? selectedSourceFRFile,
    List<ImportedFileModel>? targetFiles,
    List<ImportedFileModel>? sourceFRFiles,
  }) {
    return PEQSettingsModel(
      isEnabled: isEnabled ?? this.isEnabled,
      peqMode: peqMode ?? this.peqMode,
      activeDisplayModes:
          activeDisplayModes ?? List.from(this.activeDisplayModes),
      isCompensatedMode: isCompensatedMode ?? this.isCompensatedMode,
      activeConfigId: activeConfigId ?? this.activeConfigId,
      peqConfigs: peqConfigs ?? List.from(this.peqConfigs),
      preampGain: preampGain ?? this.preampGain,
      selectedBandId: selectedBandId ?? this.selectedBandId,
      currentEditParam: currentEditParam ?? this.currentEditParam,
      selectedTargetFile: selectedTargetFile ?? this.selectedTargetFile,
      selectedSourceFRFile: selectedSourceFRFile ?? this.selectedSourceFRFile,
      targetFiles: targetFiles ?? List.from(this.targetFiles),
      sourceFRFiles: sourceFRFiles ?? List.from(this.sourceFRFiles),
    );
  }

  factory PEQSettingsModel.createDefault() {
    final defaultConfig = PEQConfigModel.createDefault('default');

    return PEQSettingsModel(
      isEnabled: false,
      peqMode: PEQModes.allChannels,
      activeDisplayModes: ['合成响应'],
      isCompensatedMode: false,
      activeConfigId: defaultConfig.id,
      peqConfigs: [defaultConfig],
      preampGain: 0,
      selectedBandId: -1,
      currentEditParam: 'frequency',
      targetFiles: [],
      sourceFRFiles: [],
    );
  }

}
