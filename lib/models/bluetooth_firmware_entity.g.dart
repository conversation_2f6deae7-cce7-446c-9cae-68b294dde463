// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'bluetooth_firmware_entity.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

BluetoothFirmwareEntity _$BluetoothFirmwareEntityFromJson(
        Map<String, dynamic> json) =>
    BluetoothFirmwareEntity(
      id: (json['id'] as num).toInt(),
      firmwareName: json['firmwareName'] as String,
      deviceModel: json['deviceModel'] as String,
      versionCode: (json['versionCode'] as num).toInt(),
      versionName: json['versionName'] as String,
      fileUrl: json['fileUrl'] as String,
      fileSize: (json['fileSize'] as num).toInt(),
      fileMd5: json['fileMd5'] as String,
      forceUpdate: json['forceUpdate'] as bool? ?? true,
      description: json['description'] as String,
      createTime: DateTime.parse(json['createTime'] as String),
    );

Map<String, dynamic> _$BluetoothFirmwareEntityToJson(
        BluetoothFirmwareEntity instance) =>
    <String, dynamic>{
      'id': instance.id,
      'firmwareName': instance.firmwareName,
      'deviceModel': instance.deviceModel,
      'versionCode': instance.versionCode,
      'versionName': instance.versionName,
      'fileUrl': instance.fileUrl,
      'fileSize': instance.fileSize,
      'fileMd5': instance.fileMd5,
      'forceUpdate': instance.forceUpdate,
      'description': instance.description,
      'createTime': instance.createTime.toIso8601String(),
    };
