import 'package:json_annotation/json_annotation.dart';

part 'bluetooth_firmware_entity.g.dart';

/// 蓝牙固件信息
@JsonSerializable()
class BluetoothFirmwareEntity {
  final int id;
  final String firmwareName;
  final String deviceModel;
  final int versionCode;
  final String versionName;
  final String fileUrl;
  final int fileSize;
  final String fileMd5;
  @JsonKey(defaultValue: true)
  final bool forceUpdate;
  final String description;
  final DateTime createTime;

  BluetoothFirmwareEntity({
    required this.id,
    required this.firmwareName,
    required this.deviceModel,
    required this.versionCode,
    required this.versionName,
    required this.fileUrl,
    required this.fileSize,
    required this.fileMd5,
    required this.forceUpdate,
    required this.description,
    required this.createTime,
  });

  factory BluetoothFirmwareEntity.fromJson(Map<String, dynamic> json) =>
      _$BluetoothFirmwareEntityFromJson(json);

  Map<String, dynamic> toJson() => _$BluetoothFirmwareEntityToJson(this);
}
