import 'package:hive_ce/hive.dart';
import 'package:json_annotation/json_annotation.dart';

import 'hive_adapters.dart';

part 'device_entity.g.dart';

/// 设备实体类
/// 用于存储设备的基本信息和操作说明
@JsonSerializable()
@HiveType(typeId: HiveAdapterIds.deviceEntityTypeId)
class DeviceEntity extends HiveObject {
  /// 设备唯一标识符
  @HiveField(0)
  String id;

  /// 设备名称
  @HiveField(1)
  String name;

  /// 设备图片路径
  @HiveField(2)
  String image;

  /// 设备MAC地址 - 关键的持久化标识符
  @HiveField(3)
  String macAddress;

  /// 设备型号
  @HiveField(5)
  String deviceModel;

  /// 上次连接状态
  @HiveField(4)
  bool? lastConnectedState;

  /// 连接状态
  /// 0: 未连接
  /// 1: 已连接
  @HiveField(6)
  int? connected;

  // BLE 设备 ID (句柄)
  // 这个字段不需要 HiveField 注解，不会被持久化
  // 但每次启动应用时会根据 MAC 地址重新生成
  int? bleDeviceId;

  // 信号强度，用于排序设备
  // 这个字段不需要持久化，只在扫描时使用
  int? rssi;

  /// 是否已连接的便捷getter
  bool get isConnected => connected == 1;

  /// 默认构造函数
  DeviceEntity(this.id, this.name, this.image, this.macAddress,
      {this.connected, this.deviceModel = ''});

  /// 命名构造函数
  /// 用于创建包含完整信息的设备实体
  DeviceEntity.named({
    required this.id,
    required this.name,
    required this.image,
    this.connected,
    required this.macAddress,
    this.deviceModel = '',
    this.bleDeviceId = 0,
    this.rssi = 0,
  });

  /// 无参数构造函数，用于级联操作
  DeviceEntity.empty()
      : id = '',
        name = '',
        image = '',
        macAddress = '',
        deviceModel = '';

  /// 深拷贝方法 - 创建设备的完整副本
  DeviceEntity copyWith({
    String? id,
    String? name,
    String? image,
    String? macAddress,
    String? deviceModel,
    bool? lastConnectedState,
    int? connected,
    int? bleDeviceId,
  }) {
    final device = DeviceEntity(
      id ?? this.id,
      name ?? this.name,
      image ?? this.image,
      macAddress ?? this.macAddress,
      connected: connected ?? this.connected,
      deviceModel: deviceModel ?? this.deviceModel,
    );

    device.lastConnectedState = lastConnectedState ?? this.lastConnectedState;
    device.bleDeviceId = bleDeviceId ?? this.bleDeviceId;

    return device;
  }

  /// 从JSON创建实例
  factory DeviceEntity.fromJson(Map<String, dynamic> json) =>
      _$DeviceEntityFromJson(json);

  /// 转换为JSON
  Map<String, dynamic> toJson() => _$DeviceEntityToJson(this);

  /// toString
  @override
  String toString() {
    return 'DeviceEntity{id: $id, name: $name, MAC: $macAddress, model: $deviceModel, connected: $connected, bleDeviceId: $bleDeviceId}';
  }
}
