import 'package:json_annotation/json_annotation.dart';

part 'feedback_entity.g.dart';

/// 用户反馈实体类
@JsonSerializable()
class FeedbackEntity {
  final int? id;
  final int userId;
  final String deviceCode;
  final String deviceName;
  final int feedbackTypeId;
  final String feedbackTypeName;
  final String content;
  final String? contactInfo;
  final List<String> imagePaths;

  FeedbackEntity({
    this.id,
    required this.userId,
    required this.deviceCode,
    required this.deviceName,
    required this.feedbackTypeId,
    required this.feedbackTypeName,
    required this.content,
    this.contactInfo,
    this.imagePaths = const [],
  });

  factory FeedbackEntity.fromJson(Map<String, dynamic> json) =>
      _$FeedbackEntityFromJson(json);

  Map<String, dynamic> toJson() => _$FeedbackEntityToJson(this);
}
