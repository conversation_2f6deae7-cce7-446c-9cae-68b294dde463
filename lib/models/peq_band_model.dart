import 'package:hive_ce/hive.dart';
import 'package:json_annotation/json_annotation.dart';

import '../business/peq/core/models/peq_band.dart';
import '../enums/dx5/dx5_filter_type.dart';
import 'hive_adapters.dart';

part 'peq_band_model.g.dart';

/// 用于保存到Hive的PEQ波段模型
@JsonSerializable()
@HiveType(typeId: HiveAdapterIds.peqBandTypeId)
class PEQBandModel extends HiveObject {

  @HiveField(0)
  final int id;

  @HiveField(1)
  final bool enabled;

  @HiveField(2)
  final double gain;

  @HiveField(3)
  final double frequency;

  @HiveField(4)
  final Dx5FilterType filterType;

  @HiveField(5)
  final double q;

  @HiveField(6)
  final String name;

  PEQBandModel({
    required this.id,
    required this.enabled,
    required this.gain,
    required this.frequency,
    required this.filterType,
    required this.q,
    required this.name,
  });

  // 从PEQBand转换
  factory PEQBandModel.fromPEQBand(PEQBand band) {
    return PEQBandModel(
      id: band.id,
      enabled: band.enabled.value,
      gain: band.gain.value,
      frequency: band.frequency.value,
      filterType: band.filterType.value,
      q: band.q.value,
      name: band.name.value,
    );
  }

  // 从JSON创建实例
  factory PEQBandModel.fromJson(Map<String, dynamic> json) =>
      _$PEQBandModelFromJson(json);

  // 转换为JSON
  Map<String, dynamic> toJson() => _$PEQBandModelToJson(this);

  // toString
  @override
  String toString() {
    return 'PEQBandModel{id: $id, enabled: $enabled, gain: $gain, frequency: $frequency, filterType: $filterType, q: $q, name: $name}';
  }

  // PEQBandModel copyWith 方法
  PEQBandModel copyWith({
    bool? enabled,
    double? gain,
    double? frequency,
    Dx5FilterType? filterType,
    double? q,
    String? name,
  }) {
    return PEQBandModel(
      id: id,
      enabled: enabled ?? this.enabled,
      gain: gain ?? this.gain,
      frequency: frequency ?? this.frequency,
      filterType: filterType ?? this.filterType,
      q: q ?? this.q,
      name: name ?? this.name,
    );
  }
}
