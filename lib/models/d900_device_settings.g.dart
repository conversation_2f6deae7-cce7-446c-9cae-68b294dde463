// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'd900_device_settings.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class D900DeviceSettingsAdapter extends TypeAdapter<D900DeviceSettings> {
  @override
  final int typeId = 214;

  @override
  D900DeviceSettings read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return D900DeviceSettings(
      id: fields[0] as String?,
      deviceId: fields[1] as String?,
      title: fields[2] as String?,
      power: fields[3] == null ? true : fields[3] as bool,
      volume: fields[4] == null ? 0 : (fields[4] as num).toInt(),
      mute: fields[5] == null ? false : fields[5] as bool,
      theme:
          fields[9] == null ? D900ThemeType.aurora : fields[9] as D900ThemeType,
      powerTrigger: fields[10] == null
          ? D900PowerTriggerType.signal
          : fields[10] as D900PowerTriggerType,
      channelBalance: fields[11] == null ? 0 : (fields[11] as num).toInt(),
      usbSelect: fields[12] == null
          ? D900UsbSelectType.auto
          : fields[12] as D900UsbSelectType,
      audioBluetooth: fields[13] == null ? false : fields[13] as bool,
      bluetoothAPTX: fields[14] == null ? false : fields[14] as bool,
      remoteControl: fields[15] == null ? false : fields[15] as bool,
      multiFunctionKey: fields[16] == null
          ? D900MultiFunctionKeyType.inputSelect
          : fields[16] as D900MultiFunctionKeyType,
      usbDsdPassthrough: fields[17] == null ? false : fields[17] as bool,
      usbType:
          fields[18] == null ? D900UsbType.uac2 : fields[18] as D900UsbType,
      iisPhase: fields[19] == null
          ? D900IisPhaseType.standard
          : fields[19] as D900IisPhaseType,
      iisDsdChannel: fields[20] == null
          ? D900IisDsdChannelType.standard
          : fields[20] as D900IisDsdChannelType,
      screenBrightness: fields[21] == null
          ? D900ScreenBrightnessType.auto
          : fields[21] as D900ScreenBrightnessType,
      language: fields[22] == null
          ? D900LanguageType.zh
          : fields[22] as D900LanguageType,
      reset: fields[23] == null ? false : fields[23] as bool,
      sampleRate: fields[24] == null ? 0 : (fields[24] as num).toInt(),
      peqSettings: fields[25] as PEQSettingsModel?,
    );
  }

  @override
  void write(BinaryWriter writer, D900DeviceSettings obj) {
    writer
      ..writeByte(26)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.deviceId)
      ..writeByte(2)
      ..write(obj.title)
      ..writeByte(3)
      ..write(obj.power)
      ..writeByte(4)
      ..write(obj.volume)
      ..writeByte(5)
      ..write(obj.mute)
      ..writeByte(6)
      ..write(obj.selectedInputIndex)
      ..writeByte(7)
      ..write(obj.selectedOutputIndex)
      ..writeByte(8)
      ..write(obj.displayTypeIndex)
      ..writeByte(9)
      ..write(obj.theme)
      ..writeByte(10)
      ..write(obj.powerTrigger)
      ..writeByte(11)
      ..write(obj.channelBalance)
      ..writeByte(12)
      ..write(obj.usbSelect)
      ..writeByte(13)
      ..write(obj.audioBluetooth)
      ..writeByte(14)
      ..write(obj.bluetoothAPTX)
      ..writeByte(15)
      ..write(obj.remoteControl)
      ..writeByte(16)
      ..write(obj.multiFunctionKey)
      ..writeByte(17)
      ..write(obj.usbDsdPassthrough)
      ..writeByte(18)
      ..write(obj.usbType)
      ..writeByte(19)
      ..write(obj.iisPhase)
      ..writeByte(20)
      ..write(obj.iisDsdChannel)
      ..writeByte(21)
      ..write(obj.screenBrightness)
      ..writeByte(22)
      ..write(obj.language)
      ..writeByte(23)
      ..write(obj.reset)
      ..writeByte(24)
      ..write(obj.sampleRate)
      ..writeByte(25)
      ..write(obj.peqSettings);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is D900DeviceSettingsAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

D900DeviceSettings _$D900DeviceSettingsFromJson(Map<String, dynamic> json) =>
    D900DeviceSettings(
      id: json['id'] as String?,
      deviceId: json['deviceId'] as String?,
      title: json['title'] as String?,
      power: json['power'] as bool? ?? true,
      volume: (json['volume'] as num?)?.toInt() ?? 0,
      mute: json['mute'] as bool? ?? false,
      selectedInput:
          $enumDecodeNullable(_$D900InputTypeEnumMap, json['selectedInput']) ??
              D900InputType.usb,
      selectedOutput: $enumDecodeNullable(
              _$D900OutputTypeEnumMap, json['selectedOutput']) ??
          D900OutputType.dac,
      displayType:
          $enumDecodeNullable(_$D900DisplayTypeEnumMap, json['displayType']) ??
              D900DisplayType.vu,
      theme: $enumDecodeNullable(_$D900ThemeTypeEnumMap, json['theme']) ??
          D900ThemeType.aurora,
      powerTrigger: $enumDecodeNullable(
              _$D900PowerTriggerTypeEnumMap, json['powerTrigger']) ??
          D900PowerTriggerType.signal,
      channelBalance: (json['channelBalance'] as num?)?.toInt() ?? 0,
      usbSelect:
          $enumDecodeNullable(_$D900UsbSelectTypeEnumMap, json['usbSelect']) ??
              D900UsbSelectType.auto,
      audioBluetooth: json['audioBluetooth'] as bool? ?? false,
      bluetoothAPTX: json['bluetoothAPTX'] as bool? ?? false,
      remoteControl: json['remoteControl'] as bool? ?? false,
      multiFunctionKey: $enumDecodeNullable(
              _$D900MultiFunctionKeyTypeEnumMap, json['multiFunctionKey']) ??
          D900MultiFunctionKeyType.inputSelect,
      usbDsdPassthrough: json['usbDsdPassthrough'] as bool? ?? false,
      usbType: $enumDecodeNullable(_$D900UsbTypeEnumMap, json['usbType']) ??
          D900UsbType.uac2,
      iisPhase:
          $enumDecodeNullable(_$D900IisPhaseTypeEnumMap, json['iisPhase']) ??
              D900IisPhaseType.standard,
      iisDsdChannel: $enumDecodeNullable(
              _$D900IisDsdChannelTypeEnumMap, json['iisDsdChannel']) ??
          D900IisDsdChannelType.standard,
      screenBrightness: $enumDecodeNullable(
              _$D900ScreenBrightnessTypeEnumMap, json['screenBrightness']) ??
          D900ScreenBrightnessType.auto,
      language:
          $enumDecodeNullable(_$D900LanguageTypeEnumMap, json['language']) ??
              D900LanguageType.zh,
      reset: json['reset'] as bool? ?? false,
      sampleRate: (json['sampleRate'] as num?)?.toInt() ?? 0,
      peqSettings: json['peqSettings'] == null
          ? null
          : PEQSettingsModel.fromJson(
              json['peqSettings'] as Map<String, dynamic>),
    )
      ..selectedInputIndex = (json['selectedInputIndex'] as num).toInt()
      ..selectedOutputIndex = (json['selectedOutputIndex'] as num).toInt()
      ..displayTypeIndex = (json['displayTypeIndex'] as num).toInt();

Map<String, dynamic> _$D900DeviceSettingsToJson(D900DeviceSettings instance) =>
    <String, dynamic>{
      'id': instance.id,
      'deviceId': instance.deviceId,
      'title': instance.title,
      'power': instance.power,
      'volume': instance.volume,
      'mute': instance.mute,
      'selectedInput': _$D900InputTypeEnumMap[instance.selectedInput]!,
      'selectedInputIndex': instance.selectedInputIndex,
      'selectedOutput': _$D900OutputTypeEnumMap[instance.selectedOutput]!,
      'selectedOutputIndex': instance.selectedOutputIndex,
      'displayType': _$D900DisplayTypeEnumMap[instance.displayType]!,
      'displayTypeIndex': instance.displayTypeIndex,
      'theme': _$D900ThemeTypeEnumMap[instance.theme]!,
      'powerTrigger': _$D900PowerTriggerTypeEnumMap[instance.powerTrigger]!,
      'channelBalance': instance.channelBalance,
      'usbSelect': _$D900UsbSelectTypeEnumMap[instance.usbSelect]!,
      'audioBluetooth': instance.audioBluetooth,
      'bluetoothAPTX': instance.bluetoothAPTX,
      'remoteControl': instance.remoteControl,
      'multiFunctionKey':
          _$D900MultiFunctionKeyTypeEnumMap[instance.multiFunctionKey]!,
      'usbDsdPassthrough': instance.usbDsdPassthrough,
      'usbType': _$D900UsbTypeEnumMap[instance.usbType]!,
      'iisPhase': _$D900IisPhaseTypeEnumMap[instance.iisPhase]!,
      'iisDsdChannel': _$D900IisDsdChannelTypeEnumMap[instance.iisDsdChannel]!,
      'screenBrightness':
          _$D900ScreenBrightnessTypeEnumMap[instance.screenBrightness]!,
      'language': _$D900LanguageTypeEnumMap[instance.language]!,
      'reset': instance.reset,
      'sampleRate': instance.sampleRate,
      'peqSettings': instance.peqSettings,
    };

const _$D900InputTypeEnumMap = {
  D900InputType.usb: 'usb',
  D900InputType.optical1: 'optical1',
  D900InputType.optical2: 'optical2',
  D900InputType.coaxial1: 'coaxial1',
  D900InputType.coaxial2: 'coaxial2',
  D900InputType.aes: 'aes',
  D900InputType.iis: 'iis',
  D900InputType.bluetooth: 'bluetooth',
};

const _$D900OutputTypeEnumMap = {
  D900OutputType.dac: 'dac',
  D900OutputType.preamp: 'preamp',
  D900OutputType.all: 'all',
};

const _$D900DisplayTypeEnumMap = {
  D900DisplayType.normal: 'normal',
  D900DisplayType.vu: 'vu',
  D900DisplayType.fft: 'fft',
};

const _$D900ThemeTypeEnumMap = {
  D900ThemeType.aurora: 'aurora',
  D900ThemeType.orange: 'orange',
  D900ThemeType.peru: 'peru',
  D900ThemeType.green: 'green',
  D900ThemeType.khaki: 'khaki',
  D900ThemeType.rose: 'rose',
  D900ThemeType.blue: 'blue',
  D900ThemeType.purple: 'purple',
  D900ThemeType.white: 'white',
};

const _$D900PowerTriggerTypeEnumMap = {
  D900PowerTriggerType.signal: 'signal',
  D900PowerTriggerType.voltage12V: 'voltage12V',
  D900PowerTriggerType.off: 'off',
};

const _$D900UsbSelectTypeEnumMap = {
  D900UsbSelectType.typeB: 'typeB',
  D900UsbSelectType.typeC: 'typeC',
  D900UsbSelectType.auto: 'auto',
};

const _$D900MultiFunctionKeyTypeEnumMap = {
  D900MultiFunctionKeyType.inputSelect: 'inputSelect',
  D900MultiFunctionKeyType.outputSelect: 'outputSelect',
  D900MultiFunctionKeyType.homeSelect: 'homeSelect',
  D900MultiFunctionKeyType.brightnessSelect: 'brightnessSelect',
  D900MultiFunctionKeyType.screenOff: 'screenOff',
  D900MultiFunctionKeyType.peqSelect: 'peqSelect',
  D900MultiFunctionKeyType.mute: 'mute',
};

const _$D900UsbTypeEnumMap = {
  D900UsbType.uac1: 'uac1',
  D900UsbType.uac2: 'uac2',
};

const _$D900IisPhaseTypeEnumMap = {
  D900IisPhaseType.standard: 'standard',
  D900IisPhaseType.inverted: 'inverted',
};

const _$D900IisDsdChannelTypeEnumMap = {
  D900IisDsdChannelType.standard: 'standard',
  D900IisDsdChannelType.swapped: 'swapped',
};

const _$D900ScreenBrightnessTypeEnumMap = {
  D900ScreenBrightnessType.low: 'low',
  D900ScreenBrightnessType.medium: 'medium',
  D900ScreenBrightnessType.high: 'high',
  D900ScreenBrightnessType.auto: 'auto',
};

const _$D900LanguageTypeEnumMap = {
  D900LanguageType.en: 'en',
  D900LanguageType.zh: 'zh',
};
