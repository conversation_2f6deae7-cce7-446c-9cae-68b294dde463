import 'package:hive_ce/hive.dart';
import 'package:json_annotation/json_annotation.dart';
import 'package:uuid/uuid.dart';

import '../business/peq/core/models/peq_modes.dart';
import 'hive_adapters.dart';
import 'imported_file_model.dart';
import 'peq_band_model.dart';

part 'peq_config_model.g.dart';

/// 单个PEQ配置模型类
@JsonSerializable()
@HiveType(typeId: HiveAdapterIds.peqConfigTypeId)
class PEQConfigModel extends HiveObject {

  // 配置唯一ID
  @HiveField(0)
  final String id;

  // 配置名称
  @HiveField(1)
  final String name;

  // 配置描述
  @HiveField(2)
  final String description;

  // 创建时间
  @HiveField(3)
  final DateTime createdAt;

  // 最后修改时间
  @HiveField(4)
  final DateTime updatedAt;

  // 模式
  @HiveField(5)
  final String mode;

  // 所有声道波段 - 新增字段
  @HiveField(6)
  final List<PEQBandModel> allBands;

  // 左声道波段 - 新增字段
  @HiveField(7)
  final List<PEQBandModel> leftBands;

  // 右声道波段 - 新增字段
  @HiveField(8)
  final List<PEQBandModel> rightBands;

  // 目标响应文件
  @HiveField(9)
  final ImportedFileModel? targetFile;

  // 源FR文件
  @HiveField(10)
  final ImportedFileModel? sourceFRFile;

  // 使用映射表存储不同模式的波段数据，完全分离
  @HiveField(11)
  final Map<String, List<PEQBandModel>> modeBands;

  PEQConfigModel({
    required this.id,
    required this.name,
    this.description = '',
    this.mode = 'all',
    this.allBands = const [],
    this.leftBands = const [],
    this.rightBands = const [],
    this.targetFile,
    this.sourceFRFile,
    Map<String, List<PEQBandModel>>? modeBands,
    DateTime? createdAt,
    DateTime? updatedAt,
  })  : modeBands = modeBands ??
            {
              PEQModes.allChannels: allBands,
              PEQModes.leftChannel: leftBands,
              PEQModes.rightChannel: rightBands,
            },
        createdAt = createdAt ?? DateTime.now(),
        updatedAt = updatedAt ?? DateTime.now();

  /// 从JSON创建实例
  factory PEQConfigModel.fromJson(Map<String, dynamic> json) =>
      _$PEQConfigModelFromJson(json);

  /// 转换为JSON
  Map<String, dynamic> toJson() => _$PEQConfigModelToJson(this);

  /// 创建一个此配置的副本，可选择更新某些字段
  PEQConfigModel copyWith({
    String? id,
    String? name,
    String? description,
    String? mode,
    List<PEQBandModel>? bands,
    List<PEQBandModel>? allBands,
    List<PEQBandModel>? leftBands,
    List<PEQBandModel>? rightBands,
    ImportedFileModel? targetFile,
    ImportedFileModel? sourceFRFile,
    Map<String, List<PEQBandModel>>? modeBands,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return PEQConfigModel(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      mode: mode ?? this.mode,
      allBands: allBands ?? this.allBands,
      leftBands: leftBands ?? this.leftBands,
      rightBands: rightBands ?? this.rightBands,
      targetFile: targetFile ?? this.targetFile,
      sourceFRFile: sourceFRFile ?? this.sourceFRFile,
      modeBands: modeBands ?? Map.from(this.modeBands),
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  /// 创建一个配置的完整副本（深复制）
  PEQConfigModel clone({String? newId, String? newName}) {
    // 深复制波段数据
    final Map<String, List<PEQBandModel>> clonedModeBands = {};

    modeBands.forEach((mode, bands) {
      clonedModeBands[mode] = bands
          .map((band) => PEQBandModel(
                id: band.id,
                enabled: band.enabled,
                gain: band.gain,
                frequency: band.frequency,
                filterType: band.filterType,
                q: band.q,
                name: band.name,
              ))
          .toList();
    });

    return PEQConfigModel(
      id: newId ?? const Uuid().v4(),
      name: newName ?? '$name 副本',
      description: description,
      mode: mode,
      allBands: List.from(allBands),
      leftBands: List.from(leftBands),
      rightBands: List.from(rightBands),
      targetFile: targetFile,
      sourceFRFile: sourceFRFile,
      modeBands: clonedModeBands,
      createdAt: DateTime.now(), // 创建新的时间戳
      updatedAt: DateTime.now(),
    );
  }

  /// 创建默认配置
  static PEQConfigModel createDefault(String name) {
    final emptyBands = <PEQBandModel>[];
    return PEQConfigModel(
      id: const Uuid().v4(),
      name: name,
      description: '默认PEQ配置',
      mode: PEQModes.allChannels,
      allBands: emptyBands,
      leftBands: emptyBands,
      rightBands: emptyBands,
      modeBands: {
        PEQModes.allChannels: emptyBands,
        PEQModes.leftChannel: emptyBands,
        PEQModes.rightChannel: emptyBands,
      },
    );
  }

  /// 创建当前对象的副本
  PEQConfigModel copy() {
    return clone(newId: id, newName: name);
  }
}
