import 'package:json_annotation/json_annotation.dart';

part 'app_version_entity.g.dart';

/// APP版本信息
@JsonSerializable()
class AppVersionEntity {
  final int id;
  final int versionCode;
  final String versionName;
  final String fileUrl;
  final int fileSize;
  final String fileMd5;
  final bool forceUpdate;
  final String description;
  final DateTime createTime;

  AppVersionEntity({
    required this.id,
    required this.versionCode,
    required this.versionName,
    required this.fileUrl,
    required this.fileSize,
    required this.fileMd5,
    required this.forceUpdate,
    required this.description,
    required this.createTime,
  });

  factory AppVersionEntity.fromJson(Map<String, dynamic> json) =>
      _$AppVersionEntityFromJson(json);

  Map<String, dynamic> toJson() => _$AppVersionEntityToJson(this);
}
