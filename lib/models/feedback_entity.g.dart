// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'feedback_entity.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

FeedbackEntity _$FeedbackEntityFromJson(Map<String, dynamic> json) =>
    FeedbackEntity(
      id: (json['id'] as num?)?.toInt(),
      userId: (json['userId'] as num).toInt(),
      deviceCode: json['deviceCode'] as String,
      deviceName: json['deviceName'] as String,
      feedbackTypeId: (json['feedbackTypeId'] as num).toInt(),
      feedbackTypeName: json['feedbackTypeName'] as String,
      content: json['content'] as String,
      contactInfo: json['contactInfo'] as String?,
      imagePaths: (json['imagePaths'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          const [],
    );

Map<String, dynamic> _$FeedbackEntityToJson(FeedbackEntity instance) =>
    <String, dynamic>{
      'id': instance.id,
      'userId': instance.userId,
      'deviceCode': instance.deviceCode,
      'deviceName': instance.deviceName,
      'feedbackTypeId': instance.feedbackTypeId,
      'feedbackTypeName': instance.feedbackTypeName,
      'content': instance.content,
      'contactInfo': instance.contactInfo,
      'imagePaths': instance.imagePaths,
    };
