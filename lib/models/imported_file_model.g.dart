// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'imported_file_model.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class ImportedFileModelAdapter extends TypeAdapter<ImportedFileModel> {
  @override
  final int typeId = 5;

  @override
  ImportedFileModel read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return ImportedFileModel(
      id: fields[0] as String,
      name: fields[1] as String,
      data: (fields[2] as List)
          .map((e) => (e as Map).cast<String, double>())
          .toList(),
      type: fields[3] as String,
      importTime: fields[4] as DateTime,
    );
  }

  @override
  void write(BinaryWriter writer, ImportedFileModel obj) {
    writer
      ..writeByte(5)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.name)
      ..writeByte(2)
      ..write(obj.data)
      ..writeByte(3)
      ..write(obj.type)
      ..writeByte(4)
      ..write(obj.importTime);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is ImportedFileModelAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

ImportedFileModel _$ImportedFileModelFromJson(Map<String, dynamic> json) =>
    ImportedFileModel(
      id: json['id'] as String,
      name: json['name'] as String,
      data: (json['data'] as List<dynamic>)
          .map((e) => (e as Map<String, dynamic>).map(
                (k, e) => MapEntry(k, (e as num).toDouble()),
              ))
          .toList(),
      type: json['type'] as String,
      importTime: DateTime.parse(json['importTime'] as String),
    );

Map<String, dynamic> _$ImportedFileModelToJson(ImportedFileModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'data': instance.data,
      'type': instance.type,
      'importTime': instance.importTime.toIso8601String(),
    };
