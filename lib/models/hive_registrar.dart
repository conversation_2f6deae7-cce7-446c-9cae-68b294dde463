import 'package:hive_ce/hive.dart';
import 'package:topping_home/common/util/log_util.dart';
import 'package:topping_home/enums/d900/d900_display_type.dart';
import 'package:topping_home/enums/d900/d900_iis_dsd_channel_type.dart';
import 'package:topping_home/enums/d900/d900_iis_phase_type.dart';
import 'package:topping_home/enums/d900/d900_input_type.dart';
import 'package:topping_home/enums/d900/d900_language_type.dart';
import 'package:topping_home/enums/d900/d900_multi_function_key_type.dart';
import 'package:topping_home/enums/d900/d900_output_type.dart';
import 'package:topping_home/enums/d900/d900_power_trigger_type.dart';
import 'package:topping_home/enums/d900/d900_screen_brightness_type.dart';
import 'package:topping_home/enums/d900/d900_theme_type.dart';
import 'package:topping_home/enums/d900/d900_usb_select_type.dart';
import 'package:topping_home/enums/d900/d900_usb_type.dart';
import 'package:topping_home/enums/dx5/dx5_decode_mode_type.dart';
import 'package:topping_home/enums/dx5/dx5_device_type.dart';
import 'package:topping_home/enums/dx5/dx5_display_type.dart';
import 'package:topping_home/enums/dx5/dx5_filter_parameter_type.dart';
import 'package:topping_home/enums/dx5/dx5_filter_type.dart';
import 'package:topping_home/enums/dx5/dx5_frequency_type.dart';
import 'package:topping_home/enums/dx5/dx5_headphone_gain_type.dart';
import 'package:topping_home/enums/dx5/dx5_input_type.dart';
import 'package:topping_home/enums/dx5/dx5_language_type.dart';
import 'package:topping_home/enums/dx5/dx5_multi_function_key_type.dart';
import 'package:topping_home/enums/dx5/dx5_output_type.dart';
import 'package:topping_home/enums/dx5/dx5_power_trigger_type.dart';
import 'package:topping_home/enums/dx5/dx5_screen_brightness_type.dart';
import 'package:topping_home/enums/dx5/dx5_theme_type.dart';
import 'package:topping_home/enums/dx5/dx5_usb_type.dart';
import 'package:topping_home/models/d900_device_settings.dart';
import 'package:topping_home/models/device_entity.dart';
import 'package:topping_home/models/dx5_device_settings.dart';
import 'package:topping_home/models/imported_file_model.dart';
import 'package:topping_home/models/peq_band_model.dart';
import 'package:topping_home/models/peq_config_model.dart';
import 'package:topping_home/models/peq_settings_model.dart';

/// 自定义Hive适配器注册类
class HiveRegistrar {
  static const String tag = 'HiveRegistrar';
  static bool _adaptersRegistered = false;

  /// 注册所有适配器
  static void registerAdapters({bool force = false}) {
    // 防止重复注册
    if (_adaptersRegistered && !force) {
      Log.i('$tag - Hive 适配器已经注册，跳过');
      return;
    }

    if (force) {
      Log.i('$tag - 强制重新注册 Hive 适配器');
      _adaptersRegistered = false;
    }

    try {
      Log.i('$tag - 开始注册 Hive 适配器');

      // 注册DX5枚举适配器
      _registerAdapter('Dx5DecodeModeTypeAdapter', () => Hive.registerAdapter(Dx5DecodeModeTypeAdapter()));
      _registerAdapter('Dx5DeviceTypeAdapter', () => Hive.registerAdapter(Dx5DeviceTypeAdapter()));
      _registerAdapter('Dx5DisplayTypeAdapter', () => Hive.registerAdapter(Dx5DisplayTypeAdapter()));
      _registerAdapter('Dx5FilterParameterTypeAdapter', () => Hive.registerAdapter(Dx5FilterParameterTypeAdapter()));
      _registerAdapter('Dx5FilterTypeAdapter', () => Hive.registerAdapter(Dx5FilterTypeAdapter()));
      _registerAdapter('Dx5FrequencyTypeAdapter', () => Hive.registerAdapter(Dx5FrequencyTypeAdapter()));
      _registerAdapter('Dx5HeadphoneGainTypeAdapter', () => Hive.registerAdapter(Dx5HeadphoneGainTypeAdapter()));
      _registerAdapter('Dx5InputTypeAdapter', () => Hive.registerAdapter(Dx5InputTypeAdapter()));
      _registerAdapter('Dx5LanguageTypeAdapter', () => Hive.registerAdapter(Dx5LanguageTypeAdapter()));
      _registerAdapter('Dx5MultiFunctionKeyTypeAdapter', () => Hive.registerAdapter(Dx5MultiFunctionKeyTypeAdapter()));
      _registerAdapter('Dx5OutputTypeAdapter', () => Hive.registerAdapter(Dx5OutputTypeAdapter()));
      _registerAdapter('Dx5PowerTriggerTypeAdapter', () => Hive.registerAdapter(Dx5PowerTriggerTypeAdapter()));
      _registerAdapter('Dx5ScreenBrightnessTypeAdapter', () => Hive.registerAdapter(Dx5ScreenBrightnessTypeAdapter()));
      _registerAdapter('Dx5ThemeTypeAdapter', () => Hive.registerAdapter(Dx5ThemeTypeAdapter()));
      _registerAdapter('Dx5UsbTypeAdapter', () => Hive.registerAdapter(Dx5UsbTypeAdapter()));

      // 注册D900枚举适配器
      _registerAdapter('D900DisplayTypeAdapter', () => Hive.registerAdapter(D900DisplayTypeAdapter()));
      _registerAdapter('D900IisDsdChannelTypeAdapter', () => Hive.registerAdapter(D900IisDsdChannelTypeAdapter()));
      _registerAdapter('D900IisPhaseTypeAdapter', () => Hive.registerAdapter(D900IisPhaseTypeAdapter()));
      _registerAdapter('D900InputTypeAdapter', () => Hive.registerAdapter(D900InputTypeAdapter()));
      _registerAdapter('D900LanguageTypeAdapter', () => Hive.registerAdapter(D900LanguageTypeAdapter()));
      _registerAdapter('D900MultiFunctionKeyTypeAdapter', () => Hive.registerAdapter(D900MultiFunctionKeyTypeAdapter()));
      _registerAdapter('D900OutputTypeAdapter', () => Hive.registerAdapter(D900OutputTypeAdapter()));
      _registerAdapter('D900PowerTriggerTypeAdapter', () => Hive.registerAdapter(D900PowerTriggerTypeAdapter()));
      _registerAdapter('D900ScreenBrightnessTypeAdapter', () => Hive.registerAdapter(D900ScreenBrightnessTypeAdapter()));
      _registerAdapter('D900ThemeTypeAdapter', () => Hive.registerAdapter(D900ThemeTypeAdapter()));
      _registerAdapter('D900UsbSelectTypeAdapter', () => Hive.registerAdapter(D900UsbSelectTypeAdapter()));
      _registerAdapter('D900UsbTypeAdapter', () => Hive.registerAdapter(D900UsbTypeAdapter()));

      // 注册模型适配器 - 这些是最关键的适配器，需要特别关注
      Log.i('$tag - 开始注册关键模型适配器');
      _registerAdapter('DeviceEntityAdapter', () => Hive.registerAdapter(DeviceEntityAdapter()));
      _registerAdapter('DeviceSettingsAdapter', () => Hive.registerAdapter(Dx5DeviceSettingsAdapter()));
      _registerAdapter('D900DeviceSettingsAdapter', () => Hive.registerAdapter(D900DeviceSettingsAdapter()));
      _registerAdapter('ImportedFileModelAdapter', () => Hive.registerAdapter(ImportedFileModelAdapter()));
      _registerAdapter('PEQBandModelAdapter', () => Hive.registerAdapter(PEQBandModelAdapter()));
      _registerAdapter('PEQConfigModelAdapter', () => Hive.registerAdapter(PEQConfigModelAdapter()));
      _registerAdapter('PEQSettingsModelAdapter', () => Hive.registerAdapter(PEQSettingsModelAdapter()));

      _adaptersRegistered = true;
      Log.i('$tag - Hive 适配器注册完成');
    } catch (e, stackTrace) {
      Log.e('$tag - Hive 适配器注册失败: $e', e, stackTrace);
      // 如果适配器注册失败，我们仍然继续初始化应用程序
      // 但是设备设置可能无法正确保存
    }
  }

  /// 注册单个适配器，并捕获异常
  static void _registerAdapter(String name, Function registerFunc) {
    try {
      registerFunc();
      Log.i('$tag - 成功注册适配器: $name');
    } catch (e, stackTrace) {
      if (e.toString().contains('already') || e.toString().contains('已经注册')) {
        Log.w('$tag - 适配器已经注册: $name');
      } else {
        Log.e('$tag - 注册适配器失败: $name, 错误: $e', e, stackTrace);
        // 不要重新抛出异常，让注册过程继续进行，只记录错误
        // 这样可以确保其他适配器能够继续注册
      }
    }
  }

  /// 验证关键适配器是否已注册
  static Future<bool> verifyKeyAdapters() async {
    try {
      Log.i('$tag - 开始验证关键适配器');

      // 检查关键适配器是否已注册
      bool deviceEntityRegistered = await _isAdapterRegistered('DeviceEntityAdapter');
      bool deviceSettingsRegistered = await _isAdapterRegistered('DeviceSettingsAdapter');
      bool d900SettingsRegistered = await _isAdapterRegistered('D900DeviceSettingsAdapter');

      Log.i('$tag - 适配器注册状态: DeviceEntity=$deviceEntityRegistered, DeviceSettings=$deviceSettingsRegistered, D900DeviceSettings=$d900SettingsRegistered');

      // 如果有任何一个适配器未注册，则尝试强制重新注册
      if (!deviceEntityRegistered || !deviceSettingsRegistered || !d900SettingsRegistered) {
        Log.w('$tag - 关键适配器未完全注册，尝试强制重新注册');

        // 强制重新注册适配器
        registerAdapters(force: true);

        // 再次验证
        deviceEntityRegistered = await _isAdapterRegistered('DeviceEntityAdapter');
        deviceSettingsRegistered = await _isAdapterRegistered('DeviceSettingsAdapter');
        d900SettingsRegistered = await _isAdapterRegistered('D900DeviceSettingsAdapter');

        Log.i('$tag - 重新注册后的适配器状态: DeviceEntity=$deviceEntityRegistered, DeviceSettings=$deviceSettingsRegistered, D900DeviceSettings=$d900SettingsRegistered');

        if (!deviceEntityRegistered || !deviceSettingsRegistered || !d900SettingsRegistered) {
          Log.e('$tag - 即使强制重新注册后，适配器仍然未完全注册');
          return false;
        }
      }

      Log.i('$tag - 关键适配器验证成功');
      return true;
    } catch (e, stackTrace) {
      Log.e('$tag - 关键适配器验证失败: $e', e, stackTrace);
      return false;
    }
  }

  /// 检查适配器是否已注册
  static Future<bool> _isAdapterRegistered(String adapterName) async {
    try {
      // 创建一个测试对象并尝试序列化
      dynamic testObject;

      // 根据适配器名称创建相应的测试对象
      if (adapterName == 'DeviceEntityAdapter') {
        testObject = DeviceEntity.empty();
      } else if (adapterName == 'DeviceSettingsAdapter') {
        testObject = Dx5DeviceSettings(deviceId: 'test');
      } else if (adapterName == 'D900DeviceSettingsAdapter') {
        testObject = D900DeviceSettings(deviceId: 'test');
      } else {
        Log.w('$tag - 未知的适配器名称: $adapterName');
        return false;
      }

      // 创建一个临时盒子名称
      final tempBoxName = 'temp_${adapterName}_box';

      // 尝试打开一个临时盒子
      if (!Hive.isBoxOpen(tempBoxName)) {
        // 如果盒子不存在，则创建一个内存盒子
        await Hive.openBox(tempBoxName);
      }

      // 获取盒子
      final box = Hive.box(tempBoxName);

      // 尝试将测试对象写入盒子
      box.put('test', testObject);

      // 如果没有抛出异常，则表示适配器已注册
      Log.i('$tag - 适配器已注册: $adapterName');

      // 清理测试数据
      box.delete('test');

      return true;
    } catch (e) {
      Log.e('$tag - 适配器未注册或检查失败: $adapterName, 错误: $e');
      return false;
    }
  }
}
