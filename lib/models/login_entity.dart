import 'dart:convert';

import 'package:json_annotation/json_annotation.dart';

part 'login_entity.g.dart';

/// 登录实体类
@JsonSerializable()
class LoginEntity {
  String? token;
  String? type;
  int? userId;

  LoginEntity();

  factory LoginEntity.fromJson(Map<String, dynamic> json) =>
      _$LoginEntityFromJson(json);

  Map<String, dynamic> toJson() => _$LoginEntityToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }
}
