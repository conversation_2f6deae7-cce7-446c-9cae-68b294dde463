# Generated by Hive CE
# Manual modifications may be necessary for certain migrations
# Check in to version control
nextTypeId: 21
types:
  DeviceEntity:
    typeId: 0
    nextIndex: 9
    fields:
      id:
        index: 0
      name:
        index: 1
      image:
        index: 2
      connected:
        index: 3
      macAddress:
        index: 4
      bleDeviceId:
        index: 7
      lastConnectedState:
        index: 8
  DeviceType:
    typeId: 1
    nextIndex: 4
    fields:
      bluetooth:
        index: 0
      dac:
        index: 1
      headphone:
        index: 2
      player:
        index: 3
  DeviceSettings:
    typeId: 2
    nextIndex: 43
    fields:
      volume:
        index: 0
      headphoneEnabled:
        index: 12
      selectedInput:
        index: 14
      selectedOutput:
        index: 15
      title:
        index: 19
      power:
        index: 20
      mute:
        index: 21
      headphoneGainLevel:
        index: 22
      displayType:
        index: 23
      theme:
        index: 24
      powerTrigger:
        index: 25
      filter:
        index: 26
      decodeMode:
        index: 27
      audioBluetooth:
        index: 28
      bluetoothAPTX:
        index: 29
      remoteControl:
        index: 30
      multiFunctionKey:
        index: 31
      usbType:
        index: 32
      screenBrightness:
        index: 33
      language:
        index: 34
      reset:
        index: 35
      id:
        index: 36
      deviceId:
        index: 37
      sampleRate:
        index: 38
      peqSettings:
        index: 41
      channelBalance:
        index: 42
  InputType:
    typeId: 3
    nextIndex: 4
    fields:
      usb:
        index: 0
      optical:
        index: 1
      coaxial:
        index: 2
      bluetooth:
        index: 3
  OutputType:
    typeId: 4
    nextIndex: 4
    fields:
      close:
        index: 0
      singleEnded:
        index: 1
      balanced:
        index: 2
      singleEndedAndBalanced:
        index: 3
  HeadphoneGainType:
    typeId: 5
    nextIndex: 2
    fields:
      highGain:
        index: 0
      lowGain:
        index: 1
  DisplayType:
    typeId: 6
    nextIndex: 3
    fields:
      vu:
        index: 0
      fft:
        index: 1
      normal:
        index: 2
  ThemeType:
    typeId: 7
    nextIndex: 9
    fields:
      aurora:
        index: 0
      orange:
        index: 1
      peru:
        index: 2
      green:
        index: 3
      khaki:
        index: 4
      rose:
        index: 5
      blue:
        index: 6
      purple:
        index: 7
      white:
        index: 8
  PowerTriggerType:
    typeId: 8
    nextIndex: 3
    fields:
      signal:
        index: 0
      voltage:
        index: 1
      close:
        index: 2
  DecodeModeType:
    typeId: 9
    nextIndex: 2
    fields:
      prefix:
        index: 0
      dac:
        index: 1
  MultiFunctionKeyType:
    typeId: 10
    nextIndex: 9
    fields:
      inputSelect:
        index: 0
      lineOutSelect:
        index: 1
      headphoneOutSelect:
        index: 2
      homeSelect:
        index: 3
      brightnessSelect:
        index: 4
      sleep:
        index: 5
      pcmFilterSelect:
        index: 6
      mute:
        index: 7
      peqSelect:
        index: 8
  UsbType:
    typeId: 11
    nextIndex: 2
    fields:
      uac2:
        index: 0
      uac1:
        index: 1
  ScreenBrightnessType:
    typeId: 12
    nextIndex: 4
    fields:
      high:
        index: 0
      medium:
        index: 1
      low:
        index: 2
      auto:
        index: 3
  LanguageType:
    typeId: 13
    nextIndex: 2
    fields:
      zh:
        index: 0
      en:
        index: 1
  FilterType:
    typeId: 14
    nextIndex: 13
    fields:
      peakingFilter:
        index: 8
      lowPassFilter:
        index: 9
      highPassFilter:
        index: 10
      lowShelfFilter:
        index: 11
      highShelfFilter:
        index: 12
  FilterParameterType:
    typeId: 15
    nextIndex: 8
    fields:
      minimumPhase:
        index: 0
      linearPhaseApodizing:
        index: 1
      linearPhaseFast:
        index: 2
      linearPhaseFastLowRipple:
        index: 3
      linearPhaseSlow:
        index: 4
      minimumPhaseFast:
        index: 5
      minimumPhaseSlow:
        index: 6
      minimumPhaseSlowLowDispersion:
        index: 7
  ImportedFileModel:
    typeId: 16
    nextIndex: 5
    fields:
      id:
        index: 0
      name:
        index: 1
      data:
        index: 2
      type:
        index: 3
      importTime:
        index: 4
  PEQBandModel:
    typeId: 17
    nextIndex: 7
    fields:
      id:
        index: 0
      enabled:
        index: 1
      gain:
        index: 2
      frequency:
        index: 3
      filterType:
        index: 4
      q:
        index: 5
      name:
        index: 6
  PEQSettingsModel:
    typeId: 18
    nextIndex: 17
    fields:
      isEnabled:
        index: 0
      peqMode:
        index: 1
      activeDisplayModes:
        index: 2
      isCompensatedMode:
        index: 3
      preampGain:
        index: 7
      selectedBandId:
        index: 8
      currentEditParam:
        index: 9
      selectedTargetFile:
        index: 10
      selectedSourceFRFile:
        index: 11
      targetFiles:
        index: 12
      sourceFRFiles:
        index: 13
      activeConfigId:
        index: 15
      peqConfigs:
        index: 16
  PEQConfigModel:
    typeId: 19
    nextIndex: 13
    fields:
      id:
        index: 0
      name:
        index: 1
      description:
        index: 2
      createdAt:
        index: 3
      updatedAt:
        index: 4
      mode:
        index: 5
      bands:
        index: 6
      allBands:
        index: 7
      leftBands:
        index: 8
      rightBands:
        index: 9
      targetFile:
        index: 10
      sourceFRFile:
        index: 11
      modeBands:
        index: 12
  FrequencyType:
    typeId: 20
    nextIndex: 2
    fields:
      centerFrequency:
        index: 0
      cornerFrequency:
        index: 1
