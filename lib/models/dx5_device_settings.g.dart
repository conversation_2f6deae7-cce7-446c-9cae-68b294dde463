// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'dx5_device_settings.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class Dx5DeviceSettingsAdapter extends TypeAdapter<Dx5DeviceSettings> {
  @override
  final int typeId = 8;

  @override
  Dx5DeviceSettings read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return Dx5DeviceSettings(
      id: fields[0] as String?,
      deviceId: fields[1] as String?,
      title: fields[2] as String?,
      power: fields[3] == null ? true : fields[3] as bool,
      volume: fields[4] == null ? 0 : (fields[4] as num).toInt(),
      mute: fields[5] == null ? false : fields[5] as bool,
      headphoneEnabled: fields[8] == null ? false : fields[8] as bool,
      theme:
          fields[11] == null ? Dx5ThemeType.aurora : fields[11] as Dx5ThemeType,
      powerTrigger: fields[12] == null
          ? Dx5PowerTriggerType.signal
          : fields[12] as Dx5PowerTriggerType,
      channelBalance: fields[13] == null ? 0 : (fields[13] as num).toInt(),
      filter: fields[14] == null
          ? Dx5FilterParameterType.minimumPhase
          : fields[14] as Dx5FilterParameterType,
      decodeMode: fields[15] == null
          ? Dx5DecodeModeType.prefix
          : fields[15] as Dx5DecodeModeType,
      audioBluetooth: fields[16] == null ? false : fields[16] as bool,
      bluetoothAPTX: fields[17] == null ? false : fields[17] as bool,
      remoteControl: fields[18] == null ? false : fields[18] as bool,
      multiFunctionKey: fields[19] == null
          ? Dx5MultiFunctionKeyType.inputSelect
          : fields[19] as Dx5MultiFunctionKeyType,
      usbType: fields[20] == null ? Dx5UsbType.uac2 : fields[20] as Dx5UsbType,
      screenBrightness: fields[21] == null
          ? Dx5ScreenBrightnessType.auto
          : fields[21] as Dx5ScreenBrightnessType,
      language: fields[22] == null
          ? Dx5LanguageType.zh
          : fields[22] as Dx5LanguageType,
      reset: fields[23] == null ? false : fields[23] as bool,
      sampleRate: fields[24] == null ? 0 : (fields[24] as num).toInt(),
      peqSettings: fields[25] as PEQSettingsModel?,
    );
  }

  @override
  void write(BinaryWriter writer, Dx5DeviceSettings obj) {
    writer
      ..writeByte(26)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.deviceId)
      ..writeByte(2)
      ..write(obj.title)
      ..writeByte(3)
      ..write(obj.power)
      ..writeByte(4)
      ..write(obj.volume)
      ..writeByte(5)
      ..write(obj.mute)
      ..writeByte(6)
      ..write(obj.selectedInputIndex)
      ..writeByte(7)
      ..write(obj.selectedOutputIndex)
      ..writeByte(8)
      ..write(obj.headphoneEnabled)
      ..writeByte(9)
      ..write(obj.headphoneGainLevelIndex)
      ..writeByte(10)
      ..write(obj.displayTypeIndex)
      ..writeByte(11)
      ..write(obj.theme)
      ..writeByte(12)
      ..write(obj.powerTrigger)
      ..writeByte(13)
      ..write(obj.channelBalance)
      ..writeByte(14)
      ..write(obj.filter)
      ..writeByte(15)
      ..write(obj.decodeMode)
      ..writeByte(16)
      ..write(obj.audioBluetooth)
      ..writeByte(17)
      ..write(obj.bluetoothAPTX)
      ..writeByte(18)
      ..write(obj.remoteControl)
      ..writeByte(19)
      ..write(obj.multiFunctionKey)
      ..writeByte(20)
      ..write(obj.usbType)
      ..writeByte(21)
      ..write(obj.screenBrightness)
      ..writeByte(22)
      ..write(obj.language)
      ..writeByte(23)
      ..write(obj.reset)
      ..writeByte(24)
      ..write(obj.sampleRate)
      ..writeByte(25)
      ..write(obj.peqSettings);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is Dx5DeviceSettingsAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

Dx5DeviceSettings _$Dx5DeviceSettingsFromJson(Map<String, dynamic> json) =>
    Dx5DeviceSettings(
      id: json['id'] as String?,
      deviceId: json['deviceId'] as String?,
      title: json['title'] as String?,
      power: json['power'] as bool? ?? true,
      volume: (json['volume'] as num?)?.toInt() ?? 0,
      mute: json['mute'] as bool? ?? false,
      selectedInput:
          $enumDecodeNullable(_$Dx5InputTypeEnumMap, json['selectedInput']) ??
              Dx5InputType.usb,
      selectedOutput:
          $enumDecodeNullable(_$Dx5OutputTypeEnumMap, json['selectedOutput']) ??
              Dx5OutputType.close,
      headphoneEnabled: json['headphoneEnabled'] as bool? ?? false,
      headphoneGainLevel: $enumDecodeNullable(
              _$Dx5HeadphoneGainTypeEnumMap, json['headphoneGainLevel']) ??
          Dx5HeadphoneGainType.highGain,
      displayType:
          $enumDecodeNullable(_$Dx5DisplayTypeEnumMap, json['displayType']) ??
              Dx5DisplayType.vu,
      theme: $enumDecodeNullable(_$Dx5ThemeTypeEnumMap, json['theme']) ??
          Dx5ThemeType.aurora,
      powerTrigger: $enumDecodeNullable(
              _$Dx5PowerTriggerTypeEnumMap, json['powerTrigger']) ??
          Dx5PowerTriggerType.signal,
      channelBalance: (json['channelBalance'] as num?)?.toInt() ?? 0,
      filter: $enumDecodeNullable(
              _$Dx5FilterParameterTypeEnumMap, json['filter']) ??
          Dx5FilterParameterType.minimumPhase,
      decodeMode:
          $enumDecodeNullable(_$Dx5DecodeModeTypeEnumMap, json['decodeMode']) ??
              Dx5DecodeModeType.prefix,
      audioBluetooth: json['audioBluetooth'] as bool? ?? false,
      bluetoothAPTX: json['bluetoothAPTX'] as bool? ?? false,
      remoteControl: json['remoteControl'] as bool? ?? false,
      multiFunctionKey: $enumDecodeNullable(
              _$Dx5MultiFunctionKeyTypeEnumMap, json['multiFunctionKey']) ??
          Dx5MultiFunctionKeyType.inputSelect,
      usbType: $enumDecodeNullable(_$Dx5UsbTypeEnumMap, json['usbType']) ??
          Dx5UsbType.uac2,
      screenBrightness: $enumDecodeNullable(
              _$Dx5ScreenBrightnessTypeEnumMap, json['screenBrightness']) ??
          Dx5ScreenBrightnessType.auto,
      language:
          $enumDecodeNullable(_$Dx5LanguageTypeEnumMap, json['language']) ??
              Dx5LanguageType.zh,
      reset: json['reset'] as bool? ?? false,
      sampleRate: (json['sampleRate'] as num?)?.toInt() ?? 0,
      peqSettings: json['peqSettings'] == null
          ? null
          : PEQSettingsModel.fromJson(
              json['peqSettings'] as Map<String, dynamic>),
    )
      ..selectedInputIndex = (json['selectedInputIndex'] as num).toInt()
      ..selectedOutputIndex = (json['selectedOutputIndex'] as num).toInt()
      ..headphoneGainLevelIndex =
          (json['headphoneGainLevelIndex'] as num).toInt()
      ..displayTypeIndex = (json['displayTypeIndex'] as num).toInt();

Map<String, dynamic> _$Dx5DeviceSettingsToJson(Dx5DeviceSettings instance) =>
    <String, dynamic>{
      'id': instance.id,
      'deviceId': instance.deviceId,
      'title': instance.title,
      'power': instance.power,
      'volume': instance.volume,
      'mute': instance.mute,
      'selectedInput': _$Dx5InputTypeEnumMap[instance.selectedInput]!,
      'selectedInputIndex': instance.selectedInputIndex,
      'selectedOutput': _$Dx5OutputTypeEnumMap[instance.selectedOutput]!,
      'selectedOutputIndex': instance.selectedOutputIndex,
      'headphoneEnabled': instance.headphoneEnabled,
      'headphoneGainLevel':
          _$Dx5HeadphoneGainTypeEnumMap[instance.headphoneGainLevel]!,
      'headphoneGainLevelIndex': instance.headphoneGainLevelIndex,
      'displayType': _$Dx5DisplayTypeEnumMap[instance.displayType]!,
      'displayTypeIndex': instance.displayTypeIndex,
      'theme': _$Dx5ThemeTypeEnumMap[instance.theme]!,
      'powerTrigger': _$Dx5PowerTriggerTypeEnumMap[instance.powerTrigger]!,
      'channelBalance': instance.channelBalance,
      'filter': _$Dx5FilterParameterTypeEnumMap[instance.filter]!,
      'decodeMode': _$Dx5DecodeModeTypeEnumMap[instance.decodeMode]!,
      'audioBluetooth': instance.audioBluetooth,
      'bluetoothAPTX': instance.bluetoothAPTX,
      'remoteControl': instance.remoteControl,
      'multiFunctionKey':
          _$Dx5MultiFunctionKeyTypeEnumMap[instance.multiFunctionKey]!,
      'usbType': _$Dx5UsbTypeEnumMap[instance.usbType]!,
      'screenBrightness':
          _$Dx5ScreenBrightnessTypeEnumMap[instance.screenBrightness]!,
      'language': _$Dx5LanguageTypeEnumMap[instance.language]!,
      'reset': instance.reset,
      'sampleRate': instance.sampleRate,
      'peqSettings': instance.peqSettings,
    };

const _$Dx5InputTypeEnumMap = {
  Dx5InputType.usb: 'usb',
  Dx5InputType.optical: 'optical',
  Dx5InputType.coaxial: 'coaxial',
  Dx5InputType.bluetooth: 'bluetooth',
};

const _$Dx5OutputTypeEnumMap = {
  Dx5OutputType.singleEnded: 'singleEnded',
  Dx5OutputType.balanced: 'balanced',
  Dx5OutputType.singleEndedAndBalanced: 'singleEndedAndBalanced',
  Dx5OutputType.close: 'close',
};

const _$Dx5HeadphoneGainTypeEnumMap = {
  Dx5HeadphoneGainType.lowGain: 'lowGain',
  Dx5HeadphoneGainType.highGain: 'highGain',
};

const _$Dx5DisplayTypeEnumMap = {
  Dx5DisplayType.normal: 'normal',
  Dx5DisplayType.vu: 'vu',
  Dx5DisplayType.fft: 'fft',
};

const _$Dx5ThemeTypeEnumMap = {
  Dx5ThemeType.aurora: 'aurora',
  Dx5ThemeType.orange: 'orange',
  Dx5ThemeType.peru: 'peru',
  Dx5ThemeType.green: 'green',
  Dx5ThemeType.khaki: 'khaki',
  Dx5ThemeType.rose: 'rose',
  Dx5ThemeType.blue: 'blue',
  Dx5ThemeType.purple: 'purple',
  Dx5ThemeType.white: 'white',
};

const _$Dx5PowerTriggerTypeEnumMap = {
  Dx5PowerTriggerType.signal: 'signal',
  Dx5PowerTriggerType.voltage: 'voltage',
  Dx5PowerTriggerType.close: 'close',
};

const _$Dx5FilterParameterTypeEnumMap = {
  Dx5FilterParameterType.minimumPhase: 'minimumPhase',
  Dx5FilterParameterType.linearPhaseApodizing: 'linearPhaseApodizing',
  Dx5FilterParameterType.linearPhaseFast: 'linearPhaseFast',
  Dx5FilterParameterType.linearPhaseFastLowRipple: 'linearPhaseFastLowRipple',
  Dx5FilterParameterType.linearPhaseSlow: 'linearPhaseSlow',
  Dx5FilterParameterType.minimumPhaseFast: 'minimumPhaseFast',
  Dx5FilterParameterType.minimumPhaseSlow: 'minimumPhaseSlow',
  Dx5FilterParameterType.minimumPhaseSlowLowDispersion:
      'minimumPhaseSlowLowDispersion',
};

const _$Dx5DecodeModeTypeEnumMap = {
  Dx5DecodeModeType.prefix: 'prefix',
  Dx5DecodeModeType.dac: 'dac',
};

const _$Dx5MultiFunctionKeyTypeEnumMap = {
  Dx5MultiFunctionKeyType.mute: 'mute',
  Dx5MultiFunctionKeyType.inputSelect: 'inputSelect',
  Dx5MultiFunctionKeyType.lineOutSelect: 'lineOutSelect',
  Dx5MultiFunctionKeyType.headphoneOutSelect: 'headphoneOutSelect',
  Dx5MultiFunctionKeyType.homeSelect: 'homeSelect',
  Dx5MultiFunctionKeyType.brightnessSelect: 'brightnessSelect',
  Dx5MultiFunctionKeyType.sleep: 'sleep',
  Dx5MultiFunctionKeyType.pcmFilterSelect: 'pcmFilterSelect',
  Dx5MultiFunctionKeyType.peqSelect: 'peqSelect',
};

const _$Dx5UsbTypeEnumMap = {
  Dx5UsbType.uac1: 'uac1',
  Dx5UsbType.uac2: 'uac2',
};

const _$Dx5ScreenBrightnessTypeEnumMap = {
  Dx5ScreenBrightnessType.low: 'low',
  Dx5ScreenBrightnessType.medium: 'medium',
  Dx5ScreenBrightnessType.high: 'high',
  Dx5ScreenBrightnessType.auto: 'auto',
};

const _$Dx5LanguageTypeEnumMap = {
  Dx5LanguageType.en: 'en',
  Dx5LanguageType.zh: 'zh',
};
