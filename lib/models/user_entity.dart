import 'dart:convert';

import 'package:json_annotation/json_annotation.dart';

part 'user_entity.g.dart';

/// 用户实体类
@JsonSerializable()
class UserEntity {
  int? id;
  String? avatar;
  String? nickname;
  String? signature;
  String? sex;
  String? birthday;
  String? userPhone;
  String? username;
  String? inviteCode;
  String? invitedCode;
  String? password;
  String? email;

  UserEntity();

  factory UserEntity.fromJson(Map<String, dynamic> json) =>
      _$UserEntityFromJson(json);

  Map<String, dynamic> toJson() => _$UserEntityToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }
}
