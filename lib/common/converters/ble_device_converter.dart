import 'package:topping_ble_control/model/bluetooth/ble_device.dart';
import 'package:topping_home/common/ext/enums_ext.dart';

import '../../models/device_entity.dart';

class BleDeviceConverter {
  /// 把BleDevice转为DeviceEntity
  static DeviceEntity fromBleDevice(BleDevice bleDevice) {
    return DeviceEntity.named(
      id: bleDevice.id.toString(),
      name: bleDevice.name,
      macAddress: bleDevice.flutterDevice?.remoteId.str ?? "",
      image: bleDevice.deviceType.image,
      deviceModel: bleDevice.deviceType.name,
      bleDeviceId: bleDevice.nativeHandle,
      rssi: bleDevice.rssi,
    );
  }

  /// bleDevices转为DeviceEntities
  static List<DeviceEntity> fromBleDevices(List<BleDevice> bleDevices) {
    return bleDevices.map((bleDevice) => fromBleDevice(bleDevice)).toList();
  }
}