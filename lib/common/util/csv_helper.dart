import 'package:csv/csv.dart';

/// CSV 文件处理工具类
class CSVHelper {
  /// 解析CSV字符串
  static List<List<dynamic>> parseCSV(String input) {
    List<String> lines = input.split('\n');

    // 检查并跳过文件标识行
    int startIndex =
        lines.isNotEmpty && lines[0].trim() == "Topping_csv_file" ? 1 : 0;

    // 解析 CSV
    return CsvToListConverter().convert(lines.skip(startIndex).join('\n'),
        shouldParseNumbers: true // 自动将数字字符串转换为数值
        );
  }

  /// 将数据转换为CSV字符串
  static String convertToCSV(List<List<dynamic>> data) {
    return const ListToCsvConverter().convert(data);
  }

  /// 将单一的频率响应数据转换为CSV格式的List<List>
  static List<List<dynamic>> frequencyResponseToCSV(
      List<Map<String, double>> data) {
    List<List<dynamic>> result = [];

    // 添加表头
    result.add(['fre(Hz)', 'raw(dB)']);

    // 添加数据行
    for (var point in data) {
      result.add([point['frequency'], point['db']]);
    }

    return result;
  }
}
