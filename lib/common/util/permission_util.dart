import 'package:flutter/material.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:topping_home/common/util/toast_util.dart';

/// 权限检测工具
class PermissionUtil {
  // 单例写法
  PermissionUtil._internal();

  static final PermissionUtil _instance = PermissionUtil._internal();

  factory PermissionUtil() => _instance;

  /// 统一检查&请求给定权限列表
  /// - [permissions] 需要申请的权限列表
  /// - [onSuccess] 全部权限都允许时的回调
  /// - [onFailed] 存在至少一个权限被临时拒绝时的回调
  /// - [onGoSetting] 存在至少一个权限被永久拒绝时的回调（默认跳转到系统设置页）
  ///
  /// 返回值：
  /// - `true` 表示全部权限已被允许
  /// - `false` 表示有任意一个权限被拒绝（包括永久拒绝）
  Future<bool> checkAndRequestPermissions({
    required List<Permission> permissions,
    VoidCallback? onSuccess,
    VoidCallback? onFailed,
    VoidCallback? onGoSetting,
  }) async {
    // 找出实际需要申请的权限
    List<Permission> needRequest = [];
    for (var permission in permissions) {
      PermissionStatus status = await permission.status;
      if (!status.isGranted) {
        needRequest.add(permission);
      }
    }

    // 如果都已允许，直接执行成功回调
    if (needRequest.isEmpty) {
      onSuccess?.call();
      showToast("权限已经全部允许");
      return true;
    }

    // 申请权限
    Map<Permission, PermissionStatus> results = await needRequest.request();

    // 判断是否全部允许
    bool allGranted = results.values.every((status) => status.isGranted);

    if (allGranted) {
      onSuccess?.call();
      showToast("权限申请成功");
      return true;
    }

    // 如果有权限被永久拒绝，提示用户去设置页开启
    bool anyPermanentlyDenied =
        results.values.any((status) => status.isPermanentlyDenied);
    if (anyPermanentlyDenied) {
      onGoSetting != null ? onGoSetting() : await openAppSettings();
      return false;
    }

    // 其他情况：只要有一个权限被拒绝(普通拒绝或受限)，就视为整体失败
    onFailed?.call();
    showToast("权限申请失败");
    return false;
  }

  /// 示例：检查定位相关权限（分步检测，先「使用时」再「始终允许」）
  Future<bool> checkLocationAlways({
    VoidCallback? onSuccess,
    VoidCallback? onFailed,
    VoidCallback? onGoSetting,
  }) async {
    // 先检查 locationWhenInUse
    PermissionStatus whenInUseStatus =
        await Permission.locationWhenInUse.status;
    if (!whenInUseStatus.isGranted) {
      bool result = await checkAndRequestPermissions(
        permissions: [Permission.locationWhenInUse],
        onSuccess: onSuccess,
        onFailed: onFailed,
        onGoSetting: onGoSetting,
      );
      // 如果使用时权限都没拿到，就直接返回
      if (!result) return false;
    }

    // 再检查 locationAlways
    PermissionStatus alwaysStatus = await Permission.locationAlways.status;
    if (!alwaysStatus.isGranted) {
      bool result = await checkAndRequestPermissions(
        permissions: [Permission.locationAlways],
        onSuccess: onSuccess,
        onFailed: onFailed,
        onGoSetting: onGoSetting,
      );
      return result;
    }

    // 如果两种权限都已允许
    onSuccess?.call();
    showToast("定位权限已全部允许");
    return true;
  }
}
