import 'package:flutter/foundation.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:topping_home/environment_config.dart';

/// Flavor配置类，用于在Dart代码中获取当前环境
class FlavorConfig {
  /// 单例实例
  static final FlavorConfig _instance = FlavorConfig._internal();

  /// 获取单例实例
  factory FlavorConfig() => _instance;

  /// 私有构造函数
  FlavorConfig._internal();

  /// 当前环境
  late String _flavor;

  /// 应用包名信息
  late PackageInfo _packageInfo;

  /// 初始化Flavor配置
  Future<void> init() async {
    // 从环境变量获取flavor
    _flavor = const String.fromEnvironment('FLAVOR', defaultValue: EnvironmentConfig.ENV_PROD);

    // 获取包信息
    _packageInfo = await PackageInfo.fromPlatform();

    // 打印当前flavor和包名
    debugPrint('当前Flavor: $_flavor, 包名: ${getPackageName()}');
  }

  /// 获取当前环境
  String get flavor => _flavor;

  /// 判断是否为开发环境
  bool get isDev => _flavor == EnvironmentConfig.ENV_DEV;

  /// 判断是否为测试环境
  bool get isStaging => _flavor == EnvironmentConfig.ENV_STAGING;

  /// 判断是否为生产环境
  bool get isProd => _flavor == EnvironmentConfig.ENV_PROD;

  /// 根据环境获取不同的值
  T getByFlavor<T>({
    required T dev,
    required T staging,
    required T prod,
  }) {
    if (isDev) {
      return dev;
    } else if (isStaging) {
      return staging;
    } else {
      return prod;
    }
  }

  /// 获取应用包名
  String get packageName => _packageInfo.packageName;

  /// 获取环境特定的包名
  String getPackageName() {
    if (isDev) {
      return '$packageName.dev';
    } else if (isStaging) {
      return '$packageName.staging';
    } else {
      return packageName;
    }
  }
}
