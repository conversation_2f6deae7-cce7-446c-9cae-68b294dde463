import 'package:topping_home/common/util/flavor_config.dart';
import 'package:topping_home/environment_config.dart';

/// 包名工具类，用于处理与应用包名相关的功能
class PackageUtil {
  /// 获取当前环境的包名
  /// 
  /// 可以从EnvironmentConfig或FlavorConfig获取
  /// 两者实现相同的功能，但在不同的类中
  static String getCurrentPackageName() {
    // 从EnvironmentConfig获取
    final envPackageName = EnvironmentConfig().environmentPackageName;
    
    // 从FlavorConfig获取
    final flavorPackageName = FlavorConfig().getPackageName();
    
    // 两者应该返回相同的值
    assert(envPackageName == flavorPackageName, 
        '环境配置包名 ($envPackageName) 与Flavor配置包名 ($flavorPackageName) 不一致');
    
    return envPackageName;
  }
  
  /// 构建特定包名的URI
  /// 
  /// 例如: deeplink://com.topping.home.dev/path
  static String buildUriWithPackageName(String scheme, String path) {
    final packageName = getCurrentPackageName();
    return '$scheme://$packageName$path';
  }
  
  /// 检查包名是否与当前环境匹配
  /// 
  /// 用于验证从外部接收的包名是否与当前应用匹配
  static bool isPackageNameMatchingEnvironment(String packageName) {
    return packageName == getCurrentPackageName();
  }
}
