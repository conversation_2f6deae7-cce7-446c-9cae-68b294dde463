import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:topping_home/common/util/log_util.dart';
import 'package:topping_home/repositories/device_repository.dart';

/// 应用生命周期观察者
/// 用于在应用退出时执行清理操作
class AppLifecycleObserver extends WidgetsBindingObserver {
  static const String tag = 'AppLifecycleObserver';

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    Log.i('$tag: 应用生命周期状态变化: $state');
    
    if (state == AppLifecycleState.detached) {
      Log.i('$tag: 应用即将退出，执行清理操作');
      _cleanupResources();
    }
  }

  /// 清理资源
  void _cleanupResources() {
    try {
      // 关闭 DeviceRepository 中的流控制器
      if (Get.isRegistered<DeviceRepository>()) {
        final deviceRepo = Get.find<DeviceRepository>();
        deviceRepo.dispose();
        Log.i('$tag: DeviceRepository 资源已清理');
      }
    } catch (e) {
      Log.e('$tag: 清理资源时发生错误: $e');
    }
  }
}
