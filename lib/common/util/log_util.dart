import 'dart:developer' as developer;

import 'package:flutter/foundation.dart';
import 'package:logger/logger.dart';

class MyConsoleOutput extends ConsoleOutput {
  @override
  void output(OutputEvent event) {
    event.lines.forEach(developer.log);
  }
}

/// 日志工具类
class Log {
  static final Logger _logger = Logger(
    printer: PrettyPrinter(
        methodCount: 0,
        // 减少堆栈跟踪信息
        errorMethodCount: 5,
        // 错误时显示的堆栈跟踪行数
        lineLength: 120,
        // 增加每行长度
        colors: true,
        // 彩色输出
        printEmojis: false,
        // 关闭表情符号以节省空间
        printTime: true // 显示时间
        ),
  );

  static final logger = Logger(
    filter: kDebugMode ? DevelopmentFilter() : ProductionFilter(),
    printer: PrettyPrinter(methodCount: 0, errorMethodCount: 30, colors: true),
    output: MyConsoleOutput(),
  );

  static void v(String message, [dynamic error, StackTrace? stackTrace]) {
    _logger.v(message, error: error, stackTrace: stackTrace);
  }

  static void d(String message, [dynamic error, StackTrace? stackTrace]) {
    _logger.d(message, error: error, stackTrace: stackTrace);
  }

  static void i(String message, [dynamic error, StackTrace? stackTrace]) {
    _logger.i(message, error: error, stackTrace: stackTrace);
  }

  static void w(String message, [dynamic error, StackTrace? stackTrace]) {
    _logger.w(message, error: error, stackTrace: stackTrace);
  }

  static void e(String message, [dynamic error, StackTrace? stackTrace]) {
    _logger.e(message, error: error, stackTrace: stackTrace);
  }

  static void wtf(String message, [dynamic error, StackTrace? stackTrace]) {
    _logger.wtf(message, error: error, stackTrace: stackTrace);
  }

  /// 用于记录长列表数据，自动分段输出
  static void longList<T>(String message, List<T> list, {int batchSize = 20}) {
    // 使用封装方法 i
    i('$message - 总数: ${list.length}');

    for (int index = 0; index < list.length; index += batchSize) {
      int end =
          (index + batchSize < list.length) ? index + batchSize : list.length;
      // 也使用封装方法 i，而不是直接调用 _logger.i
      i('$message - 部分[${index + 1}-$end]/${list.length}: ${list.sublist(index, end)}');
    }
  }

  /// 专门用于记录频率-分贝数据
  static void frequencyData(
      String message, List<Map<String, dynamic>> freqData) {
    // 使用封装方法 i
    i('$message - 频率数据点数: ${freqData.length}');

    const int batchSize = 15;
    for (int index = 0; index < freqData.length; index += batchSize) {
      int end = (index + batchSize < freqData.length)
          ? index + batchSize
          : freqData.length;
      String batchText = freqData
          .sublist(index, end)
          .map((e) =>
              '{f:${e['frequency']?.toStringAsFixed(2)}, db:${e['db']?.toStringAsFixed(2)}}')
          .join(', ');
      // 也使用封装方法 i，而不是直接调用 _logger.i
      i('$message - 频率数据[${index + 1}-$end]/${freqData.length}: $batchText');
    }
  }
}
