import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:topping_home/common/util/log_util.dart';
import 'package:topping_home/environment_config.dart';
import 'package:topping_home/http/api/api_config.dart';
import 'package:topping_home/http/api/api_service.dart';
import 'package:topping_home/http/dio_client.dart';
import 'package:topping_home/http/retrofit_client.dart';

/// 环境切换工具类，仅在开发阶段使用
class EnvironmentSwitcher {
  /// 环境切换对话框
  static Future<void> showEnvironmentSwitchDialog(BuildContext context) async {
    if (!EnvironmentConfig().isDev) {
      // 非开发环境不显示环境切换对话框
      return;
    }

    final prefs = Get.find<SharedPreferences>();
    final currentEnv = prefs.getString('manual_environment') ?? EnvironmentConfig().environment;

    await showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: const Text('切换环境'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              _buildEnvironmentOption(context, '开发环境', EnvironmentConfig.ENV_DEV, currentEnv),
              _buildEnvironmentOption(context, '测试环境', EnvironmentConfig.ENV_STAGING, currentEnv),
              _buildEnvironmentOption(context, '生产环境', EnvironmentConfig.ENV_PROD, currentEnv),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: const Text('取消'),
            ),
          ],
        );
      },
    );
  }

  /// 构建环境选项
  static Widget _buildEnvironmentOption(
      BuildContext context, String title, String env, String currentEnv) {
    return ListTile(
      title: Text(title),
      subtitle: Text(ApiConfig.getBaseUrlForEnv(env)),
      trailing: currentEnv == env ? const Icon(Icons.check, color: Colors.green) : null,
      onTap: () async {
        // 保存手动设置的环境
        final prefs = Get.find<SharedPreferences>();
        await prefs.setString('manual_environment', env);

        // 获取新的API地址
        final newBaseUrl = ApiConfig.getBaseUrlForEnv(env);
        Log.i('切换环境到: $env, API地址: $newBaseUrl');

        // 重新初始化DioClient
        DioClient().init(baseUrl: newBaseUrl);

        try {
          // 重新创建ApiService实例
          RetrofitClient.instance.apiService = ApiService(baseUrl: newBaseUrl);

          // 重新创建Get.find中的ApiService实例
          if (Get.isRegistered<ApiService>()) {
            Get.delete<ApiService>();
            Get.put(ApiService(baseUrl: newBaseUrl));
          }
        } catch (e) {
          Log.e('切换环境时出错: $e');
        }

        // 关闭对话框
        Navigator.of(context).pop();

        // 显示切换成功提示
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('已切换到$title')),
        );
      },
    );
  }
}
