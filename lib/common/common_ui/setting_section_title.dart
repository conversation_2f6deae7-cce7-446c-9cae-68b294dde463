import 'package:flutter/material.dart';
import 'package:topping_home/theme/text_styles.dart';

/// 设置区域标题组件
class SettingSectionTitle extends StatelessWidget {
  final String title;

  const SettingSectionTitle({
    required this.title,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(left: 16, top: 16, bottom: 8),
      child: Text(
        title,
        style: TextStyles.instance.h3(
          fontWeight: FontWeight.w600,
        ),
      ),
    );
  }
}
