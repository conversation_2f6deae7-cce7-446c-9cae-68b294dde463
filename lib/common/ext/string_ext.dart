import 'dart:convert';
import 'package:convert/convert.dart';
import 'package:crypto/crypto.dart' as crypto;

/// 字符串扩展
extension StringExt on String {
  /// 检查当前字符串是否是合法手机号
  bool isPhoneNum() {
    final exp = RegExp(
        r'^((13[0-9])|(14[0-9])|(15[0-9])|(16[0-9])|(17[0-9])|(18[0-9])|(19[0-9]))\d{8}$');
    return exp.hasMatch(this);
  }

  /// 生成当前字符串的 MD5 值
  String toMd5() {
    final content = const Utf8Encoder().convert(this);
    final digest = crypto.md5.convert(content);
    return hex.encode(digest.bytes);
  }
}
