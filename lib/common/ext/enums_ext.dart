import 'package:topping_ble_control/model/enums/device_mode_type.dart';

import '../../gen/assets.gen.dart';


/// 枚举的扩展方法

/// 设备类型枚举的扩展方法
extension DeviceTypeEnumExt<T extends DeviceModeType> on T {
  /// 根据枚举类型获取设备图片
  String get image {
    switch (this) {
      case DeviceModeType.dx5:
        return Assets.image.icDx5ii.path;
      case DeviceModeType.dx9:
        return Assets.image.icDx5ii.path;
      default:
        return Assets.image.icDx5ii.path;
    }
  }
}

