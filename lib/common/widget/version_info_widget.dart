import 'package:flutter/material.dart';
import 'package:topping_home/environment_config.dart';

/// 版本信息控件，用于在应用中显示当前环境和版本信息
class VersionInfoWidget extends StatelessWidget {
  const VersionInfoWidget({super.key});

  @override
  Widget build(BuildContext context) {
    final config = EnvironmentConfig();

    return Container(
      padding: const EdgeInsets.all(16.0),
      decoration: BoxDecoration(
        color: config.isDev ? Colors.amber[100] : Colors.blue[100],
        borderRadius: BorderRadius.circular(8.0),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            '当前环境: ${config.environment}',
            style: const TextStyle(fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 8.0),
          Text('版本: ${config.displayVersion}'),
          const SizedBox(height: 8.0),
          Text('版本号: ${config.versionCode}'),
        ],
      ),
    );
  }
}
