import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../theme/color_palettes.dart';
import '../../theme/text_styles.dart';

/// 动画进度指示器
/// 提供更美观的进度显示效果
class AnimatedProgressIndicator extends StatelessWidget {
  /// 进度值 (0.0 - 1.0)
  final double value;

  /// 进度文本
  final String label;

  /// 背景颜色
  final Color? backgroundColor;

  /// 进度条颜色
  final Color? valueColor;

  /// 文本样式
  final TextStyle? textStyle;

  /// 高度
  final double? height;

  /// 是否显示动画效果
  final bool animate;

  const AnimatedProgressIndicator({
    Key? key,
    required this.value,
    required this.label,
    this.backgroundColor,
    this.valueColor,
    this.textStyle,
    this.height,
    this.animate = true,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 进度条
        TweenAnimationBuilder<double>(
          tween: Tween<double>(begin: 0.0, end: value),
          duration: animate ? const Duration(milliseconds: 500) : Duration.zero,
          curve: Curves.easeInOut,
          builder: (context, value, _) => Stack(
            alignment: Alignment.center,
            children: [
              // 进度条背景
              Container(
                height: height ?? 12.h,
                decoration: BoxDecoration(
                  color: backgroundColor ?? ColorPalettes.instance.divider.withValues(alpha: 0.3),
                  borderRadius: BorderRadius.circular(6.r),
                ),
              ),
              // 进度条前景
              Align(
                alignment: Alignment.centerLeft,
                child: LayoutBuilder(
                  builder: (context, constraints) {
                    return Container(
                      height: height ?? 12.h,
                      width: constraints.maxWidth * value,
                      decoration: BoxDecoration(
                        color: valueColor ?? ColorPalettes.instance.primary,
                        borderRadius: BorderRadius.circular(6.r),
                        boxShadow: [
                          BoxShadow(
                            color: (valueColor ?? ColorPalettes.instance.primary).withValues(alpha: 0.3),
                            blurRadius: 5,
                            offset: const Offset(0, 2),
                          ),
                        ],
                      ),
                    );
                  },
                ),
              ),
              // 进度百分比
              LayoutBuilder(
                builder: (context, constraints) {
                  // 计算进度条宽度
                  final progressWidth = constraints.maxWidth * value;
                  // 计算文本宽度
                  final textPainter = TextPainter(
                    text: TextSpan(text: '${(value * 100).toStringAsFixed(0)}%'),
                    textDirection: TextDirection.ltr,
                  );
                  textPainter.layout();
                  final textWidth = textPainter.width;

                  // 如果进度条宽度小于文本宽度的一半，使用深色文本，否则使用白色文本
                  final useWhiteText = progressWidth > textWidth * 0.7;

                  return Stack(
                    children: [
                      // 文本阴影，增强可读性
                      Text(
                        '${(value * 100).toStringAsFixed(0)}%',
                        style: TextStyle(
                          fontSize: textStyle?.fontSize ?? 10,
                          fontWeight: FontWeight.bold,
                          foreground: Paint()
                            ..style = PaintingStyle.stroke
                            ..strokeWidth = 2
                            ..color = useWhiteText ? ColorPalettes.instance.shadow.withValues(alpha: 0.38) : ColorPalettes.instance.pure.withValues(alpha: 0.7),
                        ),
                      ),
                      // 文本
                      Text(
                        '${(value * 100).toStringAsFixed(0)}%',
                        style: textStyle ??
                          TextStyles.instance.caption(
                            color: useWhiteText ? ColorPalettes.instance.pure : ColorPalettes.instance.primary,
                            fontWeight: FontWeight.bold,
                          ),
                      ),
                    ],
                  );
                },
              ),
            ],
          ),
        ),

        // 标签文本
        Padding(
          padding: EdgeInsets.only(top: 8.h),
          child: Text(
            label,
            style: textStyle ?? TextStyles.instance.body2(),
          ),
        ),
      ],
    );
  }
}
