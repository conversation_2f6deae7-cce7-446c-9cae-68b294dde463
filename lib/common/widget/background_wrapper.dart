import 'dart:io';
import 'dart:ui';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../repositories/background_repository.dart';
import 'package:topping_home/theme/color_palettes.dart';

/// BackgroundWrapper 是一个包装组件，用于给子组件添加自定义背景
/// 背景图片、透明度和模糊度可以通过 BackgroundRepository 进行控制
class BackgroundWrapper extends StatelessWidget {
  /// 需要显示在背景上的子组件
  final Widget child;

  /// 是否使用额外的暗色覆盖层
  final bool useDarkOverlay;

  /// 暗色覆盖层的不透明度 (0.0 - 1.0)
  final double darkOverlayOpacity;

  /// 背景颜色 (当没有背景图时使用)
  final Color backgroundColor;

  /// 背景管理器，用于获取和控制背景相关的设置
  /// 使用 Get 依赖注入获取 BackgroundRepository 实例
  final BackgroundRepository backgroundManager =
      Get.find<BackgroundRepository>();

  /// 构造函数
  /// [key] - 组件的键
  /// [child] - 需要显示在背景上的子组件
  /// [useDarkOverlay] - 是否使用额外的暗色覆盖层
  /// [darkOverlayOpacity] - 暗色覆盖层的不透明度
  /// [backgroundColor] - 没有背景图时使用的背景颜色
  BackgroundWrapper({
    super.key,
    required this.child,
    this.useDarkOverlay = true,
    this.darkOverlayOpacity = 0.3,
    Color? backgroundColor,
  }) : backgroundColor = backgroundColor ?? ColorPalettes.instance.background;

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      final backgroundPath = backgroundManager.currentBackground.value;
      final opacity = backgroundManager.backgroundOpacity.value;
      final blur = backgroundManager.backgroundBlur.value;

      // 动态获取当前主题的背景色
      final currentBackgroundColor = backgroundColor;

      final hasBackground = backgroundPath.isNotEmpty;

      return Stack(
        fit: StackFit.expand,
        children: [
          // 基础背景色层
          Container(
            color: currentBackgroundColor,
          ),

          // 背景图片层
          if (hasBackground)
            Builder(
              builder: (context) {
                try {
                  return Container(
                    decoration: BoxDecoration(
                      image: DecorationImage(
                        image: FileImage(File(backgroundPath)),
                        fit: BoxFit.cover,
                        opacity: opacity,
                        onError: (exception, stackTrace) {
                          print('背景图片加载失败: $exception');
                          return;
                        },
                      ),
                    ),
                    child: BackdropFilter(
                      filter: ImageFilter.blur(
                        sigmaX: blur,
                        sigmaY: blur,
                      ),
                      child: Container(
                        color: ColorPalettes.instance.transparent,
                      ),
                    ),
                  );
                } catch (e) {
                  print('背景图片处理错误: $e');
                  return const SizedBox.shrink();
                }
              },
            ),

          // 暗色遮罩层
          if (hasBackground && useDarkOverlay)
            Container(
              color: ColorPalettes.instance.shadow.withValues(alpha: darkOverlayOpacity),
            ),

          // 内容层
          child,
        ],
      );
    });
  }
}
