import 'package:flutter/material.dart';
import 'package:topping_home/common/util/environment_switcher.dart';
import 'package:topping_home/common/util/flavor_config.dart';
import 'package:topping_home/environment_config.dart';

/// 环境信息显示组件，仅在开发和测试环境显示
class EnvironmentInfoWidget extends StatelessWidget {
  /// 子组件
  final Widget child;

  /// 构造函数
  const EnvironmentInfoWidget({super.key, required this.child});

  @override
  Widget build(BuildContext context) {
    // 生产环境不显示环境信息
    if (FlavorConfig().isProd) {
      return child;
    }

    return Stack(
      children: [
        child,
        Positioned(
          right: 0,
          bottom: 0,
          child: GestureDetector(
            onTap: () {
              // 点击环境标签时显示环境切换对话框
              EnvironmentSwitcher.showEnvironmentSwitchDialog(context);
            },
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: _getEnvironmentColor(),
                borderRadius:
                    const BorderRadius.only(topLeft: Radius.circular(8)),
              ),
              child: Text(
                _getEnvironmentText(),
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 12,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ),
        ),
      ],
    );
  }

  /// 获取环境颜色
  Color _getEnvironmentColor() {
    if (FlavorConfig().isDev) {
      return Colors.red;
    } else if (FlavorConfig().isStaging) {
      return Colors.orange;
    } else {
      return Colors.green;
    }
  }

  /// 获取环境文本
  String _getEnvironmentText() {
    final env = FlavorConfig();
    final version = EnvironmentConfig().displayVersion;

    if (env.isDev) {
      return 'DEV - $version';
    } else if (env.isStaging) {
      return 'STAGING - $version';
    } else {
      return 'PROD - $version';
    }
  }
}
