import 'package:get/get.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:topping_home/environment_config.dart';

/// API配置类，用于管理不同环境的API地址
class ApiConfig {
  /// 单例实例
  static final ApiConfig _instance = ApiConfig._internal();

  /// 获取单例实例
  factory ApiConfig() => _instance;

  /// 私有构造函数
  ApiConfig._internal();

  /// 开发环境API地址
  static const String devBaseUrl = "http://192.168.2.121:8080/";

  /// 测试环境API地址
  static const String stagingBaseUrl = "http://49.232.108.42:8080/";

  /// 生产环境API地址
  static const String prodBaseUrl = "http://49.232.108.42:8080/";

  /// 获取指定环境的API地址
  static String getBaseUrlForEnv(String env) {
    switch (env) {
      case EnvironmentConfig.ENV_DEV:
        return devBaseUrl;
      case EnvironmentConfig.ENV_STAGING:
        return stagingBaseUrl;
      case EnvironmentConfig.ENV_PROD:
        return prodBaseUrl;
      default:
        return prodBaseUrl; // 默认使用生产环境
    }
  }

  /// 获取当前环境的API地址
  String getBaseUrl() {
    // 先检查是否有手动设置的环境（仅开发模式下有效）
    if (EnvironmentConfig().isDev) {
      try {
        final prefs = Get.find<SharedPreferences>();
        final manualEnv = prefs.getString('manual_environment');
        if (manualEnv != null) {
          return getBaseUrlForEnv(manualEnv);
        }
      } catch (e) {
        // 如果还没有初始化SharedPreferences，则忽略
      }
    }

    // 使用应用实际的环境
    return getBaseUrlForEnv(EnvironmentConfig().environment);
  }
}
