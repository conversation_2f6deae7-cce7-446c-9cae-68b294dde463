import 'dart:convert';

import 'package:dio/dio.dart';
import 'package:retrofit/retrofit.dart';
import 'package:topping_home/models/empty_result.dart';
import 'package:topping_home/models/user_entity.dart';

import '../../models/app_version_entity.dart';
import '../../models/base_result.dart';
import '../../models/bluetooth_firmware_entity.dart';
import '../../models/device_type_entity.dart';
import '../../models/feedback_entity.dart';
import '../../models/feedback_type_entity.dart';
import '../../models/login_entity.dart';
import '../dio_client.dart';

part 'api_service.g.dart';

// @RestApi(baseUrl: "http://192.168.2.59:8080/")
@RestApi(baseUrl: "http://49.232.108.42:8080/")

/// API服务
abstract class ApiService {
  factory ApiService({Dio? dio, String? baseUrl}) {
    return _ApiService(DioClient().dio, baseUrl: baseUrl);
  }

  /// 注册
  @POST('/api/user/register')
  Future<BaseResult<UserEntity>> register(@Body() UserEntity userEntity);

  /// 密码登录
  @POST('/api/user/login')
  Future<BaseResult<LoginEntity>> loginByPassword(
      @Body() UserEntity userEntity);

  /// 获取用户信息
  @PUT('/api/user/profile')
  Future<BaseResult<UserEntity>> getUserInfo();

  /// 退出登录
  @POST('/api/user/logout')
  Future<BaseResult<EmptyResult>?> logout();

  /// 提交反馈
  @POST('/api/public/feedback')
  @MultiPart()
  Future<BaseResult<FeedbackEntity>> submitFeedback(
      @Part(name: 'feedback') FeedbackEntity feedback,
      @Part(name: 'images') List<MultipartFile>? images);

  @GET('/api/public/feedback/feedback-types')
  Future<BaseResult<List<FeedbackTypeEntity>>> getFeedbackTypes();

  @GET('/api/public/feedback/device-types')
  Future<BaseResult<List<DeviceTypeEntity>>> getDeviceTypes();

  @GET('/api/public/app-update/check')
  Future<BaseResult<AppVersionEntity>> checkAppUpdate(
    @Query('packageName') String packageName,
    @Query('versionCode') int versionCode,
    @Query('deviceType') String? deviceType,
  );

  @GET('/api/public/app-update/latest')
  Future<BaseResult<AppVersionEntity>> getLatestVersion(
    @Query('packageName') String packageName,
  );

  /// 检查蓝牙固件更新
  @GET('/api/public/bluetooth-firmware/check')
  Future<BaseResult<BluetoothFirmwareEntity>> checkFirmwareUpdate(
    @Query('deviceModel') String deviceModel,
    @Query('macAddress') String macAddress,
  );

  /// 获取最新固件信息
  @GET('/api/public/bluetooth-firmware/latest')
  Future<BaseResult<BluetoothFirmwareEntity>> getLatestFirmware(
    @Query('deviceModel') String deviceModel,
  );

  /// 报告固件升级成功
  @POST('/api/public/bluetooth-firmware/report-upgrade')
  Future<BaseResult<void>> reportFirmwareUpgradeSuccess(
    @Query('macAddress') String macAddress,
    @Query('firmwareVersion') String firmwareVersion,
  );
}
