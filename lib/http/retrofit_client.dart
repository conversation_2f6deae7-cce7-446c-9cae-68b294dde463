import 'api/api_config.dart';
import 'api/api_service.dart';

/// Retrofit 客户端
class RetrofitClient {
  RetrofitClient._();

  static RetrofitClient get instance => _getInstance();
  static RetrofitClient? _instance;

  static RetrofitClient _getInstance() {
    _instance ??= RetrofitClient._();
    return _instance!;
  }

  // 使用ApiConfig中的API地址创建ApiService实例
  ApiService apiService = ApiService(baseUrl: ApiConfig().getBaseUrl());
}
