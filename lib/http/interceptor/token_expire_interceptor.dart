import 'package:dio/dio.dart';
import 'package:topping_home/common/util/log_util.dart';

import '../../common/util/toast_util.dart';

/// token过期拦截器
class TokenExpireInterceptor extends Interceptor {
  @override
  void onResponse(Response response, ResponseInterceptorHandler handler) {
    if (response.data["code"] == 202) {
      /// 登录过期
      showToast("当前登录信息已过期，请重新登录");
      Log.e("当前登录信息已过期，请重新登录");
      // todo 跳转到登录页
      // AppRoutes.jumpPage(AppRoutes.verifyCodeLoginPage);
    }
    super.onResponse(response, handler);
  }
}
