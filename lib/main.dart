import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:topping_home/business/guide/guide_logic.dart';
import 'package:topping_home/business/guide/guide_view.dart';
import 'package:topping_home/business/splash/splash_view.dart';
import 'package:topping_home/common/util/app_lifecycle_observer.dart';
import 'package:topping_home/common/util/flavor_config.dart';
import 'package:topping_home/common/util/log_util.dart';
import 'package:topping_home/router/router_observer.dart';
import 'package:topping_home/router/routers.dart';

import 'business/agreement/agreement_dialog.dart';
import 'business/index/index_binding.dart';
import 'business/index/index_view.dart';
import 'environment_config.dart';
import 'injection.dart';
import 'common/widget/default_refresh_indicator.dart';
import 'l10n/app_localizations.dart';
import 'repositories/agreement_repository.dart';

/// 应用入口
void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // 初始化依赖注入（包含环境配置和Flavor配置的初始化）
  await Injection().init();

  // 注册应用程序退出时的回调
  WidgetsBinding.instance.addObserver(AppLifecycleObserver());

  runApp(const MyApp());
}

class MyApp extends StatefulWidget {
  const MyApp({super.key});

  @override
  State<MyApp> createState() => _MyAppState();
}

class _MyAppState extends State<MyApp> {
  // 首页组件，默认为空白启动页面
  Widget _initialPage = const SplashPage();
  // 是否已经检查过协议状态
  bool _hasCheckedAgreement = false;

  @override
  void initState() {
    super.initState();

    // 立即检查协议同意状态，不等待界面渲染
    _checkAgreementStatus();
  }

  @override
  Widget build(BuildContext context) {
    return ScreenUtilInit(
      designSize: const Size(750, 1334),
      builder: (context, child) => _defaultRefreshConfiguration(GetMaterialApp(
        showPerformanceOverlay: false,
        getPages: AppRoutes.routerPages,
        navigatorObservers: [AppRouterObserver()],
        defaultTransition: Transition.rightToLeft,
        transitionDuration: const Duration(milliseconds: 150),
        title: 'Topping Home',
        home: _initialPage,
        // 移除initialBinding配置
        locale: const Locale.fromSubtags(languageCode: 'zh'),
        localizationsDelegates: const [
          AppLocalizations.delegate,
          GlobalMaterialLocalizations.delegate,
          GlobalWidgetsLocalizations.delegate,
          GlobalCupertinoLocalizations.delegate,
        ],
        debugShowCheckedModeBanner: false,
        supportedLocales: const [
          Locale('en'),
          Locale('zh'),
          // 繁體中文
          Locale.fromSubtags(languageCode: 'zh', scriptCode: 'Hans'),
          // 韓文
          Locale('ko'),
          // 日文
          Locale('ja'),
        ],
        builder: (context, child) {
          // 确保在这里可以使用 Get.context
          Log.e('Inside GetMaterialApp: ${Localizations.localeOf(context)}');
          // 包裹环境信息组件，仅在非生产环境显示
          return child!;
        },
      )),
    );
  }

  /// 检查协议同意状态
  Future<void> _checkAgreementStatus() async {
    // 如果已经检查过协议状态，不再重复检查
    if (_hasCheckedAgreement) return;
    _hasCheckedAgreement = true;

    final hasAccepted = await AgreementRepository().hasAcceptedAllAgreements();
    final isFirstLaunch = await GuideLogic.checkFirstLaunch();

    // 在界面渲染完成后立即显示协议对话框或跳转到相应页面
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (!hasAccepted) {
        // 用户未同意协议，显示协议对话框
        Get.dialog(
          PopScope(
            canPop: false, // 阻止返回键操作
            child: AgreementDialog(
              onAgree: () {
                // 用户同意协议后，跳转到相应页面
                _navigateToAppropriateScreen(isFirstLaunch);
              },
              onDisagree: () {
                // 用户不同意协议，立即退出应用
                // Android平台
                if (GetPlatform.isAndroid) {
                  SystemNavigator.pop();
                }
                // iOS平台
                else if (GetPlatform.isIOS) {
                  exit(0);
                }
              },
            ),
          ),
          barrierDismissible: false, // 不允许点击外部关闭对话框
        );
      } else {
        // 用户已经同意了协议，直接跳转到相应页面
        _navigateToAppropriateScreen(isFirstLaunch);
      }
    });
  }

  /// 跳转到适当的页面
  void _navigateToAppropriateScreen(bool isFirstLaunch) {
    if (isFirstLaunch) {
      // 如果是首次启动，跳转到引导页
      Get.offAllNamed(AppRoutes.quickStartPage);
    } else {
      // 否则跳转到首页
      Get.offAllNamed(AppRoutes.indexPage);
    }
  }

  /// 默认的下拉刷新配置
  RefreshConfiguration _defaultRefreshConfiguration(Widget child) {
    return RefreshConfiguration(
      headerBuilder: () => DefaultRefreshHeader(),
      footerBuilder: () => DefaultRefreshFooter(),
      headerTriggerDistance: 180.w,
      maxOverScrollExtent: 100.w,
      maxUnderScrollExtent: 0,
      enableScrollWhenRefreshCompleted: true,
      enableLoadingWhenFailed: true,
      hideFooterWhenNotFull: false,
      enableBallisticLoad: true,
      footerTriggerDistance: 140.w,
      child: child,
    );
  }
}
