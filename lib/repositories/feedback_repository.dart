import 'package:dio/dio.dart';
import 'package:topping_home/common/util/log_util.dart';
import 'package:topping_home/models/base_result.dart';
import 'package:topping_home/models/feedback_entity.dart';

import '../http/retrofit_client.dart';
import '../models/device_type_entity.dart';
import '../models/feedback_type_entity.dart';

/// 反馈仓库
class FeedbackRepository {
  final RetrofitClient _client = RetrofitClient.instance;

  /// 提交反馈
  Future<BaseResult<FeedbackEntity>> submitFeedback(
    FeedbackEntity feedback,
    List<String> imagePaths,
  ) async {
    try {
      // 准备图片文件
      List<MultipartFile> imageFiles = [];
      if (imagePaths.isNotEmpty) {
        imageFiles = await Future.wait(
          imagePaths.map((path) async => await MultipartFile.fromFile(
                path,
                filename: path.split('/').last,
              )),
        );
      }

      // 调用 Retrofit 接口
      final result = await _client.apiService.submitFeedback(
        feedback, // 直接传 FeedbackEntity，Retrofit 会处理序列化
        imageFiles.isEmpty ? null : imageFiles, // 图片列表
      );

      Log.d('Response: ${result.toString()}');

      if (result.success && result.data != null) {
        return result; // Retrofit 已解析为 BaseResult<FeedbackEntity>
      } else {
        throw Exception('Feedback submission failed: ${result.message}');
      }
    } catch (e) {
      Log.e('Submit feedback error: $e');
      return BaseResult(success: false, code: 500, message: e.toString());
    }
  }

  /// 获取反馈类型
  Future<List<FeedbackTypeEntity>> getFeedbackTypes() async {
    try {
      final result = await _client.apiService.getFeedbackTypes();
      if (result.success && result.data != null) {
        return result.data!;
      } else {
        throw Exception(result.message ?? 'Failed to get feedback types');
      }
    } catch (e) {
      Log.e('Get feedback types error: $e');
      throw Exception('Failed to get feedback types: $e');
    }
  }

  /// 获取设备类型
  Future<List<DeviceTypeEntity>> getDeviceTypes() async {
    try {
      final result = await _client.apiService.getDeviceTypes();
      Log.d('Response: $result');
      if (result.success && result.data != null) {
        return result.data!;
      } else {
        throw Exception(result.message ?? 'Failed to get device types');
      }
    } catch (e) {
      Log.e('Get device types error: $e');
      throw Exception('Failed to get device types: $e');
    }
  }
}
