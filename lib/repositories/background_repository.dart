import 'dart:io';

import 'package:get/get.dart';
import 'package:image_picker/image_picker.dart';
import 'package:path_provider/path_provider.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:topping_home/common/util/log_util.dart';

/// 背景管理类
class BackgroundRepository {
  static const String _backgroundDir = 'backgrounds';
  static const String _currentBackgroundKey = 'current_background';
  static const String _backgroundOpacityKey = 'background_opacity';
  static const String _backgroundBlurKey = 'background_blur';

  final _imagePicker = ImagePicker();
  final RxString currentBackground = RxString('');
  final RxDouble backgroundOpacity = 0.5.obs;
  final RxDouble backgroundBlur = 5.0.obs;

  BackgroundRepository() {
    _loadSettings();
  }

  /// 加载保存的设置
  Future<void> _loadSettings() async {
    final prefs = await SharedPreferences.getInstance();

    final savedPath = prefs.getString(_currentBackgroundKey);
    if (savedPath != null && File(savedPath).existsSync()) {
      currentBackground.value = savedPath;
    }

    final savedOpacity = prefs.getDouble(_backgroundOpacityKey);
    if (savedOpacity != null) {
      backgroundOpacity.value = savedOpacity;
    }

    final savedBlur = prefs.getDouble(_backgroundBlurKey);
    if (savedBlur != null) {
      backgroundBlur.value = savedBlur;
    }
  }

  /// 保存设置
  Future<void> _saveSettings() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(_currentBackgroundKey, currentBackground.value);
    await prefs.setDouble(_backgroundOpacityKey, backgroundOpacity.value);
    await prefs.setDouble(_backgroundBlurKey, backgroundBlur.value);
    Log.i('设置当前背景: ${currentBackground.value}');
  }

  /// 获取背景图片存储目录
  Future<Directory> get _backgroundDirectory async {
    final appDir = await getApplicationDocumentsDirectory();
    final backgroundDir = Directory('${appDir.path}/$_backgroundDir');
    if (!await backgroundDir.exists()) {
      await backgroundDir.create(recursive: true);
    }
    return backgroundDir;
  }

  /// 从相册选择图片
  Future<File?> pickImageFromGallery() async {
    final pickedFile =
        await _imagePicker.pickImage(source: ImageSource.gallery);
    if (pickedFile != null) {
      return File(pickedFile.path);
    }
    return null;
  }

  /// 拍照获取图片
  Future<File?> pickImageFromCamera() async {
    final pickedFile = await _imagePicker.pickImage(source: ImageSource.camera);
    if (pickedFile != null) {
      return File(pickedFile.path);
    }
    return null;
  }

  /// 保存背景图片
  Future<String?> saveBackground(File imageFile) async {
    try {
      final backgroundDir = await _backgroundDirectory;
      final fileName = '${DateTime.now().millisecondsSinceEpoch}.jpg';
      final savedFile = await imageFile.copy('${backgroundDir.path}/$fileName');
      Log.e('保存背景图片成功: ${savedFile.path}');

      // 立即设置为当前背景
      await setCurrentBackground(savedFile.path);
      return savedFile.path;
    } catch (e) {
      Log.e('保存背景图片失败: $e');
      return null;
    }
  }

  /// 获取所有保存的背景图片
  Future<List<String>> getAllBackgrounds() async {
    final backgroundDir = await _backgroundDirectory;
    final files = await backgroundDir.list().toList();
    return files.whereType<File>().map((file) => file.path).toList();
  }

  /// 删除背景图片
  Future<bool> deleteBackground(String path) async {
    try {
      final file = File(path);
      if (await file.exists()) {
        await file.delete();
        if (currentBackground.value == path) {
          currentBackground.value = '';
        }
        return true;
      }
      return false;
    } catch (e) {
      Log.e('删除背景图片失败: $e');
      return false;
    }
  }

  /// 设置当前背景
  Future<void> setCurrentBackground(String path) async {
    currentBackground.value = path;
    await _saveSettings();
  }

  /// 设置背景透明度
  Future<void> setBackgroundOpacity(double opacity) async {
    backgroundOpacity.value = opacity;
    await _saveSettings();
  }

  /// 设置背景模糊度
  Future<void> setBackgroundBlur(double blur) async {
    backgroundBlur.value = blur;
    await _saveSettings();
  }
}
