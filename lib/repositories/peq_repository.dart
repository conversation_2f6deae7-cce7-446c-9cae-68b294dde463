import '../business/peq/core/models/imported_file.dart';
import '../business/peq/core/models/peq_band.dart';
import '../common/util/log_util.dart';
import '../enums/dx5/dx5_filter_type.dart';
import '../models/imported_file_model.dart';
import '../models/peq_band_model.dart';
import '../models/peq_config_model.dart';
import '../models/peq_settings_model.dart';
import '../repositories/device_repository.dart';

/// PEQ存储库
class PEQRepository {
  final DeviceRepository _deviceRepository;

  PEQRepository(this._deviceRepository);

  /// 加载PEQ设置
  Future<PEQSettingsModel?> loadPEQSettings(String deviceId) async {
    try {
      final deviceSettings = _deviceRepository.getDeviceSettings(deviceId);
      return deviceSettings?.peqSettings;
    } catch (e) {
      Log.e('加载PEQ设置时出错: $e');
      return null;
    }
  }

  /// 保存完整的PEQ设置
  Future<bool> savePEQSettings(
      String deviceId, PEQSettingsModel settings) async {
    try {
      final deviceSettings = _deviceRepository.getDeviceSettings(deviceId);
      if (deviceSettings != null) {
        deviceSettings.peqSettings = settings;
        _deviceRepository.saveDeviceSettings(deviceId, deviceSettings);
        return true;
      }
      return false;
    } catch (e) {
      Log.e('保存PEQ设置时出错: $e');
      return false;
    }
  }

  /// 更新PEQ的启用状态
  Future<bool> updatePEQEnabled(String deviceId, bool isEnabled) async {
    return _updatePartialSettings(deviceId, (settings) {
      return settings.copyWith(isEnabled: isEnabled);
    });
  }

  /// 更新PEQ模式
  Future<bool> updatePEQMode(String deviceId, String mode) async {
    return _updatePartialSettings(deviceId, (settings) {
      return settings.copyWith(peqMode: mode);
    });
  }

  /// 更新活动的显示模式
  Future<bool> updateActiveDisplayModes(
      String deviceId, List<String> modes) async {
    return _updatePartialSettings(deviceId, (settings) {
      return settings.copyWith(activeDisplayModes: modes);
    });
  }

  /// 更新补偿模式
  Future<bool> updateCompensatedMode(
      String deviceId, bool isCompensated) async {
    return _updatePartialSettings(deviceId, (settings) {
      return settings.copyWith(isCompensatedMode: isCompensated);
    });
  }

  /// 更新前置增益
  Future<bool> updatePreampGain(String deviceId, int gain) async {
    return _updatePartialSettings(deviceId, (settings) {
      return settings.copyWith(preampGain: gain);
    });
  }

  /// 更新选中的波段ID
  Future<bool> updateSelectedBandId(String deviceId, int bandId) async {
    return _updatePartialSettings(deviceId, (settings) {
      return settings.copyWith(selectedBandId: bandId);
    });
  }

  /// 更新当前编辑参数
  Future<bool> updateCurrentEditParam(String deviceId, String param) async {
    return _updatePartialSettings(deviceId, (settings) {
      return settings.copyWith(currentEditParam: param);
    });
  }

  /// 更新特定模式的波段
  Future<bool> updateBands(
      String deviceId, String mode, List<PEQBand> bands) async {
    return _updatePartialSettings(deviceId, (settings) {
      final bandModels =
          bands.map((band) => PEQBandModel.fromPEQBand(band)).toList();

      final activeConfig = settings.activeConfig;
      if (activeConfig == null) return settings;

      final updatedModeBands =
          Map<String, List<PEQBandModel>>.from(activeConfig.modeBands);
      updatedModeBands[mode] = bandModels;

      // 创建更新后的配置列表
      final updatedConfig = activeConfig.copyWith(
        modeBands: updatedModeBands,
        updatedAt: DateTime.now(),
      );

      final updatedConfigs = settings.peqConfigs.map((config) {
        return config.id == activeConfig.id ? updatedConfig : config;
      }).toList();

      return settings.copyWith(peqConfigs: updatedConfigs);
    });
  }

  /// 更新单个波段属性
  Future<bool> updateBandProperty(String deviceId, String mode, int bandId,
      String property, dynamic value) async {
    return _updatePartialSettings(deviceId, (settings) {
      final activeConfig = settings.activeConfig;
      if (activeConfig == null) return settings;

      final bands = activeConfig.modeBands[mode] ?? [];
      final bandIndex = bands.indexWhere((band) => band.id == bandId);

      if (bandIndex == -1) return settings; // No change if band not found

      final updatedBands = List<PEQBandModel>.from(bands);
      final band = updatedBands[bandIndex];

      // 根据属性名称创建新的波段对象
      PEQBandModel updatedBand;
      switch (property) {
        case 'enabled':
          updatedBand = band.copyWith(enabled: value as bool);
          break;
        case 'gain':
          updatedBand = band.copyWith(gain: value as double);
          break;
        case 'frequency':
          updatedBand = band.copyWith(frequency: value as double);
          break;
        case 'q':
          updatedBand = band.copyWith(q: value as double);
          break;
        case 'filterType':
          updatedBand =
              band.copyWith(filterType: Dx5FilterType.values[value as int]);
          break;
        case 'name':
          updatedBand = band.copyWith(name: value as String);
          break;
        default:
          return settings;
      }

      updatedBands[bandIndex] = updatedBand;

      // 创建新的modeBands映射
      final updatedModeBands =
          Map<String, List<PEQBandModel>>.from(activeConfig.modeBands);
      updatedModeBands[mode] = updatedBands;

      // 创建更新后的配置
      final updatedConfig = activeConfig.copyWith(
        modeBands: updatedModeBands,
        updatedAt: DateTime.now(),
      );

      // 创建更新后的配置列表
      final updatedConfigs = settings.peqConfigs.map((config) {
        return config.id == activeConfig.id ? updatedConfig : config;
      }).toList();

      return settings.copyWith(peqConfigs: updatedConfigs);
    });
  }

  /// 添加波段
  Future<bool> addBand(String deviceId, String mode, PEQBand band) async {
    return _updatePartialSettings(deviceId, (settings) {
      final activeConfig = settings.activeConfig;
      if (activeConfig == null) return settings;

      final bands = activeConfig.modeBands[mode] ?? [];
      final updatedBands = List<PEQBandModel>.from(bands);
      updatedBands.add(PEQBandModel.fromPEQBand(band));

      final updatedModeBands =
          Map<String, List<PEQBandModel>>.from(activeConfig.modeBands);
      updatedModeBands[mode] = updatedBands;

      // 创建更新后的配置
      final updatedConfig = activeConfig.copyWith(
        modeBands: updatedModeBands,
        updatedAt: DateTime.now(),
      );

      // 创建更新后的配置列表
      final updatedConfigs = settings.peqConfigs.map((config) {
        return config.id == activeConfig.id ? updatedConfig : config;
      }).toList();

      return settings.copyWith(peqConfigs: updatedConfigs);
    });
  }

  /// 删除波段
  Future<bool> removeBand(String deviceId, String mode, int bandId) async {
    return _updatePartialSettings(deviceId, (settings) {
      final activeConfig = settings.activeConfig;
      if (activeConfig == null) return settings;

      final bands = activeConfig.modeBands[mode] ?? [];
      final updatedBands = bands.where((band) => band.id != bandId).toList();

      final updatedModeBands =
          Map<String, List<PEQBandModel>>.from(activeConfig.modeBands);
      updatedModeBands[mode] = updatedBands;

      // 创建更新后的配置
      final updatedConfig = activeConfig.copyWith(
        modeBands: updatedModeBands,
        updatedAt: DateTime.now(),
      );

      // 创建更新后的配置列表
      final updatedConfigs = settings.peqConfigs.map((config) {
        return config.id == activeConfig.id ? updatedConfig : config;
      }).toList();

      return settings.copyWith(peqConfigs: updatedConfigs);
    });
  }

  /// 更新目标文件
  Future<bool> updateTargetFiles(
      String deviceId, List<ImportedFile> files) async {
    return _updatePartialSettings(deviceId, (settings) {
      final updatedFiles =
          files.map((f) => ImportedFileModel.fromImportedFile(f)).toList();
      return settings.copyWith(targetFiles: updatedFiles);
    });
  }

  /// 更新源频响文件
  Future<bool> updateSourceFRFiles(
      String deviceId, List<ImportedFile> files) async {
    return _updatePartialSettings(deviceId, (settings) {
      final updatedFiles =
          files.map((f) => ImportedFileModel.fromImportedFile(f)).toList();
      return settings.copyWith(sourceFRFiles: updatedFiles);
    });
  }

  /// 更新选中的目标文件
  Future<bool> updateSelectedTargetFile(
      String deviceId, ImportedFile? file) async {
    return _updatePartialSettings(deviceId, (settings) {
      final updatedFile =
          file != null ? ImportedFileModel.fromImportedFile(file) : null;
      return settings.copyWith(selectedTargetFile: updatedFile);
    });
  }

  /// 更新选中的源频响文件
  Future<bool> updateSelectedSourceFRFile(
      String deviceId, ImportedFile? file) async {
    return _updatePartialSettings(deviceId, (settings) {
      final updatedFile =
          file != null ? ImportedFileModel.fromImportedFile(file) : null;
      return settings.copyWith(selectedSourceFRFile: updatedFile);
    });
  }

  /// 添加新配置
  Future<bool> addConfig(String deviceId, PEQConfigModel config) async {
    return _updatePartialSettings(deviceId, (settings) {
      final updatedConfigs = List<PEQConfigModel>.from(settings.peqConfigs)
        ..add(config);
      return settings.copyWith(peqConfigs: updatedConfigs);
    });
  }

  /// 删除配置
  Future<bool> removeConfig(String deviceId, String configId) async {
    return _updatePartialSettings(deviceId, (settings) {
      // 不允许删除最后一个配置
      if (settings.peqConfigs.length <= 1) {
        return settings;
      }

      final updatedConfigs =
          settings.peqConfigs.where((c) => c.id != configId).toList();

      // 如果删除的是当前活跃配置，切换到第一个可用配置
      String activeId = settings.activeConfigId;
      if (configId == activeId) {
        activeId = updatedConfigs.first.id;
      }

      return settings.copyWith(
          peqConfigs: updatedConfigs, activeConfigId: activeId);
    });
  }

  /// 切换活跃配置
  Future<bool> switchActiveConfig(String deviceId, String configId) async {
    return _updatePartialSettings(deviceId, (settings) {
      // 确保目标配置存在
      if (!settings.peqConfigs.any((c) => c.id == configId)) {
        return settings;
      }

      return settings.copyWith(activeConfigId: configId);
    });
  }

  /// 更新配置基础信息
  Future<bool> updateConfigInfo(String deviceId, String configId,
      {String? name, String? description}) async {
    return _updatePartialSettings(deviceId, (settings) {
      final configIndex =
          settings.peqConfigs.indexWhere((c) => c.id == configId);
      if (configIndex == -1) return settings;

      final updatedConfigs = List<PEQConfigModel>.from(settings.peqConfigs);
      final config = updatedConfigs[configIndex];

      updatedConfigs[configIndex] = config.copyWith(
        name: name ?? config.name,
        description: description ?? config.description,
        updatedAt: DateTime.now(),
      );

      return settings.copyWith(peqConfigs: updatedConfigs);
    });
  }

  /// 复制配置
  Future<bool> duplicateConfig(String deviceId, String configId,
      {String? newName}) async {
    return _updatePartialSettings(deviceId, (settings) {
      final sourceConfig = settings.peqConfigs.firstWhere(
          (c) => c.id == configId,
          orElse: () => settings.peqConfigs.first);

      final newConfig = sourceConfig.clone(newName: newName);
      final updatedConfigs = List<PEQConfigModel>.from(settings.peqConfigs)
        ..add(newConfig);

      return settings.copyWith(peqConfigs: updatedConfigs);
    });
  }

  /// 辅助方法：应用部分更新到PEQ设置
  Future<bool> _updatePartialSettings(String deviceId,
      PEQSettingsModel Function(PEQSettingsModel) updater) async {
    try {
      final deviceSettings = _deviceRepository.getDeviceSettings(deviceId);
      if (deviceSettings == null) return false;

      // 获取现有设置或创建新的默认设置
      final currentSettings =
          deviceSettings.peqSettings ?? PEQSettingsModel.createDefault();

      // 使用updater函数创建一个新的设置实例
      final updatedSettings = updater(currentSettings);

      // 保存更新后的设置
      deviceSettings.peqSettings = updatedSettings;
      _deviceRepository.saveDeviceSettings(deviceId, deviceSettings);
      return true;
    } catch (e) {
      Log.e('更新部分PEQ设置时出错: $e');
      return false;
    }
  }
}
