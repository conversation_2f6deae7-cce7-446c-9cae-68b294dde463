import 'package:get/get.dart';
import 'package:topping_home/common/util/preference_utils.dart';

import '../common/event/login_event.dart';
import '../common/util/event_bus_manager.dart';
import '../models/login_entity.dart';
import '../models/user_entity.dart';
import 'agreement_repository.dart';

/// 用户仓库
class UserRepository {
  final String keyLoginEntity = "loginEntity";
  final String keyUserEntity = "userEntity";
  static UserRepository? _instance;

  static UserRepository get instance => UserRepository();

  final _loginEntity = Rxn<LoginEntity>();
  final _userEntity = Rxn<UserEntity>();

  // 用户信息的响应式变量
  final nickname = "".obs;
  final signature = "期待您的创作～".obs;
  final sex = "".obs;
  final birthday = "".obs;
  final avatar = "".obs;

  Rx<LoginEntity?> get loginEntity => _loginEntity;

  Rx<UserEntity?> get userEntity => _userEntity;

  UserRepository._internal();

  factory UserRepository() {
    _instance ??= UserRepository._internal();
    return _instance!;
  }

  /// 更新登录信息
  Future<void> updateLoginEntity(LoginEntity? loginEntity) async {
    _loginEntity.value = loginEntity;
    await PreferenceUtils.instance
        .putString(keyLoginEntity, loginEntity?.toString() ?? "");
  }

  /// 更新用户信息
  Future<void> updateUserEntity(UserEntity? userEntity) async {
    _userEntity.value = userEntity;
    if (userEntity != null) {
      // 同步更新响应式变量
      nickname.value = userEntity.nickname ?? "";
      signature.value = userEntity.signature ?? "期待您的创作～";
      sex.value = userEntity.sex ?? "";
      birthday.value = userEntity.birthday ?? "";
      avatar.value = userEntity.avatar ?? "";
    }
    await PreferenceUtils.instance
        .putString(keyUserEntity, userEntity?.toString() ?? "");
  }

  bool isLogin() => loginEntity.value != null;

  /// 更新用户昵称
  void updateNickname(String value) {
    nickname.value = value;
    if (_userEntity.value != null) {
      _userEntity.value?.nickname = value;
      updateUserEntity(_userEntity.value!);
    }
  }

  /// 更新用户签名
  void updateSignature(String value) {
    signature.value = value;
    if (_userEntity.value != null) {
      _userEntity.value?.signature = value;
      updateUserEntity(_userEntity.value!);
    }
  }

  /// 更新用户性别
  void updateSex(String value) {
    sex.value = value;
    if (_userEntity.value != null) {
      _userEntity.value?.sex = value;
      updateUserEntity(_userEntity.value!);
    }
  }

  /// 更新用户生日
  void updateBirthday(String value) {
    birthday.value = value;
    if (_userEntity.value != null) {
      _userEntity.value?.birthday = value;
      updateUserEntity(_userEntity.value!);
    }
  }

  /// 更新用户头像
  void updateAvatar(String value) {
    avatar.value = value;
    if (_userEntity.value != null) {
      _userEntity.value?.avatar = value;
      updateUserEntity(_userEntity.value!);
    }
  }

  /// 判断是否是当前用户
  bool isSelf(int? userId) => _userEntity.value?.id == userId;

  /// 退出登录
  void logout() {
    updateLoginEntity(null);
    updateUserEntity(null);
    eventBus.fire(LoginEvent());
  }

  /// 添加注销账号方法
  Future<void> cancelAccount() async {
    // 清除用户数据
    await updateLoginEntity(null);
    await updateUserEntity(null);

    // 清除所有 SP 数据
    PreferenceUtils.instance.clear();

    // 触发登出事件
    eventBus.fire(LoginEvent());

    // 清除所有页面并跳转到登录页
    Get.offAllNamed('/login');
  }

  /// 判断是否已经同意了协议
  Future<bool> hasAcceptedAgreements() async {
    return await AgreementRepository().hasAcceptedAllAgreements();
  }

  // 注册
  register(String phone, String password) {
    final userId = DateTime.now().millisecondsSinceEpoch; // 使用时间戳作为临时ID

    // 创建登录实体
    final loginEntity = LoginEntity()
      ..token = 'register_token_$userId'
      ..type = 'user'
      ..userId = userId;

    // 创建用户实体
    final userEntity = UserEntity()
      ..id = userId
      ..userPhone = phone
      ..nickname = "himym"
      ..avatar =
          'https://51img2.51txapp.com/2024/06/12/ed458ba6ea0e4afc8a931455750f246e!400x400.jpeg'
      ..signature = '这是一条个性签名～'
      ..sex = '男'
      ..birthday = '2000-01-01'
      ..password = password;

    // 更新两个实体
    updateLoginEntity(loginEntity);
    updateUserEntity(userEntity);
  }
}
