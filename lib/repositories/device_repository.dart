import 'dart:async';

import 'package:get/get.dart';
import 'package:hive_ce/hive.dart';

import '../common/util/log_util.dart';
import '../factories/device_settings_factory.dart';
import '../models/d900_device_settings.dart';
import '../models/device_entity.dart';
import '../models/dx5_device_settings.dart';

/// 设备仓库
class DeviceRepository {
  static const String tag = 'DeviceRepository';
  static const String devicesBoxName = 'devices';
  static const String settingsBoxName = 'device_settings';

  final deviceListStream = <DeviceEntity>[].obs;

  // 添加设备设置更新流
  final Map<String, StreamController<dynamic>> _settingsStreamControllers = {};

  late Box<DeviceEntity> devicesBox;
  late Box<dynamic> settingsBox;

  Future<void> init() async {
    try {
      Log.i('$tag - 开始初始化设备仓库');

      // 尝试打开设备盒子
      Log.i('$tag - 打开设备盒子: $devicesBoxName');
      devicesBox = await Hive.openBox<DeviceEntity>(devicesBoxName);

      Log.i('$tag - 打开设置盒子: $settingsBoxName');
      settingsBox = await Hive.openBox<dynamic>(settingsBoxName);

      // 记录盒子状态
      final deviceCount = devicesBox.length;
      final settingsCount = settingsBox.length;
      Log.i('$tag - 盒子初始化成功: 设备数=$deviceCount, 设置数=$settingsCount');

      // 如果有设备，记录设备ID
      if (deviceCount > 0) {
        final deviceIds = devicesBox.keys.toList();
        Log.i('$tag - 已保存的设备ID: $deviceIds');
      }

      // 如果有设置，记录设置ID
      if (settingsCount > 0) {
        final settingsIds = settingsBox.keys.toList();
        Log.i('$tag - 已保存的设置ID: $settingsIds');

        // 检查每个设置的类型
        for (final id in settingsIds) {
          final settings = settingsBox.get(id);
          if (settings != null) {
            if (settings is Dx5DeviceSettings) {
              Log.i('$tag - 设置 $id 是 DX5 设备设置');
              if (settings.peqSettings != null) {
                Log.i('$tag - 设置 $id 包含PEQ设置: isEnabled=${settings.peqSettings?.isEnabled}');
              }
            } else if (settings is D900DeviceSettings) {
              Log.i('$tag - 设置 $id 是 D900 设备设置');
              if (settings.peqSettings != null) {
                Log.i('$tag - 设置 $id 包含PEQ设置: isEnabled=${settings.peqSettings?.isEnabled}');
              }
            } else {
              Log.w('$tag - 设置 $id 是未知类型: ${settings.runtimeType}');
            }
          }
        }
      }

      deviceListStream.value = getAllDevices();
      Log.i('$tag - 设备仓库初始化完成');
    } catch (e, stackTrace) {
      Log.e('$tag - 打开设备盒子失败: $e', stackTrace);

      try {
        // 如果出错，删除现有盒子并重新创建
        Log.w('$tag - 尝试删除并重新创建盒子');
        await Hive.deleteBoxFromDisk(devicesBoxName);
        await Hive.deleteBoxFromDisk(settingsBoxName);

        // 重新创建盒子
        Log.i('$tag - 重新创建设备盒子');
        devicesBox = await Hive.openBox<DeviceEntity>(devicesBoxName);

        Log.i('$tag - 重新创建设置盒子');
        settingsBox = await Hive.openBox<dynamic>(settingsBoxName);

        deviceListStream.value = [];
        Log.i('$tag - 设备仓库重置完成');
      } catch (e2, stackTrace2) {
        Log.e('$tag - 重置设备盒子失败: $e2', stackTrace2);
      }
    }
  }

  /// 保存设备
  Future<void> saveDevice(DeviceEntity device) async {
    try {
      Log.i('$tag - 开始保存设备: ${device.id}, MAC: ${device.macAddress}, 型号: ${device.deviceModel}');

      // 检查设备盒子是否已打开
      if (!Hive.isBoxOpen(devicesBoxName)) {
        Log.w('$tag - 设备盒子未打开，尝试重新打开');
        devicesBox = await Hive.openBox<DeviceEntity>(devicesBoxName);
      }

      // 保存设备
      await devicesBox.put(device.id, device);

      // 确保数据被写入磁盘
      await devicesBox.flush();

      // 验证设备是否已保存
      final savedDevice = devicesBox.get(device.id);
      if (savedDevice != null) {
        Log.i('$tag - 设备保存成功: ${device.id}');
      } else {
        Log.e('$tag - 设备保存失败，无法从盒子中读取: ${device.id}');
      }

      // 更新设备列表流
      deviceListStream.value = getAllDevices();
    } catch (e, stackTrace) {
      Log.e('$tag - 保存设备失败: ${device.id}, 错误: $e', stackTrace);
      rethrow;
    }
  }

  /// 获取所有已保存的设备
  List<DeviceEntity> getAllDevices() {
    return devicesBox.values.toList();
  }

  /// 通过ID获取设备
  DeviceEntity? getDevice(String id) {
    return devicesBox.get(id);
  }

  /// 删除设备
  void deleteDevice(String id) {
    devicesBox.delete(id);
    settingsBox.delete(id); // 同时删除设备设置
    // 确保数据被写入磁盘
    devicesBox.flush();
    settingsBox.flush();
    deviceListStream.value = getAllDevices();
  }

  /// 更新设备连接状态
  void updateDeviceConnectionState(String id, int connected) {
    final device = devicesBox.get(id);
    if (device != null) {
      device.connected = connected;
      devicesBox.put(id, device);
      // 确保数据被写入磁盘
      devicesBox.flush();
      deviceListStream.value = getAllDevices();
    }
  }

  /// 获取设备设置更新流
  Stream<dynamic> getSettingsStream(String deviceId) {
    if (!_settingsStreamControllers.containsKey(deviceId)) {
      _settingsStreamControllers[deviceId] = StreamController<dynamic>.broadcast();
    }
    return _settingsStreamControllers[deviceId]!.stream;
  }

  /// 保存设备设置
  void saveDeviceSettings(String deviceId, dynamic settings) {
    try {
      Log.i('$tag - 开始保存设备设置: deviceId=$deviceId');

      // 检查设置类型
      if (settings is Dx5DeviceSettings) {
        // DX5 设备设置
        final hasPEQ = settings.peqSettings != null;
        final settingsInfo = {
          'id': settings.id,
          'deviceId': deviceId,
          'title': settings.title,
          'hasPEQSettings': hasPEQ,
          'type': 'DX5',
        };
        Log.i('$tag - 设置内容: $settingsInfo');

        // 记录PEQ设置摘要（如果有）
        if (hasPEQ) {
          final peqInfo = {
            'isEnabled': settings.peqSettings?.isEnabled,
            'peqMode': settings.peqSettings?.peqMode,
            'configsCount': settings.peqSettings?.peqConfigs.length,
            'activeConfigId': settings.peqSettings?.activeConfigId,
          };
          Log.i('PEQ设置摘要: $peqInfo');
        }

        settings.deviceId = deviceId;
        settingsBox.put(deviceId, settings);
      } else if (settings is D900DeviceSettings) {
        // D900 设备设置
        final hasPEQ = settings.peqSettings != null;
        final settingsInfo = {
          'id': settings.id,
          'deviceId': deviceId,
          'title': settings.title,
          'hasPEQSettings': hasPEQ,
          'type': 'D900',
        };
        Log.i('$tag - 设置内容: $settingsInfo');

        // 记录PEQ设置摘要（如果有）
        if (hasPEQ) {
          final peqInfo = {
            'isEnabled': settings.peqSettings?.isEnabled,
            'peqMode': settings.peqSettings?.peqMode,
            'configsCount': settings.peqSettings?.peqConfigs.length,
            'activeConfigId': settings.peqSettings?.activeConfigId,
          };
          Log.i('PEQ设置摘要: $peqInfo');
        }

        settings.deviceId = deviceId;
        settingsBox.put(deviceId, settings);
      } else {
        Log.e('$tag - 无效的设置类型: ${settings.runtimeType}');
        return;
      }

      // 确保数据被写入磁盘
      settingsBox.flush();

      // 验证保存结果
      final savedSettings = settingsBox.get(deviceId);
      if (savedSettings != null) {
        Log.i('$tag - 设备设置保存成功: deviceId=$deviceId, 类型: ${savedSettings.runtimeType}');

        // 验证PEQ设置是否保存成功
        bool hasPEQ = false;
        bool hasSavedPEQ = false;

        if (settings is Dx5DeviceSettings) {
          hasPEQ = settings.peqSettings != null;
          if (savedSettings is Dx5DeviceSettings) {
            hasSavedPEQ = savedSettings.peqSettings != null;
          }
        } else if (settings is D900DeviceSettings) {
          hasPEQ = settings.peqSettings != null;
          if (savedSettings is D900DeviceSettings) {
            hasSavedPEQ = savedSettings.peqSettings != null;
          }
        }

        if (hasPEQ && hasSavedPEQ) {
          Log.i('$tag - PEQ设置保存成功');
        } else if (hasPEQ) {
          Log.e('$tag - PEQ设置保存失败: 原本有PEQ设置但保存后为空');
        }

        // 通知设置更新
        if (_settingsStreamControllers.containsKey(deviceId)) {
          Log.d('$tag - 通知设置更新流: deviceId=$deviceId');
          _settingsStreamControllers[deviceId]!.add(savedSettings);
        }
      } else {
        Log.e('$tag - 设备设置保存失败: 无法获取保存的设置');
      }
    } catch (e, stackTrace) {
      Log.e('$tag - 保存设备设置时发生异常: $e', stackTrace);
    }
  }

  /// 获取设备设置
  dynamic getDeviceSettings(String deviceId) {
    try {
      Log.i('$tag - 开始获取设备设置: deviceId=$deviceId');

      final settings = settingsBox.get(deviceId);

      if (settings != null) {
        // 检查设置类型
        if (settings is Dx5DeviceSettings) {
          // DX5 设备设置
          final hasPEQ = settings.peqSettings != null;
          final settingsInfo = {
            'id': settings.id,
            'deviceId': settings.deviceId,
            'title': settings.title,
            'hasPEQSettings': hasPEQ,
            'type': 'DX5',
          };
          Log.i('$tag - 获取到 DX5 设备设置: $settingsInfo');

          // 记录PEQ设置摘要（如果有）
          if (hasPEQ) {
            final peqInfo = {
              'isEnabled': settings.peqSettings?.isEnabled,
              'peqMode': settings.peqSettings?.peqMode,
              'configsCount': settings.peqSettings?.peqConfigs.length,
              'activeConfigId': settings.peqSettings?.activeConfigId,
            };
            Log.i('$tag - 获取到PEQ设置: $peqInfo');
          }

          return settings;
        } else if (settings is D900DeviceSettings) {
          // D900 设备设置
          final hasPEQ = settings.peqSettings != null;
          final settingsInfo = {
            'id': settings.id,
            'deviceId': settings.deviceId,
            'title': settings.title,
            'hasPEQSettings': hasPEQ,
            'type': 'D900',
          };
          Log.i('$tag - 获取到 D900 设备设置: $settingsInfo');

          // 记录PEQ设置摘要（如果有）
          if (hasPEQ) {
            final peqInfo = {
              'isEnabled': settings.peqSettings?.isEnabled,
              'peqMode': settings.peqSettings?.peqMode,
              'configsCount': settings.peqSettings?.peqConfigs.length,
              'activeConfigId': settings.peqSettings?.activeConfigId,
            };
            Log.i('$tag - 获取到PEQ设置: $peqInfo');
          }

          return settings;
        } else {
          Log.e('$tag - 未知的设置类型: ${settings.runtimeType}');
          return null;
        }
      } else {
        Log.w('$tag - 未找到设备设置: deviceId=$deviceId');
        return null;
      }
    } catch (e, stackTrace) {
      Log.e('$tag - 获取设备设置时发生异常: $e', stackTrace);
      return null;
    }
  }

  /// 更新设备设置
  dynamic updateDeviceSettings(String deviceId, Function updater) {
    try {
      Log.i('$tag - 开始更新设备设置: deviceId=$deviceId');

      // 获取设备实体
      final device = devicesBox.get(deviceId);
      if (device == null) {
        Log.e('$tag - 找不到设备: deviceId=$deviceId');
        return null;
      }

      // 获取现有设置
      var settings = getDeviceSettings(deviceId);

      // 如果设置不存在，根据设备型号创建新设置
      if (settings == null) {
        Log.i('$tag - 设备设置不存在，根据设备型号创建新设置: deviceModel=${device.deviceModel}');
        settings = DeviceSettingsFactory.createDefaultSettings(device.deviceModel);

        if (settings is Dx5DeviceSettings) {
          settings.deviceId = deviceId;
        } else if (settings is D900DeviceSettings) {
          settings.deviceId = deviceId;
        }
      }

      if (settings == null) {
        Log.e('$tag - 无法创建设备设置');
        return null;
      }

      // 记录更新前的设置状态
      bool hasPEQBefore = false;
      if (settings is Dx5DeviceSettings) {
        hasPEQBefore = settings.peqSettings != null;
      } else if (settings is D900DeviceSettings) {
        hasPEQBefore = settings.peqSettings != null;
      }
      Log.i('$tag - 更新前的设置状态: hasPEQ=$hasPEQBefore, 类型=${settings.runtimeType}');

      // 应用更新函数
      final updatedSettings = updater(settings);

      // 如果更新函数返回了新的设置对象，使用返回的对象
      if (updatedSettings != null) {
        settings = updatedSettings;
      }

      // 记录更新后的设置状态
      bool hasPEQAfter = false;
      if (settings is Dx5DeviceSettings) {
        hasPEQAfter = settings.peqSettings != null;
      } else if (settings is D900DeviceSettings) {
        hasPEQAfter = settings.peqSettings != null;
      }
      Log.i('$tag - 更新后的设置状态: hasPEQ=$hasPEQAfter, 类型=${settings.runtimeType}');

      // 如果PEQ设置发生变化，记录详细信息
      if (hasPEQBefore != hasPEQAfter) {
        Log.i('$tag - PEQ设置状态发生变化: $hasPEQBefore -> $hasPEQAfter');
      }

      // 保存更新后的设置
      saveDeviceSettings(deviceId, settings);
      Log.i('$tag - 设备设置更新完成: deviceId=$deviceId');

      return settings;
    } catch (e, stackTrace) {
      Log.e('$tag - 更新设备设置时发生异常: $e', stackTrace);
      return null;
    }
  }

  /// 关闭并清理资源
  void dispose() {
    // 关闭所有流控制器
    for (final controller in _settingsStreamControllers.values) {
      controller.close();
    }
    _settingsStreamControllers.clear();
  }

  /// 清除所有设备
  Future<void> clearAllDevices() async {
    await devicesBox.clear();
    await settingsBox.clear();
    // 确保数据被写入磁盘
    await devicesBox.flush();
    await settingsBox.flush();
    deviceListStream.clear();
  }
}
