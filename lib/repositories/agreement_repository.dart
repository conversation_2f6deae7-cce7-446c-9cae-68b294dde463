import 'dart:convert';
import 'dart:ui';

import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:topping_home/common/util/preference_utils.dart';

/// 协议管理类
class AgreementRepository {
  static final AgreementRepository _instance = AgreementRepository._internal();

  factory AgreementRepository() => _instance;

  AgreementRepository._internal();

  // 当前协议和隐私政策版本
  static const String currentAgreementVersion = "1.0.0";
  static const String currentPrivacyVersion = "1.0.0";

  // 保存在 SharedPreferences 中的键名
  final String keyAgreementAccepted = "agreement_accepted_version";
  final String keyPrivacyAccepted = "privacy_accepted_version";

  /// 检查用户是否已接受最新的用户协议
  Future<bool> hasAcceptedLatestAgreement() async {
    final acceptedVersion =
        PreferenceUtils.instance.getString(keyAgreementAccepted);
    return acceptedVersion == currentAgreementVersion;
  }

  /// 检查用户是否已接受最新的隐私政策
  Future<bool> hasAcceptedLatestPrivacy() async {
    final acceptedVersion =
        PreferenceUtils.instance.getString(keyPrivacyAccepted);
    return acceptedVersion == currentPrivacyVersion;
  }

  /// 检查用户是否已接受所有必要的协议
  Future<bool> hasAcceptedAllAgreements() async {
    return await hasAcceptedLatestAgreement() &&
        await hasAcceptedLatestPrivacy();
  }

  /// 记录用户接受用户协议
  Future<void> acceptAgreement() async {
    await PreferenceUtils.instance
        .putString(keyAgreementAccepted, currentAgreementVersion);
  }

  /// 记录用户接受隐私政策
  Future<void> acceptPrivacy() async {
    await PreferenceUtils.instance
        .putString(keyPrivacyAccepted, currentPrivacyVersion);
  }

  /// 接受所有协议
  Future<void> acceptAllAgreements() async {
    await acceptAgreement();
    await acceptPrivacy();
  }

  /// 加载当前语言的用户协议内容
  Future<Map<String, dynamic>> loadAgreementContent() async {
    final locale = Get.locale;
    String lang = _getLanguageCode(locale);

    try {
      final jsonString =
          await rootBundle.loadString('assets/legal/agreement_$lang.json');
      return json.decode(jsonString);
    } catch (e) {
      // 如果找不到当前语言的文件，默认使用英文
      final defaultJson =
          await rootBundle.loadString('assets/legal/agreement_zh.json');
      return json.decode(defaultJson);
    }
  }

  /// 加载当前语言的隐私政策内容
  Future<Map<String, dynamic>> loadPrivacyContent() async {
    final locale = Get.locale;
    String lang = _getLanguageCode(locale);

    try {
      final jsonString =
          await rootBundle.loadString('assets/legal/privacy_$lang.json');
      return json.decode(jsonString);
    } catch (e) {
      // 如果找不到当前语言的文件，默认使用英文
      final defaultJson =
          await rootBundle.loadString('assets/legal/privacy_zh.json');
      return json.decode(defaultJson);
    }
  }

  /// 获取语言代码
  String _getLanguageCode(Locale? locale) {
    if (locale == null) return 'en';

    if (locale.scriptCode == 'Hans' || locale.scriptCode == 'TW') {
      return 'zh_${locale.scriptCode}';
    }

    return locale.languageCode;
  }
}
