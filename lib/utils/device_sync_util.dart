import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'package:topping_ble_control/device/device_factory.dart';
import 'package:topping_ble_control/utils/log_util.dart';
import 'package:topping_home/enums/d900/d900_display_type.dart';
import 'package:topping_home/enums/d900/d900_input_type.dart';
import 'package:topping_home/enums/d900/d900_language_type.dart';
import 'package:topping_home/enums/d900/d900_multi_function_key_type.dart';
import 'package:topping_home/enums/d900/d900_output_type.dart';
import 'package:topping_home/enums/d900/d900_power_trigger_type.dart';
import 'package:topping_home/enums/d900/d900_screen_brightness_type.dart';
import 'package:topping_home/enums/d900/d900_theme_type.dart';
import 'package:topping_home/enums/d900/d900_usb_type.dart';
import 'package:topping_home/enums/dx5/dx5_decode_mode_type.dart';
import 'package:topping_home/enums/dx5/dx5_display_type.dart';
import 'package:topping_home/enums/dx5/dx5_filter_parameter_type.dart';
import 'package:topping_home/enums/dx5/dx5_headphone_gain_type.dart';
import 'package:topping_home/enums/dx5/dx5_input_type.dart';
import 'package:topping_home/enums/dx5/dx5_language_type.dart';
import 'package:topping_home/enums/dx5/dx5_multi_function_key_type.dart';
import 'package:topping_home/enums/dx5/dx5_output_type.dart';
import 'package:topping_home/enums/dx5/dx5_power_trigger_type.dart';
import 'package:topping_home/enums/dx5/dx5_screen_brightness_type.dart';
import 'package:topping_home/enums/dx5/dx5_theme_type.dart';
import 'package:topping_home/enums/dx5/dx5_usb_type.dart';
import 'package:topping_home/factories/device_settings_factory.dart';
import 'package:topping_home/models/d900_device_settings.dart';
import 'package:topping_home/models/device_entity.dart';
import 'package:topping_home/models/dx5_device_settings.dart';
import 'package:topping_home/repositories/device_repository.dart';
import 'package:topping_home/router/routers.dart';

/// 设备同步工具类，统一封装设备数据同步逻辑
/// 用于在设备连接成功后同步设备数据并导航到详情页
class DeviceSyncUtil {
  static const String tag = "DeviceSyncUtil";

  /// 同步设备数据并在适当时导航到设备详情页
  ///
  /// [device] 要同步的设备实体
  /// [deviceFactory] 设备工厂实例
  /// [onSyncComplete] 同步完成后的可选回调函数
  static void syncDeviceAndNavigate({
    required DeviceEntity device,
    required DeviceFactory deviceFactory,
    VoidCallback? onSyncComplete,
  }) {
    Log.i("$tag 开始同步设备数据和导航过程");

    // 在没有设备实体的情况下直接跳出
    if (device.id.isEmpty) {
      Log.e("$tag 设备ID为空，无法同步");
      return;
    }

    final storage = Get.find<DeviceRepository>();

    // 先检查设备是否存在于数据库中
    var existingDevice = storage.getDevice(device.id);
    if (existingDevice == null) {
      Log.w("$tag 数据库中找不到设备 ID: ${device.id}，尝试使用 MAC 地址查找");

      // 尝试使用 MAC 地址查找设备
      if (device.macAddress.isNotEmpty) {
        var devices = storage.getAllDevices();
        existingDevice = devices.firstWhere(
          (d) => d.macAddress == device.macAddress,
          orElse: () => DeviceEntity.empty(),
        );

        if (existingDevice.id.isNotEmpty) {
          Log.i("通过 MAC 地址找到设备: ${existingDevice.id}");
          // 更新设备 ID
          device.id = existingDevice.id;
        }
      }
    }

    // 保存设备实体，确保设备ID存在于数据库中
    Log.i(
        "$tag 保存设备实体: ${device.id}, MAC: ${device.macAddress}, 型号: ${device.deviceModel}");
    storage.saveDevice(device);

    // 再次检查设备是否存在
    existingDevice = storage.getDevice(device.id);
    if (existingDevice == null) {
      Log.e("$tag 保存设备后仍然找不到设备: ${device.id}");
    } else {
      Log.i("$tag 设备已成功保存到数据库: ${existingDevice.id}");
    }

    // 获取当前设备管理器
    final deviceManager = deviceFactory.currentDeviceManager;
    if (deviceManager == null) {
      Log.e("$tag 无法获取当前设备管理器");
      return;
    }

    // 然后同步数据 - 这是唯一调用requestSettings的地方
    Log.i("$tag 开始同步设备数据（唯一调用requestSettings的地方）");
    deviceManager.requestSettings();

    // 等待数据同步完成后再跳转
    deviceManager.settings?.listen((settings) {
      Log.i("$tag 收到设备设置数据，更新本地存储 $settings");

      // 根据设备型号使用不同的设置类
      if (DeviceSettingsFactory.isD900Device(device.deviceModel)) {
        // 使用 D900 设备设置
        Log.i("$tag 检测到 D900 设备，使用 D900 设置类");

        // 再次检查设备是否存在
        var existingDevice = storage.getDevice(device.id);
        if (existingDevice == null) {
          Log.e("$tag 更新设置前找不到设备: ${device.id}");
          // 尝试再次保存设备
          storage.saveDevice(device);
          existingDevice = storage.getDevice(device.id);
          if (existingDevice == null) {
            Log.e("$tag 再次保存后仍然找不到设备: ${device.id}");
            // 跳过设置更新
            Log.i("$tag 数据同步完成，跳转到设备详情页");
            Get.toNamed(AppRoutes.deviceDetailPage, arguments: device);
            if (onSyncComplete != null) {
              onSyncComplete();
            }
            return;
          }
        }

        storage.updateDeviceSettings(device.id, (deviceSettings) {
          // 检查设置类型
          if (deviceSettings is! D900DeviceSettings) {
            Log.e(
                "$tag 预期的是D900DeviceSettings类型，但实际收到的是${deviceSettings.runtimeType}");
            Log.i("$tag 跳过设置更新，请确保DeviceRepository已正确创建了D900DeviceSettings对象");
            return deviceSettings; // 返回原始设置，不做修改
          }

          Log.i("$tag 更新 D900 设备设置");

          // 更新现有的 D900 设备设置
          var d900Settings = deviceSettings;

          // 更新基本设置
          d900Settings.power = settings.isOn;
          // 将设备返回的正值转换为UI需要的负值
          d900Settings.volume = -settings.volume;
          Log.i(
              "$tag 设置 D900 音量为 ${d900Settings.volume} (原始值: ${settings.volume})");
          d900Settings.mute = settings.mute;
          d900Settings.selectedInput =
              D900InputType.fromValue(settings.inputType);
          d900Settings.selectedOutput =
              D900OutputType.fromValue(settings.outputType);
          d900Settings.displayType =
              D900DisplayType.fromValue(settings.displayMode);

          // --- 新增采样率同步 ---
          if (d900Settings.sampleRate != settings.sampling) {
            Log.i(
                "$tag 同步 D900 采样率: 从 ${d900Settings.sampleRate} 到 ${settings.sampling}");
            d900Settings.sampleRate = settings.sampling;
          } else {
            Log.d("$tag D900 采样率未变化: ${settings.sampling}");
          }
          // --- 采样率同步结束 ---

          // 更新高级设置
          d900Settings.theme = D900ThemeType.fromValue(settings.theme);
          d900Settings.powerTrigger =
              D900PowerTriggerType.fromValue(settings.powerTrigger);
          d900Settings.channelBalance = settings.balance;
          d900Settings.audioBluetooth = settings.audioBluetooth;
          d900Settings.bluetoothAPTX = settings.bluetoothAptx;
          d900Settings.remoteControl = settings.remoteEnabled;
          d900Settings.multiFunctionKey =
              D900MultiFunctionKeyType.fromValue(settings.multifunctionKey);
          d900Settings.usbType = D900UsbType.fromValue(settings.usbMode);
          d900Settings.screenBrightness =
              D900ScreenBrightnessType.fromValue(settings.screenBrightness);
          d900Settings.language = D900LanguageType.fromValue(settings.language);

          // 返回更新后的设置
          return d900Settings;
        });
      } else {
        // 使用 DX5 设备设置（默认）
        Log.i("$tag 使用 DX5 设置类");

        // 再次检查设备是否存在
        var existingDevice = storage.getDevice(device.id);
        if (existingDevice == null) {
          Log.e("$tag 更新设置前找不到设备: ${device.id}");
          // 尝试再次保存设备
          storage.saveDevice(device);
          existingDevice = storage.getDevice(device.id);
          if (existingDevice == null) {
            Log.e("$tag 再次保存后仍然找不到设备: ${device.id}");
            // 跳过设置更新
            Log.i("$tag 数据同步完成，跳转到设备详情页");
            Get.toNamed(AppRoutes.deviceDetailPage, arguments: device);
            if (onSyncComplete != null) {
              onSyncComplete();
            }
            return;
          }
        }

        storage.updateDeviceSettings(device.id, (deviceSettings) {
          // 检查设置类型
          if (deviceSettings is! Dx5DeviceSettings) {
            Log.e(
                "$tag 预期的是DeviceSettings类型，但实际收到的是${deviceSettings.runtimeType}");
            Log.i("$tag 跳过设置更新，请确保DeviceRepository已正确创建了DeviceSettings对象");
            return deviceSettings; // 返回原始设置，不做修改
          }

          // 更新基本设置
          deviceSettings.power = settings.isOn;
          // 将设备返回的正值转换为UI需要的负值
          deviceSettings.volume = -settings.volume;
          Log.i(
              "$tag 设置 DX5 音量为 ${deviceSettings.volume} (原始值: ${settings.volume})");
          deviceSettings.mute = settings.mute;
          deviceSettings.selectedInput =
              Dx5InputType.fromValue(settings.inputType);
          deviceSettings.selectedOutput =
              Dx5OutputType.fromValue(settings.outputType);
          deviceSettings.headphoneEnabled = settings.headphoneEnabled;
          deviceSettings.headphoneGainLevel =
              Dx5HeadphoneGainType.fromValue(settings.headphoneGain);
          deviceSettings.displayType =
              Dx5DisplayType.fromValue(settings.displayMode);

          // 更新高级设置
          deviceSettings.theme = Dx5ThemeType.fromValue(settings.theme);
          deviceSettings.powerTrigger =
              Dx5PowerTriggerType.fromValue(settings.powerTrigger);
          deviceSettings.channelBalance = settings.balance;
          deviceSettings.filter =
              Dx5FilterParameterType.fromValue(settings.pcmFilter);
          deviceSettings.decodeMode =
              Dx5DecodeModeType.fromValue(settings.decodeMode);
          deviceSettings.audioBluetooth = settings.audioBluetooth;
          deviceSettings.bluetoothAPTX = settings.bluetoothAptx;
          deviceSettings.remoteControl = settings.remoteEnabled;
          deviceSettings.multiFunctionKey =
              Dx5MultiFunctionKeyType.fromValue(settings.multifunctionKey);
          deviceSettings.usbType = Dx5UsbType.fromValue(settings.usbMode);
          deviceSettings.screenBrightness =
              Dx5ScreenBrightnessType.fromValue(settings.screenBrightness);
          deviceSettings.language =
              Dx5LanguageType.fromValue(settings.language);

          // 返回更新后的设置
          return deviceSettings;
        });
      }

      Log.i("$tag 数据同步完成，跳转到设备详情页");

      // 执行可选的回调函数
      if (onSyncComplete != null) {
        onSyncComplete();
      }

      // 导航到设备详情页
      Get.toNamed(AppRoutes.deviceDetailPage, arguments: device);
    });
  }
}
