import 'dart:async';

import 'package:get/get.dart';
import 'package:topping_ble_control/device/d900/d900_device_manager.dart';
import 'package:topping_ble_control/device/device_factory.dart';
import 'package:topping_ble_control/model/d900/d900_settings.dart';
import 'package:topping_ble_control/model/dx5ii/dx5ii_settings.dart';
import 'package:topping_ble_control/model/enums/device_mode_type.dart';
import 'package:topping_home/enums/d900/d900_display_type.dart';
import 'package:topping_home/enums/d900/d900_input_type.dart';
import 'package:topping_home/enums/d900/d900_language_type.dart';
import 'package:topping_home/enums/d900/d900_multi_function_key_type.dart';
import 'package:topping_home/enums/d900/d900_output_type.dart';
import 'package:topping_home/enums/d900/d900_power_trigger_type.dart';
import 'package:topping_home/enums/d900/d900_screen_brightness_type.dart';
import 'package:topping_home/enums/d900/d900_theme_type.dart';
import 'package:topping_home/enums/d900/d900_usb_type.dart';
import 'package:topping_home/models/d900_device_settings.dart';

import '../common/util/log_util.dart';
import '../enums/d900/d900_iis_dsd_channel_type.dart';
import '../enums/d900/d900_iis_phase_type.dart';
import '../enums/d900/d900_usb_select_type.dart';
import '../enums/dx5/dx5_decode_mode_type.dart';
import '../enums/dx5/dx5_display_type.dart';
import '../enums/dx5/dx5_filter_parameter_type.dart';
import '../enums/dx5/dx5_headphone_gain_type.dart';
import '../enums/dx5/dx5_input_type.dart';
import '../enums/dx5/dx5_language_type.dart';
import '../enums/dx5/dx5_multi_function_key_type.dart';
import '../enums/dx5/dx5_output_type.dart';
import '../enums/dx5/dx5_power_trigger_type.dart';
import '../enums/dx5/dx5_screen_brightness_type.dart';
import '../enums/dx5/dx5_theme_type.dart';
import '../enums/dx5/dx5_usb_type.dart';
import '../models/dx5_device_settings.dart';
import '../repositories/device_repository.dart';

/// 设备事件同步器
/// 监听来自 DeviceManager 的事件流，并将更新同步到 DeviceRepository
class DeviceEventSynchronizer {
  final String deviceId;
  final DeviceManager _manager;
  final DeviceRepository _storage = Get.find<DeviceRepository>();
  final List<StreamSubscription> _subscriptions = [];
  final bool _isD900Device;

  static const String tag = "DeviceEventSynchronizer";

  DeviceEventSynchronizer({
    required this.deviceId,
    required DeviceManager manager,
  })  : _manager = manager,
        _isD900Device = _determineIsD900Device(deviceId) {
    Log.i("$tag: Initialized for device $deviceId (isD900: $_isD900Device)");
  }

  /// 确定设备是否为D900
  static bool _determineIsD900Device(String deviceId) {
    try {
      // 优先使用设备类型判断
      final deviceFactory = DeviceFactory();
      final connectedDevice = deviceFactory.getConnectedDevice();
      if (connectedDevice != null &&
          connectedDevice.id.toString() == deviceId) {
        final isD900 = connectedDevice.deviceType == DeviceModeType.dx9;
        Log.i(
            "$tag: 使用设备类型判断设备 $deviceId 是否为D900: $isD900 (类型: ${connectedDevice.deviceType})");
        return isD900;
      }

      // 退化到使用设备模型判断
      final device = Get.find<DeviceRepository>().getDevice(deviceId);
      final isD900ByModel = device?.deviceModel == 'D900';
      Log.i(
          "$tag: 使用设备模型判断设备 $deviceId 是否为D900: $isD900ByModel (模型: ${device?.deviceModel})");
      return isD900ByModel;
    } catch (e) {
      Log.e("$tag: 判断设备类型时出错: $e");
      return false;
    }
  }

  /// 开始监听所有相关事件流
  void startListening() {
    stopListening(); //确保之前的监听已停止

    Log.i("$tag: Starting to listen for events from ${_manager.runtimeType}");

    // 辅助函数，简化监听和更新逻辑
    void listenAndUpdate<T>(Stream<T> stream, String eventName,
        void Function(dynamic settings, T value) updateLogic) {
      final subscription = stream.listen((value) {
        Log.d("$tag: Received event '$eventName': $value");
        final currentSettings = _storage.getDeviceSettings(deviceId);
        if (currentSettings != null) {
          // 检查类型并应用更新逻辑
          if (_isD900Device && currentSettings is D900DeviceSettings) {
            updateLogic(currentSettings, value);
            _saveIfChanged(currentSettings, eventName);
          } else if (!_isD900Device && currentSettings is Dx5DeviceSettings) {
            updateLogic(currentSettings, value);
            _saveIfChanged(currentSettings, eventName);
          } else {
            Log.w(
                "$tag: Settings type mismatch for $deviceId. Expected ${_isD900Device ? 'D900' : 'DX5'}, got ${currentSettings.runtimeType}");
          }
        } else {
          Log.w(
              "$tag: Cannot update '$eventName', settings not found for device $deviceId");
        }
      }, onError: (error) {
        Log.e("$tag: Error listening to '$eventName': $error");
      });
      _subscriptions.add(subscription);
    }

    // 监听各个具体的事件流
    listenAndUpdate<bool>(_manager.powerState, "powerState", (settings, value) {
      settings.power = value;
      Log.d("$tag: Updated power state to ${settings.power}");
    });
    listenAndUpdate<int>(_manager.volume, "volume", (settings, value) {
      Log.i("【音量调试】$tag: 接收到音量值: $value, 当前设置中的音量值: ${settings.volume}");
      // 将设备返回的值转换为UI需要的负值
      int oldVolume = settings.volume;
      // 确保音量值为负值（对于DX5设备）
      settings.volume = value > 0 ? -value : value;
      Log.i(
          "【音量调试】$tag: 更新音量值: 从 $oldVolume 变为 ${settings.volume} (原始设备值: $value)");
      Log.d("$tag: Updated volume to ${settings.volume} (原始值: $value)");

      // 当音量不为-99时，如果当前是静音状态，则取消静音
      if (settings.volume != -99 && settings.mute) {
        Log.i("【音量调试】$tag: 音量不为最小值，自动取消静音, 音量值: ${settings.volume}");
        settings.mute = false;
        // 同步静音状态到设备
        _manager.setMute(false);
      }
    });
    listenAndUpdate<bool>(_manager.mute, "mute", (settings, value) {
      settings.mute = value;
      Log.d("$tag: Updated mute to ${settings.mute}");
    });
    listenAndUpdate<int>(_manager.inputType, "inputType", (settings, value) {
      if (settings is D900DeviceSettings) {
        settings.selectedInput =
            _safeEnumFromIndex(D900InputType.values, value, D900InputType.usb);
        Log.d("$tag: Updated input type to ${settings.selectedInput}");
      } else if (settings is Dx5DeviceSettings) {
        settings.selectedInput =
            _safeEnumFromIndex(Dx5InputType.values, value, Dx5InputType.usb);
        Log.d("$tag: Updated input type to ${settings.selectedInput}");
      }
    });
    listenAndUpdate<int>(_manager.outputType, "outputType", (settings, value) {
      if (settings is D900DeviceSettings) {
        settings.selectedOutput = _safeEnumFromIndex(
            D900OutputType.values, value, D900OutputType.dac);
        Log.d("$tag: Updated output type to ${settings.selectedOutput}");
      } else if (settings is Dx5DeviceSettings) {
        settings.selectedOutput = _safeEnumFromIndex(
            Dx5OutputType.values, value, Dx5OutputType.close);
        Log.d("$tag: Updated output type to ${settings.selectedOutput}");
      }
    });
    listenAndUpdate<int>(_manager.displayMode, "displayMode",
        (settings, value) {
      if (settings is D900DeviceSettings) {
        settings.displayType = _safeEnumFromIndex(
            D900DisplayType.values, value, D900DisplayType.vu);
        Log.d("$tag: Updated display mode to ${settings.displayType}");
      } else if (settings is Dx5DeviceSettings) {
        settings.displayType =
            _safeEnumFromIndex(Dx5DisplayType.values, value, Dx5DisplayType.vu);
        Log.d("$tag: Updated display mode to ${settings.displayType}");
      }
    });

    // DX5 specific streams (check if manager supports them or handle gracefully)
    if (!_isD900Device) {
      listenAndUpdate<bool>(_manager.headphoneEnabled, "headphoneEnabled",
          (settings, value) {
        if (settings is Dx5DeviceSettings) {
          settings.headphoneEnabled = value;
          Log.d(
              "$tag: Updated headphone enabled to ${settings.headphoneEnabled}");
        }
      });
      listenAndUpdate<int>(_manager.headphoneGain, "headphoneGain",
          (settings, value) {
        if (settings is Dx5DeviceSettings) {
          settings.headphoneGainLevel = _safeEnumFromIndex(
              Dx5HeadphoneGainType.values,
              value,
              Dx5HeadphoneGainType.highGain);
          Log.d(
              "$tag: Updated headphone gain to ${settings.headphoneGainLevel}");
        }
      });
      listenAndUpdate<int>(_manager.filter, "filter", (settings, value) {
        if (settings is Dx5DeviceSettings) {
          settings.filter = _safeEnumFromIndex(Dx5FilterParameterType.values,
              value, Dx5FilterParameterType.minimumPhase);
          Log.d("$tag: Updated filter to ${settings.filter}");
        }
      });
      listenAndUpdate<int>(_manager.decodeMode, "decodeMode",
          (settings, value) {
        if (settings is Dx5DeviceSettings) {
          settings.decodeMode = _safeEnumFromIndex(
              Dx5DecodeModeType.values, value, Dx5DecodeModeType.prefix);
          Log.d("$tag: Updated decode mode to ${settings.decodeMode}");
        }
      });
    } else {
      Log.w("$tag: Skipping DX5 specific streams for D900 device");

      var d900Manager = _manager as D900DeviceManager;
      listenAndUpdate<int>(d900Manager.usbSelect, "usbSelect",
          (settings, value) {
        if (settings is D900DeviceSettings) {
          settings.usbSelect = _safeEnumFromIndex(
              D900UsbSelectType.values, value, D900UsbSelectType.auto);
          Log.d("$tag: Updated usbSelect to ${settings.usbSelect}");
        }
      });
      listenAndUpdate<bool>(d900Manager.usbDsdEnabled, "usbDsdEnabled",
          (settings, value) {
        if (settings is D900DeviceSettings) {
          settings.usbDsdPassthrough = value;
          Log.d("$tag: Updated usbDsdEnabled to ${settings.usbDsdPassthrough}");
        }
      });
      listenAndUpdate<int>(d900Manager.iisPhase, "iisPhase", (settings, value) {
        if (settings is D900DeviceSettings) {
          settings.iisPhase = _safeEnumFromIndex(
              D900IisPhaseType.values, value, D900IisPhaseType.standard);
          Log.d("$tag: Updated iisPhase to ${settings.iisPhase}");
        }
      });
      listenAndUpdate<int>(d900Manager.iisChannel, "iisChannel",
          (settings, value) {
        if (settings is D900DeviceSettings) {
          settings.iisDsdChannel = _safeEnumFromIndex(
              D900IisDsdChannelType.values,
              value,
              D900IisDsdChannelType.standard);
          Log.d("$tag: Updated iisChannel to ${settings.iisDsdChannel}");
        }
      });
    }

    // Common advanced streams
    listenAndUpdate<int>(_manager.theme, "theme", (settings, value) {
      if (settings is D900DeviceSettings) {
        settings.theme = _safeEnumFromIndex(
            D900ThemeType.values, value, D900ThemeType.aurora);
        Log.d("$tag: Updated theme to ${settings.theme}");
      } else if (settings is Dx5DeviceSettings) {
        settings.theme =
            _safeEnumFromIndex(Dx5ThemeType.values, value, Dx5ThemeType.aurora);
        Log.d("$tag: Updated theme to ${settings.theme}");
      }
    });
    listenAndUpdate<int>(_manager.powerTrigger, "powerTrigger",
        (settings, value) {
      if (settings is D900DeviceSettings) {
        settings.powerTrigger = _safeEnumFromIndex(
            D900PowerTriggerType.values, value, D900PowerTriggerType.signal);
        Log.d("$tag: Updated power trigger to ${settings.powerTrigger}");
      } else if (settings is Dx5DeviceSettings) {
        settings.powerTrigger = _safeEnumFromIndex(
            Dx5PowerTriggerType.values, value, Dx5PowerTriggerType.signal);
        Log.d("$tag: Updated power trigger to ${settings.powerTrigger}");
      }
    });
    listenAndUpdate<int>(_manager.balance, "balance", (settings, value) {
      settings.channelBalance = value;
    });
    listenAndUpdate<bool>(_manager.audioBluetooth, "audioBluetooth",
        (settings, value) {
      settings.audioBluetooth = value;
      Log.d("$tag: Updated audio Bluetooth to $value");
    });
    listenAndUpdate<bool>(_manager.bluetoothAptx, "bluetoothAptx",
        (settings, value) {
      settings.bluetoothAPTX = value;
      Log.d("$tag: Updated Bluetooth APTX to $value");
    });
    listenAndUpdate<bool>(_manager.relay, "relay", (settings, value) {
      settings.remoteControl = value;
      Log.d("$tag: Updated relay to $value");
    });
    listenAndUpdate<int>(_manager.multifunctionKey, "multifunctionKey",
        (settings, value) {
      if (settings is D900DeviceSettings) {
        settings.multiFunctionKey = _safeEnumFromIndex(
            D900MultiFunctionKeyType.values,
            value,
            D900MultiFunctionKeyType.inputSelect);
        Log.d(
            "$tag: Updated multifunction key to ${settings.multiFunctionKey}");
      } else if (settings is Dx5DeviceSettings) {
        settings.multiFunctionKey = _safeEnumFromIndex(
            Dx5MultiFunctionKeyType.values,
            value,
            Dx5MultiFunctionKeyType.inputSelect);
      }
    });
    listenAndUpdate<int>(_manager.usbMode, "usbMode", (settings, value) {
      if (settings is D900DeviceSettings) {
        settings.usbType =
            _safeEnumFromIndex(D900UsbType.values, value, D900UsbType.uac2);
        Log.d("$tag: Updated USB type to ${settings.usbType}");
      } else if (settings is Dx5DeviceSettings) {
        settings.usbType =
            _safeEnumFromIndex(Dx5UsbType.values, value, Dx5UsbType.uac2);
        Log.d("$tag: Updated USB type to ${settings.usbType}");
      }
    });
    listenAndUpdate<int>(_manager.screenBrightness, "screenBrightness",
        (settings, value) {
      if (settings is D900DeviceSettings) {
        settings.screenBrightness = _safeEnumFromIndex(
            D900ScreenBrightnessType.values,
            value,
            D900ScreenBrightnessType.auto);
        Log.d(
            "$tag: Updated screen brightness to ${settings.screenBrightness}");
      } else if (settings is Dx5DeviceSettings) {
        settings.screenBrightness = _safeEnumFromIndex(
            Dx5ScreenBrightnessType.values,
            value,
            Dx5ScreenBrightnessType.auto);
        Log.d(
            "$tag: Updated screen brightness to ${settings.screenBrightness}");
      }
    });
    listenAndUpdate<int>(_manager.language, "language", (settings, value) {
      if (settings is D900DeviceSettings) {
        settings.language = _safeEnumFromIndex(
            D900LanguageType.values, value, D900LanguageType.zh);
        Log.d("$tag: Updated language to ${settings.language}");
      } else if (settings is Dx5DeviceSettings) {
        settings.language = _safeEnumFromIndex(
            Dx5LanguageType.values, value, Dx5LanguageType.zh);
        Log.d("$tag: Updated language to ${settings.language}");
      }
    });

    // --- 监听聚合的 settings 流 ---
    // 这个流对于一次性获取所有设置很有用，比如初始连接后
    final settingsStream = _manager.settings;
    if (settingsStream != null) {
      final settingsSubscription = settingsStream.listen((nativeSettings) {
        Log.i(
            "$tag: Received full settings object update: ${nativeSettings.runtimeType}");
        _updateAllSettingsFromNative(nativeSettings);
      }, onError: (error) {
        Log.e("$tag: Error listening to settings stream: $error");
      });
      _subscriptions.add(settingsSubscription);
    } else {
      Log.w("$tag: Manager does not provide a 'settings' stream.");
    }

    Log.i("$tag: Started listening to ${_subscriptions.length} streams.");
  }

  /// 停止监听所有事件流
  void stopListening() {
    Log.i("$tag: Stopping listening to ${_subscriptions.length} streams.");
    for (var sub in _subscriptions) {
      sub.cancel();
    }
    _subscriptions.clear();
  }

  /// 如果数据有变动则保存 (私有辅助方法)
  void _saveIfChanged(dynamic settings, String eventName) {
    // 在这里，我们假设 'settings' 对象已经被修改了
    // GetX 的状态管理通常是基于对象引用的变化，
    // 但直接修改对象内部字段可能不会触发所有监听者。
    // DeviceRepository.saveDeviceSettings 内部应该能处理这种情况，
    // 或者确保它触发 GetX 的更新机制。
    try {
      _storage.saveDeviceSettings(deviceId, settings);
      Log.d("$tag: Settings updated and potentially saved due to '$eventName'");
    } catch (e) {
      Log.e("$tag: Failed to save settings after '$eventName' update: $e");
    }
  }

  /// 从原生设置对象更新 App 的所有设置 (处理聚合 settings 流)
  void _updateAllSettingsFromNative(dynamic nativeSettings) {
    final currentSettings = _storage.getDeviceSettings(deviceId);
    if (currentSettings == null) {
      Log.w(
          "$tag: Cannot update all settings, settings not found for device $deviceId");
      return;
    }

    bool changed = false;

    if (currentSettings is D900DeviceSettings &&
        nativeSettings is D900Settings) {
      changed = _updateSettingsFromNativeD900(currentSettings, nativeSettings);
    } else if (currentSettings is Dx5DeviceSettings &&
        nativeSettings is Dx5iiSettings) {
      changed = _updateSettingsFromNativeDx5(currentSettings, nativeSettings);
    } else {
      Log.e(
          "$tag: Settings type mismatch! currentSettings: ${currentSettings.runtimeType}, nativeSettings: ${nativeSettings.runtimeType}");
      return; // 或者抛出异常，具体取决于您的错误处理策略
    }

    // Only save if changes were detected
    if (changed) {
      Log.i("$tag: Full settings object caused changes, saving.");
      _saveIfChanged(currentSettings, "full settings update");
    } else {
      Log.d(
          "$tag: Full settings object received, but no changes detected in app state.");
    }
  }

  /// 更新 D900 设备的设置
  bool _updateSettingsFromNativeD900(
      D900DeviceSettings currentSettings, D900Settings nativeSettings) {
    bool changed = false;
    String localTag = "$tag/D900Update";
    Log.d("$localTag: Syncing D900 settings from native.");
    Log.i(
        "$localTag: 收到原始采样率值: ${nativeSettings.sampling}, 采样率枚举值 => 实际频率: ${D900Settings.convertSamplingRateToHz(nativeSettings.sampling)} Hz");

    // --- HANDLE D900 SAMPLE RATE CONVERSION ---
    // 使用D900Settings的转换函数将采样率枚举转换为可读值
    String samplingDisplayText =
        D900Settings.convertSamplingRateToDisplayText(nativeSettings.sampling);

    // 更新采样率显示值（去掉单位）
    if (currentSettings.sampleRate != nativeSettings.sampling) {
      Log.i(
          "$localTag: Updating D900 sampleRate from ${currentSettings.sampleRate} to ${nativeSettings.sampling} (${samplingDisplayText})");
      currentSettings.sampleRate = nativeSettings.sampling;
      changed = true;
    } else {
      Log.i(
          "$localTag: D900采样率未变化, 当前值: ${currentSettings.sampleRate}, 显示文本: ${samplingDisplayText}");
    }
    // --- END D900 SAMPLE RATE LOGIC ---

    if (currentSettings.power != nativeSettings.isOn) {
      currentSettings.power = nativeSettings.isOn;
      changed = true;
    }
    // ... (rest of the D900 specific updates, ensure all fields are covered) ...
    // Example for one field:
    final newTheme = _safeEnumFromIndex(
        D900ThemeType.values, nativeSettings.theme, currentSettings.theme);
    if (currentSettings.theme != newTheme) {
      currentSettings.theme = newTheme;
      changed = true;
    }
    // ... (add all other D900 field updates here) ...

    if (changed) Log.i("$localTag: D900 settings updated.");
    return changed;
  }

  /// 更新 DX5 设备的设置
  bool _updateSettingsFromNativeDx5(
      Dx5DeviceSettings currentSettings, Dx5iiSettings nativeSettings) {
    bool changed = false;
    String localTag = "$tag/DX5Update";
    Log.d("$localTag: Syncing DX5 settings from native for device $deviceId.");

    // Power
    if (currentSettings.power != nativeSettings.isOn) {
      currentSettings.power = nativeSettings.isOn;
      Log.d("$localTag: Power updated to ${currentSettings.power}");
      changed = true;
    }
    // Mute
    if (currentSettings.mute != nativeSettings.mute) {
      currentSettings.mute = nativeSettings.mute;
      Log.d("$localTag: Mute updated to ${currentSettings.mute}");
      changed = true;
    }

    // Input - Corrected: Dx5InputType.fromValue only takes one argument
    final newInput = Dx5InputType.fromValue(nativeSettings.inputType);
    if (currentSettings.selectedInput != newInput) {
      currentSettings.selectedInput = newInput;
      Log.d("$localTag: Input updated to ${currentSettings.selectedInput}");
      changed = true;
    }

    // Output - Corrected: Dx5OutputType.fromValue only takes one argument
    final newOutput = Dx5OutputType.fromValue(nativeSettings.outputType);
    if (currentSettings.selectedOutput != newOutput) {
      currentSettings.selectedOutput = newOutput;
      Log.d("$localTag: Output updated to ${currentSettings.selectedOutput}");
      changed = true;
    }

    // Headphone Enabled
    if (currentSettings.headphoneEnabled != nativeSettings.headphoneEnabled) {
      currentSettings.headphoneEnabled = nativeSettings.headphoneEnabled;
      Log.d(
          "$localTag: HeadphoneEnabled updated to ${currentSettings.headphoneEnabled}");
      changed = true;
    }
    // Headphone Gain
    final newGain = _safeEnumFromIndex(Dx5HeadphoneGainType.values,
        nativeSettings.headphoneGain, currentSettings.headphoneGainLevel);
    if (currentSettings.headphoneGainLevel != newGain) {
      currentSettings.headphoneGainLevel = newGain;
      Log.d(
          "$localTag: HeadphoneGain updated to ${currentSettings.headphoneGainLevel}");
      changed = true;
    }
    // Display Type
    final newDisplay = _safeEnumFromIndex(Dx5DisplayType.values,
        nativeSettings.displayMode, currentSettings.displayType);
    if (currentSettings.displayType != newDisplay) {
      currentSettings.displayType = newDisplay;
      Log.d("$localTag: DisplayType updated to ${currentSettings.displayType}");
      changed = true;
    }
    // Theme
    final newTheme = _safeEnumFromIndex(
        Dx5ThemeType.values, nativeSettings.theme, currentSettings.theme);
    if (currentSettings.theme != newTheme) {
      currentSettings.theme = newTheme;
      Log.d("$localTag: Theme updated to ${currentSettings.theme}");
      changed = true;
    }
    // Power Trigger
    final newPowerTrigger = _safeEnumFromIndex(Dx5PowerTriggerType.values,
        nativeSettings.powerTrigger, currentSettings.powerTrigger);
    if (currentSettings.powerTrigger != newPowerTrigger) {
      currentSettings.powerTrigger = newPowerTrigger;
      Log.d(
          "$localTag: PowerTrigger updated to ${currentSettings.powerTrigger}");
      changed = true;
    }
    // Channel Balance
    if (currentSettings.channelBalance != nativeSettings.balance) {
      currentSettings.channelBalance = nativeSettings.balance;
      Log.d(
          "$localTag: ChannelBalance updated to ${currentSettings.channelBalance}");
      changed = true;
    }
    // Filter (PCM Filter)
    final newFilter = _safeEnumFromIndex(Dx5FilterParameterType.values,
        nativeSettings.pcmFilter, currentSettings.filter);
    if (currentSettings.filter != newFilter) {
      currentSettings.filter = newFilter;
      Log.d("$localTag: Filter updated to ${currentSettings.filter}");
      changed = true;
    }
    // Decode Mode
    final newDecodeMode = _safeEnumFromIndex(Dx5DecodeModeType.values,
        nativeSettings.decodeMode, currentSettings.decodeMode);
    if (currentSettings.decodeMode != newDecodeMode) {
      currentSettings.decodeMode = newDecodeMode;
      Log.d("$localTag: DecodeMode updated to ${currentSettings.decodeMode}");
      changed = true;
    }
    // Audio Bluetooth
    if (currentSettings.audioBluetooth != nativeSettings.audioBluetooth) {
      currentSettings.audioBluetooth = nativeSettings.audioBluetooth;
      Log.d(
          "$localTag: AudioBluetooth updated to ${currentSettings.audioBluetooth}");
      changed = true;
    }
    // Bluetooth APTX
    if (currentSettings.bluetoothAPTX != nativeSettings.bluetoothAptx) {
      currentSettings.bluetoothAPTX = nativeSettings.bluetoothAptx;
      Log.d(
          "$localTag: BluetoothAPTX updated to ${currentSettings.bluetoothAPTX}");
      changed = true;
    }
    // Remote Control
    if (currentSettings.remoteControl != nativeSettings.remoteEnabled) {
      currentSettings.remoteControl = nativeSettings.remoteEnabled;
      Log.d(
          "$localTag: RemoteControl updated to ${currentSettings.remoteControl}");
      changed = true;
    }
    // Multifunction Key
    final newMultiKey = _safeEnumFromIndex(Dx5MultiFunctionKeyType.values,
        nativeSettings.multifunctionKey, currentSettings.multiFunctionKey);
    if (currentSettings.multiFunctionKey != newMultiKey) {
      currentSettings.multiFunctionKey = newMultiKey;
      Log.d(
          "$localTag: MultiFunctionKey updated to ${currentSettings.multiFunctionKey}");
      changed = true;
    }
    // USB Type (Mode)
    final newUsbMode = _safeEnumFromIndex(
        Dx5UsbType.values, nativeSettings.usbMode, currentSettings.usbType);
    if (currentSettings.usbType != newUsbMode) {
      currentSettings.usbType = newUsbMode;
      Log.d("$localTag: UsbType updated to ${currentSettings.usbType}");
      changed = true;
    }
    // Screen Brightness
    final newScreenBrightness = _safeEnumFromIndex(
        Dx5ScreenBrightnessType.values,
        nativeSettings.screenBrightness,
        currentSettings.screenBrightness);
    if (currentSettings.screenBrightness != newScreenBrightness) {
      currentSettings.screenBrightness = newScreenBrightness;
      Log.d(
          "$localTag: ScreenBrightness updated to ${currentSettings.screenBrightness}");
      changed = true;
    }
    // Language
    final newLanguage = _safeEnumFromIndex(Dx5LanguageType.values,
        nativeSettings.language, currentSettings.language);
    if (currentSettings.language != newLanguage) {
      currentSettings.language = newLanguage;
      Log.d("$localTag: Language updated to ${currentSettings.language}");
      changed = true;
    }

    // --- DIRECTLY UPDATE SAMPLE RATE ---
    // Assuming nativeSettings.sampling is the final displayable sample rate value.
    if (currentSettings.sampleRate != nativeSettings.sampling) {
      Log.i(
          "$localTag: Updating sampleRate for $deviceId from ${currentSettings.sampleRate} to ${nativeSettings.sampling}");
      currentSettings.sampleRate = nativeSettings.sampling;
      changed = true;
    }
    // --- END SAMPLE RATE LOGIC ---

    if (changed)
      Log.i("$localTag: DX5 settings were updated for device $deviceId.");
    return changed;
  }

  /// 安全地从索引获取枚举值的辅助函数
  T _safeEnumFromIndex<T extends Enum>(
      List<T> values, int index, T defaultValue) {
    if (index >= 0 && index < values.length) {
      return values[index];
    } else {
      Log.e(
          "$tag: Invalid ${T.toString()} index: $index, using default $defaultValue");
      return defaultValue; // 返回默认值
    }
  }
}
