import 'dart:io';
import 'package:intl/intl.dart';
import 'package:path_provider/path_provider.dart';
import 'package:topping_ble_control/model/base/topping_verify_result_type.dart';
import 'package:topping_ble_control/utils/log_util.dart';
import 'package:get/get.dart';
import 'package:flutter/material.dart';

/// 验证日志助手类，用于记录设备验证的相关日志
class VerifyLogHelper {
  static final VerifyLogHelper _instance = VerifyLogHelper._internal();
  static VerifyLogHelper get instance => _instance;

  // 私有构造函数
  VerifyLogHelper._internal();

  // 日志文件名称格式
  static final DateFormat _fileNameFormatter = DateFormat('yyyy-MM-dd');
  // 日志时间格式
  static final DateFormat _timeFormatter = DateFormat(
    'yyyy-MM-dd HH:mm:ss.SSS',
  );

  // 是否已初始化
  static bool _isInitialized = false;

  /// 初始化日志助手
  static void init() {
    if (!_isInitialized) {
      _isInitialized = true;
      Log.i('验证日志助手已初始化');
      
      // 清理过期日志
      instance.cleanupOldLogs();
    }
  }

  /// 停止日志记录
  static void stop() {
    if (_isInitialized) {
      _isInitialized = false;
      Log.i('验证日志助手已停止');
    }
  }

  /// 显示日志查看器
  static void showLogViewer(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('验证日志'),
        content: SizedBox(
          width: double.maxFinite,
          height: 400,
          child: FutureBuilder<List<File>>(
            future: instance.getAllLogFiles(),
            builder: (context, snapshot) {
              if (snapshot.connectionState == ConnectionState.waiting) {
                return Center(child: CircularProgressIndicator());
              }
              
              if (snapshot.hasError) {
                return Center(child: Text('加载日志文件失败: ${snapshot.error}'));
              }
              
              final List<File> logFiles = snapshot.data ?? [];
              if (logFiles.isEmpty) {
                return Center(child: Text('没有找到日志文件'));
              }
              
              return ListView.builder(
                itemCount: logFiles.length,
                itemBuilder: (context, index) {
                  final File file = logFiles[index];
                  final String fileName = file.path.split('/').last;
                  return ListTile(
                    title: Text(fileName),
                    onTap: () {
                      _showLogFileContent(context, file);
                    },
                  );
                },
              );
            },
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text('关闭'),
          ),
        ],
      ),
    );
  }

  /// 显示日志文件内容
  static void _showLogFileContent(BuildContext context, File file) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(file.path.split('/').last),
        content: SizedBox(
          width: double.maxFinite,
          height: 400,
          child: FutureBuilder<String>(
            future: file.readAsString(),
            builder: (context, snapshot) {
              if (snapshot.connectionState == ConnectionState.waiting) {
                return Center(child: CircularProgressIndicator());
              }
              
              if (snapshot.hasError) {
                return Center(child: Text('读取日志内容失败: ${snapshot.error}'));
              }
              
              final String content = snapshot.data ?? '';
              if (content.isEmpty) {
                return Center(child: Text('日志文件为空'));
              }
              
              return SingleChildScrollView(
                child: Text(content),
              );
            },
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text('关闭'),
          ),
        ],
      ),
    );
  }

  /// 记录验证开始日志
  Future<void> logVerifyStart(String deviceName) async {
    if (!_isInitialized) return;
    final String message = '设备验证开始: $deviceName';
    await _writeLog(message);
  }

  /// 记录发送数据日志
  Future<void> logDataSent(String data) async {
    if (!_isInitialized) return;
    final String message = '发送验证数据: $data';
    await _writeLog(message);
  }

  /// 记录写入结果日志
  Future<void> logWriteResult(bool success, {String? reason}) async {
    if (!_isInitialized) return;
    final String resultMsg = success ? '成功' : '失败';
    final String message =
        '数据写入结果: $resultMsg${reason != null ? ', 原因: $reason' : ''}';
    await _writeLog(message);
  }

  /// 记录收到回复日志
  Future<void> logResponseReceived(String response) async {
    if (!_isInitialized) return;
    final String message = '收到验证回复: $response';
    await _writeLog(message);
  }

  /// 记录验证结果日志
  Future<void> logVerifyResult(String result) async {
    if (!_isInitialized) return;
    final String message = '验证结果: $result';
    await _writeLog(message);
  }

  /// 记录一般日志信息
  Future<void> logInfo(String message) async {
    if (!_isInitialized) return;
    await _writeLog(message);
  }

  /// 写入日志到文件
  Future<void> _writeLog(String message) async {
    try {
      // 获取当前时间
      final String timestamp = _timeFormatter.format(DateTime.now());
      final String logEntry = '[$timestamp] $message\n';

      // 记录到系统日志
      Log.i('验证日志: $message');

      // 获取日志文件
      final File logFile = await _getLogFile();

      // 写入日志
      await logFile.writeAsString(logEntry, mode: FileMode.append);
    } catch (e) {
      Log.e('写入验证日志失败: $e');
    }
  }

  /// 获取日志文件
  Future<File> _getLogFile() async {
    final Directory appDocDir = await getApplicationDocumentsDirectory();
    final String logDirPath = '${appDocDir.path}/verify_logs';

    // 确保目录存在
    final Directory logDir = Directory(logDirPath);
    if (!await logDir.exists()) {
      await logDir.create(recursive: true);
    }

    // 以当天日期命名日志文件
    final String fileName =
        'verify_log_${_fileNameFormatter.format(DateTime.now())}.txt';
    return File('$logDirPath/$fileName');
  }

  /// 获取所有日志文件
  Future<List<File>> getAllLogFiles() async {
    try {
      final Directory appDocDir = await getApplicationDocumentsDirectory();
      final String logDirPath = '${appDocDir.path}/verify_logs';

      final Directory logDir = Directory(logDirPath);
      if (!await logDir.exists()) {
        return [];
      }

      List<FileSystemEntity> entities = await logDir.list().toList();
      return entities
          .whereType<File>()
          .where((file) => file.path.endsWith('.txt'))
          .toList();
    } catch (e) {
      Log.e('获取日志文件列表失败: $e');
      return [];
    }
  }

  /// 清除过期日志（保留最近30天）
  Future<void> cleanupOldLogs({int retentionDays = 30}) async {
    try {
      final List<File> logFiles = await getAllLogFiles();
      final DateTime now = DateTime.now();

      for (File file in logFiles) {
        final String fileName = file.path.split('/').last;
        // 从文件名中提取日期
        final RegExp dateRegExp = RegExp(
          r'verify_log_(\d{4}-\d{2}-\d{2})\.txt',
        );
        final Match? match = dateRegExp.firstMatch(fileName);

        if (match != null) {
          final String dateStr = match.group(1)!;
          final DateTime fileDate = DateFormat('yyyy-MM-dd').parse(dateStr);
          final int daysDifference = now.difference(fileDate).inDays;

          if (daysDifference > retentionDays) {
            await file.delete();
            Log.i('已删除过期日志文件: $fileName');
          }
        }
      }
    } catch (e) {
      Log.e('清理过期日志失败: $e');
    }
  }
}
