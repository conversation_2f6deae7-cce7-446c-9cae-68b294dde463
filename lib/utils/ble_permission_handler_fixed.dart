// import 'dart:io';
//
// import 'package:permission_handler/permission_handler.dart';
//
// import 'log_util.dart';
//
// /// BLE权限处理
// class BlePermissionService {
//   static final BlePermissionService _instance = BlePermissionService._();
//   factory BlePermissionService() => _instance;
//   BlePermissionService._();
//
//   ///  请求必要的权限
//   static Future<bool> requestPermissions() async {
//     // 检查是否需要提前显示权限说明
//     bool needsRationale = await _needsPermissionRationale();
//
//     if (needsRationale) {
//       // 在实际应用中，这里可能需要显示一个对话框来解释为什么需要权限
//       Log.i("需要向用户解释权限用途");
//     }
//
//     // 根据平台请求不同权限
//     if (Platform.isIOS) {
//       // 如果存在之前被永久拒绝的权限，可以尝试打开设置
//       if (await _checkIfAnyPermissionIsPermanentlyDenied()) {
//         Log.i("存在永久拒绝的权限，尝试通过openAppSettings重新获取");
//         bool opened = await openAppSettings();
//         if (opened) {
//           Log.i("成功打开应用设置");
//         } else {
//           Log.e("无法打开应用设置");
//         }
//         return false;
//       }
//
//       // iOS下需要请求位置权限和蓝牙权限
//       return await _requestiOSPermissions();
//     } else {
//       // Android权限
//       return await _requestAndroidPermissions();
//     }
//   }
//
//   /// 请求iOS所需权限
//   static Future<bool> _requestiOSPermissions() async {
//     // 检查位置权限状态
//     PermissionStatus locationStatus = await Permission.location.status;
//     Log.i('当前位置权限状态: $locationStatus');
//
//     // 检查蓝牙权限状态
//     PermissionStatus bluetoothScanStatus = await Permission.bluetoothScan.status;
//     PermissionStatus bluetoothConnectStatus = await Permission.bluetoothConnect.status;
//     Log.i('当前蓝牙扫描权限状态: $bluetoothScanStatus');
//     Log.i('当前蓝牙连接权限状态: $bluetoothConnectStatus');
//
//     // 先请求位置权限 - iOS需要位置权限才能扫描BLE设备
//     if (!locationStatus.isGranted) {
//       locationStatus = await Permission.location.request();
//       Log.i('请求后位置权限状态: $locationStatus');
//
//       // 如果位置权限被拒绝，尝试显示解释
//       if (!locationStatus.isGranted) {
//         Log.i('位置权限未授予: $locationStatus');
//         // 不立即返回false，继续请求蓝牙权限
//       }
//     }
//
//     // 请求蓝牙权限
//     Map<Permission, PermissionStatus> statuses =
//         await [Permission.bluetoothScan, Permission.bluetoothConnect].request();
//
//     // 记录请求后的状态
//     statuses.forEach((permission, status) {
//       Log.i('请求后 $permission 状态: $status');
//     });
//
//     // 检查结果 - 对于iOS，我们需要特殊处理limited状态
//     bool allAccepted = statuses.values.every((status) =>
//         status.isGranted || status == PermissionStatus.limited);
//
//     if (!allAccepted) {
//       // 记录具体哪些权限被拒绝了
//       statuses.forEach((permission, status) {
//         if (!status.isGranted && status != PermissionStatus.limited) {
//           Log.i('权限 $permission 未被接受: $status');
//         }
//       });
//       return false;
//     }
//
//     // 即使位置权限被拒绝，如果蓝牙权限被接受，我们也认为是成功的
//     // 因为在某些iOS版本上，位置权限可能不是必需的
//     return true;
//   }
//
//   /// 请求Android所需权限
//   static Future<bool> _requestAndroidPermissions() async {
//     // 请求权限
//     Map<Permission, PermissionStatus> statuses =
//         await [
//           Permission.bluetoothScan,
//           Permission.bluetoothConnect,
//           Permission.location,
//         ].request();
//
//     // 检查结果
//     bool allGranted = statuses.values.every((status) => status.isGranted);
//
//     if (!allGranted) {
//       // 记录具体哪些权限被拒绝了
//       statuses.forEach((permission, status) {
//         if (!status.isGranted) {
//           Log.i('权限 $permission 未授予: $status');
//         }
//       });
//     }
//
//     return allGranted;
//   }
//
//   /// 检查是否需要显示权限解释
//   static Future<bool> _needsPermissionRationale() async {
//     if (Platform.isAndroid) {
//       // Android需要检查是否已被拒绝过
//       final status = await Permission.bluetoothScan.status;
//       return status.isDenied && !status.isPermanentlyDenied;
//     }
//     return false;
//   }
//
//   /// 检查是否有永久被拒绝的权限
//   static Future<bool> _checkIfAnyPermissionIsPermanentlyDenied() async {
//     List<Permission> permissions = [
//       Permission.bluetoothScan,
//       Permission.bluetoothConnect,
//       Permission.location,
//     ];
//
//     bool anyPermanentlyDenied = false;
//
//     for (var permission in permissions) {
//       PermissionStatus status = await permission.status;
//       Log.i('检查权限 $permission 状态: $status');
//
//       if (status.isPermanentlyDenied) {
//         Log.i('权限 $permission 已被永久拒绝');
//         anyPermanentlyDenied = true;
//         // 不立即返回，继续检查其他权限并记录
//       }
//     }
//
//     return anyPermanentlyDenied;
//   }
// }
