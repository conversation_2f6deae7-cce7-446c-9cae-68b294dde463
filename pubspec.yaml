name: topping_home
description: "Topping Home"

publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# 注意：Android 平台的版本号已迁移到 android/version.properties 文件中管理
# iOS 平台仍然使用此版本号
version: 1.0.1+2

environment:
  sdk: '>=3.0.1 <4.0.0'

dependencies:
  flutter:
    sdk: flutter

  topping_ble_control:

    path: ../topping_ble_control

  cupertino_icons: ^1.0.8
  # lottie动画 https://pub-web.flutter-io.cn/packages/lottie/install
  lottie: ^3.3.1
  # getX：https://pub-web.flutter-io.cn/packages/get/install
  get: ^4.7.2
  # 屏幕适配：https://pub-web.flutter-io.cn/packages/flutter_screenutil/install
  flutter_screenutil: ^5.9.3

  # 下拉刷新  https://pub-web.flutter-io.cn/packages/pull_to_refresh/install
  pull_to_refresh: ^2.0.0
  # 图片加载  https://pub-web.flutter-io.cn/packages/cached_network_image/install
  cached_network_image: ^3.2.3
  # retrofit https://pub-web.flutter-io.cn/packages/retrofit/install   flutter pub run build_runner build
  retrofit: ^4.0.1
  # dio https://pub-web.flutter-io.cn/packages/dio/install
  dio: ^5.3.1
  # json_serializable https://pub-web.flutter-io.cn/packages/json_serializable/install
  json_serializable: ^6.1.5
  # json_annotation https://pub-web.flutter-io.cn/packages/json_annotation/install
  json_annotation: ^4.8.1
  # 网络请求日志 https://pub-web.flutter-io.cn/packages/pretty_dio_logger/install
  pretty_dio_logger: ^1.3.1
  # encrypt https://pub-web.flutter-io.cn/packages/encrypt/install
  encrypt: ^5.0.1
  # Widget可见行监听 https://pub-web.flutter-io.cn/packages/visibility_detector/install
  visibility_detector: ^0.4.0+2
  # shared_preferences https://pub-web.flutter-io.cn/packages/shared_preferences/install
  shared_preferences: ^2.5.2
  # toast https://pub-web.flutter-io.cn/packages/fluttertoast
  fluttertoast: ^8.2.12
  # extended_nested_scroll_view https://pub-web.flutter-io.cn/packages/extended_nested_scroll_view/install
  extended_nested_scroll_view: ^6.1.2
  # 动态权限 https://pub-web.flutter-io.cn/packages/permission_handler/install
  permission_handler: ^11.4.0
  # path_provider https://pub-web.flutter-io.cn/packages/path_provider/install
  path_provider: ^2.1.0
  # 事件总线 https://pub-web.flutter-io.cn/packages/event_bus/install
  event_bus: ^2.0.0
  # flutter_gen https://pub-web.flutter-io.cn/packages/flutter_gen/install
  flutter_gen: ^5.8.0
  # package_info_plus https://pub-web.flutter-io.cn/packages/package_info_plus/install
  package_info_plus: ^8.1.3
  # 图片选择 https://pub-web.flutter-io.cn/packages/image_picker/install
  image_picker: ^1.1.2
  # 二维码扫描 https://pub-web.flutter-io.cn/packages/qr_code_scanner/install
  qr_code_scanner_plus: ^2.0.7
  # 国际化
  flutter_localizations:
    sdk: flutter
  intl: 0.19.0
  # hive https://pub-web.flutter-io.cn/packages/hive/install
  hive_ce: ^2.10.1
  hive_ce_flutter: ^2.2.0
  # pdf https://pub.dev/packages/flutter_pdfview/install
  flutter_pdfview: ^1.4.0
  # 文件选择 https://pub.dev/packages/file_picker/install
  file_picker: ^10.1.2
  # url_launcher https://pub.dev/packages/url_launcher/install
  url_launcher: ^6.3.1
  # convert https://pub.dev/packages/convert/install
  convert: ^3.1.2
  # share_plus https://pub.dev/packages/share_plus/install
  share_plus: ^11.0.0
  #csv https://pub.dev/packages/csv/install
  csv: ^6.0.0
  # open_file https://pub.dev/packages/open_file/install
  open_file: ^3.5.10
  # device_info_plus https://pub.dev/packages/device_info_plus/install
  device_info_plus: ^11.3.3
  # auto_size_text_plus https://pub.dev/packages/auto_size_text_plus/install
  auto_size_text_plus: ^3.0.2
  uuid: ^4.5.1
  flutter_native_splash: ^2.4.6
  scidart: ^0.0.2-dev.12
  matrix2d: ^1.0.4

dev_dependencies:
  flutter_test:
    sdk: flutter
#  intl_utils: ^2.8.10

  analyzer: ^6.11.0
  flutter_lints: ^5.0.0
  # retrofit
  retrofit_generator: ^9.1.7
  # build_runner: '>=2.3.0 <4.0.0'
  build_runner: ^2.4.6
  flutter_gen_runner: ^5.8.0
  hive_ce_generator: ^1.8.2
  flutter_launcher_icons: "^0.14.3"

flutter_launcher_icons:
  android: "launcher_icon"
  ios: true
  image_path: "assets/icon/icon.png"
  min_sdk_android: 21
  adaptive_icon_background: "#121212"
  adaptive_icon_foreground: "assets/icon.png"
  web:
    generate: false
  windows:
    generate: false
  macos:
    generate: false

dependency_overrides:
  analyzer: ^7.0.0
  protobuf: ^3.1.0

flutter:
  generate: true

  uses-material-design: true

  assets:
    - assets/icon/
    - assets/lottie/
    - assets/image/
    - assets/pdfs/operation_guide.pdf
    - assets/legal/

# 在 pubspec.yaml 文件末尾添加
#flutter_intl:
#  enabled: true
#  class_name: I10n
#  main_locale: en
#  arb_dir: lib/l10n
#  output_dir: lib/generated

