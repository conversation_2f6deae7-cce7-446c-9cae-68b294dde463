#!/bin/bash

# 遍历 lib/enums/dx5/ 目录下的所有 dx5_*.dart 文件
for file in lib/enums/dx5/dx5_*.dart; do
  # 获取文件名（不含路径）
  filename=$(basename "$file")
  # 获取不带 dx5_ 前缀和 .dart 后缀的文件名
  base_name=${filename#dx5_}
  base_name=${base_name%.dart}
  
  # 将下划线分隔的名称转换为驼峰命名
  # 例如：decode_mode_type -> DecodeMode
  camel_case=""
  IFS='_' read -ra PARTS <<< "$base_name"
  for part in "${PARTS[@]}"; do
    # 首字母大写
    part="$(tr '[:lower:]' '[:upper:]' <<< ${part:0:1})${part:1}"
    camel_case="$camel_case$part"
  done
  
  # 替换 fromValue 方法中的引用
  # 例如：Convert.fromValue(DecodeMode.values, value, DecodeMode.prefix)
  # 替换为：Convert.fromValue(Dx5DecodeMode.values, value, Dx5DecodeMode.prefix)
  sed -i '' "s/$camel_case\.values, value, $camel_case\./Dx5$camel_case.values, value, Dx5$camel_case./g" "$file"
  
  echo "已修复 $file 中的 fromValue 方法"
done

echo "所有文件修复完成！"
