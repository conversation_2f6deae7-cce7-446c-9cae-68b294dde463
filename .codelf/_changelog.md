## 2024-05-26 14:00:00

### 1. 优化应用启动页面显示效果

**Change Type**: fix

> **Purpose**: 修复启动页面图标显示不完整的问题
> **Detailed Description**: 
> 1. 修改flutter_native_splash.yaml配置文件，调整图像显示参数
> 2. 添加android_gravity参数设置为center，确保Android上居中显示
> 3. 添加ios_content_mode参数设置为scaleAspectFit，确保iOS上保持图像比例并完整显示
> 4. 移除不支持的自定义参数image_size，改用标准参数
> **Reason for Change**: 启动页面上的图标底部被裁剪，无法完整显示"TOPPING"文字
> **Impact Scope**: 应用启动时显示的初始屏幕

   ```
   - flutter_native_splash.yaml // update 修改配置，使用正确的参数设置图像位置和大小
   - android/app/src/main/res/drawable/ // update 更新Android启动背景资源
   - ios/Runner/ // update 更新iOS启动页面配置和资源
   ```

## 2024-05-26 13:00:00

### 1. 更新应用启动页面

**Change Type**: enhancement

> **Purpose**: 更新应用的启动页面(Splash Screen)，使用新设计的图标
> **Detailed Description**: 
> 1. 修改flutter_native_splash.yaml配置文件，将启动图像从icon_small.png更改为icon.png
> 2. 更新Android和iOS平台的启动页面资源
> 3. 保持白色背景以确保图标在启动页面上清晰可见
> **Reason for Change**: 使应用启动页面与新的应用图标保持一致，提升品牌形象和用户体验
> **Impact Scope**: 应用启动时显示的初始屏幕

   ```
   - flutter_native_splash.yaml // update 修改配置，使用新图标
   - android/app/src/main/res/drawable/ // update 更新Android启动背景资源
   - android/app/src/main/res/drawable-v21/ // update 更新Android v21启动背景资源
   - ios/Runner/Info.plist // update 更新iOS启动配置
   ```

## 2024-05-26 12:00:00

### 1. 更新应用图标

**Change Type**: enhancement

> **Purpose**: 更新应用的启动图标，使用新的设计
> **Detailed Description**: 
> 1. 替换assets/icon/icon.png文件为新设计的图标
> 2. 使用flutter_launcher_icons生成Android和iOS平台的应用图标
> 3. 修改flutter_launcher_icons.yaml配置文件，移除不需要的web、Windows和macOS平台配置
> **Reason for Change**: 更新应用图标以提升品牌形象和UI一致性
> **Impact Scope**: 应用在移动设备上显示的启动图标

   ```
   - assets/icon/icon.png // replace 替换为新设计的应用图标
   - flutter_launcher_icons.yaml // update 修改配置，移除不必要的平台设置
   - android/app/src/main/res/mipmap-*/ // update 更新Android平台图标
   - ios/Runner/Assets.xcassets/AppIcon.appiconset/ // update 更新iOS平台图标
   ```

## 2024-05-22 20:00:00

### 1. 重构和封装设备音量处理逻辑

**Change Type**: refactor

> **Purpose**: 封装不同设备类型的音量处理逻辑，提供统一的接口
> **Detailed Description**: 
> 1. 添加统一的音量转换辅助方法：`convertVolumeToDeviceValue`、`convertDeviceValueToVolume`、`getDeviceVolumeRange`
> 2. 重构`setVolumeEnd`、`getVolumePercentage`、`volumeFromPercentage`和`getFormattedVolumeText`方法，使用新的辅助方法
> 3. 使用线性映射实现音量百分比与实际值的转换，提高代码的一致性和可维护性
> **Reason for Change**: 之前的实现中设备类型特定的逻辑分散在多个方法中，重复代码多，不易维护
> **Impact Scope**: 设备详情页面的音量控制功能

   ```
   lib
   - business
     - device_detail
       - device_detail_logic.dart // refactor 重构音量处理逻辑，封装为统一接口
   ```

## 2024-05-22 19:45:00

### 1. 修复D900设备音量滑块显示问题

**Change Type**: fix

> **Purpose**: 修复D900设备音量滑块无法显示到最大值位置的问题
> **Detailed Description**: 
> 1. 修改`getVolumePercentage`方法，正确处理正值音量（+1到+8dB）
> 2. 将正值音量映射到滑块的92%-100%区间，确保+8dB对应滑块100%位置
> 3. 移除之前强制将正值转为负值的代码，避免音量计算错误
> **Reason for Change**: 之前的实现在处理+1到+8dB的音量时，将其转为负值再计算百分比，导致+8dB对应85%而非100%
> **Impact Scope**: 设备详情页面的音量控制功能

   ```
   lib
   - business
     - device_detail
       - device_detail_logic.dart // fix 修改音量百分比计算方法，确保+8dB对应滑块100%
   ```

## 2024-05-22 19:30:00

### 1. 修复D900设备最大音量设置问题

**Change Type**: fix

> **Purpose**: 修复D900设备无法将音量设置到最大值+8dB的问题
> **Detailed Description**: 
> 1. 修正setVolumeEnd方法中的逻辑错误，移除了开头强制将正值转换为负值的代码
> 2. 正确实现D900设备的音量规则：-99到0范围发送正值，+1到+8范围发送负值
> 3. 优化了音量范围限制，使用clamp方法确保值在有效范围内
> **Reason for Change**: 之前的实现将所有正值强制转为负值，导致无法设置+1到+8dB的范围
> **Impact Scope**: 设备详情页面的音量控制功能

   ```
   lib
   - business
     - device_detail
       - device_detail_logic.dart // fix 修复D900设备音量设置逻辑，修正+8dB无法设置的问题
   ```

## 2024-05-22 18:30:00

### 1. 修复D900设备音量控制问题

**Change Type**: fix

> **Purpose**: 修复D900设备音量控制的两个关键问题
> **Detailed Description**: 
> 1. 修复音量滑块拖动时发送过多命令的问题，改为仅在松开时发送一次
> 2. 修正D900设备的音量值规则处理，确保可以正确设置从-99到+8dB的全范围
> 3. 修复特别是+0到+8dB范围（对应UI显示中的-8到0）的发送规则
> **Reason for Change**: 之前的实现在滑块拖动过程中发送过多命令，且无法正确设置0到+8dB的音量范围
> **Impact Scope**: 设备详情页面的音量控制功能

   ```
   lib
   - business
     - device_detail
       - device_detail_logic.dart // fix 修复音量规则处理逻辑，移除setVolumeUI方法
       - view
         - device_control_page.dart // fix 修改音量滑块实现，只在松开时发送命令
   ```

## 2024-05-22 17:30:00

### 1. 修复D900设备音量条无法拉到最大值的问题

**Change Type**: fix

> **Purpose**: 修复D900设备音量条无法拉到最大值+8dB的问题
> **Detailed Description**: 
> 1. 修改volumeFromPercentage方法，确保D900设备在音量拉到100%时能够正确地设置为+8dB
> 2. 修改音量滑块组件的divisions值，从100修改为根据设备类型动态设置(D900为107，DX5为99)，以匹配实际音量范围
> **Reason for Change**: D900设备音量范围为-99到+8dB，由于浮点运算精度问题，导致滑块拉到最大值时无法准确设置为+8dB
> **Impact Scope**: 设备详情页面的音量控制功能

   ```
   lib
   - business
     - device_detail
       - device_detail_logic.dart // fix 修复百分比转音量值的计算方法
       - view
         - device_control_page.dart // fix 修改音量滑块的divisions值以匹配设备范围
   ```

## 2024-05-07 17:00:00

### 1. 修复Dx5iiDeviceManager的编译错误并完善接口实现

**Change Type**: fix

> **Purpose**: 修复Dx5iiDeviceManager中的编译错误并确保它正确实现DeviceOperations接口中新增的D900特有方法
> **Detailed Description**: 
> 1. 修复Dx5iiSettings对象创建时的方法名和属性名错误
> 2. 修复setBalance方法调用不存在API的问题
> 3. 修复isCommandSupported中对未定义类型的引用问题
> 4. 为D900特有方法添加@override注解并实现空方法(带警告日志)
> **Reason for Change**: 确保不同设备的设备管理器都正确实现了接口中定义的所有方法,提高代码一致性
> **Impact Scope**: DX5设备管理器的实现

   ```
   lib
   - device
     - dx5
       - dx5ii_device_manager.dart // fix 修复编译错误并实现D900特有方法
   ```

## 2024-05-07 16:30:00

### 1. 优化设备管理器代码结构

**Change Type**: refactor

> **Purpose**: 优化设备类型特有功能的代码结构,提高代码一致性和可维护性
> **Detailed Description**: 
> 1. 在`DeviceOperations`接口中添加了D900设备特有的方法
> 2. 在`ToppingDeviceManager`抽象基类中为这些D900特有方法提供了默认空实现
> 3. 在`D900DeviceManager`类中添加了正确的`@override`注解
> **Reason for Change**: 之前D900特有方法没有在接口中定义,导致代码结构不一致;此次修改使设备特定功能的管理更加规范
> **Impact Scope**: 设备控制层的接口定义和实现类

   ```
   lib
   - interfaces
     - device_operations.dart // refact 添加D900设备特有的方法声明
   - device
     - topping_device_manager.dart // refact 添加D900设备特有方法的默认空实现
     - d900
       - d900_device_manager.dart // refact 添加正确的@override注解
   ```

## 2023-05-22 16:00:00

### 1. 修复设备设置页面的设备类型识别问题

**Change Type**: fix

> **Purpose**: 修复设备设置页面无法正确识别并显示D900设备特有功能的问题
> **Detailed Description**: 修改DeviceSettingPage的初始化逻辑，使用DeviceFactory获取当前连接设备的ID，而不是传入空字符串，并添加设备类型调试信息显示
> **Reason for Change**: 空字符串作为deviceId导致无法正确判断设备类型，isD900Device始终返回false，显示了DX5的设置项而非D900的设置项
> **Impact Scope**: 设备设置页面对D900设备的支持

   ```
   lib
   - business
     - device_setting
       - device_setting_view.dart // refact 修改设备ID的获取方式，并添加设备类型调试信息
   ```

## 2023-05-22 14:00:00

### 1. 修复执行命令辅助方法的Future返回问题

**Change Type**: fix

> **Purpose**: 修复执行命令后UI未能正确更新的问题，尤其是解码模式设置
> **Detailed Description**: 修改_executeCommandHelper方法，确保在所有分支路径上都返回有效的Future对象，使得then回调能够正常触发
> **Reason for Change**: 由于_executeCommandHelper方法在某些条件分支下没有明确返回Future对象，导致依赖then回调更新UI的设置方法失效
> **Impact Scope**: 设备设置逻辑中的所有使用then回调更新UI的设置方法，特别是解码模式设置

   ```
   lib
   - business
     - device_setting
       - device_setting_logic.dart // refact 修改_executeCommandHelper方法，确保正确返回Future对象
   ```

## 2023-05-21 12:30:00

### 1. 修复设备设置页面重复显示主题和开关机触发设置的问题

**Change Type**: fix

> **Purpose**: 修复设备设置页面中重复显示主题和开关机触发设置的问题
> **Detailed Description**: 
> 1. 删除了主页面中的PowerTriggerSection组件，因为这个功能已经在SystemSettingsSection中包含
> 2. 删除了主页面中的DisplaySettingsSection组件，因为其中的主题设置功能已经在SystemSettingsSection中包含
> **Reason for Change**: 避免界面上显示重复的设置项，提高用户体验
> **Impact Scope**: 设备设置页面布局

   ```
   lib
   - business
     - device_setting
       - device_setting_view.dart // refact 移除重复的DisplaySettingsSection和PowerTriggerSection组件
   ```

## 2023-05-21 11:40:00

### 1. 修复设备设置页面的枚举类型转换错误

**Change Type**: fix

> **Purpose**: 修复设备设置页面中类型错误导致的界面崩溃问题
> **Detailed Description**: 修改音频设置和系统设置部分中枚举类型的处理逻辑，解决"type 'Dx5DecodeModeType' is not a subtype of type 'int'"等类型错误
> **Reason for Change**: 内部存储的枚举类型在恢复时可能以int形式返回，直接传递给SettingDropdown会导致类型转换错误
> **Impact Scope**: 设备设置页面的所有下拉菜单组件

   ```
   lib
   - business
     - device_setting
       - view
         - audio_settings_section.dart // refact 修复解码模式类型转换错误
         - system_settings_section.dart // refact 修复所有枚举类型转换错误
   ```

## 2023-05-21 10:30:00

### 1. 修复设备设置逻辑中的类型转换错误

**Change Type**: fix

> **Purpose**: 修复设备设置页面中出现的类型错误导致的崩溃问题
> **Detailed Description**: 在设备设置逻辑中，所有涉及枚举类型参数的方法中存在类型转换问题，将`as int`强制类型转换改为安全的条件判断处理
> **Reason for Change**: 主要解决"type 'Dx5LanguageType' is not a subtype of type 'int'"类型的错误，提高代码稳定性
> **Impact Scope**: 设备设置逻辑类中的所有设置方法

   ```
   lib
   - business
     - device_setting
       - device_setting_logic.dart // refact 修复所有设置方法中的类型转换问题
   ```

## 2023-05-20 15:35:00

### 1. 修复D900设备显示解码模式和滤波器功能的问题

**Change Type**: fix

> **Purpose**: 纠正D900设备显示不支持的功能
> **Detailed Description**: 根据D900和DX5的功能差异，移除了D900设备UI中不支持的解码模式和滤波器设置选项
> **Reason for Change**: D900设备模型中没有decodeMode和filter属性，不应该在UI中显示这些功能选项
> **Impact Scope**: 设备设置页面的音频设置部分

   ```
   lib
   - business
     - device_setting
       - view
         - audio_settings_section.dart // refact 修改音频设置区域，为D900设备隐藏不支持的功能
       - device_setting_logic.dart // refact 修改逻辑处理，避免对D900设备执行不支持的功能
   - enums
     - d900
       - d900_decode_mode_type.dart // del 删除不需要的D900解码模式枚举类型文件
   ```

### 2. 添加D900设备特有功能的国际化支持

**Change Type**: improvement

> **Purpose**: 为D900设备特有功能添加完整的国际化支持
> **Detailed Description**: 在国际化资源文件中添加D900设备特有功能（USB选择、USB DSD直通、IIS相位和IIS DSD通道）的多语言字符串
> **Reason for Change**: 确保D900设备的特有功能在各语言环境中能正确显示
> **Impact Scope**: 国际化资源文件

   ```
   lib/l10n
   - intl_en.arb // update 添加D900特有功能的英文字符串
   - intl_zh.arb // update 添加D900特有功能的中文字符串
   - intl_zh_Hant.arb // update 添加D900特有功能的繁体中文字符串
   - intl_ja.arb // update 添加D900特有功能的日文字符串
   - intl_ko.arb // update 添加D900特有功能的韩文字符串
   ```

### 2. {function simple description}

**Change Type**: {type: feature/fix/improvement/refactor/docs/test/build}

> **Purpose**: {function purpose}
> **Detailed Description**: {function detailed description}
> **Reason for Change**: {why this change is needed}
> **Impact Scope**: {other modules or functions that may be affected by this change}
> **API Changes**: {if there are API changes, detail the old and new APIs}
> **Configuration Changes**: {changes to environment variables, config files, etc.}
> **Performance Impact**: {impact of the change on system performance}

   ```
   root
   - pkg    // {type: add/del/refact/-} {The role of a folder}
    - utils // {type: add/del/refact} {The function of the file}
   - xxx    // {type: add/del/refact} {The function of the file}
   ```

## {Current Date/Time}

### 1. 重构系统设置区域以使用 SettingCardFactory

**Change Type**: refactor

> **Purpose**: 简化 `SystemSettingsSection` 的代码，提高可维护性。
> **Detailed Description**: 将 `SystemSettingsSection` 中创建各个设置项卡片的逻辑（包括设备类型判断和弹出选择对话框）迁移到 `SettingCardFactory` 中。`SystemSettingsSection` 现在直接调用工厂方法来生成 UI。
> **Reason for Change**: 原有代码包含大量重复的 `Obx`、设备类型判断和对话框显示逻辑，使用工厂模式可以消除这些重复代码。
> **Impact Scope**: 设备设置页面的系统设置部分 (`SystemSettingsSection`)

   ```
   lib
   - business
     - device_setting
       - view
         - system_settings_section.dart // refact 重构为使用 SettingCardFactory
         - common
           - setting_card_factory.dart // add 新增工厂类，被 SystemSettingsSection 使用
           - enum_setting_card.dart // add 工厂依赖的通用枚举设置卡片
   ```

### 1. 增强 SettingCardFactory 并重构 Sections 以提高可扩展性

**Change Type**: refactor

> **Purpose**: 优化设备设置卡片的创建逻辑，使其更容易支持未来的新设备类型。
> **Detailed Description**: 
> 1.  在 `DeviceSettingLogic` 中添加了 `deviceType` getter 以暴露 `DeviceModeType` 枚举。
> 2.  重构 `SettingCardFactory`：使用基于 `DeviceModeType` 的 Map 来创建通用设置卡片，并使用 Map 管理特定于设备的卡片创建函数列表。
> 3.  新增 `createDeviceSpecificSettingCards` 方法，用于获取当前设备特有的设置卡片。
> 4.  为音频监听和蓝牙 AptX 添加了通用的 `SettingSwitch` 卡片创建方法。
> 5.  重构 `SystemSettingsSection` 和 `AudioSettingsSection`，移除内部的 `if/else` 设备判断逻辑，改为统一调用工厂方法。
> **Reason for Change**: 消除 View 层和 Factory 层中基于设备类型的 `if/else` 结构，使代码更易于维护和扩展。
> **Impact Scope**: 设备设置功能 (`DeviceSettingLogic`, `SettingCardFactory`, `SystemSettingsSection`, `AudioSettingsSection`)

   ```
   lib
   - business
     - device_setting
       - device_setting_logic.dart // refact 添加 deviceType getter
       - view
         - system_settings_section.dart // refact 移除 if/else, 调用新工厂方法
         - audio_settings_section.dart // refact 移除 if/else, 调用新工厂方法
         - common
           - setting_card_factory.dart // refact 使用 Map 提高可扩展性，添加新方法
   ```

## 2024-05-07 17:30:00

### 1. 优化D900DeviceManager类的代码结构

**Change Type**: refactor

> **Purpose**: 重构D900DeviceManager类，移除重复代码，提高可维护性
> **Detailed Description**: 
> 1. 移除D900DeviceManager中已经在基类ToppingDeviceManager中定义的控制器声明
> 2. 保留D900特有的功能和控制器(usbSelect、usbDsd、iisPhase、iisChannel)
> 3. 移除自定义的_mapVerifyTypeToEnum方法，使用基类方法
> 4. 移除重复的executeCommand实现，通过handleDeviceSpecificCommand提供扩展
> 5. 在所有回调方法中添加控制器非空检查
> **Reason for Change**: 原有代码包含大量与基类重复的代码，导致代码冗长且难以维护
> **Impact Scope**: D900设备控制模块

   ```
   lib
   - device
     - d900
       - d900_device_manager.dart // refact 优化类结构，移除重复代码
   ```

...