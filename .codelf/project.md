## {Project Name} (init from readme/docs)

> {Project Description}

> {Project Purpose}

> {Project Status}

> {Project Team}

> {Framework/language/other(you think it is important to know)}



## Dependencies (init from programming language specification like package.json, requirements.txt, etc.)

* package1 (version): simple description
* package2 (version): simple description


## Development Environment

> include all the tools and environments needed to run the project
> makefile introduction (if exists)


## Structrue (init from project tree)

> It is essential to consistently refine the analysis down to the file level — this level of granularity is of utmost importance.

> If the number of files is too large, you should at least list all the directories, and provide comments for the parts you consider particularly important.

> In the code block below, add comments to the directories/files to explain their functionality and usage scenarios.

> if you think the directory/file is not important, you can not skip it, just add a simple comment to it.

> but if you think the directory/file is important, you should read the files and add more detail comments on it (e.g. add comments on the functions, classes, and variables. explain the functionality and usage scenarios. write the importance of the directory/file).
- ios
    - .DS_Store
    - .gitignore
    - .symlinks
        - plugins
            - device_info_plus
            - file_picker
            - flutter_blue_plus_darwin
            - flutter_native_splash
            - flutter_pdfview
            - fluttertoast
            - image_picker_ios
            - open_file_ios
            - package_info_plus
            - path_provider_foundation
            - permission_handler_apple
            - qr_code_scanner_plus
            - share_plus
            - shared_preferences_foundation
            - sqflite_darwin
            - topping_ble_control
            - url_launcher_ios
    - Flutter
        - AppFrameworkInfo.plist
        - Configs
            - Debug-dev.xcconfig
            - Debug-prod.xcconfig
            - Debug-staging.xcconfig
            - Profile-dev.xcconfig
            - Profile-prod.xcconfig
            - Profile-staging.xcconfig
            - Release-dev.xcconfig
            - Release-prod.xcconfig
            - Release-staging.xcconfig
        - Debug.xcconfig
        - Flavors
            - Dev.xcconfig
            - Prod.xcconfig
            - Staging.xcconfig
        - Flavors.xcconfig
        - Flutter.podspec
        - Generated.xcconfig
        - Release.xcconfig
        - flutter_export_environment.sh
    - Podfile
    - Podfile.lock
    - Pods
        - DKImagePickerController
            - LICENSE
            - README.md
            - Sources
                - DKImageDataManager
                    - DKImageBaseManager.swift
                    - DKImageDataManager.swift
                    - DKImageGroupDataManager.swift
                    - Model
                        - DKAsset+Export.swift
                        - DKAsset+Fetch.swift
                        - DKAsset.swift
                        - DKAssetGroup.swift
                - DKImagePickerController
                    - DKImageAssetExporter.swift
                    - DKImageExtensionController.swift
                    - DKImagePickerController.swift
                    - DKImagePickerControllerBaseUIDelegate.swift
                    - DKPopoverViewController.swift
                    - Resource
                        - DKImagePickerControllerResource.swift
                        - Resources
                            - Base.lproj
                                - DKImagePickerController.strings
                            - Images.xcassets
                                - Contents.json
                                - camera.imageset
                                    - Contents.json
                                    - camera.png
                                    - <EMAIL>
                                    - <EMAIL>
                                - checked_background.imageset
                                    - Contents.json
                                    - checked_background.png
                                    - <EMAIL>
                                    - <EMAIL>
                                - empty_album.imageset
                                    - Contents.json
                                    - empty_album.png
                                - photoGallery_back_arrow.imageset
                                    - Contents.json
                                    - photoGallery_back_arrow.png
                                    - <EMAIL>
                                    - <EMAIL>
                                - photoGallery_checked_image.imageset
                                    - Contents.json
                                    - photoGallery_checked_image.png
                                    - <EMAIL>
                                    - <EMAIL>
                                - photoGallery_unchecked_image.imageset
                                    - Contents.json
                                    - photoGallery_unchecked_image.png
                                    - <EMAIL>
                                    - <EMAIL>
                                - tick_blue.imageset
                                    - Contents.json
                                    - tick_blue.png
                                    - <EMAIL>
                                    - <EMAIL>
                                - video_camera.imageset
                                    - Contents.json
                                    - video_camera.png
                                    - <EMAIL>
                                    - <EMAIL>
                            - PrivacyInfo.xcprivacy
                            - ar.lproj
                                - DKImagePickerController.strings
                            - da.lproj
                                - DKImagePickerController.strings
                            - de.lproj
                                - DKImagePickerController.strings
                            - en.lproj
                                - DKImagePickerController.strings
                            - es.lproj
                                - DKImagePickerController.strings
                            - fr.lproj
                                - DKImagePickerController.strings
                            - hu.lproj
                                - DKImagePickerController.strings
                            - it.lproj
                                - DKImagePickerController.strings
                            - ja.lproj
                                - DKImagePickerController.strings
                            - ko.lproj
                                - DKImagePickerController.strings
                            - nb-NO.lproj
                                - DKImagePickerController.strings
                            - nl.lproj
                                - DKImagePickerController.strings
                            - pt_BR.lproj
                                - DKImagePickerController.strings
                            - ru.lproj
                                - DKImagePickerController.strings
                            - tr.lproj
                                - DKImagePickerController.strings
                            - ur.lproj
                                - DKImagePickerController.strings
                            - vi.lproj
                                - DKImagePickerController.strings
                            - zh-Hans.lproj
                                - DKImagePickerController.strings
                            - zh-Hant.lproj
                                - DKImagePickerController.strings
                    - View
                        - Cell
                            - DKAssetGroupCellItemProtocol.swift
                            - DKAssetGroupDetailBaseCell.swift
                            - DKAssetGroupDetailCameraCell.swift
                            - DKAssetGroupDetailImageCell.swift
                            - DKAssetGroupDetailVideoCell.swift
                        - DKAssetGroupDetailVC.swift
                        - DKAssetGroupGridLayout.swift
                        - DKAssetGroupListVC.swift
                        - DKPermissionView.swift
                - Extensions
                    - DKImageExtensionGallery.swift
        - DKPhotoGallery
            - DKPhotoGallery
                - DKPhotoGallery.swift
                - DKPhotoGalleryContentVC.swift
                - DKPhotoGalleryItem.swift
                - DKPhotoGalleryScrollView.swift
                - DKPhotoIncrementalIndicator.swift
                - DKPhotoPreviewFactory.swift
                - Preview
                    - DKPhotoBasePreviewVC.swift
                    - DKPhotoContentAnimationView.swift
                    - DKPhotoProgressIndicator.swift
                    - DKPhotoProgressIndicatorProtocol.swift
                    - ImagePreview
                        - DKPhotoBaseImagePreviewVC.swift
                        - DKPhotoImageDownloader.swift
                        - DKPhotoImagePreviewVC.swift
                        - DKPhotoImageUtility.swift
                        - DKPhotoImageView.swift
                    - PDFPreview
                        - DKPDFView.swift
                        - DKPhotoPDFPreviewVC.swift
                    - PlayerPreview
                        - DKPhotoPlayerPreviewVC.swift
                        - DKPlayerView.swift
                    - QRCode
                        - DKPhotoQRCodeResultVC.swift
                        - DKPhotoWebVC.swift
                - Resource
                    - DKPhotoGalleryResource.swift
                    - Resources
                        - Base.lproj
                            - DKPhotoGallery.strings
                        - Images.xcassets
                            - Contents.json
                            - ImageFailed.imageset
                                - Contents.json
                                - ImageFailed.png
                                - <EMAIL>
                                - <EMAIL>
                            - ToolbarPause.imageset
                                - Contents.json
                                - ToolbarPause.png
                                - <EMAIL>
                                - <EMAIL>
                            - ToolbarPlay.imageset
                                - Contents.json
                                - ToolbarPlay.png
                                - <EMAIL>
                                - <EMAIL>
                            - VideoClose.imageset
                                - Contents.json
                                - VideoClose.png
                                - <EMAIL>
                                - <EMAIL>
                            - VideoPlay.imageset
                                - Contents.json
                                - VideoPlay.png
                                - <EMAIL>
                                - <EMAIL>
                            - VideoPlayControlBackground.imageset
                                - Contents.json
                                - VideoPlayControlBackground.png
                                - <EMAIL>
                                - <EMAIL>
                            - VideoTimeSlider.imageset
                                - Contents.json
                                - VideoTimeSlider.png
                                - <EMAIL>
                                - <EMAIL>
                        - PrivacyInfo.xcprivacy
                        - en.lproj
                            - DKPhotoGallery.strings
                        - zh-Hans.lproj
                            - DKPhotoGallery.strings
                - Transition
                    - DKPhotoGalleryInteractiveTransition.swift
                    - DKPhotoGalleryTransitionController.swift
                    - DKPhotoGalleryTransitionDismiss.swift
                    - DKPhotoGalleryTransitionPresent.swift
            - LICENSE
            - README.md
        - Headers
        - Local Podspecs
            - Flutter.podspec.json
            - device_info_plus.podspec.json
            - file_picker.podspec.json
            - flutter_blue_plus_darwin.podspec.json
            - flutter_blue_plus_ios.podspec.json
            - flutter_native_splash.podspec.json
            - flutter_pdfview.podspec.json
            - fluttertoast.podspec.json
            - image_cropper.podspec.json
            - image_gallery_saver.podspec.json
            - image_picker_ios.podspec.json
            - open_file_ios.podspec.json
            - package_info_plus.podspec.json
            - path_provider_foundation.podspec.json
            - permission_handler_apple.podspec.json
            - photo_manager.podspec.json
            - qr_code_scanner_plus.podspec.json
            - share_plus.podspec.json
            - shared_preferences_foundation.podspec.json
            - sqflite_darwin.podspec.json
            - topping_ble_control.podspec.json
            - url_launcher_ios.podspec.json
            - video_player_avfoundation.podspec.json
        - MTBBarcodeScanner
            - Classes
                - ios
                    - Scanners
                        - MTBBarcodeScanner.h
                        - MTBBarcodeScanner.m
            - LICENSE
            - README.md
        - Manifest.lock
        - Pods.xcodeproj
            - project.pbxproj
            - xcshareddata
                - xcschemes
            - xcuserdata
                - wangjianguo.xcuserdatad
                    - xcschemes
                        - DKImagePickerController-DKImagePickerController.xcscheme
                        - DKImagePickerController.xcscheme
                        - DKPhotoGallery-DKPhotoGallery.xcscheme
                        - DKPhotoGallery.xcscheme
                        - Flutter.xcscheme
                        - MTBBarcodeScanner.xcscheme
                        - Pods-Runner.xcscheme
                        - Pods-RunnerTests.xcscheme
                        - SDWebImage-SDWebImage.xcscheme
                        - SDWebImage.xcscheme
                        - SwiftyGif-SwiftyGif.xcscheme
                        - SwiftyGif.xcscheme
                        - device_info_plus-device_info_plus_privacy.xcscheme
                        - device_info_plus.xcscheme
                        - file_picker-file_picker_ios_privacy.xcscheme
                        - file_picker.xcscheme
                        - flutter_blue_plus_darwin.xcscheme
                        - flutter_native_splash-flutter_native_splash_privacy.xcscheme
                        - flutter_native_splash.xcscheme
                        - flutter_pdfview.xcscheme
                        - fluttertoast-fluttertoast_privacy.xcscheme
                        - fluttertoast.xcscheme
                        - image_picker_ios-image_picker_ios_privacy.xcscheme
                        - image_picker_ios.xcscheme
                        - open_file_ios.xcscheme
                        - package_info_plus-package_info_plus_privacy.xcscheme
                        - package_info_plus.xcscheme
                        - path_provider_foundation-path_provider_foundation_privacy.xcscheme
                        - path_provider_foundation.xcscheme
                        - permission_handler_apple-permission_handler_apple_privacy.xcscheme
                        - permission_handler_apple.xcscheme
                        - qr_code_scanner_plus.xcscheme
                        - share_plus-share_plus_privacy.xcscheme
                        - share_plus.xcscheme
                        - shared_preferences_foundation-shared_preferences_foundation_privacy.xcscheme
                        - shared_preferences_foundation.xcscheme
                        - sqflite_darwin-sqflite_darwin_privacy.xcscheme
                        - sqflite_darwin.xcscheme
                        - topping_ble_control.xcscheme
                        - url_launcher_ios-url_launcher_ios_privacy.xcscheme
                        - url_launcher_ios.xcscheme
                        - xcschememanagement.plist
        - SDWebImage
            - LICENSE
            - README.md
            - SDWebImage
                - Core
                    - NSButton+WebCache.h
                    - NSButton+WebCache.m
                    - NSData+ImageContentType.h
                    - NSData+ImageContentType.m
                    - NSImage+Compatibility.h
                    - NSImage+Compatibility.m
                    - SDAnimatedImage.h
                    - SDAnimatedImage.m
                    - SDAnimatedImagePlayer.h
                    - SDAnimatedImagePlayer.m
                    - SDAnimatedImageRep.h
                    - SDAnimatedImageRep.m
                    - SDAnimatedImageView+WebCache.h
                    - SDAnimatedImageView+WebCache.m
                    - SDAnimatedImageView.h
                    - SDAnimatedImageView.m
                    - SDCallbackQueue.h
                    - SDCallbackQueue.m
                    - SDDiskCache.h
                    - SDDiskCache.m
                    - SDGraphicsImageRenderer.h
                    - SDGraphicsImageRenderer.m
                    - SDImageAPNGCoder.h
                    - SDImageAPNGCoder.m
                    - SDImageAWebPCoder.h
                    - SDImageAWebPCoder.m
                    - SDImageCache.h
                    - SDImageCache.m
                    - SDImageCacheConfig.h
                    - SDImageCacheConfig.m
                    - SDImageCacheDefine.h
                    - SDImageCacheDefine.m
                    - SDImageCachesManager.h
                    - SDImageCachesManager.m
                    - SDImageCoder.h
                    - SDImageCoder.m
                    - SDImageCoderHelper.h
                    - SDImageCoderHelper.m
                    - SDImageCodersManager.h
                    - SDImageCodersManager.m
                    - SDImageFrame.h
                    - SDImageFrame.m
                    - SDImageGIFCoder.h
                    - SDImageGIFCoder.m
                    - SDImageGraphics.h
                    - SDImageGraphics.m
                    - SDImageHEICCoder.h
                    - SDImageHEICCoder.m
                    - SDImageIOAnimatedCoder.h
                    - SDImageIOAnimatedCoder.m
                    - SDImageIOCoder.h
                    - SDImageIOCoder.m
                    - SDImageLoader.h
                    - SDImageLoader.m
                    - SDImageLoadersManager.h
                    - SDImageLoadersManager.m
                    - SDImageTransformer.h
                    - SDImageTransformer.m
                    - SDMemoryCache.h
                    - SDMemoryCache.m
                    - SDWebImageCacheKeyFilter.h
                    - SDWebImageCacheKeyFilter.m
                    - SDWebImageCacheSerializer.h
                    - SDWebImageCacheSerializer.m
                    - SDWebImageCompat.h
                    - SDWebImageCompat.m
                    - SDWebImageDefine.h
                    - SDWebImageDefine.m
                    - SDWebImageDownloader.h
                    - SDWebImageDownloader.m
                    - SDWebImageDownloaderConfig.h
                    - SDWebImageDownloaderConfig.m
                    - SDWebImageDownloaderDecryptor.h
                    - SDWebImageDownloaderDecryptor.m
                    - SDWebImageDownloaderOperation.h
                    - SDWebImageDownloaderOperation.m
                    - SDWebImageDownloaderRequestModifier.h
                    - SDWebImageDownloaderRequestModifier.m
                    - SDWebImageDownloaderResponseModifier.h
                    - SDWebImageDownloaderResponseModifier.m
                    - SDWebImageError.h
                    - SDWebImageError.m
                    - SDWebImageIndicator.h
                    - SDWebImageIndicator.m
                    - SDWebImageManager.h
                    - SDWebImageManager.m
                    - SDWebImageOperation.h
                    - SDWebImageOperation.m
                    - SDWebImageOptionsProcessor.h
                    - SDWebImageOptionsProcessor.m
                    - SDWebImagePrefetcher.h
                    - SDWebImagePrefetcher.m
                    - SDWebImageTransition.h
                    - SDWebImageTransition.m
                    - UIButton+WebCache.h
                    - UIButton+WebCache.m
                    - UIImage+ExtendedCacheData.h
                    - UIImage+ExtendedCacheData.m
                    - UIImage+ForceDecode.h
                    - UIImage+ForceDecode.m
                    - UIImage+GIF.h
                    - UIImage+GIF.m
                    - UIImage+MemoryCacheCost.h
                    - UIImage+MemoryCacheCost.m
                    - UIImage+Metadata.h
                    - UIImage+Metadata.m
                    - UIImage+MultiFormat.h
                    - UIImage+MultiFormat.m
                    - UIImage+Transform.h
                    - UIImage+Transform.m
                    - UIImageView+HighlightedWebCache.h
                    - UIImageView+HighlightedWebCache.m
                    - UIImageView+WebCache.h
                    - UIImageView+WebCache.m
                    - UIView+WebCache.h
                    - UIView+WebCache.m
                    - UIView+WebCacheOperation.h
                    - UIView+WebCacheOperation.m
                    - UIView+WebCacheState.h
                    - UIView+WebCacheState.m
                - Private
                    - NSBezierPath+SDRoundedCorners.h
                    - NSBezierPath+SDRoundedCorners.m
                    - SDAssociatedObject.h
                    - SDAssociatedObject.m
                    - SDAsyncBlockOperation.h
                    - SDAsyncBlockOperation.m
                    - SDDeviceHelper.h
                    - SDDeviceHelper.m
                    - SDDisplayLink.h
                    - SDDisplayLink.m
                    - SDFileAttributeHelper.h
                    - SDFileAttributeHelper.m
                    - SDImageAssetManager.h
                    - SDImageAssetManager.m
                    - SDImageCachesManagerOperation.h
                    - SDImageCachesManagerOperation.m
                    - SDImageFramePool.h
                    - SDImageFramePool.m
                    - SDImageIOAnimatedCoderInternal.h
                    - SDInternalMacros.h
                    - SDInternalMacros.m
                    - SDWeakProxy.h
                    - SDWeakProxy.m
                    - SDWebImageTransitionInternal.h
                    - SDmetamacros.h
                    - UIColor+SDHexString.h
                    - UIColor+SDHexString.m
            - WebImage
                - PrivacyInfo.xcprivacy
                - SDWebImage.h
        - SwiftyGif
            - LICENSE
            - README.md
            - SwiftyGif
                - NSImage+SwiftyGif.swift
                - NSImageView+SwiftyGif.swift
                - ObjcAssociatedWeakObject.swift
                - PrivacyInfo.xcprivacy
                - SwiftyGif.h
                - SwiftyGifManager.swift
                - UIImage+SwiftyGif.swift
                - UIImageView+SwiftyGif.swift
        - TOCropViewController
            - LICENSE
            - Objective-C
                - TOCropViewController
                    - Categories
                        - UIImage+CropRotate.h
                        - UIImage+CropRotate.m
                    - Constants
                        - TOCropViewConstants.h
                    - Models
                        - TOActivityCroppedImageProvider.h
                        - TOActivityCroppedImageProvider.m
                        - TOCropViewControllerTransitioning.h
                        - TOCropViewControllerTransitioning.m
                        - TOCroppedImageAttributes.h
                        - TOCroppedImageAttributes.m
                    - Resources
                        - Base.lproj
                            - TOCropViewControllerLocalizable.strings
                        - PrivacyInfo.xcprivacy
                        - ar.lproj
                            - TOCropViewControllerLocalizable.strings
                        - ca.lproj
                            - TOCropViewControllerLocalizable.strings
                        - cs.lproj
                            - TOCropViewControllerLocalizable.strings
                        - da-DK.lproj
                            - TOCropViewControllerLocalizable.strings
                        - de.lproj
                            - TOCropViewControllerLocalizable.strings
                        - en.lproj
                            - TOCropViewControllerLocalizable.strings
                        - es.lproj
                            - TOCropViewControllerLocalizable.strings
                        - fa-IR.lproj
                            - TOCropViewControllerLocalizable.strings
                        - fa.lproj
                            - TOCropViewControllerLocalizable.strings
                        - fi.lproj
                            - TOCropViewControllerLocalizable.strings
                        - fr.lproj
                            - TOCropViewControllerLocalizable.strings
                        - hu.lproj
                            - TOCropViewControllerLocalizable.strings
                        - id.lproj
                            - TOCropViewControllerLocalizable.strings
                        - it.lproj
                            - TOCropViewControllerLocalizable.strings
                        - ja.lproj
                            - TOCropViewControllerLocalizable.strings
                        - ko.lproj
                            - TOCropViewControllerLocalizable.strings
                        - ms.lproj
                            - TOCropViewControllerLocalizable.strings
                        - nl.lproj
                            - TOCropViewControllerLocalizable.strings
                        - pl.lproj
                            - TOCropViewControllerLocalizable.strings
                        - pt-BR.lproj
                            - TOCropViewControllerLocalizable.strings
                        - pt.lproj
                            - TOCropViewControllerLocalizable.strings
                        - ro.lproj
                            - TOCropViewControllerLocalizable.strings
                        - ru.lproj
                            - TOCropViewControllerLocalizable.strings
                        - sk.lproj
                            - TOCropViewControllerLocalizable.strings
                        - tr.lproj
                            - TOCropViewControllerLocalizable.strings
                        - uk.lproj
                            - TOCropViewControllerLocalizable.strings
                        - vi.lproj
                            - TOCropViewControllerLocalizable.strings
                        - zh-Hans.lproj
                            - TOCropViewControllerLocalizable.strings
                        - zh-Hant.lproj
                            - TOCropViewControllerLocalizable.strings
                    - TOCropViewController.h
                    - TOCropViewController.m
                    - Views
                        - TOCropOverlayView.h
                        - TOCropOverlayView.m
                        - TOCropScrollView.h
                        - TOCropScrollView.m
                        - TOCropToolbar.h
                        - TOCropToolbar.m
                        - TOCropView.h
                        - TOCropView.m
            - README.md
        - Target Support Files
            - DKImagePickerController
                - DKImagePickerController-Info.plist
                - DKImagePickerController-dummy.m
                - DKImagePickerController-prefix.pch
                - DKImagePickerController-umbrella.h
                - DKImagePickerController.debug.xcconfig
                - DKImagePickerController.modulemap
                - DKImagePickerController.release.xcconfig
                - ResourceBundle-DKImagePickerController-DKImagePickerController-Info.plist
            - DKPhotoGallery
                - DKPhotoGallery-Info.plist
                - DKPhotoGallery-dummy.m
                - DKPhotoGallery-prefix.pch
                - DKPhotoGallery-umbrella.h
                - DKPhotoGallery.debug.xcconfig
                - DKPhotoGallery.modulemap
                - DKPhotoGallery.release.xcconfig
                - ResourceBundle-DKPhotoGallery-DKPhotoGallery-Info.plist
            - Flutter
                - Flutter.debug.xcconfig
                - Flutter.release.xcconfig
            - MTBBarcodeScanner
                - MTBBarcodeScanner-Info.plist
                - MTBBarcodeScanner-dummy.m
                - MTBBarcodeScanner-prefix.pch
                - MTBBarcodeScanner-umbrella.h
                - MTBBarcodeScanner.debug.xcconfig
                - MTBBarcodeScanner.modulemap
                - MTBBarcodeScanner.release.xcconfig
            - Pods-Runner
                - Pods-Runner-Info.plist
                - Pods-Runner-acknowledgements.markdown
                - Pods-Runner-acknowledgements.plist
                - Pods-Runner-dummy.m
                - Pods-Runner-frameworks-Debug copy-input-files.xcfilelist
                - Pods-Runner-frameworks-Debug copy-output-files.xcfilelist
                - Pods-Runner-frameworks-Debug-dev-input-files.xcfilelist
                - Pods-Runner-frameworks-Debug-dev-output-files.xcfilelist
                - Pods-Runner-frameworks-Debug-input-files.xcfilelist
                - Pods-Runner-frameworks-Debug-output-files.xcfilelist
                - Pods-Runner-frameworks-Debug-prod-input-files.xcfilelist
                - Pods-Runner-frameworks-Debug-prod-output-files.xcfilelist
                - Pods-Runner-frameworks-Debug-staging-input-files.xcfilelist
                - Pods-Runner-frameworks-Debug-staging-output-files.xcfilelist
                - Pods-Runner-frameworks-Profile-dev-input-files.xcfilelist
                - Pods-Runner-frameworks-Profile-dev-output-files.xcfilelist
                - Pods-Runner-frameworks-Profile-input-files.xcfilelist
                - Pods-Runner-frameworks-Profile-output-files.xcfilelist
                - Pods-Runner-frameworks-Profile-prod-input-files.xcfilelist
                - Pods-Runner-frameworks-Profile-prod-output-files.xcfilelist
                - Pods-Runner-frameworks-Profile-staging-input-files.xcfilelist
                - Pods-Runner-frameworks-Profile-staging-output-files.xcfilelist
                - Pods-Runner-frameworks-Release-dev-input-files.xcfilelist
                - Pods-Runner-frameworks-Release-dev-output-files.xcfilelist
                - Pods-Runner-frameworks-Release-input-files.xcfilelist
                - Pods-Runner-frameworks-Release-output-files.xcfilelist
                - Pods-Runner-frameworks-Release-prod-input-files.xcfilelist
                - Pods-Runner-frameworks-Release-prod-output-files.xcfilelist
                - Pods-Runner-frameworks-Release-staging-input-files.xcfilelist
                - Pods-Runner-frameworks-Release-staging-output-files.xcfilelist
                - Pods-Runner-frameworks.sh
                - Pods-Runner-resources-Debug copy-input-files.xcfilelist
                - Pods-Runner-resources-Debug copy-output-files.xcfilelist
                - Pods-Runner-resources-Debug-dev-input-files.xcfilelist
                - Pods-Runner-resources-Debug-dev-output-files.xcfilelist
                - Pods-Runner-resources-Debug-input-files.xcfilelist
                - Pods-Runner-resources-Debug-output-files.xcfilelist
                - Pods-Runner-resources-Debug-prod-input-files.xcfilelist
                - Pods-Runner-resources-Debug-prod-output-files.xcfilelist
                - Pods-Runner-resources-Debug-staging-input-files.xcfilelist
                - Pods-Runner-resources-Debug-staging-output-files.xcfilelist
                - Pods-Runner-resources-Profile-dev-input-files.xcfilelist
                - Pods-Runner-resources-Profile-dev-output-files.xcfilelist
                - Pods-Runner-resources-Profile-input-files.xcfilelist
                - Pods-Runner-resources-Profile-output-files.xcfilelist
                - Pods-Runner-resources-Profile-prod-input-files.xcfilelist
                - Pods-Runner-resources-Profile-prod-output-files.xcfilelist
                - Pods-Runner-resources-Profile-staging-input-files.xcfilelist
                - Pods-Runner-resources-Profile-staging-output-files.xcfilelist
                - Pods-Runner-resources-Release-dev-input-files.xcfilelist
                - Pods-Runner-resources-Release-dev-output-files.xcfilelist
                - Pods-Runner-resources-Release-input-files.xcfilelist
                - Pods-Runner-resources-Release-output-files.xcfilelist
                - Pods-Runner-resources-Release-prod-input-files.xcfilelist
                - Pods-Runner-resources-Release-prod-output-files.xcfilelist
                - Pods-Runner-resources-Release-staging-input-files.xcfilelist
                - Pods-Runner-resources-Release-staging-output-files.xcfilelist
                - Pods-Runner-resources.sh
                - Pods-Runner-umbrella.h
                - Pods-Runner.debug copy.xcconfig
                - Pods-Runner.debug-dev.xcconfig
                - Pods-Runner.debug-prod.xcconfig
                - Pods-Runner.debug-staging.xcconfig
                - Pods-Runner.debug.xcconfig
                - Pods-Runner.modulemap
                - Pods-Runner.profile-dev.xcconfig
                - Pods-Runner.profile-prod.xcconfig
                - Pods-Runner.profile-staging.xcconfig
                - Pods-Runner.profile.xcconfig
                - Pods-Runner.release-dev.xcconfig
                - Pods-Runner.release-prod.xcconfig
                - Pods-Runner.release-staging.xcconfig
                - Pods-Runner.release.xcconfig
            - Pods-RunnerTests
                - Pods-RunnerTests-Info.plist
                - Pods-RunnerTests-acknowledgements.markdown
                - Pods-RunnerTests-acknowledgements.plist
                - Pods-RunnerTests-dummy.m
                - Pods-RunnerTests-umbrella.h
                - Pods-RunnerTests.debug copy.xcconfig
                - Pods-RunnerTests.debug-dev.xcconfig
                - Pods-RunnerTests.debug-prod.xcconfig
                - Pods-RunnerTests.debug-staging.xcconfig
                - Pods-RunnerTests.debug.xcconfig
                - Pods-RunnerTests.modulemap
                - Pods-RunnerTests.profile-dev.xcconfig
                - Pods-RunnerTests.profile-prod.xcconfig
                - Pods-RunnerTests.profile-staging.xcconfig
                - Pods-RunnerTests.profile.xcconfig
                - Pods-RunnerTests.release-dev.xcconfig
                - Pods-RunnerTests.release-prod.xcconfig
                - Pods-RunnerTests.release-staging.xcconfig
                - Pods-RunnerTests.release.xcconfig
            - SDWebImage
                - ResourceBundle-SDWebImage-SDWebImage-Info.plist
                - SDWebImage-Info.plist
                - SDWebImage-dummy.m
                - SDWebImage-prefix.pch
                - SDWebImage-umbrella.h
                - SDWebImage.debug.xcconfig
                - SDWebImage.modulemap
                - SDWebImage.release.xcconfig
            - SwiftyGif
                - ResourceBundle-SwiftyGif-SwiftyGif-Info.plist
                - SwiftyGif-Info.plist
                - SwiftyGif-dummy.m
                - SwiftyGif-prefix.pch
                - SwiftyGif-umbrella.h
                - SwiftyGif.debug.xcconfig
                - SwiftyGif.modulemap
                - SwiftyGif.release.xcconfig
            - device_info_plus
                - ResourceBundle-device_info_plus_privacy-device_info_plus-Info.plist
                - device_info_plus-Info.plist
                - device_info_plus-dummy.m
                - device_info_plus-prefix.pch
                - device_info_plus-umbrella.h
                - device_info_plus.debug.xcconfig
                - device_info_plus.modulemap
                - device_info_plus.release.xcconfig
            - file_picker
                - ResourceBundle-file_picker_ios_privacy-file_picker-Info.plist
                - file_picker-Info.plist
                - file_picker-dummy.m
                - file_picker-prefix.pch
                - file_picker.debug.xcconfig
                - file_picker.modulemap
                - file_picker.release.xcconfig
            - flutter_blue_plus_darwin
                - flutter_blue_plus_darwin-Info.plist
                - flutter_blue_plus_darwin-dummy.m
                - flutter_blue_plus_darwin-prefix.pch
                - flutter_blue_plus_darwin-umbrella.h
                - flutter_blue_plus_darwin.debug.xcconfig
                - flutter_blue_plus_darwin.modulemap
                - flutter_blue_plus_darwin.release.xcconfig
            - flutter_native_splash
                - ResourceBundle-flutter_native_splash_privacy-flutter_native_splash-Info.plist
                - flutter_native_splash-Info.plist
                - flutter_native_splash-dummy.m
                - flutter_native_splash-prefix.pch
                - flutter_native_splash-umbrella.h
                - flutter_native_splash.debug.xcconfig
                - flutter_native_splash.modulemap
                - flutter_native_splash.release.xcconfig
            - flutter_pdfview
                - flutter_pdfview-Info.plist
                - flutter_pdfview-dummy.m
                - flutter_pdfview-prefix.pch
                - flutter_pdfview-umbrella.h
                - flutter_pdfview.debug.xcconfig
                - flutter_pdfview.modulemap
                - flutter_pdfview.release.xcconfig
            - fluttertoast
                - ResourceBundle-fluttertoast_privacy-fluttertoast-Info.plist
                - fluttertoast-Info.plist
                - fluttertoast-dummy.m
                - fluttertoast-prefix.pch
                - fluttertoast-umbrella.h
                - fluttertoast.debug.xcconfig
                - fluttertoast.modulemap
                - fluttertoast.release.xcconfig
            - image_picker_ios
                - ResourceBundle-image_picker_ios_privacy-image_picker_ios-Info.plist
                - image_picker_ios-Info.plist
                - image_picker_ios-dummy.m
                - image_picker_ios-prefix.pch
                - image_picker_ios.debug.xcconfig
                - image_picker_ios.modulemap
                - image_picker_ios.release.xcconfig
            - open_file_ios
                - open_file_ios-Info.plist
                - open_file_ios-dummy.m
                - open_file_ios-prefix.pch
                - open_file_ios-umbrella.h
                - open_file_ios.debug.xcconfig
                - open_file_ios.modulemap
                - open_file_ios.release.xcconfig
            - package_info_plus
                - ResourceBundle-package_info_plus_privacy-package_info_plus-Info.plist
                - package_info_plus-Info.plist
                - package_info_plus-dummy.m
                - package_info_plus-prefix.pch
                - package_info_plus-umbrella.h
                - package_info_plus.debug.xcconfig
                - package_info_plus.modulemap
                - package_info_plus.release.xcconfig
            - path_provider_foundation
                - ResourceBundle-path_provider_foundation_privacy-path_provider_foundation-Info.plist
                - path_provider_foundation-Info.plist
                - path_provider_foundation-dummy.m
                - path_provider_foundation-prefix.pch
                - path_provider_foundation-umbrella.h
                - path_provider_foundation.debug.xcconfig
                - path_provider_foundation.modulemap
                - path_provider_foundation.release.xcconfig
            - permission_handler_apple
                - ResourceBundle-permission_handler_apple_privacy-permission_handler_apple-Info.plist
                - permission_handler_apple-Info.plist
                - permission_handler_apple-dummy.m
                - permission_handler_apple-prefix.pch
                - permission_handler_apple-umbrella.h
                - permission_handler_apple.debug.xcconfig
                - permission_handler_apple.modulemap
                - permission_handler_apple.release.xcconfig
            - qr_code_scanner_plus
                - qr_code_scanner_plus-Info.plist
                - qr_code_scanner_plus-dummy.m
                - qr_code_scanner_plus-prefix.pch
                - qr_code_scanner_plus-umbrella.h
                - qr_code_scanner_plus.debug.xcconfig
                - qr_code_scanner_plus.modulemap
                - qr_code_scanner_plus.release.xcconfig
            - share_plus
                - ResourceBundle-share_plus_privacy-share_plus-Info.plist
                - share_plus-Info.plist
                - share_plus-dummy.m
                - share_plus-prefix.pch
                - share_plus-umbrella.h
                - share_plus.debug.xcconfig
                - share_plus.modulemap
                - share_plus.release.xcconfig
            - shared_preferences_foundation
                - ResourceBundle-shared_preferences_foundation_privacy-shared_preferences_foundation-Info.plist
                - shared_preferences_foundation-Info.plist
                - shared_preferences_foundation-dummy.m
                - shared_preferences_foundation-prefix.pch
                - shared_preferences_foundation-umbrella.h
                - shared_preferences_foundation.debug.xcconfig
                - shared_preferences_foundation.modulemap
                - shared_preferences_foundation.release.xcconfig
            - sqflite_darwin
                - ResourceBundle-sqflite_darwin_privacy-sqflite_darwin-Info.plist
                - sqflite_darwin-Info.plist
                - sqflite_darwin-dummy.m
                - sqflite_darwin-prefix.pch
                - sqflite_darwin-umbrella.h
                - sqflite_darwin.debug.xcconfig
                - sqflite_darwin.modulemap
                - sqflite_darwin.release.xcconfig
            - topping_ble_control
                - topping_ble_control-Info.plist
                - topping_ble_control-dummy.m
                - topping_ble_control-prefix.pch
                - topping_ble_control-umbrella.h
                - topping_ble_control.debug.xcconfig
                - topping_ble_control.modulemap
                - topping_ble_control.release.xcconfig
            - url_launcher_ios
                - ResourceBundle-url_launcher_ios_privacy-url_launcher_ios-Info.plist
                - url_launcher_ios-Info.plist
                - url_launcher_ios-dummy.m
                - url_launcher_ios-prefix.pch
                - url_launcher_ios-umbrella.h
                - url_launcher_ios.debug.xcconfig
                - url_launcher_ios.modulemap
                - url_launcher_ios.release.xcconfig
        - Toast
            - LICENSE
            - README.md
            - Toast
                - Resources
                    - PrivacyInfo.xcprivacy
                - UIView+Toast.h
                - UIView+Toast.m
            - Toast-Framework
                - Toast.h

## Recent Changes

### 2024-05-22: 重构和封装设备音量处理逻辑

1. 封装不同设备类型的音量处理逻辑:
   - 添加统一的音量转换辅助方法，分离设备类型特定的逻辑
   - 重构现有的音量相关方法，使用新的辅助方法提高代码一致性
   - 使用线性映射统一实现音量百分比与实际值的转换算法
   - 减少重复代码，提高代码可维护性和可扩展性

### 2024-05-22: 修复D900设备音量控制问题 (第三阶段)

1. 修复D900设备音量滑块显示问题:
   - 修改`getVolumePercentage`方法，正确处理正值音量（+1到+8dB）
   - 将正值音量映射到滑块的92%-100%区间，确保+8dB对应滑块100%位置
   - 解决了音量设置为+8dB时滑块仅显示85%而非100%的问题

### 2024-05-22: 修复D900设备音量控制问题 (第二阶段)

1. 修复D900设备无法将音量设置到最大值+8dB的问题:
   - 修正`setVolumeEnd`方法，移除强制将正值转为负值的代码
   - 正确实现D900设备音量规则：-99到0范围发送正值，+1到+8范围发送负值
   - 解决了无法设置到最大音量+8dB的问题

### 2024-05-22: 修复D900设备音量条问题 (第一阶段)

1. 修复D900设备音量条无法拉到最大值的问题:
   - 修改`volumeFromPercentage`方法，确保D900设备在音量拉到100%时能够正确地设置为+8dB
   - 修改音量滑块组件的divisions值，根据设备类型动态设置(D900为107，DX5为99)，以匹配实际音量范围
   - 解决了由于浮点运算精度问题导致的音量无法达到最大值的Bug
   - 修改音量调节逻辑，减少命令发送频率，改为只在用户松开滑块时发送一次命令

### 2024-05-07: 代码结构优化

1. 在`DeviceOperations`接口中添加了D900设备特有的方法:
   - `setUsbType(int type)`: 设置USB类型
   - `enableUsbDsdPassthrough(bool enable)`: 启用/禁用USB DSD直通
   - `setIisPhase(int phase)`: 设置IIS相位
   - `setIisDsdChannel(int channel)`: 设置IIS DSD通道
   - `setUsbSelect(int type)`: 设置USB选择
   - `enableUsbDsd(bool enable)`: 启用/禁用USB DSD
   - `setIisChannel(int channel)`: 设置IIS通道

2. 在`ToppingDeviceManager`抽象基类中为这些D900特有方法提供了默认空实现,便于其他设备类型(如DX5)继承而不需要实现这些方法

3. 在`D900DeviceManager`类中添加了`@override`注解,正确标记覆盖了父类的方法

4. 修复`Dx5iiDeviceManager`中的编译错误并确保其正确实现了`DeviceOperations`接口:
   - 修复了Settings对象创建和使用的错误
   - 添加了D900特有方法的空实现(带警告日志)
   - 使用正确的API调用替换不存在的方法

5. 优化`D900DeviceManager`类的代码结构:
   - 移除重复的控制器声明和事件流getter
   - 保留D900特有的功能和控制器
   - 移除自定义的验证结果映射方法,使用基类实现
   - 优化各回调方法,添加控制器非空检查
   - 重构deviceSpecificCommand处理方式

这些修改提高了代码的可维护性和扩展性,使设备特定功能的管理更加清晰和统一。

```