{"output": {"filePath": "compressed-output.txt", "style": "plain", "parsableStyle": false, "fileSummary": false, "directoryStructure": true, "removeComments": true, "removeEmptyLines": true, "compress": true, "topFilesLength": 0, "showLineNumbers": false, "copyToClipboard": false, "git": {"sortByChanges": true}}, "include": ["lib/**/*.dart", "pubspec.*", "test/**/*.dart"], "ignore": {"useGitignore": true, "useDefaultPatterns": true, "customPatterns": ["**/build/**", "**/.dart_tool/**", "**/.idea/**", "**/*.g.dart", "**/*.freezed.dart", "**/generated_plugins/**", "**/*.png", "**/*.jpg", "**/fonts/**", "**/l10n/**"]}, "security": {"enableSecurityCheck": true, "scanDependencies": true}, "tokenCount": {"encoding": "cl100k_base", "perFileAnalysis": true}}