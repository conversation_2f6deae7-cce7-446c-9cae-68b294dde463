/// 设备类型枚举
enum DeviceModeType {
  dx5,  // DX5 II
  dx9,    // 900
  // 添加更多设备类型
  unknown; // 未知设备
  
  /// 从字符串转换为枚举值
  static DeviceModeType fromString(String name, {DeviceModeType defaultValue = DeviceModeType.unknown}) {
    try {
      return DeviceModeType.values.firstWhere(
        (type) => type.name.toLowerCase() == name.toLowerCase(),
        orElse: () => defaultValue,
      );
    } catch (e) {
      return defaultValue;
    }
  }
}
