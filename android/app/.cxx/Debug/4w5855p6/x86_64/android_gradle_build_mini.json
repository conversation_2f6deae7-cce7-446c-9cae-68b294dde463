{"buildFiles": ["/opt/homebrew/Caskroom/flutter/3.27.1/flutter/packages/flutter_tools/gradle/src/main/groovy/CMakeLists.txt"], "cleanCommandsComponents": [["/Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/ninja", "-C", "/Volumes/Android/ToppingProject/topping_control/android/app/.cxx/Debug/4w5855p6/x86_64", "clean"]], "buildTargetsCommandComponents": ["/Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/ninja", "-C", "/Volumes/Android/ToppingProject/topping_control/android/app/.cxx/Debug/4w5855p6/x86_64", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}