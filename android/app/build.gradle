plugins {
    id "com.android.application"
    id "kotlin-android"
    id "dev.flutter.flutter-gradle-plugin"
}

def keystorePropertiesFile = rootProject.file("key.properties")
def keystoreProperties = new Properties()
keystoreProperties.load(new FileInputStream(keystorePropertiesFile))

// 加载版本配置
def versionPropertiesFile = rootProject.file("version.properties")
def versionProperties = new Properties()
versionProperties.load(new FileInputStream(versionPropertiesFile))

// 加载包名配置
def packagePropertiesFile = rootProject.file("../package_names.properties")
def packageProperties = new Properties()
packageProperties.load(new FileInputStream(packagePropertiesFile))

// 获取开发/测试环境版本号
def devVersionCode = versionProperties['devVersionCode'].toInteger()
def devVersionName = versionProperties['devVersionName']

// 获取生产环境版本号
def prodVersionCode = versionProperties['prodVersionCode'].toInteger()
def prodVersionName = versionProperties['prodVersionName']

// 获取各环境包名
def basePackageName = packageProperties['basePackageName']
def devPackageName = packageProperties['devPackageName']
def stagingPackageName = packageProperties['stagingPackageName']
def prodPackageName = packageProperties['prodPackageName']

// 添加构建前的版本号设置任务
task setVersionBeforeBuild {
    doLast {
        // 获取当前的构建变体
        def flavor = project.gradle.startParameter.taskRequests.toString().find(/(dev|staging|prod)/) ?: "prod"
        println "\n\n当前构建环境: $flavor"

        // 包名
        def packageName = basePackageName

        if (flavor == "dev") {
            packageName = devPackageName
        } else if (flavor == "staging") {
            packageName = stagingPackageName
        } else {
            packageName = prodPackageName
        }
        println "使用包名: $packageName"

        // 根据环境选择版本号
        def versionName
        def versionCode

        if (flavor == "prod") {
            versionName = prodVersionName
            versionCode = prodVersionCode.toInteger()
            println "使用生产版本号: $versionName ($versionCode)"
        } else {
            versionName = devVersionName
            versionCode = devVersionCode.toInteger()
            println "使用开发/测试版本号: $versionName ($versionCode)"
        }

        // 如果是开发或测试环境，添加后缀
        if (flavor == "dev") {
            versionName = "$versionName-dev"
        } else if (flavor == "staging") {
            versionName = "$versionName-staging"
        }

        println "最终版本号: $versionName ($versionCode)\n\n"
    }
}

// 在构建前运行版本号设置任务
preBuild.dependsOn setVersionBeforeBuild

android {
    namespace = basePackageName
    compileSdk = flutter.compileSdkVersion
    ndkVersion = flutter.ndkVersion

    buildFeatures {
        buildConfig true
    }

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_17
        targetCompatibility JavaVersion.VERSION_17
    }
    kotlinOptions {
        jvmTarget = 17
    }

    defaultConfig {
        applicationId = basePackageName
        minSdk = flutter.minSdkVersion
        targetSdk = flutter.targetSdkVersion
        versionCode = flutter.versionCode
        versionName = flutter.versionName
    }

    // 添加开发、测试和生产环境的flavor配置
    flavorDimensions "environment"
    productFlavors {
        dev {
            dimension "environment"
            // 使用配置文件中的开发环境包名
            applicationId = devPackageName
            versionNameSuffix "-dev"
            // 开发环境使用开发版本号
            versionCode devVersionCode
            versionName devVersionName

            // 定义开发环境特有的BuildConfig字段
            buildConfigField "String", "FLAVOR_TYPE", '"dev"'
            buildConfigField "String", "API_BASE_URL", '"http://192.168.2.121:8080/"'
            buildConfigField "String", "PACKAGE_NAME", '"' + devPackageName + '"'

            // 开发环境应用名称
            resValue "string", "app_name", "ToppingHome Dev"
        }
        staging {
            dimension "environment"
            // 使用配置文件中的测试环境包名
            applicationId = stagingPackageName
            versionNameSuffix "-staging"
            // 测试环境使用与开发环境相同的版本号
            versionCode devVersionCode
            versionName devVersionName

            // 定义测试环境特有的BuildConfig字段
            buildConfigField "String", "FLAVOR_TYPE", '"staging"'
            buildConfigField "String", "API_BASE_URL", '"http://test.api.topping.com:8080/"'
            buildConfigField "String", "PACKAGE_NAME", '"' + stagingPackageName + '"'

            // 测试环境应用名称
            resValue "string", "app_name", "ToppingHome Test"
        }
        prod {
            dimension "environment"
            // 使用配置文件中的生产环境包名
            applicationId = prodPackageName
            // 生产环境使用生产版本号
            versionCode prodVersionCode
            versionName prodVersionName

            // 定义生产环境特有的BuildConfig字段
            buildConfigField "String", "FLAVOR_TYPE", '"prod"'
            buildConfigField "String", "API_BASE_URL", '"http://49.232.108.42:8080/"'
            buildConfigField "String", "PACKAGE_NAME", '"' + prodPackageName + '"'

            // 生产环境应用名称
            resValue "string", "app_name", "ToppingHome"
        }
    }

    signingConfigs {
        debug {
            // 使用与release相同的签名配置
            keyAlias keystoreProperties['keyAlias']
            keyPassword keystoreProperties['keyPassword']
            storeFile file(keystoreProperties['storeFile'])
            storePassword keystoreProperties['storePassword']
        }
        release {
            keyAlias keystoreProperties['keyAlias']
            keyPassword keystoreProperties['keyPassword']
            storeFile file(keystoreProperties['storeFile'])
            storePassword keystoreProperties['storePassword']
        }
    }

    buildTypes {
        release {
            signingConfig signingConfigs.release
            minifyEnabled true // 启用代码压缩（可选）
            shrinkResources true // 启用资源压缩（可选）
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }

    // 自定义APK输出文件名，添加版本号
    applicationVariants.all { variant ->
        variant.outputs.all { output ->
            def flavor = variant.flavorName
            def buildType = variant.buildType.name
            def versionName = variant.versionName
            def versionCode = variant.versionCode
            
            // 新的文件名格式：应用名称_环境_构建类型_版本名_版本号.apk
            // 例如：ToppingHome_prod_release_1.0.0_1000.apk
            def newFileName = "ToppingHome_${flavor}_${buildType}_${versionName}_${versionCode}.apk"
            output.outputFileName = newFileName
            
            println "输出文件名: ${newFileName}"
        }
    }
}


flutter {
    source = "../.."
}
