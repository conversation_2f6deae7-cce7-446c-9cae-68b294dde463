package com.topping.home

import io.flutter.embedding.engine.FlutterEngine
import io.flutter.plugin.common.MethodChannel

/**
 * Flavor通道，用于向Flutter传递环境信息
 */
class FlavorChannel {
    companion object {
        private const val CHANNEL_NAME = "com.topping.home.flavor_channel"

        /**
         * 设置Flavor通道
         */
        fun setup(flutterEngine: FlutterEngine) {
            MethodChannel(flutterEngine.dartExecutor.binaryMessenger, CHANNEL_NAME).setMethodCallHandler { call, result ->
                when (call.method) {
                    "getFlavor" -> {
                        // 返回当前环境类型
                        result.success(BuildConfig.FLAVOR_TYPE)
                    }
                    "getApiBaseUrl" -> {
                        // 返回当前环境的API基础URL
                        result.success(BuildConfig.API_BASE_URL)
                    }
                    "getPackageName" -> {
                        // 返回当前环境的包名
                        try {
                            // 尝试使用BuildConfig中的PACKAGE_NAME字段
                            val packageNameField = BuildConfig::class.java.getField("PACKAGE_NAME")
                            val packageName = packageNameField.get(null) as String
                            result.success(packageName)
                        } catch (e: Exception) {
                            // 如果字段不存在，则返回应用的包名
                            result.success(BuildConfig.APPLICATION_ID)
                        }
                    }
                    else -> {
                        result.notImplemented()
                    }
                }
            }
        }
    }
}
