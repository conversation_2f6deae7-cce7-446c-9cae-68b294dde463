package com.topping.home

import android.content.Context
import io.flutter.embedding.engine.FlutterEngine
import io.flutter.plugin.common.MethodCall
import io.flutter.plugin.common.MethodChannel
import io.flutter.plugin.common.MethodChannel.MethodCallHandler
import io.flutter.plugin.common.MethodChannel.Result

/**
 * 处理Flavor相关的平台通道调用
 */
class FlavorChannelHandler(private val context: Context) : MethodCallHandler {
    
    companion object {
        private const val CHANNEL_NAME = "com.topping.home.flavor_channel"
        
        /**
         * 在FlutterEngine中注册通道
         */
        fun registerWith(flutterEngine: FlutterEngine, context: Context) {
            val channel = MethodChannel(flutterEngine.dartExecutor.binaryMessenger, CHANNEL_NAME)
            channel.setMethodCallHandler(FlavorChannelHandler(context))
        }
    }
    
    override fun onMethodCall(call: MethodCall, result: Result) {
        when (call.method) {
            "getFlavor" -> {
                // 获取当前的Flavor
                val flavor = BuildConfig.FLAVOR_TYPE
                result.success(flavor)
            }
            "getPackageName" -> {
                // 获取当前的包名
                val packageName = BuildConfig.PACKAGE_NAME ?: context.packageName
                result.success(packageName)
            }
            else -> {
                result.notImplemented()
            }
        }
    }
}
