#!/bin/bash

# 提示说明
echo "======================================================"
echo "AppTheme 替换为 ThemeHelper 的批量替换脚本"
echo "======================================================"
echo "该脚本将帮助您将项目中的AppTheme替换为ThemeHelper"
echo "执行前请确保已备份您的代码！"
echo "======================================================"
echo ""

# 检查环境
if ! command -v find &> /dev/null; then
    echo "错误: 未找到find命令，请确保您的系统支持基本命令行工具"
    exit 1
fi

# 设置项目路径
PROJECT_ROOT="$(pwd)"
echo "项目路径: $PROJECT_ROOT"
echo ""

# 1. 添加导入语句
echo "步骤1: 为使用AppTheme的文件添加ThemeHelper导入语句"
find $PROJECT_ROOT/lib -type f -name "*.dart" -exec grep -l "AppTheme" {} \; | xargs -I{} sed -i '' 's/import.*app_theme.dart.*/import '\''package:topping_home\/theme\/theme_helper.dart'\'';/' {}
echo "完成"
echo ""

# 2. 替换主题颜色引用
echo "步骤2: 替换主题颜色引用"
find $PROJECT_ROOT/lib -type f -name "*.dart" -exec sed -i '' 's/AppTheme\.primaryColor/ThemeHelper.primaryColor/g' {} \;
find $PROJECT_ROOT/lib -type f -name "*.dart" -exec sed -i '' 's/AppTheme\.secondaryColor/ThemeHelper.secondaryColor/g' {} \;
find $PROJECT_ROOT/lib -type f -name "*.dart" -exec sed -i '' 's/AppTheme\.accentColor/ThemeHelper.accentColor/g' {} \;
find $PROJECT_ROOT/lib -type f -name "*.dart" -exec sed -i '' 's/AppTheme\.white/ThemeHelper.white/g' {} \;
find $PROJECT_ROOT/lib -type f -name "*.dart" -exec sed -i '' 's/AppTheme\.black/ThemeHelper.black/g' {} \;
find $PROJECT_ROOT/lib -type f -name "*.dart" -exec sed -i '' 's/AppTheme\.transparent/ThemeHelper.transparent/g' {} \;
echo "完成"
echo ""

# 3. 替换文本颜色引用
echo "步骤3: 替换文本颜色引用"
find $PROJECT_ROOT/lib -type f -name "*.dart" -exec sed -i '' 's/AppTheme\.textColor/ThemeHelper.textColor/g' {} \;
find $PROJECT_ROOT/lib -type f -name "*.dart" -exec sed -i '' 's/AppTheme\.textColorSecondary/ThemeHelper.textColorSecondary/g' {} \;
find $PROJECT_ROOT/lib -type f -name "*.dart" -exec sed -i '' 's/AppTheme\.textColorWhite/ThemeHelper.textColorWhite/g' {} \;
find $PROJECT_ROOT/lib -type f -name "*.dart" -exec sed -i '' 's/AppTheme\.textColorBlack/ThemeHelper.textColorBlack/g' {} \;
find $PROJECT_ROOT/lib -type f -name "*.dart" -exec sed -i '' 's/AppTheme\.textColorGrey/ThemeHelper.textColorGrey/g' {} \;
echo "完成"
echo ""

# 4. 替换背景颜色引用
echo "步骤4: 替换背景颜色引用"
find $PROJECT_ROOT/lib -type f -name "*.dart" -exec sed -i '' 's/AppTheme\.backgroundColor/ThemeHelper.backgroundColor/g' {} \;
find $PROJECT_ROOT/lib -type f -name "*.dart" -exec sed -i '' 's/AppTheme\.backgroundColorDark/ThemeHelper.backgroundColorDark/g' {} \;
find $PROJECT_ROOT/lib -type f -name "*.dart" -exec sed -i '' 's/AppTheme\.backgroundColorDarker/ThemeHelper.backgroundColorDarker/g' {} \;
find $PROJECT_ROOT/lib -type f -name "*.dart" -exec sed -i '' 's/AppTheme\.backgroundColorDarkest/ThemeHelper.backgroundColorDarkest/g' {} \;
find $PROJECT_ROOT/lib -type f -name "*.dart" -exec sed -i '' 's/AppTheme\.backgroundColorTransparent/ThemeHelper.backgroundColorTransparent/g' {} \;
find $PROJECT_ROOT/lib -type f -name "*.dart" -exec sed -i '' 's/AppTheme\.backgroundColorGrey/ThemeHelper.backgroundColorGrey/g' {} \;
find $PROJECT_ROOT/lib -type f -name "*.dart" -exec sed -i '' 's/AppTheme\.backgroundColorGreyDark/ThemeHelper.backgroundColorGreyDark/g' {} \;
find $PROJECT_ROOT/lib -type f -name "*.dart" -exec sed -i '' 's/AppTheme\.backgroundColorGreyLight/ThemeHelper.backgroundColorGreyLight/g' {} \;
find $PROJECT_ROOT/lib -type f -name "*.dart" -exec sed -i '' 's/AppTheme\.backgroundColorGreyLightest/ThemeHelper.backgroundColorGreyLightest/g' {} \;
find $PROJECT_ROOT/lib -type f -name "*.dart" -exec sed -i '' 's/AppTheme\.backgroundColorGreyLightestest/ThemeHelper.backgroundColorGreyLightestest/g' {} \;
find $PROJECT_ROOT/lib -type f -name "*.dart" -exec sed -i '' 's/AppTheme\.backgroundColorGreen/ThemeHelper.backgroundColorGreen/g' {} \;
find $PROJECT_ROOT/lib -type f -name "*.dart" -exec sed -i '' 's/AppTheme\.backgroundColorGreyDarkest/ThemeHelper.backgroundColorGreyDarkest/g' {} \;
echo "完成"
echo ""

# 5. 替换状态颜色引用
echo "步骤5: 替换状态和其他颜色引用"
find $PROJECT_ROOT/lib -type f -name "*.dart" -exec sed -i '' 's/AppTheme\.successColor/ThemeHelper.successColor/g' {} \;
find $PROJECT_ROOT/lib -type f -name "*.dart" -exec sed -i '' 's/AppTheme\.errorColor/ThemeHelper.errorColor/g' {} \;
find $PROJECT_ROOT/lib -type f -name "*.dart" -exec sed -i '' 's/AppTheme\.warningColor/ThemeHelper.warningColor/g' {} \;
find $PROJECT_ROOT/lib -type f -name "*.dart" -exec sed -i '' 's/AppTheme\.successColorLight/ThemeHelper.successColorLight/g' {} \;
find $PROJECT_ROOT/lib -type f -name "*.dart" -exec sed -i '' 's/AppTheme\.successColorDark/ThemeHelper.successColorDark/g' {} \;
find $PROJECT_ROOT/lib -type f -name "*.dart" -exec sed -i '' 's/AppTheme\.errorColorLight/ThemeHelper.errorColorLight/g' {} \;
find $PROJECT_ROOT/lib -type f -name "*.dart" -exec sed -i '' 's/AppTheme\.errorColorDark/ThemeHelper.errorColorDark/g' {} \;
find $PROJECT_ROOT/lib -type f -name "*.dart" -exec sed -i '' 's/AppTheme\.overlayColor/ThemeHelper.overlayColor/g' {} \;
find $PROJECT_ROOT/lib -type f -name "*.dart" -exec sed -i '' 's/AppTheme\.overlayColorLight/ThemeHelper.overlayColorLight/g' {} \;
find $PROJECT_ROOT/lib -type f -name "*.dart" -exec sed -i '' 's/AppTheme\.shadowColor/ThemeHelper.shadowColor/g' {} \;
find $PROJECT_ROOT/lib -type f -name "*.dart" -exec sed -i '' 's/AppTheme\.dividerColor/ThemeHelper.dividerColor/g' {} \;
find $PROJECT_ROOT/lib -type f -name "*.dart" -exec sed -i '' 's/AppTheme\.iconColorWhite/ThemeHelper.iconColorWhite/g' {} \;
find $PROJECT_ROOT/lib -type f -name "*.dart" -exec sed -i '' 's/AppTheme\.iconColorGrey/ThemeHelper.iconColorGrey/g' {} \;
find $PROJECT_ROOT/lib -type f -name "*.dart" -exec sed -i '' 's/AppTheme\.borderColor/ThemeHelper.borderColor/g' {} \;
find $PROJECT_ROOT/lib -type f -name "*.dart" -exec sed -i '' 's/AppTheme\.borderColorLight/ThemeHelper.borderColorLight/g' {} \;
echo "完成"
echo ""

# 6. 替换字体大小引用
echo "步骤6: 替换字体大小引用"
find $PROJECT_ROOT/lib -type f -name "*.dart" -exec sed -i '' 's/AppTheme\.fontSizeXSmall/ThemeHelper.fontSizeXSmall/g' {} \;
find $PROJECT_ROOT/lib -type f -name "*.dart" -exec sed -i '' 's/AppTheme\.fontSizeSmall/ThemeHelper.fontSizeSmall/g' {} \;
find $PROJECT_ROOT/lib -type f -name "*.dart" -exec sed -i '' 's/AppTheme\.fontSizeNormal/ThemeHelper.fontSizeNormal/g' {} \;
find $PROJECT_ROOT/lib -type f -name "*.dart" -exec sed -i '' 's/AppTheme\.fontSizeMedium/ThemeHelper.fontSizeMedium/g' {} \;
find $PROJECT_ROOT/lib -type f -name "*.dart" -exec sed -i '' 's/AppTheme\.fontSizeLarge/ThemeHelper.fontSizeLarge/g' {} \;
find $PROJECT_ROOT/lib -type f -name "*.dart" -exec sed -i '' 's/AppTheme\.fontSizeXLarge/ThemeHelper.fontSizeXLarge/g' {} \;
find $PROJECT_ROOT/lib -type f -name "*.dart" -exec sed -i '' 's/AppTheme\.fontSizeXXLarge/ThemeHelper.fontSizeXXLarge/g' {} \;
echo "完成"
echo ""

# 7. 替换文本样式引用
echo "步骤7: 替换文本样式引用"
find $PROJECT_ROOT/lib -type f -name "*.dart" -exec sed -i '' 's/AppTheme\.textStyleNormal/ThemeHelper.textStyleNormal/g' {} \;
find $PROJECT_ROOT/lib -type f -name "*.dart" -exec sed -i '' 's/AppTheme\.textStyleTitle/ThemeHelper.textStyleTitle/g' {} \;
find $PROJECT_ROOT/lib -type f -name "*.dart" -exec sed -i '' 's/AppTheme\.textStyleSubtitle/ThemeHelper.textStyleSubtitle/g' {} \;
find $PROJECT_ROOT/lib -type f -name "*.dart" -exec sed -i '' 's/AppTheme\.textStyleHeading/ThemeHelper.textStyleHeading/g' {} \;
find $PROJECT_ROOT/lib -type f -name "*.dart" -exec sed -i '' 's/AppTheme\.textStyleSmall/ThemeHelper.textStyleSmall/g' {} \;
echo "完成"
echo ""

# 8. 查找并列出可能还有问题的文件
echo "步骤8: 检查是否有未处理的AppTheme引用"
REMAINING_FILES=$(find $PROJECT_ROOT/lib -type f -name "*.dart" -exec grep -l "AppTheme" {} \;)
if [ -n "$REMAINING_FILES" ]; then
    echo "以下文件可能仍然包含AppTheme引用，请手动检查："
    echo "$REMAINING_FILES"
else
    echo "未发现未处理的AppTheme引用"
fi
echo ""

echo "======================================================"
echo "替换基本完成！"
echo "请检查导入路径问题，批量替换后可能有些导入路径不正确"
echo "同时验证应用是否能正常运行和切换主题"
echo "======================================================" 