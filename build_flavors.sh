#!/bin/bash
# 为不同环境构建Flutter应用的脚本

# 检查参数
if [ "$1" != "dev" ] && [ "$1" != "staging" ] && [ "$1" != "prod" ]; then
  echo "用法: ./build_flavors.sh [dev|staging|prod] [apk|appbundle|ios]"
  echo "  dev: 开发环境，使用原始versionCode和带有-dev后缀的versionName"
  echo "  staging: 测试环境，使用原始versionCode和带有-staging后缀的versionName"
  echo "  prod: 生产环境，使用较大的versionCode(+1000)和正式的versionName"
  echo "  apk: 构建Android APK"
  echo "  appbundle: 构建Android App Bundle"
  echo "  ios: 构建iOS应用"
  exit 1
fi

# 获取参数
FLAVOR=$1
BUILD_TYPE=$2

# 检查构建类型
if [ "$BUILD_TYPE" != "apk" ] && [ "$BUILD_TYPE" != "appbundle" ] && [ "$BUILD_TYPE" != "ios" ]; then
  echo "错误: 构建类型必须是 apk, appbundle 或 ios"
  exit 1
fi

# 设置环境变量
export FLAVOR=$FLAVOR

# 读取版本配置
source "android/version.properties"

# 读取包名配置
source "package_names.properties"

# 根据FLAVOR环境变量选择版本号
if [ "$FLAVOR" == "prod" ]; then
  VERSION_NAME=$prodVersionName
  VERSION_CODE=$prodVersionCode
else
  VERSION_NAME=$devVersionName
  VERSION_CODE=$devVersionCode
fi

# 如果是开发或测试环境，添加后缀
if [ "$FLAVOR" == "dev" ]; then
  DISPLAY_VERSION="${VERSION_NAME}-dev"
elif [ "$FLAVOR" == "staging" ]; then
  DISPLAY_VERSION="${VERSION_NAME}-staging"
else
  DISPLAY_VERSION="$VERSION_NAME"
fi

# 显示当前构建信息
echo "开始构建 $FLAVOR 环境的应用 ($BUILD_TYPE)..."
echo "版本号: $DISPLAY_VERSION ($VERSION_CODE)"

# 获取当前环境的包名
if [ "$FLAVOR" == "dev" ]; then
  PACKAGE_NAME=$devPackageName
echo "开发环境包名: $PACKAGE_NAME"
elif [ "$FLAVOR" == "staging" ]; then
  PACKAGE_NAME=$stagingPackageName
echo "测试环境包名: $PACKAGE_NAME"
else
  PACKAGE_NAME=$prodPackageName
echo "生产环境包名: $PACKAGE_NAME"
fi

# 根据构建类型执行不同的命令
if [ "$BUILD_TYPE" = "apk" ]; then
  flutter build apk --flavor $FLAVOR -t lib/main.dart --dart-define=FLAVOR=$FLAVOR

  # 构建完成后直接重命名APK文件，添加版本号
  SOURCE_APK="build/app/outputs/flutter-apk/app-${FLAVOR}-release.apk"

  # 使用更简洁的文件名格式
  if [ "$FLAVOR" == "prod" ]; then
    # 生产环境直接使用版本号
    TARGET_APK="build/app/outputs/flutter-apk/ToppingHome_v${VERSION_NAME}.apk"
  else
    # 非生产环境显示环境名称，但使用基础版本号
    TARGET_APK="build/app/outputs/flutter-apk/ToppingHome_${FLAVOR}_v${devVersionName}.apk"
  fi

  # 检查源文件是否存在
  if [ -f "$SOURCE_APK" ]; then
    cp "$SOURCE_APK" "$TARGET_APK"
    echo "已将APK文件重命名为: $TARGET_APK"
  else
    echo "错误: 找不到源APK文件 $SOURCE_APK"
  fi
elif [ "$BUILD_TYPE" = "appbundle" ]; then
  flutter build appbundle --flavor $FLAVOR -t lib/main.dart --dart-define=FLAVOR=$FLAVOR
elif [ "$BUILD_TYPE" = "ios" ]; then
  # 在构建 iOS 应用前设置版本号和包名
  echo "设置 iOS 版本号: $DISPLAY_VERSION ($VERSION_CODE)"
  echo "设置 iOS 包名: $PACKAGE_NAME"

  # 更新包名配置
  chmod +x ios/update_package_names.sh
  ./ios/update_package_names.sh

  # 更新 Info.plist 文件中的版本号
  /usr/libexec/PlistBuddy -c "Set :CFBundleShortVersionString $DISPLAY_VERSION" "ios/Runner/Info.plist"
  /usr/libexec/PlistBuddy -c "Set :CFBundleVersion $VERSION_CODE" "ios/Runner/Info.plist"

  # 更新应用名称
  if [ "$FLAVOR" == "dev" ]; then
    /usr/libexec/PlistBuddy -c "Set :CFBundleDisplayName 'Topping Dev'" "ios/Runner/Info.plist"
  elif [ "$FLAVOR" == "staging" ]; then
    /usr/libexec/PlistBuddy -c "Set :CFBundleDisplayName 'Topping Test'" "ios/Runner/Info.plist"
  else
    /usr/libexec/PlistBuddy -c "Set :CFBundleDisplayName 'Topping'" "ios/Runner/Info.plist"
  fi

  # 使用正确的scheme构建 iOS 应用
  flutter build ios --flavor $FLAVOR --dart-define=FLAVOR=$FLAVOR -t lib/main.dart
fi

# 显示构建完成信息
echo "构建完成！"
echo "已生成 $FLAVOR 环境的 $BUILD_TYPE 应用。"

# 对于iOS构建，提供额外说明
if [ "$BUILD_TYPE" = "ios" ]; then
  echo "注意：对于iOS应用，您需要在Xcode中打开项目并设置正确的版本号。"
  echo "位置：Runner > Info.plist > CFBundleVersion 和 CFBundleShortVersionString"
fi