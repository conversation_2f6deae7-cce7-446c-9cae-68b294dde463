#!/bin/bash

# 遍历 lib/enums/dx5/ 目录下的所有 dx5_*.dart 文件
for file in lib/enums/dx5/dx5_*.dart; do
  # 获取文件名（不含路径）
  filename=$(basename "$file")
  # 获取不带 dx5_ 前缀的文件名
  base_name=${filename#dx5_}
  
  # 使用 sed 替换 part 声明
  # 将 part 'xxx.g.dart'; 替换为 part 'dx5_xxx.g.dart';
  sed -i '' "s/part '${base_name%.dart}.g.dart';/part '${filename%.dart}.g.dart';/" "$file"
  
  echo "已修复 $file 中的 part 声明"
done

echo "所有文件修复完成！"
