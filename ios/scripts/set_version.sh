#!/bin/bash

# 此脚本用于在构建iOS应用时设置正确的版本号
# 根据FLAVOR环境变量选择开发/测试版本号或生产版本号

# 获取脚本所在目录
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"
# 项目根目录
PROJECT_DIR="$SCRIPT_DIR/../.."
# 版本配置文件
VERSION_PROPS="$PROJECT_DIR/android/version.properties"

# 检查版本配置文件是否存在
if [ ! -f "$VERSION_PROPS" ]; then
  echo "错误: 找不到版本配置文件 $VERSION_PROPS"
  exit 1
fi

# 读取版本配置
source "$VERSION_PROPS"

# 根据FLAVOR环境变量选择版本号
if [ "$FLAVOR" == "prod" ]; then
  VERSION_NAME=$prodVersionName
  VERSION_CODE=$prodVersionCode
else
  VERSION_NAME=$devVersionName
  VERSION_CODE=$devVersionCode
fi

# 如果是开发或测试环境，添加后缀
if [ "$FLAVOR" == "dev" ]; then
  VERSION_NAME="${VERSION_NAME}-dev"
elif [ "$FLAVOR" == "staging" ]; then
  VERSION_NAME="${VERSION_NAME}-staging"
fi

echo "设置iOS版本号: $VERSION_NAME ($VERSION_CODE)"

# 更新Info.plist文件中的版本号
/usr/libexec/PlistBuddy -c "Set :CFBundleShortVersionString $VERSION_NAME" "$PROJECT_DIR/ios/Runner/Info.plist"
/usr/libexec/PlistBuddy -c "Set :CFBundleVersion $VERSION_CODE" "$PROJECT_DIR/ios/Runner/Info.plist"

echo "iOS版本号设置完成"
