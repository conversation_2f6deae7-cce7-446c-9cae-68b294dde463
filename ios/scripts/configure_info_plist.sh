#!/bin/bash

# 此脚本用于为不同环境配置Info.plist文件

# 获取脚本所在目录
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"
# iOS项目目录
IOS_DIR="$SCRIPT_DIR/.."
# Info.plist文件路径
INFO_PLIST="$IOS_DIR/Runner/Info.plist"

# 检查Info.plist文件是否存在
if [ ! -f "$INFO_PLIST" ]; then
  echo "错误: 找不到Info.plist文件: $INFO_PLIST"
  exit 1
fi

echo "配置Info.plist文件..."

# 创建不同环境的Info.plist文件
for FLAVOR in dev staging prod; do
  # 复制原始Info.plist文件
  FLAVOR_INFO_PLIST="$IOS_DIR/Runner/Info-$FLAVOR.plist"
  cp "$INFO_PLIST" "$FLAVOR_INFO_PLIST"
  
  # 设置应用名称
  if [ "$FLAVOR" == "dev" ]; then
    /usr/libexec/PlistBuddy -c "Set :CFBundleDisplayName 'Topping Dev'" "$FLAVOR_INFO_PLIST"
  elif [ "$FLAVOR" == "staging" ]; then
    /usr/libexec/PlistBuddy -c "Set :CFBundleDisplayName 'Topping Test'" "$FLAVOR_INFO_PLIST"
  else
    /usr/libexec/PlistBuddy -c "Set :CFBundleDisplayName 'Topping'" "$FLAVOR_INFO_PLIST"
  fi
  
  echo "创建了 $FLAVOR 环境的Info.plist文件: $FLAVOR_INFO_PLIST"
done

echo "Info.plist配置完成！"
