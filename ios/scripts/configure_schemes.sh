#!/bin/bash

# 此脚本用于为iOS项目配置不同环境的scheme

# 获取脚本所在目录
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"
# iOS项目目录
IOS_DIR="$SCRIPT_DIR/.."
# Xcode项目文件
XCODEPROJ="$IOS_DIR/Runner.xcodeproj"
# Xcode工作区文件
XCWORKSPACE="$IOS_DIR/Runner.xcworkspace"

echo "配置iOS项目的scheme..."

# 检查是否安装了xcodeproj gem
if ! command -v xcodeproj &> /dev/null; then
    echo "需要安装xcodeproj gem，正在安装..."
    gem install xcodeproj
fi

# 创建Ruby脚本来配置scheme
cat > "$SCRIPT_DIR/configure_schemes.rb" << 'EOL'
require 'xcodeproj'

# 打开Xcode项目
project_path = ARGV[0]
project = Xcodeproj::Project.open(project_path)

# 获取主目标
main_target = project.targets.find { |target| target.name == 'Runner' }
if main_target.nil?
  puts "错误: 找不到Runner目标"
  exit 1
end

# 创建不同环境的scheme
schemes = ['dev', 'staging', 'prod']
schemes.each do |scheme|
  scheme_path = File.join(File.dirname(project_path), 'Runner.xcodeproj', 'xcshareddata', 'xcschemes', "#{scheme}.xcscheme")
  
  # 确保xcshareddata/xcschemes目录存在
  FileUtils.mkdir_p(File.dirname(scheme_path))
  
  # 创建scheme文件
  File.open(scheme_path, 'w') do |f|
    f.write <<-EOT
<?xml version="1.0" encoding="UTF-8"?>
<Scheme
   LastUpgradeVersion = "1430"
   version = "1.7">
   <BuildAction
      parallelizeBuildables = "YES"
      buildImplicitDependencies = "YES">
      <BuildActionEntries>
         <BuildActionEntry
            buildForTesting = "YES"
            buildForRunning = "YES"
            buildForProfiling = "YES"
            buildForArchiving = "YES"
            buildForAnalyzing = "YES">
            <BuildableReference
               BuildableIdentifier = "primary"
               BlueprintIdentifier = "#{main_target.uuid}"
               BuildableName = "Runner.app"
               BlueprintName = "Runner"
               ReferencedContainer = "container:Runner.xcodeproj">
            </BuildableReference>
         </BuildActionEntry>
      </BuildActionEntries>
   </BuildAction>
   <TestAction
      buildConfiguration = "Debug"
      selectedDebuggerIdentifier = "Xcode.DebuggerFoundation.Debugger.LLDB"
      selectedLauncherIdentifier = "Xcode.DebuggerFoundation.Launcher.LLDB"
      shouldUseLaunchSchemeArgsEnv = "YES"
      shouldAutocreateTestPlan = "YES">
   </TestAction>
   <LaunchAction
      buildConfiguration = "Debug"
      selectedDebuggerIdentifier = "Xcode.DebuggerFoundation.Debugger.LLDB"
      selectedLauncherIdentifier = "Xcode.DebuggerFoundation.Launcher.LLDB"
      launchStyle = "0"
      useCustomWorkingDirectory = "NO"
      ignoresPersistentStateOnLaunch = "NO"
      debugDocumentVersioning = "YES"
      debugServiceExtension = "internal"
      allowLocationSimulation = "YES">
      <BuildableProductRunnable
         runnableDebuggingMode = "0">
         <BuildableReference
            BuildableIdentifier = "primary"
            BlueprintIdentifier = "#{main_target.uuid}"
            BuildableName = "Runner.app"
            BlueprintName = "Runner"
            ReferencedContainer = "container:Runner.xcodeproj">
         </BuildableReference>
      </BuildableProductRunnable>
      <CommandLineArguments>
         <CommandLineArgument
            argument = "--dart-define=FLAVOR=#{scheme}"
            isEnabled = "YES">
         </CommandLineArgument>
      </CommandLineArguments>
   </LaunchAction>
   <ProfileAction
      buildConfiguration = "Release"
      shouldUseLaunchSchemeArgsEnv = "YES"
      savedToolIdentifier = ""
      useCustomWorkingDirectory = "NO"
      debugDocumentVersioning = "YES">
      <BuildableProductRunnable
         runnableDebuggingMode = "0">
         <BuildableReference
            BuildableIdentifier = "primary"
            BlueprintIdentifier = "#{main_target.uuid}"
            BuildableName = "Runner.app"
            BlueprintName = "Runner"
            ReferencedContainer = "container:Runner.xcodeproj">
         </BuildableReference>
      </BuildableProductRunnable>
   </ProfileAction>
   <AnalyzeAction
      buildConfiguration = "Debug">
   </AnalyzeAction>
   <ArchiveAction
      buildConfiguration = "Release"
      revealArchiveInOrganizer = "YES">
   </ArchiveAction>
</Scheme>
    EOT
  end
  
  puts "创建了scheme: #{scheme}"
end

# 保存项目
project.save

puts "iOS scheme配置完成！"
EOL

# 运行Ruby脚本
ruby "$SCRIPT_DIR/configure_schemes.rb" "$XCODEPROJ"

echo "iOS项目scheme配置完成！"
echo "现在您可以使用以下命令运行不同环境的iOS应用："
echo "flutter run --flavor dev --dart-define=FLAVOR=dev -t lib/main.dart"
echo "flutter run --flavor staging --dart-define=FLAVOR=staging -t lib/main.dart"
echo "flutter run --flavor prod --dart-define=FLAVOR=prod -t lib/main.dart"
