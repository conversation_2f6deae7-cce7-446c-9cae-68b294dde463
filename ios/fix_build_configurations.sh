#!/bin/bash

# 此脚本用于修复Xcode项目的构建配置

echo "开始修复Xcode项目构建配置..."

# 项目目录
PROJECT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
XCODEPROJ="$PROJECT_DIR/Runner.xcodeproj"
PROJECT_PBXPROJ="$XCODEPROJ/project.pbxproj"

# 备份原始文件
cp "$PROJECT_PBXPROJ" "${PROJECT_PBXPROJ}.bak"
echo "已备份原始项目文件到 ${PROJECT_PBXPROJ}.bak"

# 创建Flutter配置文件
mkdir -p "$PROJECT_DIR/Flutter/Configs"

# 创建Debug配置
for FLAVOR in dev staging prod; do
  cat > "$PROJECT_DIR/Flutter/Configs/Debug-$FLAVOR.xcconfig" << EOL
#include "../Debug.xcconfig"
FLUTTER_FLAVOR=$FLAVOR
FLUTTER_TARGET=lib/main.dart
PRODUCT_BUNDLE_IDENTIFIER=com.topping.home.$FLAVOR
DISPLAY_NAME=Topping $(echo $FLAVOR | sed -e 's/\b\(.\)/\u\1/g')
EOL
  echo "创建了 Debug-$FLAVOR.xcconfig"
done

# 创建Release配置
for FLAVOR in dev staging prod; do
  cat > "$PROJECT_DIR/Flutter/Configs/Release-$FLAVOR.xcconfig" << EOL
#include "../Release.xcconfig"
FLUTTER_FLAVOR=$FLAVOR
FLUTTER_TARGET=lib/main.dart
PRODUCT_BUNDLE_IDENTIFIER=com.topping.home.$FLAVOR
DISPLAY_NAME=Topping $(echo $FLAVOR | sed -e 's/\b\(.\)/\u\1/g')
EOL
  echo "创建了 Release-$FLAVOR.xcconfig"
done

# 创建Profile配置
for FLAVOR in dev staging prod; do
  cat > "$PROJECT_DIR/Flutter/Configs/Profile-$FLAVOR.xcconfig" << EOL
#include "../Profile.xcconfig"
FLUTTER_FLAVOR=$FLAVOR
FLUTTER_TARGET=lib/main.dart
PRODUCT_BUNDLE_IDENTIFIER=com.topping.home.$FLAVOR
DISPLAY_NAME=Topping $(echo $FLAVOR | sed -e 's/\b\(.\)/\u\1/g')
EOL
  echo "创建了 Profile-$FLAVOR.xcconfig"
done

# 修改Info.plist
PLIST="$PROJECT_DIR/Runner/Info.plist"
/usr/libexec/PlistBuddy -c "Set :CFBundleIdentifier \$(PRODUCT_BUNDLE_IDENTIFIER)" "$PLIST"
/usr/libexec/PlistBuddy -c "Set :CFBundleDisplayName \$(DISPLAY_NAME)" "$PLIST"
echo "修改了Info.plist文件"

echo "配置文件创建完成"
echo "请在Xcode中手动添加这些配置："
echo "1. 打开Xcode项目：open ios/Runner.xcworkspace"
echo "2. 点击左侧项目导航器中的'Runner'"
echo "3. 确保选择的是Runner项目(PROJECT)，而不是Runner目标(TARGET)"
echo "4. 点击'Info'标签页"
echo "5. 在'Configurations'部分，点击'+'按钮"
echo "6. 添加以下配置："
echo "   - Debug-dev, Debug-staging, Debug-prod"
echo "   - Release-dev, Release-staging, Release-prod"
echo "   - Profile-dev, Profile-staging, Profile-prod"
echo "7. 对于每个配置，选择相应的xcconfig文件"
echo "8. 保存并关闭Xcode"
echo ""
echo "或者，您可以使用以下命令直接运行不同环境的应用："
echo "./ios/Scripts/run_dev.sh"
echo "./ios/Scripts/run_staging.sh"
echo "./ios/Scripts/run_prod.sh"
