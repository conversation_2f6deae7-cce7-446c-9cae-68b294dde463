#!/bin/bash

# 此脚本用于更新iOS配置文件中的包名

# 项目目录
PROJECT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PACKAGE_PROPS="../package_names.properties"

echo "开始更新iOS包名配置..."

# 检查包名配置文件是否存在
if [ ! -f "$PACKAGE_PROPS" ]; then
    echo "错误: 找不到包名配置文件 $PACKAGE_PROPS"
    exit 1
fi

# 读取包名配置
source "$PACKAGE_PROPS"

echo "读取到的包名配置:"
echo "基础包名: $basePackageName"
echo "开发环境包名: $devPackageName"
echo "测试环境包名: $stagingPackageName"
echo "生产环境包名: $prodPackageName"

# 更新开发环境配置
DEV_CONFIG="$PROJECT_DIR/Flutter/Flavors/Dev.xcconfig"
if [ -f "$DEV_CONFIG" ]; then
    echo "更新开发环境配置: $DEV_CONFIG"
    sed -i '' "s/PRODUCT_BUNDLE_IDENTIFIER = .*/PRODUCT_BUNDLE_IDENTIFIER = $devPackageName/" "$DEV_CONFIG"
else
    echo "警告: 找不到开发环境配置文件 $DEV_CONFIG"
fi

# 更新测试环境配置
STAGING_CONFIG="$PROJECT_DIR/Flutter/Flavors/Staging.xcconfig"
if [ -f "$STAGING_CONFIG" ]; then
    echo "更新测试环境配置: $STAGING_CONFIG"
    sed -i '' "s/PRODUCT_BUNDLE_IDENTIFIER = .*/PRODUCT_BUNDLE_IDENTIFIER = $stagingPackageName/" "$STAGING_CONFIG"
else
    echo "警告: 找不到测试环境配置文件 $STAGING_CONFIG"
fi

# 更新生产环境配置
PROD_CONFIG="$PROJECT_DIR/Flutter/Flavors/Prod.xcconfig"
if [ -f "$PROD_CONFIG" ]; then
    echo "更新生产环境配置: $PROD_CONFIG"
    sed -i '' "s/PRODUCT_BUNDLE_IDENTIFIER = .*/PRODUCT_BUNDLE_IDENTIFIER = $prodPackageName/" "$PROD_CONFIG"
else
    echo "警告: 找不到生产环境配置文件 $PROD_CONFIG"
fi

echo "iOS包名配置更新完成！"
