#!/bin/bash

# 此脚本用于手动设置iOS的flavor

# 项目目录
PROJECT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
XCODEPROJ="$PROJECT_DIR/Runner.xcodeproj"

echo "开始配置iOS flavor..."

# 创建xcconfig文件
mkdir -p "$PROJECT_DIR/Flutter/Flavors"

# 创建开发环境配置
cat > "$PROJECT_DIR/Flutter/Flavors/Dev.xcconfig" << EOL
#include "../Flutter.xcconfig"
PRODUCT_BUNDLE_IDENTIFIER = com.topping.home.dev
DISPLAY_NAME = Topping Dev
EOL

# 创建测试环境配置
cat > "$PROJECT_DIR/Flutter/Flavors/Staging.xcconfig" << EOL
#include "../Flutter.xcconfig"
PRODUCT_BUNDLE_IDENTIFIER = com.topping.home.staging
DISPLAY_NAME = Topping Test
EOL

# 创建生产环境配置
cat > "$PROJECT_DIR/Flutter/Flavors/Prod.xcconfig" << EOL
#include "../Flutter.xcconfig"
PRODUCT_BUNDLE_IDENTIFIER = com.topping.home
DISPLAY_NAME = Topping
EOL

echo "创建了xcconfig文件"

# 修改Info.plist
PLIST="$PROJECT_DIR/Runner/Info.plist"

# 备份原始Info.plist
cp "$PLIST" "${PLIST}.bak"

# 修改Info.plist，使用环境变量
/usr/libexec/PlistBuddy -c "Set :CFBundleIdentifier \$(PRODUCT_BUNDLE_IDENTIFIER)" "$PLIST"
/usr/libexec/PlistBuddy -c "Set :CFBundleDisplayName \$(DISPLAY_NAME)" "$PLIST"

echo "修改了Info.plist文件"

# 创建Run脚本
mkdir -p "$PROJECT_DIR/Scripts"

# 创建开发环境运行脚本
cat > "$PROJECT_DIR/Scripts/run_dev.sh" << EOL
#!/bin/bash
cd "\$(dirname "\${BASH_SOURCE[0]}")/../.."
flutter run --debug --flavor dev --dart-define=FLAVOR=dev -t lib/main.dart
EOL

# 创建测试环境运行脚本
cat > "$PROJECT_DIR/Scripts/run_staging.sh" << EOL
#!/bin/bash
cd "\$(dirname "\${BASH_SOURCE[0]}")/../.."
flutter run --debug --flavor staging --dart-define=FLAVOR=staging -t lib/main.dart
EOL

# 创建生产环境运行脚本
cat > "$PROJECT_DIR/Scripts/run_prod.sh" << EOL
#!/bin/bash
cd "\$(dirname "\${BASH_SOURCE[0]}")/../.."
flutter run --debug --flavor prod --dart-define=FLAVOR=prod -t lib/main.dart
EOL

# 设置脚本可执行权限
chmod +x "$PROJECT_DIR/Scripts/run_dev.sh"
chmod +x "$PROJECT_DIR/Scripts/run_staging.sh"
chmod +x "$PROJECT_DIR/Scripts/run_prod.sh"

echo "创建了运行脚本"

echo "iOS flavor配置完成！"
echo "请按照以下步骤在Xcode中手动配置scheme："
echo "1. 打开Xcode项目：open ios/Runner.xcworkspace"
echo "2. 点击顶部的'Runner'项目，选择'Edit Scheme...'"
echo "3. 点击左上角的'+'按钮，选择'New Scheme'"
echo "4. 创建三个scheme：dev、staging和prod"
echo "5. 对于每个scheme，在'Run'选项的'Arguments'标签页中，添加以下参数："
echo "   --flavor dev --dart-define=FLAVOR=dev (对于dev scheme)"
echo "   --flavor staging --dart-define=FLAVOR=staging (对于staging scheme)"
echo "   --flavor prod --dart-define=FLAVOR=prod (对于prod scheme)"
echo ""
echo "或者，您可以使用以下脚本直接运行不同环境的应用："
echo "./ios/Scripts/run_dev.sh"
echo "./ios/Scripts/run_staging.sh"
echo "./ios/Scripts/run_prod.sh"
