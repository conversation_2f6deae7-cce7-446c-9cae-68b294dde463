<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
	<dict>
		<key>CADisableMinimumFrameDurationOnPhone</key>
		<true/>
		<key>CFBundleDevelopmentRegion</key>
		<string>$(DEVELOPMENT_LANGUAGE)</string>
		<key>CFBundleDisplayName</key>
		<string>$(DISPLAY_NAME)</string>
		<key>CFBundleExecutable</key>
		<string>$(EXECUTABLE_NAME)</string>
		<key>CFBundleIdentifier</key>
		<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
		<key>CFBundleInfoDictionaryVersion</key>
		<string>6.0</string>
		<key>CFBundleLocalizations</key>
		<array>
			<string>en</string>
			<string>zh</string>
			<string>zh_Hant</string>
			<string>ko</string>
			<string>ja</string>
		</array>
		<key>CFBundleName</key>
		<string>topping_home</string>
		<key>CFBundlePackageType</key>
		<string>APPL</string>
		<key>CFBundleShortVersionString</key>
		<string>$(FLUTTER_BUILD_NAME)</string>
		<key>CFBundleSignature</key>
		<string>????</string>
		<key>CFBundleVersion</key>
		<string>$(FLUTTER_BUILD_NUMBER)</string>
		<key>LSApplicationQueriesSchemes</key>
		<array>
			<string>sms</string>
			<string>tel</string>
		</array>
		<key>LSRequiresIPhoneOS</key>
		<true/>
		<key>NSBluetoothAlwaysUsageDescription</key>
		<string>需要蓝牙权限以连接设备</string>
		<key>NSBluetoothPeripheralUsageDescription</key>
		<string>需要蓝牙权限以连接设备</string>
		<key>NSCameraUsageDescription</key>
		<string>需要使用相机进行设备扫码</string>
		<key>NSLocationAlwaysAndWhenInUseUsageDescription</key>
		<string>需要位置权限以扫描附近的蓝牙设备</string>
		<key>NSLocationWhenInUseUsageDescription</key>
		<string>需要位置权限以扫描附近的蓝牙设备</string>
		<key>NSPhotoLibraryUsageDescription</key>
		<string>需要访问相册以选择自定义背景图片</string>
		<key>UIApplicationSupportsIndirectInputEvents</key>
		<true/>
		<key>UILaunchStoryboardName</key>
		<string>LaunchScreen</string>
		<key>UIMainStoryboardFile</key>
		<string>Main</string>
		<key>UIStatusBarHidden</key>
		<false/>
		<key>UISupportedInterfaceOrientations</key>
		<array>
			<string>UIInterfaceOrientationPortrait</string>
			<string>UIInterfaceOrientationLandscapeLeft</string>
			<string>UIInterfaceOrientationLandscapeRight</string>
		</array>
		<key>UISupportedInterfaceOrientations~ipad</key>
		<array>
			<string>UIInterfaceOrientationPortrait</string>
			<string>UIInterfaceOrientationPortraitUpsideDown</string>
			<string>UIInterfaceOrientationLandscapeLeft</string>
			<string>UIInterfaceOrientationLandscapeRight</string>
		</array>
		<key>io.flutter.embedded_views_preview</key>
		<true/>
	</dict>
</plist>
