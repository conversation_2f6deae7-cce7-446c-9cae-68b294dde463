import UIKit
import Flutter

@main
@objc class AppDelegate: FlutterAppDelegate {
  override func application(
    _ application: UIApplication,
    didFinishLaunchingWithOptions launchOptions: [UIApplication.LaunchOptionsKey: Any]?
  ) -> Bool {
    // 设置环境渠道
    let controller = window?.rootViewController as! FlutterViewController
    let flavorChannel = FlutterMethodChannel(name: "com.topping.home.flavor_channel", binaryMessenger: controller.binaryMessenger)
    
    flavorChannel.setMethodCallHandler({
      (call: FlutterMethodCall, result: @escaping FlutterResult) -> Void in
      if call.method == "getFlavor" {
        // 从Info.plist读取FLAVOR，如果不存在则默认为prod
        if let flavor = Bundle.main.infoDictionary?["FLAVOR"] as? String {
          result(flavor)
        } else {
          result("prod")
        }
      } else {
        result(FlutterMethodNotImplemented)
      }
    })
    
    GeneratedPluginRegistrant.register(with: self)
    return super.application(application, didFinishLaunchingWithOptions: launchOptions)
  }
}
