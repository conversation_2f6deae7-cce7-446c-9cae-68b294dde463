# Topping Home

Topping
Home是一款专为音频发烧友和专业用户设计的Flutter移动应用，提供全面的音频设备管理和高级参数化均衡器(
PEQ)功能。通过直观的界面和专业的音频处理能力，帮助用户获得最佳的音频体验。

## 项目概述

本应用使用Flutter框架开发，采用GetX进行状态管理，为用户提供功能强大且用户友好的界面来控制和调整Topping音频设备。无论是设备连接、参数调整，还是定制化均衡设置，Topping
Home都能满足各种专业需求。

## 主要功能

### 设备管理

- 智能设备扫描与自动发现功能
- 快速连接与状态监控
- 多设备并行管理
- 设备详细信息显示
- 设备固件更新

### 参数化均衡器(PEQ)

- 多通道支持（左声道、右声道或双通道均衡）
- 丰富的滤波器类型（峰值滤波器、低通滤波器、高通滤波器、低架滤波器、高架滤波器）
- 实时频率响应可视化
- 频率、增益和Q值精确调节
- 多模式显示（组合滤波器、单独滤波器、目标响应、源频率响应、过滤后响应）
- CSV导入/导出功能
- 原始/补偿模式切换

### 用户系统

- 账户注册与登录
- 个人信息管理
- 账户安全设置
- 邮箱绑定功能
- 用户协议与隐私政策

### 其他功能

- 应用界面自定义背景
- 主题切换（包括默认、蓝色、绿色、橙色、紫色和黄色主题）
- 明暗模式支持
- 用户反馈系统
- 应用自动更新

## 技术架构

- **前端框架**：Flutter
- **状态管理**：GetX
- **设计模式**：MVVM (Model-View-ViewModel)
- **本地存储**：Hive
- **网络请求**：Dio + Retrofit
- **响应式编程**：Rx模式
- **权限管理**：Permission Handler
- **文件处理**：Path Provider + File Picker

## 项目结构

项目采用模块化架构，主要目录结构如下：

lib/
├── business/ # 业务逻辑模块
│ ├── peq/ # 参数化均衡器模块
│ ├── device_add/ # 设备添加模块
│ ├── device_detail/ # 设备详情模块
│ └── ...
├── common/ # 通用工具和组件
├── enums/ # 枚举定义
├── http/ # 网络请求相关
├── models/ # 数据模型
├── repositories/ # 数据仓库
├── router/ # 路由管理
├── service/ # 服务层
└── theme/ # 主题管理

## 特色功能亮点

### 高精度均衡器

PEQ模块提供专业级音频均衡功能，支持精确的频率响应调整，满足音频发烧友的严苛要求。通过直观的可视化界面，用户可以实时预览均衡效果并进行精细调整。

### 自定义界面

用户可以根据个人喜好自定义应用背景和主题，提供个性化的用户体验。多种预设主题和背景模糊度调整使界面更加美观舒适。

### 设备智能管理

智能设备发现和管理系统让用户可以轻松连接和控制多个Topping设备，提供直观的设备状态展示和详细控制选项。

## 安装要求

- Android 5.0 (API级别21)或更高版本
- iOS 11.0或更高版本
- 支持蓝牙5.0以获得最佳体验

Topping Home - 为音乐发烧友打造的专业音频设备管理解决方案。
