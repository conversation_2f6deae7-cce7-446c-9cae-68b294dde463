#!/bin/bash

# 此脚本用于设置iOS环境

echo "开始设置iOS环境..."

# 确保脚本可执行
chmod +x ios/scripts/configure_schemes.sh
chmod +x ios/scripts/configure_info_plist.sh

# 运行配置脚本
./ios/scripts/configure_schemes.sh
./ios/scripts/configure_info_plist.sh

echo "iOS环境设置完成！"
echo "现在您可以使用以下命令运行不同环境的iOS应用："
echo "flutter run --flavor dev --dart-define=FLAVOR=dev -t lib/main.dart"
echo "flutter run --flavor staging --dart-define=FLAVOR=staging -t lib/main.dart"
echo "flutter run --flavor prod --dart-define=FLAVOR=prod -t lib/main.dart"
