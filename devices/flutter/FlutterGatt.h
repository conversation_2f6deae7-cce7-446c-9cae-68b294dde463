#ifndef __FLUTTER_GATT_H__
#define __FLUTTER_GATT_H__
#include "BluetoothGatt.h"
#include "BluetoothLeAdapter.h"
#include <vector>
#include "BluetoothGattService.h"
extern "C" {
    struct bluetooth_gatt_characteristic_t
    {
        char *uuid;
        int property;
        uint8_t *value;
        int value_len;
    };

    struct flutter_gatt_functions_t
    {
        long (*init)(long native_object);
        void (*uninit)(long flutter_object);
        void (*close)(long flutter_object);
        void (*connect)(long flutter_object, long device);
        void (*disconnect)(long flutter_object);
        bool (*request_mtu)(long flutter_object, int mtu);
        bool (*write_characteristic)(long flutter_object, struct bluetooth_gatt_characteristic_t *characteristic);
        bool (*set_characteristic_notification)(long flutter_object, struct bluetooth_gatt_characteristic_t *characteristic, bool enable);
        long (*get_service)(long flutter_object, const char *uuid);
    };

    void flutter_gatt_register_functions(struct flutter_gatt_functions_t *functions);
    void flutter_gatt_on_connection_state_change(long native_object, int state, int newState);
    void flutter_gatt_on_services_discovered(long native_object);
    void flutter_gatt_on_characteristic_changed(long native_object, struct bluetooth_gatt_characteristic_t *characteristic);
}

namespace Topping
{
    class FlutterGatt : public BluetoothGatt
    {
    public:
        FlutterGatt(BluetoothDevice &BluetoothDevice, BluetoothLeAdapter::BluetoothGattCallback *bluetoothGattCallback);
        virtual ~FlutterGatt();
        virtual void close() override;
        virtual void connect() override;
        virtual void disconnect() override;
        virtual bool requestMtu(int mtu) override;
        virtual bool writeCharacteristic(BluetoothGattCharacteristic &characteristic) override;
        virtual bool setCharacteristicNotification(BluetoothGattCharacteristic &characteristic, bool enable) override;
        virtual BluetoothGattService *getService(const std::string &uuid) override;

        static void registerFunctions(struct flutter_gatt_functions_t *functions);
        
        // 打印回调对象状态，用于调试
        void printCallbackState();

    public:
        BluetoothLeAdapter::BluetoothGattCallback *mCallback;

    private:
        BluetoothGattService mBluetoothGattService;
        static flutter_gatt_functions_t mFunctions;
        long mFlutterObject;
        BluetoothDevice mBluetoothDevice;
    };
}
#endif