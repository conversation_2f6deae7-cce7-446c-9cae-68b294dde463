#include "FlutterGatt.h"
#include <signal.h>
#include "common.h"
#include "tpprintf.h"
#include "BluetoothGattCharacteristic.h"
#include <unistd.h>
#include <string.h>
#include <sys/stat.h>
#include <fcntl.h>
#include <string.h>
namespace Topping
{
    flutter_gatt_functions_t FlutterGatt::mFunctions;


    FlutterGatt::FlutterGatt(BluetoothDevice &BluetoothDevice, BluetoothLeAdapter::BluetoothGattCallback *bluetoothGattCallback) : mCallback(bluetoothGattCallback), mBluetoothGattService(SERVICE_UUID), mBluetoothDevice(BluetoothDevice)
    {
        TPPRINTF("[BLE日志] 构造FlutterGatt对象, 回调指针: %p\n", bluetoothGattCallback);
        BluetoothGattCharacteristic writeCharacteristic(WRITE_CHARACTERISTIC_UUID, BluetoothGattCharacteristic::CHARACTERISTIC_PROPERTY_WRITE_NO_RESPONSE);
        mBluetoothGattService.addCharacteristic(writeCharacteristic);
        BluetoothGattCharacteristic notifyCharacteristic(NOTIFY_CHARACTERISTIC_UUID, BluetoothGattCharacteristic::CHARACTERISTIC_PROPERTY_NOTIFY);
        mBluetoothGattService.addCharacteristic(notifyCharacteristic);
        mFlutterObject = mFunctions.init((long)this);
        printCallbackState();
        connect();
    }

    FlutterGatt::~FlutterGatt()
    {
        TPPRINTF("[BLE日志 GATT] 析构FlutterGatt对象\n");
        mFunctions.uninit(mFlutterObject);
    }

    void FlutterGatt::close()
    {
        TPPRINTF("[BLE日志 GATT] FlutterGatt::close被调用\n");
        mFunctions.close(mFlutterObject);
        delete this;
    }

    void FlutterGatt::connect()
    {
        mFunctions.connect(mFlutterObject, (long)mBluetoothDevice.getDevice());
    }

    void FlutterGatt::disconnect()
    {
        mFunctions.disconnect(mFlutterObject);
    }

    bool FlutterGatt::requestMtu(int mtu)
    {
        return mFunctions.request_mtu(mFlutterObject, mtu);;
    }

    bool FlutterGatt::writeCharacteristic(BluetoothGattCharacteristic &characteristic)
    {
        struct bluetooth_gatt_characteristic_t ch;
        ch.uuid = (char *)characteristic.getUUID().c_str();
        ch.property = characteristic.getProperty();
        ch.value = new uint8_t[characteristic.getValue().size()];
        for (size_t i = 0; i < characteristic.getValue().size(); i++)
        {
            ch.value[i] = characteristic.getValue()[i];
        }
        ch.value_len = characteristic.getValue().size();
        bool ret = mFunctions.write_characteristic(mFlutterObject, &ch);
        delete[] ch.value;
        return ret;
    }

    bool FlutterGatt::setCharacteristicNotification(BluetoothGattCharacteristic &characteristic, bool enable)
    {
        TPPRINTF("[BLE日志] FlutterGatt::setCharacteristicNotification被调用, UUID: %s, enable: %d\n", 
                 characteristic.getUUID().c_str(), enable);
        printCallbackState();
        
        struct bluetooth_gatt_characteristic_t ch;
        ch.uuid = (char *)characteristic.getUUID().c_str();
        ch.property = characteristic.getProperty();
        
        bool result = mFunctions.set_characteristic_notification(mFlutterObject, &ch, enable);
        TPPRINTF("[BLE日志] setCharacteristicNotification结果: %d\n", result);
        
        return result;
    }

    BluetoothGattService *FlutterGatt::getService(const std::string &uuid)
    {
        if (mFunctions.get_service(mFlutterObject, uuid.c_str()) > 0)
        {
            return &mBluetoothGattService;
        }
        return nullptr;
    }

    void FlutterGatt::registerFunctions(struct flutter_gatt_functions_t *functions)
    {
        mFunctions = *functions;
    }

    void FlutterGatt::printCallbackState()
    {
        TPPRINTF("[BLE日志] FlutterGatt回调状态:\n");
        TPPRINTF("  - this指针: %p\n", this);
        TPPRINTF("  - mCallback指针: %p\n", mCallback);
        TPPRINTF("  - mFlutterObject: %ld\n", mFlutterObject);
        
        // 检查回调指针是否有效
        if (mCallback != nullptr) {
            TPPRINTF("  - mCallback有效\n");
        } else {
            TPPRINTF("  - 警告: mCallback为空!\n");
        }
        
        // 检查函数表是否已注册
        TPPRINTF("  - mFunctions.init: %p\n", mFunctions.init);
        TPPRINTF("  - mFunctions.write_characteristic: %p\n", mFunctions.write_characteristic);
        TPPRINTF("  - mFunctions.set_characteristic_notification: %p\n", mFunctions.set_characteristic_notification);
    }
}

extern "C" {
void flutter_gatt_register_functions(struct flutter_gatt_functions_t *functions)
{
    Topping::FlutterGatt::registerFunctions(functions);
}

void flutter_gatt_on_connection_state_change(long native_object, int state, int newState)
{
    Topping::FlutterGatt *flutterGatt = (Topping::FlutterGatt *)native_object;
    flutterGatt->mCallback->onConnectionStateChange(flutterGatt, state, newState);
}

void flutter_gatt_on_services_discovered(long native_object)
{
    Topping::FlutterGatt *flutterGatt = (Topping::FlutterGatt *)native_object;
    flutterGatt->mCallback->onServicesDiscovered(flutterGatt);
}

void flutter_gatt_on_characteristic_changed(long native_object, struct bluetooth_gatt_characteristic_t *characteristic)
{
    TPPRINTF("[BLE日志] flutter_gatt_on_characteristic_changed被调用 - native_object: %ld, uuid: %s\n", 
             native_object, characteristic->uuid);
    
    Topping::FlutterGatt *flutterGatt = (Topping::FlutterGatt *)native_object;
    if (flutterGatt == nullptr) {
        TPPRINTF("[BLE日志] 错误: flutterGatt为空指针!\n");
        return;
    }
    
    if (flutterGatt->mCallback == nullptr) {
        TPPRINTF("[BLE日志] 错误: flutterGatt->mCallback为空指针!\n");
        return;
    }
    
    TPPRINTF("[BLE日志] 准备创建bluetoothGattCharacteristic对象\n");
    Topping::BluetoothGattCharacteristic bluetoothGattCharacteristic(characteristic->uuid, characteristic->property);
    bluetoothGattCharacteristic.setValue(characteristic->value, characteristic->value_len);
    
    // 打印特征值数据的前几个字节
    if (characteristic->value != nullptr && characteristic->value_len > 0) {
        TPPRINTF("[BLE日志] 特征值数据前8字节(或更少): ");
        for (int i = 0; i < characteristic->value_len && i < 8; i++) {
            TPPRINTF("%02x ", characteristic->value[i]);
        }
        TPPRINTF("\n");
    }
    
    TPPRINTF("[BLE日志] 准备调用flutterGatt->mCallback->onCharacteristicChanged\n");
    flutterGatt->mCallback->onCharacteristicChanged(flutterGatt, bluetoothGattCharacteristic);
    TPPRINTF("[BLE日志] onCharacteristicChanged回调调用完成\n");
}
}