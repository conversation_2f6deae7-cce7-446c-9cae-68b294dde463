#ifndef __D900_DEVICE_H__
#define __D900_DEVICE_H__
#include "ControllerScanner.h"
#include "ControllerClient.h"
#include <string>
extern "C"
{
    enum d900_device_cmd_t
    {
        D900_DEVICE_CMD_POWER_ON = 0x100,
        D900_DEVICE_CMD_SET_VOLUME = 0x102,
        D900_DEVICE_CMD_SET_MUTE = 0x104,
        D900_DEVICE_CMD_SET_INPUT_TYPE = 0x106,
        D900_DEVICE_CMD_SET_OUTPUT_TYPE = 0x108,
        D900_DEVICE_CMD_SET_DISPLAY_MODE = 0x10a,
        D900_DEVICE_CMD_SET_THEME = 0x10c,
        D900_DEVICE_CMD_SET_POWER_TRIGGER = 0x10e,
        D900_DEVICE_CMD_USB_SELECT = 0x110,
        D900_DEVICE_CMD_SET_BALANCE = 0x112,
        D900_DEVICE_CMD_ENABLE_AUDIO_BLUETOOTH = 0x114,
        D900_DEVICE_CMD_ENABLE_BLUETOOTH_APTX = 0x116,
        D900_DEVICE_CMD_ENABLE_REMOTE = 0x118,
        D900_DEVICE_CMD_SET_MULTIFUNCTION_KEY = 0x11a,
        D900_DEVICE_CMD_ENABLE_USB_DSD = 0x11c,
        D900_DEVICE_CMD_SET_USB_MODE = 0x11e,
        D900_DEVICE_CMD_SET_IIS_PHASE = 0x120,
        D900_DEVICE_CMD_SET_IIS_CHANNEL = 0x122,
        D900_DEVICE_CMD_SET_SCREEN_BRIGHTNESS = 0x124,
        D900_DEVICE_CMD_SET_LANGUAGE = 0x126,
        D900_DEVICE_CMD_RESTORE_FACTORY_SETTINGS = 0x128,
        D900_DEVICE_CMD_GET_SETTINGS = 0x12a,

        // --- 新增命令 (D900新版功能) ---
        D900_DEVICE_CMD_SET_VU_METER_LEVEL = 0x12c, // 新增: 设置VU表0dDB幅值
        D900_DEVICE_CMD_SET_VU_METER_DISPLAY_MODE = 0x12e, // 新增: 设置VU条显示模式
        D900_DEVICE_CMD_SET_INPUT_OPTIONS = 0x130, // 新增: 设置可用的输入选项(位掩码)
        D900_DEVICE_CMD_SET_OUTPUT_OPTIONS = 0x132, // 新增: 设置可用的输出选项(位掩码)
        D900_DEVICE_CMD_SET_USB_PORT_SELECT = 0x134, // 新增: 设置USB接口选择
        D900_DEVICE_CMD_SET_DSD_MUTE = 0x136, // 新增: 设置DSD MUTE模式
        D900_DEVICE_CMD_SET_OUTPUT_LEVEL = 0x138, // 新增: 设置输出幅值
        D900_DEVICE_CMD_SET_VOLUME_STEP = 0x13a, // 新增: 设置音量步进
        D900_DEVICE_CMD_SET_POLARITY = 0x13c, // 新增: 设置极性
        D900_DEVICE_CMD_ENABLE_PEQ = 0x13e, // 新增: 启用/禁用PEQ
        D900_DEVICE_CMD_SET_PEQ_PRESET = 0x140, // 新增: 设置PEQ预设
        D900_DEVICE_CMD_ENABLE_DSD_DIRECT = 0x142, // 新增: 启用/禁用DSD直通
        D900_DEVICE_CMD_SET_VOLUME_MEMORY_MODE = 0x144, // 新增: 设置音量记忆方式
        D900_DEVICE_CMD_SET_PEQ_MEMORY_MODE = 0x146, // 新增: 设置PEQ记忆方式
        D900_DEVICE_CMD_SET_MAIN_KEY_FUNCTION = 0x148, // 新增: 设置主按键功能
        D900_DEVICE_CMD_SET_REMOTE_A_KEY_FUNCTION = 0x14a, // 新增: 设置遥控A键功能
        D900_DEVICE_CMD_SET_REMOTE_B_KEY_FUNCTION = 0x14c, // 新增: 设置遥控B键功能
    };

    enum d900_device_input_t
    {
        D900_DEVICE_INPUT_USB = 0,
        D900_DEVICE_INPUT_OPTICAL1 = 1,
        D900_DEVICE_INPUT_OPTICAL2 = 2,
        D900_DEVICE_INPUT_COAXIAL1 = 3,
        D900_DEVICE_INPUT_COAXIAL2 = 4,
        D900_DEVICE_INPUT_AES = 5,
        D900_DEVICE_INPUT_IIS = 6,
        D900_DEVICE_INPUT_BT = 7,
    };

    enum d900_device_output_t
    {
        D900_DEVICE_OUTPUT_DAC = 0,
        D900_DEVICE_OUTPUT_PREAMP = 1,
        D900_DEVICE_OUTPUT_ALL = 2,
    };

    enum d900_device_display_mode_t
    {
        D900_DEVICE_DISPLAY_MODE_NORMAL = 0,
        D900_DEVICE_DISPLAY_MODE_VU = 1,
        D900_DEVICE_DISPLAY_MODE_FFT = 2,
    };

    enum d900_device_theme_t
    {
        D900_DEVICE_THEME_AURORA = 0,
        D900_DEVICE_THEME_ORANGE = 1,
        D900_DEVICE_THEME_PERU = 2,
        D900_DEVICE_THEME_GREEN = 3,
        D900_DEVICE_THEME_KHAKI = 4,
        D900_DEVICE_THEME_ROSE = 5,
        D900_DEVICE_THEME_BLUE = 6,
        D900_DEVICE_THEME_PURPLE = 7,
        D900_DEVICE_THEME_WHITE = 8,
    };

    enum d900_device_power_trigger_t
    {
        D900_DEVICE_POWER_TRIGGER_SIGNAL = 0,
        D900_DEVICE_POWER_TRIGGER_VOLTAGE = 1,
        D900_DEVICE_POWER_TRIGGER_CLOSE = 2,
    };

    enum d900_device_usb_select_t
    {
        D900_DEVICE_USB_SELECT_TYPEC = 0,
        D900_DEVICE_USB_SELECT_TYPEB = 1,
        D900_DEVICE_USB_SELECT_AUTO = 2,
    };

    enum d900_device_multifunction_key_t
    {
        D900_DEVICE_MULTIFUNCTION_KEY_INPUT_SELECT = 0,
        D900_DEVICE_MULTIFUNCTION_KEY_OUTPUT_SELECT = 1,
        D900_DEVICE_MULTIFUNCTION_KEY_HOME_SELECT = 2,
        D900_DEVICE_MULTIFUNCTION_KEY_BRIGHTNESS_SELECT = 3,
        D900_DEVICE_MULTIFUNCTION_KEY_SCREEN_OFF = 4,
        D900_DEVICE_MULTIFUNCTION_KEY_PEQ_SELECT = 5,
        D900_DEVICE_MULTIFUNCTION_KEY_MUTE = 6,
    };

    enum d900_device_usb_mode_t
    {
        D900_DEVICE_USB_MODE_UAC1 = 0,
        D900_DEVICE_USB_MODE_UAC2 = 1,
    };

    enum d900_device_iis_phase_t
    {
        D900_DEVICE_IIS_PHASE_STANDARD = 0,
        D900_DEVICE_IIS_PHASE_INVERTED = 1,
    };

    enum d900_device_iis_channel_t
    {
        D900_DEVICE_IIS_CHANNEL_STANDARD = 0,
        D900_DEVICE_IIS_CHANNEL_SWAPPED = 1,
    };

    enum d900_device_screen_brightness_t
    {
        D900_DEVICE_SCREEN_BRIGHTNESS_LOW = 0,
        D900_DEVICE_SCREEN_BRIGHTNESS_MEDIUM = 1,
        D900_DEVICE_SCREEN_BRIGHTNESS_HIGH = 2,
        D900_DEVICE_SCREEN_BRIGHTNESS_AUTO = 3,
    };

    enum d900_device_language_t
    {
        D900_DEVICE_LANGUAGE_EH = 0,
        D900_DEVICE_LANGUAGE_CN = 1,
    };

    // --- 新增枚举定义 (D900新版功能) ---

    // 新增: VU表0dDB幅值
    enum d900_vu_meter_level_t
    {
        D900_VU_METER_LEVEL_4DBU = 0,
        D900_VU_METER_LEVEL_10DBU = 1,
    };

    // 新增: VU条显示模式
    enum d900_vu_meter_display_mode_t
    {
        D900_VU_METER_DISPLAY_ALWAYS_ON = 0,       // 全开
        D900_VU_METER_DISPLAY_ON_NORMAL_UI = 1,    // 常规界面
        D900_VU_METER_DISPLAY_ON_FFT_UI = 2,       // FFT界面
        D900_VU_METER_DISPLAY_OFF = 3,             // 全关
    };

    // 新增: 输入选项位掩码 (用于多选组合)
    enum d900_input_options_t
    {
        D900_INPUT_OPTION_USB = 0x01,
        D900_INPUT_OPTION_OPTICAL1 = 0x02,
        D900_INPUT_OPTION_OPTICAL2 = 0x04,
        D900_INPUT_OPTION_COAXIAL1 = 0x08,
        D900_INPUT_OPTION_COAXIAL2 = 0x10,
        D900_INPUT_OPTION_AES = 0x20,
        D900_INPUT_OPTION_IIS = 0x40,
        D900_INPUT_OPTION_BT = 0x80,
    };

    // 新增: 输出选项位掩码 (用于多选组合)
    enum d900_output_options_t
    {
        D900_OUTPUT_OPTION_ALL = 0x01,      // 全部
        D900_OUTPUT_OPTION_LINE = 0x02,     // 线路
        D900_OUTPUT_OPTION_PREAMP = 0x04,   // 前级
    };

    // 新增: USB接口选择
    enum d900_usb_port_select_t
    {
        D900_USB_PORT_TYPE_C = 0,
        D900_USB_PORT_TYPE_B = 1,
        D900_USB_PORT_AUTO = 2,
    };

    // 新增: DSD MUTE模式
    enum d900_dsd_mute_t
    {
        D900_DSD_MUTE_HIGH_ACTIVE = 0,  // 高电平有效
        D900_DSD_MUTE_LOW_ACTIVE = 1,   // 低电平有效
        D900_DSD_MUTE_OFF = 2,          // 关
    };

    // 新增: 输出幅值
    enum d900_output_level_t
    {
        D900_OUTPUT_LEVEL_5V = 0,
        D900_OUTPUT_LEVEL_4V = 1,
    };

    // 新增: 音量步进
    enum d900_volume_step_t
    {
        D900_VOLUME_STEP_0_5_DB = 0,
        D900_VOLUME_STEP_1_0_DB = 1,
    };

    // 新增: 极性设置
    enum d900_polarity_t
    {
        D900_POLARITY_NORMAL = 0,   // 标准
        D900_POLARITY_INVERTED = 1, // 反相
    };

    // 新增: PEQ预设选择
    enum d900_peq_preset_t
    {
        D900_PEQ_PRESET_BASS1 = 0,
        D900_PEQ_PRESET_BASS2 = 1,
        D900_PEQ_PRESET_AIRY = 2,
        D900_PEQ_PRESET_WARM = 3,
        D900_PEQ_PRESET_DYNAMIC = 4,
        // Config6-10 为用户自定义
    };

    // 新增: 记忆方式 (音量/PEQ共用)
    enum d900_memory_mode_t
    {
        D900_MEMORY_FOLLOW_INPUT = 0,
        D900_MEMORY_FOLLOW_OUTPUT = 1,
        D900_MEMORY_OFF = 2,
    };

    // 新增: 按键可分配的功能列表
    enum d900_assignable_function_t
    {
        D900_FUNCTION_INPUT_SELECT = 0,
        D900_FUNCTION_OUTPUT_SELECT = 1,
        D900_FUNCTION_HOME_SELECT = 2,
        D900_FUNCTION_BRIGHTNESS_SELECT = 3,
        D900_FUNCTION_SLEEP_SCREEN = 4, // 息屏
        D900_FUNCTION_MUTE = 5,
        D900_FUNCTION_PEQ_SELECT = 6,
        D900_FUNCTION_POWER_TRIGGER_SELECT = 7,
    };

    struct d900_scan_result_t
    {
        char *name;
        long device;
        int rssi;
    };

    enum d900_device_sampling_t
    {
        D900_DEVICE_FSR_ERR = 0,
        D900_DEVICE_FSR_44_1K,
        D900_DEVICE_FSR_48K,
        D900_DEVICE_FSR_88_2K,
        D900_DEVICE_FSR_96K,
        D900_DEVICE_FSR_176_4K,
        D900_DEVICE_FSR_192K,
        D900_DEVICE_FSR_352_8K,
        D900_DEVICE_FSR_384K,
        D900_DEVICE_FSR_705_6K,
        D900_DEVICE_FSR_768K,
        D900_DEVICE_FSR_2_82M,
        D900_DEVICE_FSR_5_64M,
        D900_DEVICE_FSR_11_28M,
        D900_DEVICE_FSR_22_57M,
        D900_DEVICE_FSR_45_15M,
        D900_DEVICE_FSR_49_15M,
    };

    struct d900_settings_t
    {
        int is_on;
        char device_name[32];//蓝牙模块只支持名称长度为32，
        int volume;
        int is_mute;
        int input_type;
        int output_type;
        int display_mode;
        int theme;
        int power_trigger;
        int usb_select;
        int balance;
        int audio_bt_enable;
        int aptx_enable;
        int remote_enable;
        int multifunction_key;
        int usb_dsd_enable;
        int usb_mode;
        int iis_phase;
        int iis_channel;
        int screen_brightness;
        int language;
        int sampling;

        // --- 新增字段 (D900新版功能) ---
        int vu_meter_level;              // 新增: VU表0dDB幅值
        int vu_meter_display_mode;       // 新增: VU条显示模式
        int input_options;               // 新增: 输入选项位掩码
        int output_options;              // 新增: 输出选项位掩码
        int usb_port_select;             // 新增: USB接口选择
        int dsd_mute;                    // 新增: DSD MUTE模式
        int output_level;                // 新增: 输出幅值
        int volume_step;                 // 新增: 音量步进
        int polarity;                    // 新增: 极性设置
        int peq_enable;                  // 新增: PEQ启用状态
        int peq_preset;                  // 新增: PEQ预设选择
        int dsd_direct_enable;           // 新增: DSD直通启用状态
        int volume_memory_mode;          // 新增: 音量记忆方式
        int peq_memory_mode;             // 新增: PEQ记忆方式
        int main_key_function;           // 新增: 主按键功能
        int remote_a_key_function;       // 新增: 遥控A键功能
        int remote_b_key_function;       // 新增: 遥控B键功能
    };

    struct d900_device_callback_t
    {
        void (*on_scan_results)(long flutter_object, struct d900_scan_result_t *results, size_t count);
        void (*on_scan_failed)(long flutter_object, int errorCode);
        void (*on_state_change)(long flutter_object, int state);
        void (*on_verify_result)(long flutter_object, int type);
        void (*on_power_change)(long flutter_object, int is_on);
        void (*on_device_volume_change)(long flutter_object, int volume);
        void (*on_device_mute_change)(long flutter_object, int is_mute);
        void (*on_device_input_type_change)(long flutter_object, int input_type);
        void (*on_device_output_type_change)(long flutter_object, int output_type);
        void (*on_device_display_mode_change)(long flutter_object, int display_mode);
        void (*on_device_theme_change)(long flutter_object, int theme);
        void (*on_device_power_trigger_change)(long flutter_object, int trigger_type);
        void (*on_device_usb_select_change)(long flutter_object, int type);
        void (*on_device_balance_change)(long flutter_object, int balance);
        void (*on_device_enable_audio_bluetooth)(long flutter_object, int enable);
        void (*on_device_enable_bluetooth_aptx)(long flutter_object, int enable);
        void (*on_device_enable_remote)(long flutter_object, int enable);
        void (*on_device_multifunction_key_change)(long flutter_object, int key_type);
        void (*on_device_enable_usb_dsd)(long flutter_object, int enable);
        void (*on_device_usb_mode_change)(long flutter_object, int usb_mode);
        void (*on_device_iis_phase_change)(long flutter_object, int phase);
        void (*on_device_iis_channel_change)(long flutter_object, int channel);
        void (*on_device_screen_brightness_change)(long flutter_object, int brightness_type);
        void (*on_device_language_change)(long flutter_object, int language);
        void (*on_device_restore_factory_settings)(long flutter_object);
        void (*on_device_settings_response)(long flutter_object, struct d900_settings_t *d900_settings);

        // --- 新增回调函数 (D900新版功能) ---
        void (*on_device_vu_meter_level_change)(long flutter_object, int level);                    // 新增: VU表0dDB幅值变化回调
        void (*on_device_vu_meter_display_mode_change)(long flutter_object, int mode);             // 新增: VU条显示模式变化回调
        void (*on_device_input_options_change)(long flutter_object, int options);                  // 新增: 输入选项变化回调
        void (*on_device_output_options_change)(long flutter_object, int options);                 // 新增: 输出选项变化回调
        void (*on_device_usb_port_select_change)(long flutter_object, int port);                   // 新增: USB接口选择变化回调
        void (*on_device_dsd_mute_change)(long flutter_object, int mode);                          // 新增: DSD MUTE模式变化回调
        void (*on_device_output_level_change)(long flutter_object, int level);                     // 新增: 输出幅值变化回调
        void (*on_device_volume_step_change)(long flutter_object, int step);                       // 新增: 音量步进变化回调
        void (*on_device_polarity_change)(long flutter_object, int polarity);                      // 新增: 极性变化回调
        void (*on_device_peq_enable_change)(long flutter_object, int enable);                      // 新增: PEQ启用变化回调
        void (*on_device_peq_preset_change)(long flutter_object, int preset);                      // 新增: PEQ预设变化回调
        void (*on_device_dsd_direct_enable_change)(long flutter_object, int enable);               // 新增: DSD直通启用变化回调
        void (*on_device_volume_memory_mode_change)(long flutter_object, int mode);                // 新增: 音量记忆方式变化回调
        void (*on_device_peq_memory_mode_change)(long flutter_object, int mode);                   // 新增: PEQ记忆方式变化回调
        void (*on_device_main_key_function_change)(long flutter_object, int function);             // 新增: 主按键功能变化回调
        void (*on_device_remote_a_key_function_change)(long flutter_object, int function);         // 新增: 遥控A键功能变化回调
        void (*on_device_remote_b_key_function_change)(long flutter_object, int function);         // 新增: 遥控B键功能变化回调
    };

    struct d900_device_t
    {
        void *handle;
        long flutter_object;
        struct d900_device_callback_t callback;
    };

    long d900_device_create(long flutter_object);
    void d900_device_destory(long native_object);
    void d900_device_register_callback(long native_object, struct d900_device_callback_t *callback);
    void d900_device_startScan(long native_object);
    void d900_device_stopScan(long native_object);
    void d900_device_connect(long native_object, long device);
    void d900_device_disconnect(long native_object);
    void d900_device_verify(long native_object);
    void d900_device_power_on(long native_object, int is_on);
    void d900_device_set_volume(long native_object, int volume);
    void d900_device_set_mute(long native_object, int is_mute);
    void d900_device_set_input_type(long native_object, int input_type);
    void d900_device_set_output_type(long native_object, int output_type);
    void d900_device_set_diaplay_mode(long native_object, int diaplay_mode);
    void d900_device_set_theme(long native_object, int theme);
    void d900_device_set_power_trigger(long native_object, int trigger_type);
    void d900_device_set_usb_select(long native_object, int type);
    void d900_device_set_balance(long native_object, int balance);
    void d900_device_enable_audio_bluetooth(long native_object, int enable);
    void d900_device_enable_bluetooth_aptx(long native_object, int enable);
    void d900_device_enable_remote(long native_object, int enable);
    void d900_device_set_multifunction_key(long native_object, int key_type);
    void d900_device_enebla_usb_dsd(long native_object, int enable);
    void d900_device_set_usb_mode(long native_object, int usb_mode);
    void d900_device_set_iis_phase(long native_object, int phase);
    void d900_device_set_iis_channel(long native_object, int channel);
    void d900_device_set_screen_brightness(long native_object, int brightness_type);
    void d900_device_set_language(long native_object, int language);
    void d900_device_restore_factory_settings(long native_object);
    void d900_device_request_settings(long native_object);

    // --- 新增函数声明 (D900新版功能) ---
    void d900_device_set_vu_meter_level(long native_object, int level);                          // 新增: 设置VU表0dDB幅值
    void d900_device_set_vu_meter_display_mode(long native_object, int mode);                    // 新增: 设置VU条显示模式
    void d900_device_set_input_options(long native_object, int options);                         // 新增: 设置输入选项
    void d900_device_set_output_options(long native_object, int options);                        // 新增: 设置输出选项
    void d900_device_set_usb_port_select(long native_object, int port);                          // 新增: 设置USB接口选择
    void d900_device_set_dsd_mute(long native_object, int mode);                                  // 新增: 设置DSD MUTE模式
    void d900_device_set_output_level(long native_object, int level);                            // 新增: 设置输出幅值
    void d900_device_set_volume_step(long native_object, int step);                              // 新增: 设置音量步进
    void d900_device_set_polarity(long native_object, int polarity);                             // 新增: 设置极性
    void d900_device_enable_dsd_direct(long native_object, int enable);                          // 新增: 启用/禁用DSD直通
    void d900_device_set_volume_memory_mode(long native_object, int mode);                       // 新增: 设置音量记忆方式
    void d900_device_set_peq_memory_mode(long native_object, int mode);                          // 新增: 设置PEQ记忆方式
    void d900_device_set_main_key_function(long native_object, int function);                    // 新增: 设置主按键功能
    void d900_device_set_remote_a_key_function(long native_object, int function);                // 新增: 设置遥控A键功能
    void d900_device_set_remote_b_key_function(long native_object, int function);                // 新增: 设置遥控B键功能
}

namespace Topping
{
    class D900Device : public ControllerScanner::ScanCallback, public ControllerClient::Callback
    {
    public:
        D900Device(d900_device_t *d900_device);
        ~D900Device();

        void startScan();
        void stopScan();
        void connect(BluetoothDevice &device);
        void disconnect();
        void verify();
        void powerOn(bool isOn);
        void setVolume(int volume);
        void setMute(bool isMute);
        void setInputType(int inputType);
        void setOutputType(int outputType);
        void setDisplayMode(int displayMode);
        void setTheme(int theme);
        void setPowerTrigger(int triggerType);
        void setUsbSelect(int type);
        void setBalance(int balance);
        void enableAudioBluetooth(bool enable);
        void enableBluetoothAPTX(bool enable);
        void enableRemote(bool enable);
        void setMultifunctionKey(int keyType);
        void enableUsbDsd(bool enable);
        void setUsbMode(int usbMode);
        void setIisPhase(int phase);
        void setIisChannel(int channel);
        void setScreenBrightness(int brightnessType);
        void setLanguage(int language);
        void restoreFactorySettings();
        void requestSettings();

        // --- 新增方法声明 (D900新版功能) ---
        void setVuMeterLevel(int level);                          // 新增: 设置VU表0dDB幅值
        void setVuMeterDisplayMode(int mode);                     // 新增: 设置VU条显示模式
        void setInputOptions(int options);                        // 新增: 设置输入选项
        void setOutputOptions(int options);                       // 新增: 设置输出选项
        void setUsbPortSelect(int port);                          // 新增: 设置USB接口选择
        void setDsdMute(int mode);                                // 新增: 设置DSD MUTE模式
        void setOutputLevel(int level);                           // 新增: 设置输出幅值
        void setVolumeStep(int step);                             // 新增: 设置音量步进
        void setPolarity(int polarity);                           // 新增: 设置极性
        void enableDsdDirect(bool enable);                        // 新增: 启用/禁用DSD直通
        void setVolumeMemoryMode(int mode);                       // 新增: 设置音量记忆方式
        void setPeqMemoryMode(int mode);                          // 新增: 设置PEQ记忆方式
        void setMainKeyFunction(int function);                    // 新增: 设置主按键功能
        void setRemoteAKeyFunction(int function);                 // 新增: 设置遥控A键功能
        void setRemoteBKeyFunction(int function);                 // 新增: 设置遥控B键功能

    protected:
        virtual void onBatchScanResults(const std::vector<ControllerScanner::ScanResult> &results) override;
        virtual void onScanFailed(int errorCode) override;

        virtual void onStateChange(int state) override;
        virtual void onVerifyResult(int type) override;
        virtual void onReceiveRequest(int session_id, int cmd, const std::string &msg) override;
        virtual void onReceiveResponse(int cmd, int errcode, const std::string &errmsg, const std::string &msg) override;

    private:
        void parseRequest(int sessionId, int cmd, const std::string &data);
        void parseResponse(int cmd, const std::string &data);
        void parseResponsePowerOn(const std::string &data);
        void parseResponseSetVolume(const std::string &data);
        void parseResponseSetMute(const std::string &data);
        void parseResponseSetInputType(const std::string &data);
        void parseResponseSetOutputType(const std::string &data);
        void parseResponseSetDisplayMode(const std::string &data);
        void parseResponseSetTheme(const std::string &data);
        void parseResponseSetPowerTrigger(const std::string &data);
        void parseResponseSetUsbSelect(const std::string &data);
        void parseResponseSetBalance(const std::string &data);
        void parseResponseEnableAudioBluetooth(const std::string &data);
        void parseResponseEnableBluetoothAPTX(const std::string &data);
        void parseResponseEnableRemote(const std::string &data);
        void parseResponseSetMultifunctionKey(const std::string &data);
        void parseResponseEnableUsbDsd(const std::string &data);
        void parseResponseSetUsbMode(const std::string &data);
        void parseResponseSetIisPhase(const std::string &data);
        void parseResponseSetIisChannel(const std::string &data);
        void parseResponseSetScreenBrightness(const std::string &data);
        void parseResponseSetLanguage(const std::string &data);
        void parseResponseRestoreFactorySettings(const std::string &data);
        void parseResponseRequestSettings(const std::string &data);
        void responseCmdError(int session_id, int cmd);

    private:
        ControllerScanner mControllerScanner;
        ControllerClient mControllerClient;
        const SupportedDevice *mSupportedDevice;
        d900_device_t *m_d900_device;
    };
}
#endif