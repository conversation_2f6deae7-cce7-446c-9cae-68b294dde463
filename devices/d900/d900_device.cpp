#include "d900_device.h"
#include "common.h"
#include "tpprintf.h"
#include <memory>
#include "errcode.h"
#include <string.h>
namespace Topping
{
    D900Device::D900Device(d900_device_t *d900_device) : mControllerClient(260), mSupportedDevice(&SUPPORTED_DEVICES[2]), m_d900_device(d900_device)
    {
    }

    D900Device::~D900Device()
    {
        TPPRINTF("[BLE日志 GATT] D900Device::~D900Device\n");
    }

    void D900Device::startScan()
    {
        mControllerScanner.startScan(this);
    }

    void D900Device::stopScan()
    {
        mControllerScanner.stopScan(this);
    }

    void D900Device::connect(BluetoothDevice &device)
    {
        mControllerClient.connect(device, this);
    }

    void D900Device::disconnect()
    {
        mControllerClient.disconnect();
    }

    void D900Device::verify()
    {
        mControllerClient.verify(mSupportedDevice);
    }

    void D900Device::powerOn(bool isOn)
    {
        cJSON *rootJson = cJSON_CreateObject();
        cJSON_AddBoolToObject(rootJson, "is_on", isOn);
        const char *dump = cJSON_Print(rootJson);
        cJSON_Delete(rootJson);
        mControllerClient.sendRequest(D900_DEVICE_CMD_POWER_ON, dump);
        cJSON_free((void *)dump);
    }

    void D900Device::setVolume(int volume)
    {
        cJSON *rootJson = cJSON_CreateObject();
        cJSON_AddNumberToObject(rootJson, "volume", volume);
        const char *dump = cJSON_Print(rootJson);
        cJSON_Delete(rootJson);
        mControllerClient.sendRequest(D900_DEVICE_CMD_SET_VOLUME, dump);
        cJSON_free((void *)dump);
    }

    void D900Device::setMute(bool isMute)
    {
        cJSON *rootJson = cJSON_CreateObject();
        cJSON_AddBoolToObject(rootJson, "is_mute", isMute);
        const char *dump = cJSON_Print(rootJson);
        cJSON_Delete(rootJson);
        mControllerClient.sendRequest(D900_DEVICE_CMD_SET_MUTE, dump);
        cJSON_free((void *)dump);
    }

    void D900Device::setInputType(int inputType)
    {
        cJSON *rootJson = cJSON_CreateObject();
        cJSON_AddNumberToObject(rootJson, "input_type", inputType);
        const char *dump = cJSON_Print(rootJson);
        cJSON_Delete(rootJson);
        mControllerClient.sendRequest(D900_DEVICE_CMD_SET_INPUT_TYPE, dump);
        cJSON_free((void *)dump);
    }

    void D900Device::setOutputType(int outputType)
    {
        cJSON *rootJson = cJSON_CreateObject();
        cJSON_AddNumberToObject(rootJson, "output_type", outputType);
        const char *dump = cJSON_Print(rootJson);
        cJSON_Delete(rootJson);
        mControllerClient.sendRequest(D900_DEVICE_CMD_SET_OUTPUT_TYPE, dump);
        cJSON_free((void *)dump);
    }

    void D900Device::setDisplayMode(int displayMode)
    {
        cJSON *rootJson = cJSON_CreateObject();
        cJSON_AddNumberToObject(rootJson, "display_mode", displayMode);
        const char *dump = cJSON_Print(rootJson);
        cJSON_Delete(rootJson);
        mControllerClient.sendRequest(D900_DEVICE_CMD_SET_DISPLAY_MODE, dump);
        cJSON_free((void *)dump);
    }

    void D900Device::setTheme(int theme)
    {
        cJSON *rootJson = cJSON_CreateObject();
        cJSON_AddNumberToObject(rootJson, "theme", theme);
        const char *dump = cJSON_Print(rootJson);
        cJSON_Delete(rootJson);
        mControllerClient.sendRequest(D900_DEVICE_CMD_SET_THEME, dump);
        cJSON_free((void *)dump);
    }

    void D900Device::setPowerTrigger(int triggerType)
    {
        cJSON *rootJson = cJSON_CreateObject();
        cJSON_AddNumberToObject(rootJson, "trigger_type", triggerType);
        const char *dump = cJSON_Print(rootJson);
        cJSON_Delete(rootJson);
        mControllerClient.sendRequest(D900_DEVICE_CMD_SET_POWER_TRIGGER, dump);
        cJSON_free((void *)dump);
    }

    void D900Device::setUsbSelect(int type)
    {
        cJSON *rootJson = cJSON_CreateObject();
        cJSON_AddNumberToObject(rootJson, "type", type);
        const char *dump = cJSON_Print(rootJson);
        cJSON_Delete(rootJson);
        mControllerClient.sendRequest(D900_DEVICE_CMD_USB_SELECT, dump);
        cJSON_free((void *)dump);
    }

    void D900Device::setBalance(int balance)
    {
        cJSON *rootJson = cJSON_CreateObject();
        cJSON_AddNumberToObject(rootJson, "balance", balance);
        const char *dump = cJSON_Print(rootJson);
        cJSON_Delete(rootJson);
        mControllerClient.sendRequest(D900_DEVICE_CMD_SET_BALANCE, dump);
        cJSON_free((void *)dump);
    }

    void D900Device::enableAudioBluetooth(bool enable)
    {
        cJSON *rootJson = cJSON_CreateObject();
        cJSON_AddBoolToObject(rootJson, "enable", enable);
        const char *dump = cJSON_Print(rootJson);
        cJSON_Delete(rootJson);
        mControllerClient.sendRequest(D900_DEVICE_CMD_ENABLE_AUDIO_BLUETOOTH, dump);
        cJSON_free((void *)dump);
    }

    void D900Device::enableBluetoothAPTX(bool enable)
    {
        cJSON *rootJson = cJSON_CreateObject();
        cJSON_AddBoolToObject(rootJson, "enable", enable);
        const char *dump = cJSON_Print(rootJson);
        cJSON_Delete(rootJson);
        mControllerClient.sendRequest(D900_DEVICE_CMD_ENABLE_BLUETOOTH_APTX, dump);
        cJSON_free((void *)dump);
    }

    void D900Device::enableRemote(bool enable)
    {
        cJSON *rootJson = cJSON_CreateObject();
        cJSON_AddBoolToObject(rootJson, "enable", enable);
        const char *dump = cJSON_Print(rootJson);
        cJSON_Delete(rootJson);
        mControllerClient.sendRequest(D900_DEVICE_CMD_ENABLE_REMOTE, dump);
        cJSON_free((void *)dump);
    }

    void D900Device::setMultifunctionKey(int keyType)
    {
        cJSON *rootJson = cJSON_CreateObject();
        cJSON_AddNumberToObject(rootJson, "key_type", keyType);
        const char *dump = cJSON_Print(rootJson);
        cJSON_Delete(rootJson);
        mControllerClient.sendRequest(D900_DEVICE_CMD_SET_MULTIFUNCTION_KEY, dump);
        cJSON_free((void *)dump);
    }

    void D900Device::enableUsbDsd(bool enable)
    {
        cJSON *rootJson = cJSON_CreateObject();
        cJSON_AddBoolToObject(rootJson, "enable", enable);
        const char *dump = cJSON_Print(rootJson);
        cJSON_Delete(rootJson);
        mControllerClient.sendRequest(D900_DEVICE_CMD_ENABLE_USB_DSD, dump);
        cJSON_free((void *)dump);
    }

    void D900Device::setUsbMode(int usbMode)
    {
        cJSON *rootJson = cJSON_CreateObject();
        cJSON_AddNumberToObject(rootJson, "usb_mode", usbMode);
        const char *dump = cJSON_Print(rootJson);
        cJSON_Delete(rootJson);
        mControllerClient.sendRequest(D900_DEVICE_CMD_SET_USB_MODE, dump);
        cJSON_free((void *)dump);
    }

    void D900Device::setIisPhase(int phase)
    {
        cJSON *rootJson = cJSON_CreateObject();
        cJSON_AddNumberToObject(rootJson, "phase", phase);
        const char *dump = cJSON_Print(rootJson);
        cJSON_Delete(rootJson);
        mControllerClient.sendRequest(D900_DEVICE_CMD_SET_IIS_PHASE, dump);
        cJSON_free((void *)dump);
    }

    void D900Device::setIisChannel(int channel)
    {
        cJSON *rootJson = cJSON_CreateObject();
        cJSON_AddNumberToObject(rootJson, "channel", channel);
        const char *dump = cJSON_Print(rootJson);
        cJSON_Delete(rootJson);
        mControllerClient.sendRequest(D900_DEVICE_CMD_SET_IIS_CHANNEL, dump);
        cJSON_free((void *)dump);
    }

    void D900Device::setScreenBrightness(int brightnessType)
    {
        cJSON *rootJson = cJSON_CreateObject();
        cJSON_AddNumberToObject(rootJson, "brightness_type", brightnessType);
        const char *dump = cJSON_Print(rootJson);
        cJSON_Delete(rootJson);
        mControllerClient.sendRequest(D900_DEVICE_CMD_SET_SCREEN_BRIGHTNESS, dump);
        cJSON_free((void *)dump);
    }

    void D900Device::setLanguage(int language)
    {
        cJSON *rootJson = cJSON_CreateObject();
        cJSON_AddNumberToObject(rootJson, "language", language);
        const char *dump = cJSON_Print(rootJson);
        cJSON_Delete(rootJson);
        mControllerClient.sendRequest(D900_DEVICE_CMD_SET_LANGUAGE, dump);
        cJSON_free((void *)dump);
    }

    // --- 新增方法实现 (D900新版功能) ---
    void D900Device::setVuMeterLevel(int level)
    {
        cJSON *rootJson = cJSON_CreateObject();
        cJSON_AddNumberToObject(rootJson, "level", level);
        const char *dump = cJSON_Print(rootJson);
        cJSON_Delete(rootJson);
        mControllerClient.sendRequest(D900_DEVICE_CMD_SET_VU_METER_LEVEL, dump);
        cJSON_free((void *)dump);
    }

    void D900Device::setVuMeterDisplayMode(int mode)
    {
        cJSON *rootJson = cJSON_CreateObject();
        cJSON_AddNumberToObject(rootJson, "mode", mode);
        const char *dump = cJSON_Print(rootJson);
        cJSON_Delete(rootJson);
        mControllerClient.sendRequest(D900_DEVICE_CMD_SET_VU_METER_DISPLAY_MODE, dump);
        cJSON_free((void *)dump);
    }

    void D900Device::setInputOptions(int options)
    {
        cJSON *rootJson = cJSON_CreateObject();
        cJSON_AddNumberToObject(rootJson, "options", options);
        const char *dump = cJSON_Print(rootJson);
        cJSON_Delete(rootJson);
        mControllerClient.sendRequest(D900_DEVICE_CMD_SET_INPUT_OPTIONS, dump);
        cJSON_free((void *)dump);
    }

    void D900Device::setOutputOptions(int options)
    {
        cJSON *rootJson = cJSON_CreateObject();
        cJSON_AddNumberToObject(rootJson, "options", options);
        const char *dump = cJSON_Print(rootJson);
        cJSON_Delete(rootJson);
        mControllerClient.sendRequest(D900_DEVICE_CMD_SET_OUTPUT_OPTIONS, dump);
        cJSON_free((void *)dump);
    }

    void D900Device::setUsbPortSelect(int port)
    {
        cJSON *rootJson = cJSON_CreateObject();
        cJSON_AddNumberToObject(rootJson, "port", port);
        const char *dump = cJSON_Print(rootJson);
        cJSON_Delete(rootJson);
        mControllerClient.sendRequest(D900_DEVICE_CMD_SET_USB_PORT_SELECT, dump);
        cJSON_free((void *)dump);
    }

    void D900Device::setDsdMute(int mode)
    {
        cJSON *rootJson = cJSON_CreateObject();
        cJSON_AddNumberToObject(rootJson, "mode", mode);
        const char *dump = cJSON_Print(rootJson);
        cJSON_Delete(rootJson);
        mControllerClient.sendRequest(D900_DEVICE_CMD_SET_DSD_MUTE, dump);
        cJSON_free((void *)dump);
    }

    void D900Device::setOutputLevel(int level)
    {
        cJSON *rootJson = cJSON_CreateObject();
        cJSON_AddNumberToObject(rootJson, "level", level);
        const char *dump = cJSON_Print(rootJson);
        cJSON_Delete(rootJson);
        mControllerClient.sendRequest(D900_DEVICE_CMD_SET_OUTPUT_LEVEL, dump);
        cJSON_free((void *)dump);
    }

    void D900Device::setVolumeStep(int step)
    {
        cJSON *rootJson = cJSON_CreateObject();
        cJSON_AddNumberToObject(rootJson, "step", step);
        const char *dump = cJSON_Print(rootJson);
        cJSON_Delete(rootJson);
        mControllerClient.sendRequest(D900_DEVICE_CMD_SET_VOLUME_STEP, dump);
        cJSON_free((void *)dump);
    }

    void D900Device::setPolarity(int polarity)
    {
        cJSON *rootJson = cJSON_CreateObject();
        cJSON_AddNumberToObject(rootJson, "polarity", polarity);
        const char *dump = cJSON_Print(rootJson);
        cJSON_Delete(rootJson);
        mControllerClient.sendRequest(D900_DEVICE_CMD_SET_POLARITY, dump);
        cJSON_free((void *)dump);
    }



    void D900Device::enableDsdDirect(bool enable)
    {
        cJSON *rootJson = cJSON_CreateObject();
        cJSON_AddBoolToObject(rootJson, "enable", enable);
        const char *dump = cJSON_Print(rootJson);
        cJSON_Delete(rootJson);
        mControllerClient.sendRequest(D900_DEVICE_CMD_ENABLE_DSD_DIRECT, dump);
        cJSON_free((void *)dump);
    }

    void D900Device::setVolumeMemoryMode(int mode)
    {
        cJSON *rootJson = cJSON_CreateObject();
        cJSON_AddNumberToObject(rootJson, "mode", mode);
        const char *dump = cJSON_Print(rootJson);
        cJSON_Delete(rootJson);
        mControllerClient.sendRequest(D900_DEVICE_CMD_SET_VOLUME_MEMORY_MODE, dump);
        cJSON_free((void *)dump);
    }

    void D900Device::setPeqMemoryMode(int mode)
    {
        cJSON *rootJson = cJSON_CreateObject();
        cJSON_AddNumberToObject(rootJson, "mode", mode);
        const char *dump = cJSON_Print(rootJson);
        cJSON_Delete(rootJson);
        mControllerClient.sendRequest(D900_DEVICE_CMD_SET_PEQ_MEMORY_MODE, dump);
        cJSON_free((void *)dump);
    }

    void D900Device::setMainKeyFunction(int function)
    {
        cJSON *rootJson = cJSON_CreateObject();
        cJSON_AddNumberToObject(rootJson, "function", function);
        const char *dump = cJSON_Print(rootJson);
        cJSON_Delete(rootJson);
        mControllerClient.sendRequest(D900_DEVICE_CMD_SET_MAIN_KEY_FUNCTION, dump);
        cJSON_free((void *)dump);
    }

    void D900Device::setRemoteAKeyFunction(int function)
    {
        cJSON *rootJson = cJSON_CreateObject();
        cJSON_AddNumberToObject(rootJson, "function", function);
        const char *dump = cJSON_Print(rootJson);
        cJSON_Delete(rootJson);
        mControllerClient.sendRequest(D900_DEVICE_CMD_SET_REMOTE_A_KEY_FUNCTION, dump);
        cJSON_free((void *)dump);
    }

    void D900Device::setRemoteBKeyFunction(int function)
    {
        cJSON *rootJson = cJSON_CreateObject();
        cJSON_AddNumberToObject(rootJson, "function", function);
        const char *dump = cJSON_Print(rootJson);
        cJSON_Delete(rootJson);
        mControllerClient.sendRequest(D900_DEVICE_CMD_SET_REMOTE_B_KEY_FUNCTION, dump);
        cJSON_free((void *)dump);
    }

    void D900Device::restoreFactorySettings()
    {
        mControllerClient.sendRequest(D900_DEVICE_CMD_RESTORE_FACTORY_SETTINGS, "{}");
    }

    void D900Device::requestSettings()
    {
        mControllerClient.sendRequest(D900_DEVICE_CMD_GET_SETTINGS, "{}");
    }

    void D900Device::onBatchScanResults(const std::vector<ControllerScanner::ScanResult> &results)
    {
        // for (auto result : results)
        // {
        //     TPPRINTF("name:%s, rssi:%d\n", result.device.getName().c_str(), result.rssi);
        // }
        d900_scan_result_t *res = new d900_scan_result_t[results.size()];
        size_t i = 0;
        for (; i < results.size(); i++)
        {
            ControllerScanner::ScanResult result = results[i];
            if (result.supportedDevice->vendorId == mSupportedDevice->vendorId && result.supportedDevice->productId == mSupportedDevice->productId)
            {
                res[i].name = (char *)result.device.getName().c_str();
                res[i].device = (long)result.device.getDevice();
                res[i].rssi = result.rssi;
            }
        }
        if (m_d900_device != nullptr)
        {
            m_d900_device->callback.on_scan_results(m_d900_device->flutter_object, res, i);
        }
        delete[] res;
    }

    void D900Device::onScanFailed(int errorCode)
    {
        TPPRINTF("onScanFailed; errorCode:%d\n", errorCode);
        if (m_d900_device != nullptr)
        {
            m_d900_device->callback.on_scan_failed(m_d900_device->flutter_object, errorCode);
        }
    }

    void D900Device::onStateChange(int state)
    {
        TPPRINTF("onStateChange; state:%d\n", state);
        if (m_d900_device != nullptr)
        {
            m_d900_device->callback.on_state_change(m_d900_device->flutter_object, state);
        }
    }

    void D900Device::onVerifyResult(int type)
    {
        TPPRINTF("onVerifyResult; type:%d\n", type);
        if (m_d900_device != nullptr)
        {
            m_d900_device->callback.on_verify_result(m_d900_device->flutter_object, type);
        }
    }

    void D900Device::onReceiveRequest(int session_id, int cmd, const std::string &msg)
    {
        parseRequest(session_id, cmd, msg);
    }

    void D900Device::onReceiveResponse(int cmd, int errcode, const std::string &errmsg, const std::string &msg)
    {
        TPPRINTF("onReceiveResponse\n");
        if (errcode != 0)
        {
            TPPRINTF("Receive error. errcode:%d; errmsg%s\n", errcode, errmsg.c_str());
            return;
        }
        parseResponse(cmd, msg);
    }

    void D900Device::parseRequest(int sessionId, int cmd, const std::string &data)
    {
    }

    void D900Device::parseResponse(int cmd, const std::string &data)
    {
        TPPRINTF("parseResponse; cmd:%d\n", cmd);
        switch (cmd)
        {
        case D900_DEVICE_CMD_POWER_ON:
            parseResponsePowerOn(data);
            break;
        case D900_DEVICE_CMD_SET_VOLUME:
            parseResponseSetVolume(data);
            break;
        case D900_DEVICE_CMD_SET_MUTE:
            parseResponseSetMute(data);
            break;
        case D900_DEVICE_CMD_SET_INPUT_TYPE:
            parseResponseSetInputType(data);
            break;
        case D900_DEVICE_CMD_SET_OUTPUT_TYPE:
            parseResponseSetOutputType(data);
            break;
        case D900_DEVICE_CMD_SET_DISPLAY_MODE:
            parseResponseSetDisplayMode(data);
            break;
        case D900_DEVICE_CMD_SET_THEME:
            parseResponseSetTheme(data);
            break;
        case D900_DEVICE_CMD_SET_POWER_TRIGGER:
            parseResponseSetPowerTrigger(data);
            break;
        case D900_DEVICE_CMD_USB_SELECT:
            parseResponseSetUsbSelect(data);
            break;
        case D900_DEVICE_CMD_SET_BALANCE:
            parseResponseSetBalance(data);
            break;
        case D900_DEVICE_CMD_ENABLE_AUDIO_BLUETOOTH:
            parseResponseEnableAudioBluetooth(data);
            break;
        case D900_DEVICE_CMD_ENABLE_BLUETOOTH_APTX:
            parseResponseEnableBluetoothAPTX(data);
            break;
        case D900_DEVICE_CMD_ENABLE_REMOTE:
            parseResponseEnableRemote(data);
            break;
        case D900_DEVICE_CMD_SET_MULTIFUNCTION_KEY:
            parseResponseSetMultifunctionKey(data);
            break;
        case D900_DEVICE_CMD_ENABLE_USB_DSD:
            parseResponseEnableUsbDsd(data);
            break;
        case D900_DEVICE_CMD_SET_USB_MODE:
            parseResponseSetUsbMode(data);
            break;
        case D900_DEVICE_CMD_SET_IIS_PHASE:
            parseResponseSetIisPhase(data);
            break;
        case D900_DEVICE_CMD_SET_IIS_CHANNEL:
            parseResponseSetIisChannel(data);
            break;
        case D900_DEVICE_CMD_SET_SCREEN_BRIGHTNESS:
            parseResponseSetScreenBrightness(data);
            break;
        case D900_DEVICE_CMD_SET_LANGUAGE:
            parseResponseSetLanguage(data);
            break;
        case D900_DEVICE_CMD_RESTORE_FACTORY_SETTINGS:
            parseResponseRestoreFactorySettings(data);
            break;
        case D900_DEVICE_CMD_GET_SETTINGS:
            parseResponseRequestSettings(data);
            break;
        default:
            TPPRINTF("This command %d is not supported\n", cmd);
            break;
        }
    }

    void D900Device::parseResponsePowerOn(const std::string &data)
    {
        cJSON *rootJson = cJSON_Parse(data.c_str());
        if (NULL == rootJson)
        {
            TPPRINTF("Message format error.\n");
            return;
        }
        try
        {
            if (!cJSON_IsObject(rootJson))
            {
                TPPRINTF("Message format error.\n");
                throw 0;
            }
            cJSON *isOnJson = cJSON_GetObjectItem(rootJson, "is_on");
            if (NULL == isOnJson)
            {
                TPPRINTF("Message format error.\n");
                throw 0;
            }
            cJSON_bool isOn = cJSON_IsTrue(isOnJson);
            if (m_d900_device != nullptr)
            {
                m_d900_device->callback.on_power_change(m_d900_device->flutter_object, isOn);
            }
        }
        catch(int& e)
        {
        }
        cJSON_Delete(rootJson);
    }

    void D900Device::parseResponseSetVolume(const std::string &data)
    {
        cJSON *rootJson = cJSON_Parse(data.c_str());
        if (NULL == rootJson)
        {
            TPPRINTF("Message format error.\n");
            return;
        }
        try
        {
            if (!cJSON_IsObject(rootJson))
            {
                TPPRINTF("Message format error.\n");
                throw 0;
            }
            cJSON *volumeJson = cJSON_GetObjectItem(rootJson, "volume");
            if (NULL == volumeJson)
            {
                TPPRINTF("Message format error.\n");
                throw 0;
            }
            int volume = (int)cJSON_GetNumberValue(volumeJson);
            if (m_d900_device != nullptr)
            {
                m_d900_device->callback.on_device_volume_change(m_d900_device->flutter_object, volume);
            }
        }
        catch(int& e)
        {
        }
        cJSON_Delete(rootJson);
    }

    void D900Device::parseResponseSetMute(const std::string &data)
    {
        cJSON *rootJson = cJSON_Parse(data.c_str());
        if (NULL == rootJson)
        {
            TPPRINTF("Message format error.\n");
            return;
        }
        try
        {
            if (!cJSON_IsObject(rootJson))
            {
                TPPRINTF("Message format error.\n");
                throw 0;
            }
            cJSON *isMuteJson = cJSON_GetObjectItem(rootJson, "is_mute");
            if (NULL == isMuteJson)
            {
                TPPRINTF("Message format error.\n");
                throw 0;
            }
            cJSON_bool isMute = cJSON_IsTrue(isMuteJson);
            if (m_d900_device != nullptr)
            {
                m_d900_device->callback.on_device_mute_change(m_d900_device->flutter_object, isMute);
            }
        }
        catch(int& e)
        {
        }
        cJSON_Delete(rootJson);
    }

    void D900Device::parseResponseSetInputType(const std::string &data)
    {
        cJSON *rootJson = cJSON_Parse(data.c_str());
        if (NULL == rootJson)
        {
            TPPRINTF("Message format error.\n");
            return;
        }
        try
        {
            if (!cJSON_IsObject(rootJson))
            {
                TPPRINTF("Message format error.\n");
                throw 0;
            }
            cJSON *inputTypeJson = cJSON_GetObjectItem(rootJson, "input_type");
            if (NULL == inputTypeJson)
            {
                TPPRINTF("Message format error.\n");
                throw 0;
            }
            int inputType = (int)cJSON_GetNumberValue(inputTypeJson);
            if (m_d900_device != nullptr)
            {
                m_d900_device->callback.on_device_input_type_change(m_d900_device->flutter_object, inputType);
            }
        }
        catch(int& e)
        {
        }
        cJSON_Delete(rootJson);
    }

    void D900Device::parseResponseSetOutputType(const std::string &data)
    {
        cJSON *rootJson = cJSON_Parse(data.c_str());
        if (NULL == rootJson)
        {
            TPPRINTF("Message format error.\n");
            return;
        }
        try
        {
            if (!cJSON_IsObject(rootJson))
            {
                TPPRINTF("Message format error.\n");
                throw 0;
            }
            cJSON *outputTypeJson = cJSON_GetObjectItem(rootJson, "output_type");
            if (NULL == outputTypeJson)
            {
                TPPRINTF("Message format error.\n");
                throw 0;
            }
            int outputType = (int)cJSON_GetNumberValue(outputTypeJson);
            if (m_d900_device != nullptr)
            {
                m_d900_device->callback.on_device_output_type_change(m_d900_device->flutter_object, outputType);
            }
        }
        catch(int& e)
        {
        }
        cJSON_Delete(rootJson);
    }

    void D900Device::parseResponseSetDisplayMode(const std::string &data)
    {
        cJSON *rootJson = cJSON_Parse(data.c_str());
        if (NULL == rootJson)
        {
            TPPRINTF("Message format error.\n");
            return;
        }
        try
        {
            if (!cJSON_IsObject(rootJson))
            {
                TPPRINTF("Message format error.\n");
                throw 0;
            }
            cJSON *displayModeJson = cJSON_GetObjectItem(rootJson, "display_mode");
            if (NULL == displayModeJson)
            {
                TPPRINTF("Message format error.\n");
                throw 0;
            }
            int display_mode = (int)cJSON_GetNumberValue(displayModeJson);
            if (m_d900_device != nullptr)
            {
                m_d900_device->callback.on_device_display_mode_change(m_d900_device->flutter_object, display_mode);
            }
        }
        catch(int& e)
        {
        }
        cJSON_Delete(rootJson);
    }

    void D900Device::parseResponseSetTheme(const std::string &data)
    {
        cJSON *rootJson = cJSON_Parse(data.c_str());
        if (NULL == rootJson)
        {
            TPPRINTF("Message format error.\n");
            return;
        }
        try
        {
            if (!cJSON_IsObject(rootJson))
            {
                TPPRINTF("Message format error.\n");
                throw 0;
            }
            cJSON *themeJson = cJSON_GetObjectItem(rootJson, "theme");
            if (NULL == themeJson)
            {
                TPPRINTF("Message format error.\n");
                throw 0;
            }
            int theme = (int)cJSON_GetNumberValue(themeJson);
            if (m_d900_device != nullptr)
            {
                m_d900_device->callback.on_device_theme_change(m_d900_device->flutter_object, theme);
            }
        }
        catch(int& e)
        {
        }
        cJSON_Delete(rootJson);
    }

    void D900Device::parseResponseSetPowerTrigger(const std::string &data)
    {
        cJSON *rootJson = cJSON_Parse(data.c_str());
        if (NULL == rootJson)
        {
            TPPRINTF("Message format error.\n");
            return;
        }
        try
        {
            if (!cJSON_IsObject(rootJson))
            {
                TPPRINTF("Message format error.\n");
                throw 0;
            }
            cJSON *triggerTypeJson = cJSON_GetObjectItem(rootJson, "trigger_type");
            if (NULL == triggerTypeJson)
            {
                TPPRINTF("Message format error.\n");
                throw 0;
            }
            int triggerType = (int)cJSON_GetNumberValue(triggerTypeJson);
            if (m_d900_device != nullptr)
            {
                m_d900_device->callback.on_device_power_trigger_change(m_d900_device->flutter_object, triggerType);
            }
        }
        catch(int& e)
        {
        }
        cJSON_Delete(rootJson);
    }

    void D900Device::parseResponseSetUsbSelect(const std::string &data)
    {
        cJSON *rootJson = cJSON_Parse(data.c_str());
        if (NULL == rootJson)
        {
            TPPRINTF("Message format error.\n");
            return;
        }
        try
        {
            if (!cJSON_IsObject(rootJson))
            {
                TPPRINTF("Message format error.\n");
                throw 0;
            }
            cJSON *typeJson = cJSON_GetObjectItem(rootJson, "type");
            if (NULL == typeJson)
            {
                TPPRINTF("Message format error.\n");
                throw 0;
            }
            int type = (int)cJSON_GetNumberValue(typeJson);
            if (m_d900_device != nullptr)
            {
                m_d900_device->callback.on_device_usb_select_change(m_d900_device->flutter_object, type);
            }
        }
        catch(int& e)
        {
        }
        cJSON_Delete(rootJson);
    }

    void D900Device::parseResponseSetBalance(const std::string &data)
    {
        cJSON *rootJson = cJSON_Parse(data.c_str());
        if (NULL == rootJson)
        {
            TPPRINTF("Message format error.\n");
            return;
        }
        try
        {
            if (!cJSON_IsObject(rootJson))
            {
                TPPRINTF("Message format error.\n");
                throw 0;
            }
            cJSON *balanceJson = cJSON_GetObjectItem(rootJson, "balance");
            if (NULL == balanceJson)
            {
                TPPRINTF("Message format error.\n");
                throw 0;
            }
            int balance = (int)cJSON_GetNumberValue(balanceJson);
            if (m_d900_device != nullptr)
            {
                m_d900_device->callback.on_device_balance_change(m_d900_device->flutter_object, balance);
            }
        }
        catch(int& e)
        {
        }
        cJSON_Delete(rootJson);
    }

    void D900Device::parseResponseEnableAudioBluetooth(const std::string &data)
    {
        cJSON *rootJson = cJSON_Parse(data.c_str());
        if (NULL == rootJson)
        {
            TPPRINTF("Message format error.\n");
            return;
        }
        try
        {
            if (!cJSON_IsObject(rootJson))
            {
                TPPRINTF("Message format error.\n");
                throw 0;
            }
            cJSON *enableJson = cJSON_GetObjectItem(rootJson, "enable");
            if (NULL == enableJson)
            {
                TPPRINTF("Message format error.\n");
                throw 0;
            }
            cJSON_bool enable = cJSON_IsTrue(enableJson);
            if (m_d900_device != nullptr)
            {
                m_d900_device->callback.on_device_enable_audio_bluetooth(m_d900_device->flutter_object, enable);
            }
        }
        catch(int& e)
        {
        }
        cJSON_Delete(rootJson);
    }

    void D900Device::parseResponseEnableBluetoothAPTX(const std::string &data)
    {
        cJSON *rootJson = cJSON_Parse(data.c_str());
        if (NULL == rootJson)
        {
            TPPRINTF("Message format error.\n");
            return;
        }
        try
        {
            if (!cJSON_IsObject(rootJson))
            {
                TPPRINTF("Message format error.\n");
                throw 0;
            }
            cJSON *enableJson = cJSON_GetObjectItem(rootJson, "enable");
            if (NULL == enableJson)
            {
                TPPRINTF("Message format error.\n");
                throw 0;
            }
            cJSON_bool enable = cJSON_IsTrue(enableJson);
            if (m_d900_device != nullptr)
            {
                m_d900_device->callback.on_device_enable_bluetooth_aptx(m_d900_device->flutter_object, enable);
            }
        }
        catch(int& e)
        {
        }
        cJSON_Delete(rootJson);
    }

    void D900Device::parseResponseEnableRemote(const std::string &data)
    {
        cJSON *rootJson = cJSON_Parse(data.c_str());
        if (NULL == rootJson)
        {
            TPPRINTF("Message format error.\n");
            return;
        }
        try
        {
            if (!cJSON_IsObject(rootJson))
            {
                TPPRINTF("Message format error.\n");
                throw 0;
            }
            cJSON *enableJson = cJSON_GetObjectItem(rootJson, "enable");
            if (NULL == enableJson)
            {
                TPPRINTF("Message format error.\n");
                throw 0;
            }
            cJSON_bool enable = cJSON_IsTrue(enableJson);
            if (m_d900_device != nullptr)
            {
                m_d900_device->callback.on_device_enable_remote(m_d900_device->flutter_object, enable);
            }
        }
        catch(int& e)
        {
        }
        cJSON_Delete(rootJson);
    }

    void D900Device::parseResponseSetMultifunctionKey(const std::string &data)
    {
        cJSON *rootJson = cJSON_Parse(data.c_str());
        if (NULL == rootJson)
        {
            TPPRINTF("Message format error.\n");
            return;
        }
        try
        {
            if (!cJSON_IsObject(rootJson))
            {
                TPPRINTF("Message format error.\n");
                throw 0;
            }
            cJSON *keyTypeJson = cJSON_GetObjectItem(rootJson, "key_type");
            if (NULL == keyTypeJson)
            {
                TPPRINTF("Message format error.\n");
                throw 0;
            }
            int keyType = (int)cJSON_GetNumberValue(keyTypeJson);
            if (m_d900_device != nullptr)
            {
                m_d900_device->callback.on_device_multifunction_key_change(m_d900_device->flutter_object, keyType);
            }
        }
        catch(int& e)
        {
        }
        cJSON_Delete(rootJson);
    }

    void D900Device::parseResponseEnableUsbDsd(const std::string &data)
    {
        cJSON *rootJson = cJSON_Parse(data.c_str());
        if (NULL == rootJson)
        {
            TPPRINTF("Message format error.\n");
            return;
        }
        try
        {
            if (!cJSON_IsObject(rootJson))
            {
                TPPRINTF("Message format error.\n");
                throw 0;
            }
            cJSON *enableJson = cJSON_GetObjectItem(rootJson, "enable");
            if (NULL == enableJson)
            {
                TPPRINTF("Message format error.\n");
                throw 0;
            }
            cJSON_bool enable = cJSON_IsTrue(enableJson);
            if (m_d900_device != nullptr)
            {
                m_d900_device->callback.on_device_enable_usb_dsd(m_d900_device->flutter_object, enable);
            }
        }
        catch(int& e)
        {
        }
        cJSON_Delete(rootJson);
    }

    void D900Device::parseResponseSetUsbMode(const std::string &data)
    {
        cJSON *rootJson = cJSON_Parse(data.c_str());
        if (NULL == rootJson)
        {
            TPPRINTF("Message format error.\n");
            return;
        }
        try
        {
            if (!cJSON_IsObject(rootJson))
            {
                TPPRINTF("Message format error.\n");
                throw 0;
            }
            cJSON *usbModeJson = cJSON_GetObjectItem(rootJson, "usb_mode");
            if (NULL == usbModeJson)
            {
                TPPRINTF("Message format error.\n");
                throw 0;
            }
            int usbMode = (int)cJSON_GetNumberValue(usbModeJson);
            if (m_d900_device != nullptr)
            {
                m_d900_device->callback.on_device_usb_mode_change(m_d900_device->flutter_object, usbMode);
            }
        }
        catch(int& e)
        {
        }
        cJSON_Delete(rootJson);
    }

    void D900Device::parseResponseSetIisPhase(const std::string &data)
    {
        cJSON *rootJson = cJSON_Parse(data.c_str());
        if (NULL == rootJson)
        {
            TPPRINTF("Message format error.\n");
            return;
        }
        try
        {
            if (!cJSON_IsObject(rootJson))
            {
                TPPRINTF("Message format error.\n");
                throw 0;
            }
            cJSON *phaseJson = cJSON_GetObjectItem(rootJson, "phase");
            if (NULL == phaseJson)
            {
                TPPRINTF("Message format error.\n");
                throw 0;
            }
            int phase = (int)cJSON_GetNumberValue(phaseJson);
            if (m_d900_device != nullptr)
            {
                m_d900_device->callback.on_device_iis_phase_change(m_d900_device->flutter_object, phase);
            }
        }
        catch(int& e)
        {
        }
        cJSON_Delete(rootJson);
    }

    void D900Device::parseResponseSetIisChannel(const std::string &data)
    {
        cJSON *rootJson = cJSON_Parse(data.c_str());
        if (NULL == rootJson)
        {
            TPPRINTF("Message format error.\n");
            return;
        }
        try
        {
            if (!cJSON_IsObject(rootJson))
            {
                TPPRINTF("Message format error.\n");
                throw 0;
            }
            cJSON *channelJson = cJSON_GetObjectItem(rootJson, "channel");
            if (NULL == channelJson)
            {
                TPPRINTF("Message format error.\n");
                throw 0;
            }
            int channel = (int)cJSON_GetNumberValue(channelJson);
            if (m_d900_device != nullptr)
            {
                m_d900_device->callback.on_device_iis_channel_change(m_d900_device->flutter_object, channel);
            }
        }
        catch(int& e)
        {
        }
        cJSON_Delete(rootJson);
    }

    void D900Device::parseResponseSetScreenBrightness(const std::string &data)
    {
        cJSON *rootJson = cJSON_Parse(data.c_str());
        if (NULL == rootJson)
        {
            TPPRINTF("Message format error.\n");
            return;
        }
        try
        {
            if (!cJSON_IsObject(rootJson))
            {
                TPPRINTF("Message format error.\n");
                throw 0;
            }
            cJSON *brightnessTypeJson = cJSON_GetObjectItem(rootJson, "brightness_type");
            if (NULL == brightnessTypeJson)
            {
                TPPRINTF("Message format error.\n");
                throw 0;
            }
            int brightnessType = (int)cJSON_GetNumberValue(brightnessTypeJson);
            if (m_d900_device != nullptr)
            {
                m_d900_device->callback.on_device_screen_brightness_change(m_d900_device->flutter_object, brightnessType);
            }
        }
        catch(int& e)
        {
        }
        cJSON_Delete(rootJson);
    }

    void D900Device::parseResponseSetLanguage(const std::string &data)
    {
        cJSON *rootJson = cJSON_Parse(data.c_str());
        if (NULL == rootJson)
        {
            TPPRINTF("Message format error.\n");
            return;
        }
        try
        {
            if (!cJSON_IsObject(rootJson))
            {
                TPPRINTF("Message format error.\n");
                throw 0;
            }
            cJSON *languageJson = cJSON_GetObjectItem(rootJson, "language");
            if (NULL == languageJson)
            {
                TPPRINTF("Message format error.\n");
                throw 0;
            }
            int language = (int)cJSON_GetNumberValue(languageJson);
            if (m_d900_device != nullptr)
            {
                m_d900_device->callback.on_device_language_change(m_d900_device->flutter_object, language);
            }
        }
        catch(int& e)
        {
        }
        cJSON_Delete(rootJson);
    }

    void D900Device::parseResponseRestoreFactorySettings(const std::string &data)
    {
        if (m_d900_device != nullptr)
        {
            m_d900_device->callback.on_device_restore_factory_settings(m_d900_device->flutter_object);
        }
    }



    void D900Device::parseResponseRequestSettings(const std::string &data)
    {
        TPPRINTF("parseResponseRequestSettings: %s\n", data.c_str());
        cJSON *rootJson = cJSON_Parse(data.c_str());
        if (NULL == rootJson)
        {
            TPPRINTF("Message format error.\n");
            return;
        }
        try
        {
            if (!cJSON_IsObject(rootJson))
            {
                TPPRINTF("Message format error.\n");
                throw 0;
            }
            d900_settings_t d900_settings;
            cJSON *isOnJson = cJSON_GetObjectItem(rootJson, "is_on");
            if (NULL == isOnJson)
            {
                TPPRINTF("Message format error.\n");
                throw 0;
            }
            d900_settings.is_on = (int)cJSON_IsTrue(isOnJson);

            cJSON *deviceNameJson = cJSON_GetObjectItem(rootJson, "device_name");
            if (NULL == deviceNameJson)
            {
                TPPRINTF("Message format error.\n");
                throw 0;
            }
            strcpy(d900_settings.device_name, cJSON_GetStringValue(deviceNameJson));

            cJSON *volumeJson = cJSON_GetObjectItem(rootJson, "volume");
            if (NULL == volumeJson)
            {
                TPPRINTF("Message format error.\n");
                throw 0;
            }
            d900_settings.volume = (int)cJSON_GetNumberValue(volumeJson);

            cJSON *isMuteJson = cJSON_GetObjectItem(rootJson, "is_mute");
            if (NULL == isMuteJson)
            {
                TPPRINTF("Message format error.\n");
                throw 0;
            }
            d900_settings.is_mute = (int)cJSON_IsTrue(isMuteJson);

            cJSON *inputTypeJson = cJSON_GetObjectItem(rootJson, "input_type");
            if (NULL == inputTypeJson)
            {
                TPPRINTF("Message format error.\n");
                throw 0;
            }
            d900_settings.input_type = (int)cJSON_GetNumberValue(inputTypeJson);

            cJSON *outputTypeJson = cJSON_GetObjectItem(rootJson, "output_type");
            if (NULL == outputTypeJson)
            {
                TPPRINTF("Message format error.\n");
                throw 0;
            }
            d900_settings.output_type = (int)cJSON_GetNumberValue(outputTypeJson);

            cJSON *displayModeJson = cJSON_GetObjectItem(rootJson, "display_mode");
            if (NULL == displayModeJson)
            {
                TPPRINTF("Message format error.\n");
                throw 0;
            }
            d900_settings.display_mode = (int)cJSON_GetNumberValue(displayModeJson);

            cJSON *themeJson = cJSON_GetObjectItem(rootJson, "theme");
            if (NULL == themeJson)
            {
                TPPRINTF("Message format error.\n");
                throw 0;
            }
            d900_settings.theme = (int)cJSON_GetNumberValue(themeJson);

            cJSON *powerTriggerJson = cJSON_GetObjectItem(rootJson, "power_trigger");
            if (NULL == powerTriggerJson)
            {
                TPPRINTF("Message format error.\n");
                throw 0;
            }
            d900_settings.power_trigger = (int)cJSON_GetNumberValue(powerTriggerJson);

            cJSON *usbSelectJson = cJSON_GetObjectItem(rootJson, "usb_select");
            if (NULL == usbSelectJson)
            {
                TPPRINTF("Message format error.\n");
                throw 0;
            }
            d900_settings.usb_select = (int)cJSON_GetNumberValue(usbSelectJson);

            cJSON *balanceJson = cJSON_GetObjectItem(rootJson, "balance");
            if (NULL == balanceJson)
            {
                TPPRINTF("Message format error.\n");
                throw 0;
            }
            d900_settings.balance = (int)cJSON_GetNumberValue(balanceJson);

            cJSON *audioBtEnableJson = cJSON_GetObjectItem(rootJson, "audio_bt_enable");
            if (NULL == audioBtEnableJson)
            {
                TPPRINTF("Message format error.\n");
                throw 0;
            }
            d900_settings.audio_bt_enable = (int)cJSON_IsTrue(audioBtEnableJson);

            cJSON *aptxEnableJson = cJSON_GetObjectItem(rootJson, "aptx_enable");
            if (NULL == aptxEnableJson)
            {
                TPPRINTF("Message format error.\n");
                throw 0;
            }
            d900_settings.aptx_enable = (int)cJSON_IsTrue(aptxEnableJson);

            cJSON *remoteEnableJson = cJSON_GetObjectItem(rootJson, "remote_enable");
            if (NULL == remoteEnableJson)
            {
                TPPRINTF("Message format error.\n");
                throw 0;
            }
            d900_settings.remote_enable = (int)cJSON_IsTrue(remoteEnableJson);

            cJSON *multifunctionKeyJson = cJSON_GetObjectItem(rootJson, "multifunction_key");
            if (NULL == multifunctionKeyJson)
            {
                TPPRINTF("Message format error.\n");
                throw 0;
            }
            d900_settings.multifunction_key = (int)cJSON_GetNumberValue(multifunctionKeyJson);

            cJSON *usbDsdEnableJson = cJSON_GetObjectItem(rootJson, "usb_dsd_enable");
            if (NULL == usbDsdEnableJson)
            {
                TPPRINTF("Message format error.\n");
                throw 0;
            }
            d900_settings.usb_dsd_enable = (int)cJSON_IsTrue(usbDsdEnableJson);

            cJSON *usbModeJson = cJSON_GetObjectItem(rootJson, "usb_mode");
            if (NULL == usbModeJson)
            {
                TPPRINTF("Message format error.\n");
                throw 0;
            }
            d900_settings.usb_mode = (int)cJSON_GetNumberValue(usbModeJson);

            cJSON *iisPhaseJson = cJSON_GetObjectItem(rootJson, "iis_phase");
            if (NULL == iisPhaseJson)
            {
                TPPRINTF("Message format error.\n");
                throw 0;
            }
            d900_settings.iis_phase = (int)cJSON_GetNumberValue(iisPhaseJson);

            cJSON *iisChannelJson = cJSON_GetObjectItem(rootJson, "iis_channel");
            if (NULL == iisChannelJson)
            {
                TPPRINTF("Message format error.\n");
                throw 0;
            }
            d900_settings.iis_channel = (int)cJSON_GetNumberValue(iisChannelJson);

            cJSON *screenBrightnessJson = cJSON_GetObjectItem(rootJson, "screen_brightness");
            if (NULL == screenBrightnessJson)
            {
                TPPRINTF("Message format error.\n");
                throw 0;
            }
            d900_settings.screen_brightness = (int)cJSON_GetNumberValue(screenBrightnessJson);

            cJSON *languageJson = cJSON_GetObjectItem(rootJson, "language");
            if (NULL == languageJson)
            {
                TPPRINTF("Message format error.\n");
                throw 0;
            }
            d900_settings.language = (int)cJSON_GetNumberValue(languageJson);

            TPPRINTF("language: %p\n", languageJson);
            cJSON *samplingJson = cJSON_GetObjectItem(rootJson, "sampling");
            TPPRINTF("samplingJson: %p\n", samplingJson);
            if (NULL == samplingJson)
            {
                TPPRINTF("Message format error.\n");
                throw 0;
            }
            d900_settings.sampling = (int)cJSON_GetNumberValue(samplingJson);

            if (m_d900_device != nullptr)
            {
                m_d900_device->callback.on_device_settings_response(m_d900_device->flutter_object, &d900_settings);
            }
        }
        catch(int& e)
        {
        }
        cJSON_Delete(rootJson);
    }

    void D900Device::responseCmdError(int session_id, int cmd)
    {
        mControllerClient.sendResponse(session_id, cmd, ERRCODE_CMD_UNSUPPORTED, "This command is not supported.", "{}");
    }
}

extern "C"
{
    long d900_device_create(long flutter_object)
    {
        d900_device_t *d900_device = new d900_device_t;
        Topping::D900Device *d900Device = new Topping::D900Device(d900_device);
        d900_device->handle = d900Device;
        d900_device->flutter_object = flutter_object;
        return (long)d900_device;
    }

    void d900_device_destory(long native_object)
    {
        struct d900_device_t *d900_device = (struct d900_device_t *)native_object;
        if (d900_device != nullptr)
        {
            Topping::D900Device *d900Device = (Topping::D900Device *)d900_device->handle;
            if (d900Device != nullptr)
            {
                delete d900Device;
            }
            delete d900_device;
        }
    }

    void d900_device_register_callback(long native_object, struct d900_device_callback_t *callback)
    {
        struct d900_device_t *d900_device = (struct d900_device_t *)native_object;
        if (d900_device != nullptr)
        {
            d900_device->callback = *callback;
        }
    }

    void d900_device_startScan(long native_object)
    {
        struct d900_device_t *d900_device = (struct d900_device_t *)native_object;
        if (nullptr == d900_device)
        {
            return;
        }
        Topping::D900Device *d900Device = (Topping::D900Device *)d900_device->handle;
        if (d900Device != nullptr)
        {
            d900Device->startScan();
        }
    }

    void d900_device_stopScan(long native_object)
    {
        struct d900_device_t *d900_device = (struct d900_device_t *)native_object;
        if (nullptr == d900_device)
        {
            return;
        }
        Topping::D900Device *d900Device = (Topping::D900Device *)d900_device->handle;
        if (d900Device != nullptr)
        {
            d900Device->stopScan();
        }
    }

    void d900_device_connect(long native_object, long device)
    {
        struct d900_device_t *d900_device = (struct d900_device_t *)native_object;
        if (nullptr == d900_device)
        {
            return;
        }
        Topping::D900Device *d900Device = (Topping::D900Device *)d900_device->handle;
        if (d900Device != nullptr)
        {
            Topping::BluetoothDevice bluetoothDevice((void *)device);
            d900Device->connect(bluetoothDevice);
        }
    }

    void d900_device_disconnect(long native_object)
    {
        struct d900_device_t *d900_device = (struct d900_device_t *)native_object;
        if (nullptr == d900_device)
        {
            return;
        }
        Topping::D900Device *d900Device = (Topping::D900Device *)d900_device->handle;
        if (d900Device != nullptr)
        {
            d900Device->disconnect();
        }
    }

    void d900_device_verify(long native_object)
    {
        struct d900_device_t *d900_device = (struct d900_device_t *)native_object;
        if (nullptr == d900_device)
        {
            return;
        }
        Topping::D900Device *d900Device = (Topping::D900Device *)d900_device->handle;
        if (d900Device != nullptr)
        {
            d900Device->verify();
        }
    }

    void d900_device_power_on(long native_object, int is_on)
    {
        struct d900_device_t *d900_device = (struct d900_device_t *)native_object;
        if (nullptr == d900_device)
        {
            return;
        }
        Topping::D900Device *d900Device = (Topping::D900Device *)d900_device->handle;
        if (d900Device != nullptr)
        {
            d900Device->powerOn(is_on);
        }
    }

    void d900_device_set_volume(long native_object, int volume)
    {
        struct d900_device_t *d900_device = (struct d900_device_t *)native_object;
        if (nullptr == d900_device)
        {
            return;
        }
        Topping::D900Device *d900Device = (Topping::D900Device *)d900_device->handle;
        if (d900Device != nullptr)
        {
            d900Device->setVolume(volume);
        }
    }

    void d900_device_set_mute(long native_object, int is_mute)
    {
        struct d900_device_t *d900_device = (struct d900_device_t *)native_object;
        if (nullptr == d900_device)
        {
            return;
        }
        Topping::D900Device *d900Device = (Topping::D900Device *)d900_device->handle;
        if (d900Device != nullptr)
        {
            d900Device->setMute(is_mute);
        }
    }

    void d900_device_set_input_type(long native_object, int input_type)
    {
        struct d900_device_t *d900_device = (struct d900_device_t *)native_object;
        if (nullptr == d900_device)
        {
            return;
        }
        Topping::D900Device *d900Device = (Topping::D900Device *)d900_device->handle;
        if (d900Device != nullptr)
        {
            d900Device->setInputType(input_type);
        }
    }

    void d900_device_set_output_type(long native_object, int output_type)
    {
        struct d900_device_t *d900_device = (struct d900_device_t *)native_object;
        if (nullptr == d900_device)
        {
            return;
        }
        Topping::D900Device *d900Device = (Topping::D900Device *)d900_device->handle;
        if (d900Device != nullptr)
        {
            d900Device->setOutputType(output_type);
        }
    }

    void d900_device_set_diaplay_mode(long native_object, int diaplay_mode)
    {
        struct d900_device_t *d900_device = (struct d900_device_t *)native_object;
        if (nullptr == d900_device)
        {
            return;
        }
        Topping::D900Device *d900Device = (Topping::D900Device *)d900_device->handle;
        if (d900Device != nullptr)
        {
            d900Device->setDisplayMode(diaplay_mode);
        }
    }

    void d900_device_set_theme(long native_object, int theme)
    {
        struct d900_device_t *d900_device = (struct d900_device_t *)native_object;
        if (nullptr == d900_device)
        {
            return;
        }
        Topping::D900Device *d900Device = (Topping::D900Device *)d900_device->handle;
        if (d900Device != nullptr)
        {
            d900Device->setTheme(theme);
        }
    }

    void d900_device_set_power_trigger(long native_object, int trigger_type)
    {
        struct d900_device_t *d900_device = (struct d900_device_t *)native_object;
        if (nullptr == d900_device)
        {
            return;
        }
        Topping::D900Device *d900Device = (Topping::D900Device *)d900_device->handle;
        if (d900Device != nullptr)
        {
            d900Device->setPowerTrigger(trigger_type);
        }
    }

    void d900_device_set_usb_select(long native_object, int type)
    {
        struct d900_device_t *d900_device = (struct d900_device_t *)native_object;
        if (nullptr == d900_device)
        {
            return;
        }
        Topping::D900Device *d900Device = (Topping::D900Device *)d900_device->handle;
        if (d900Device != nullptr)
        {
            d900Device->setUsbSelect(type);
        }
    }

    void d900_device_set_balance(long native_object, int balance)
    {
        struct d900_device_t *d900_device = (struct d900_device_t *)native_object;
        if (nullptr == d900_device)
        {
            return;
        }
        Topping::D900Device *d900Device = (Topping::D900Device *)d900_device->handle;
        if (d900Device != nullptr)
        {
            d900Device->setBalance(balance);
        }
    }

    void d900_device_enable_audio_bluetooth(long native_object, int enable)
    {
        struct d900_device_t *d900_device = (struct d900_device_t *)native_object;
        if (nullptr == d900_device)
        {
            return;
        }
        Topping::D900Device *d900Device = (Topping::D900Device *)d900_device->handle;
        if (d900Device != nullptr)
        {
            d900Device->enableAudioBluetooth(enable);
        }
    }

    void d900_device_enable_bluetooth_aptx(long native_object, int enable)
    {
        struct d900_device_t *d900_device = (struct d900_device_t *)native_object;
        if (nullptr == d900_device)
        {
            return;
        }
        Topping::D900Device *d900Device = (Topping::D900Device *)d900_device->handle;
        if (d900Device != nullptr)
        {
            d900Device->enableBluetoothAPTX(enable);
        }
    }

    void d900_device_enable_remote(long native_object, int enable)
    {
        struct d900_device_t *d900_device = (struct d900_device_t *)native_object;
        if (nullptr == d900_device)
        {
            return;
        }
        Topping::D900Device *d900Device = (Topping::D900Device *)d900_device->handle;
        if (d900Device != nullptr)
        {
            d900Device->enableRemote(enable);
        }
    }

    void d900_device_set_multifunction_key(long native_object, int key_type)
    {
        struct d900_device_t *d900_device = (struct d900_device_t *)native_object;
        if (nullptr == d900_device)
        {
            return;
        }
        Topping::D900Device *d900Device = (Topping::D900Device *)d900_device->handle;
        if (d900Device != nullptr)
        {
            d900Device->setMultifunctionKey(key_type);
        }
    }

    void d900_device_enebla_usb_dsd(long native_object, int enable)
    {
        struct d900_device_t *d900_device = (struct d900_device_t *)native_object;
        if (nullptr == d900_device)
        {
            return;
        }
        Topping::D900Device *d900Device = (Topping::D900Device *)d900_device->handle;
        if (d900Device != nullptr)
        {
            d900Device->enableUsbDsd(enable);
        }
    }

    void d900_device_set_usb_mode(long native_object, int usb_mode)
    {
        struct d900_device_t *d900_device = (struct d900_device_t *)native_object;
        if (nullptr == d900_device)
        {
            return;
        }
        Topping::D900Device *d900Device = (Topping::D900Device *)d900_device->handle;
        if (d900Device != nullptr)
        {
            d900Device->setUsbMode(usb_mode);
        }
    }

    void d900_device_set_iis_phase(long native_object, int phase)
    {
        struct d900_device_t *d900_device = (struct d900_device_t *)native_object;
        if (nullptr == d900_device)
        {
            return;
        }
        Topping::D900Device *d900Device = (Topping::D900Device *)d900_device->handle;
        if (d900Device != nullptr)
        {
            d900Device->setIisPhase(phase);
        }
    }

    void d900_device_set_iis_channel(long native_object, int channel)
    {
        struct d900_device_t *d900_device = (struct d900_device_t *)native_object;
        if (nullptr == d900_device)
        {
            return;
        }
        Topping::D900Device *d900Device = (Topping::D900Device *)d900_device->handle;
        if (d900Device != nullptr)
        {
            d900Device->setIisChannel(channel);
        }
    }

    void d900_device_set_screen_brightness(long native_object, int brightness_type)
    {
        struct d900_device_t *d900_device = (struct d900_device_t *)native_object;
        if (nullptr == d900_device)
        {
            return;
        }
        Topping::D900Device *d900Device = (Topping::D900Device *)d900_device->handle;
        if (d900Device != nullptr)
        {
            d900Device->setScreenBrightness(brightness_type);
        }
    }

    void d900_device_set_language(long native_object, int language)
    {
        struct d900_device_t *d900_device = (struct d900_device_t *)native_object;
        if (nullptr == d900_device)
        {
            return;
        }
        Topping::D900Device *d900Device = (Topping::D900Device *)d900_device->handle;
        if (d900Device != nullptr)
        {
            d900Device->setLanguage(language);
        }
    }

    void d900_device_restore_factory_settings(long native_object)
    {
        struct d900_device_t *d900_device = (struct d900_device_t *)native_object;
        if (nullptr == d900_device)
        {
            return;
        }
        Topping::D900Device *d900Device = (Topping::D900Device *)d900_device->handle;
        if (d900Device != nullptr)
        {
            d900Device->restoreFactorySettings();
        }
    }

    void d900_device_request_settings(long native_object)
    {
        struct d900_device_t *d900_device = (struct d900_device_t *)native_object;
        if (nullptr == d900_device)
        {
            return;
        }
        Topping::D900Device *d900Device = (Topping::D900Device *)d900_device->handle;
        if (d900Device != nullptr)
        {
            d900Device->requestSettings();
        }
    }

    // --- 新增C接口函数实现 (D900新版功能) ---
    void d900_device_set_vu_meter_level(long native_object, int level)
    {
        struct d900_device_t *d900_device = (struct d900_device_t *)native_object;
        if (nullptr == d900_device)
        {
            return;
        }
        Topping::D900Device *d900Device = (Topping::D900Device *)d900_device->handle;
        if (d900Device != nullptr)
        {
            d900Device->setVuMeterLevel(level);
        }
    }

    void d900_device_set_vu_meter_display_mode(long native_object, int mode)
    {
        struct d900_device_t *d900_device = (struct d900_device_t *)native_object;
        if (nullptr == d900_device)
        {
            return;
        }
        Topping::D900Device *d900Device = (Topping::D900Device *)d900_device->handle;
        if (d900Device != nullptr)
        {
            d900Device->setVuMeterDisplayMode(mode);
        }
    }

    void d900_device_set_input_options(long native_object, int options)
    {
        struct d900_device_t *d900_device = (struct d900_device_t *)native_object;
        if (nullptr == d900_device)
        {
            return;
        }
        Topping::D900Device *d900Device = (Topping::D900Device *)d900_device->handle;
        if (d900Device != nullptr)
        {
            d900Device->setInputOptions(options);
        }
    }

    void d900_device_set_output_options(long native_object, int options)
    {
        struct d900_device_t *d900_device = (struct d900_device_t *)native_object;
        if (nullptr == d900_device)
        {
            return;
        }
        Topping::D900Device *d900Device = (Topping::D900Device *)d900_device->handle;
        if (d900Device != nullptr)
        {
            d900Device->setOutputOptions(options);
        }
    }

    void d900_device_set_usb_port_select(long native_object, int port)
    {
        struct d900_device_t *d900_device = (struct d900_device_t *)native_object;
        if (nullptr == d900_device)
        {
            return;
        }
        Topping::D900Device *d900Device = (Topping::D900Device *)d900_device->handle;
        if (d900Device != nullptr)
        {
            d900Device->setUsbPortSelect(port);
        }
    }

    void d900_device_set_dsd_mute(long native_object, int mode)
    {
        struct d900_device_t *d900_device = (struct d900_device_t *)native_object;
        if (nullptr == d900_device)
        {
            return;
        }
        Topping::D900Device *d900Device = (Topping::D900Device *)d900_device->handle;
        if (d900Device != nullptr)
        {
            d900Device->setDsdMute(mode);
        }
    }

    void d900_device_set_output_level(long native_object, int level)
    {
        struct d900_device_t *d900_device = (struct d900_device_t *)native_object;
        if (nullptr == d900_device)
        {
            return;
        }
        Topping::D900Device *d900Device = (Topping::D900Device *)d900_device->handle;
        if (d900Device != nullptr)
        {
            d900Device->setOutputLevel(level);
        }
    }

    void d900_device_set_volume_step(long native_object, int step)
    {
        struct d900_device_t *d900_device = (struct d900_device_t *)native_object;
        if (nullptr == d900_device)
        {
            return;
        }
        Topping::D900Device *d900Device = (Topping::D900Device *)d900_device->handle;
        if (d900Device != nullptr)
        {
            d900Device->setVolumeStep(step);
        }
    }

    void d900_device_set_polarity(long native_object, int polarity)
    {
        struct d900_device_t *d900_device = (struct d900_device_t *)native_object;
        if (nullptr == d900_device)
        {
            return;
        }
        Topping::D900Device *d900Device = (Topping::D900Device *)d900_device->handle;
        if (d900Device != nullptr)
        {
            d900Device->setPolarity(polarity);
        }
    }



    void d900_device_enable_dsd_direct(long native_object, int enable)
    {
        struct d900_device_t *d900_device = (struct d900_device_t *)native_object;
        if (nullptr == d900_device)
        {
            return;
        }
        Topping::D900Device *d900Device = (Topping::D900Device *)d900_device->handle;
        if (d900Device != nullptr)
        {
            d900Device->enableDsdDirect(enable);
        }
    }

    void d900_device_set_volume_memory_mode(long native_object, int mode)
    {
        struct d900_device_t *d900_device = (struct d900_device_t *)native_object;
        if (nullptr == d900_device)
        {
            return;
        }
        Topping::D900Device *d900Device = (Topping::D900Device *)d900_device->handle;
        if (d900Device != nullptr)
        {
            d900Device->setVolumeMemoryMode(mode);
        }
    }

    void d900_device_set_peq_memory_mode(long native_object, int mode)
    {
        struct d900_device_t *d900_device = (struct d900_device_t *)native_object;
        if (nullptr == d900_device)
        {
            return;
        }
        Topping::D900Device *d900Device = (Topping::D900Device *)d900_device->handle;
        if (d900Device != nullptr)
        {
            d900Device->setPeqMemoryMode(mode);
        }
    }

    void d900_device_set_main_key_function(long native_object, int function)
    {
        struct d900_device_t *d900_device = (struct d900_device_t *)native_object;
        if (nullptr == d900_device)
        {
            return;
        }
        Topping::D900Device *d900Device = (Topping::D900Device *)d900_device->handle;
        if (d900Device != nullptr)
        {
            d900Device->setMainKeyFunction(function);
        }
    }

    void d900_device_set_remote_a_key_function(long native_object, int function)
    {
        struct d900_device_t *d900_device = (struct d900_device_t *)native_object;
        if (nullptr == d900_device)
        {
            return;
        }
        Topping::D900Device *d900Device = (Topping::D900Device *)d900_device->handle;
        if (d900Device != nullptr)
        {
            d900Device->setRemoteAKeyFunction(function);
        }
    }

    void d900_device_set_remote_b_key_function(long native_object, int function)
    {
        struct d900_device_t *d900_device = (struct d900_device_t *)native_object;
        if (nullptr == d900_device)
        {
            return;
        }
        Topping::D900Device *d900Device = (Topping::D900Device *)d900_device->handle;
        if (d900Device != nullptr)
        {
            d900Device->setRemoteBKeyFunction(function);
        }
    }
}