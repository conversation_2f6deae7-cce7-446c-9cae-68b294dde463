#include "dx5ii_device.h"
#include "common.h"
#include "tpprintf.h"
#include <memory>
#include "errcode.h"
#include <string.h>
namespace Topping
{
    Dx5iiDevice::Dx5iiDevice(dx5ii_device_t *dx5ii_device) : mControllerClient(260), mSupportedDevice(&SUPPORTED_DEVICES[1]), m_dx5ii_device(dx5ii_device)
    {
    }

    Dx5iiDevice::~Dx5iiDevice()
    {
    }

    void Dx5iiDevice::startScan()
    {
        mControllerScanner.startScan(this);
    }

    void Dx5iiDevice::stopScan()
    {
        mControllerScanner.stopScan(this);
    }

    void Dx5iiDevice::connect(BluetoothDevice &device)
    {
        mControllerClient.connect(device, this);
    }

    void Dx5iiDevice::disconnect()
    {
        mControllerClient.disconnect();
    }

    void Dx5iiDevice::verify()
    {
        mControllerClient.verify(mSupportedDevice);
    }

    void Dx5iiDevice::powerOn(bool isOn)
    {
        cJSON *rootJson = cJSON_CreateObject();
        cJSON_AddBoolToObject(rootJson, "is_on", isOn);
        const char *dump = cJSON_Print(rootJson);
        cJSON_Delete(rootJson);
        mControllerClient.sendRequest(DX5II_DEVICE_CMD_POWER_ON, dump);
        cJSON_free((void *)dump);
    }

    void Dx5iiDevice::setDeviceName(const std::string &name)
    {
        cJSON *rootJson = cJSON_CreateObject();
        cJSON_AddStringToObject(rootJson, "device_name", name.c_str());
        const char *dump = cJSON_Print(rootJson);
        cJSON_Delete(rootJson);
        mControllerClient.sendRequest(DX5II_DEVICE_CMD_SET_DEVICE_NAME, dump);
        cJSON_free((void *)dump);
    }

    void Dx5iiDevice::setVolume(int volume)
    {
        cJSON *rootJson = cJSON_CreateObject();
        cJSON_AddNumberToObject(rootJson, "volume", volume);
        const char *dump = cJSON_Print(rootJson);
        cJSON_Delete(rootJson);
        mControllerClient.sendRequest(DX5II_DEVICE_CMD_SET_VOLUME, dump);
        cJSON_free((void *)dump);
    }

    void Dx5iiDevice::setMute(bool isMute)
    {
        cJSON *rootJson = cJSON_CreateObject();
        cJSON_AddBoolToObject(rootJson, "is_mute", isMute);
        const char *dump = cJSON_Print(rootJson);
        cJSON_Delete(rootJson);
        mControllerClient.sendRequest(DX5II_DEVICE_CMD_SET_MUTE, dump);
        cJSON_free((void *)dump);
    }

    void Dx5iiDevice::setInputType(int inputType)
    {
        cJSON *rootJson = cJSON_CreateObject();
        cJSON_AddNumberToObject(rootJson, "input_type", inputType);
        const char *dump = cJSON_Print(rootJson);
        cJSON_Delete(rootJson);
        mControllerClient.sendRequest(DX5II_DEVICE_CMD_SET_INPUT_TYPE, dump);
        cJSON_free((void *)dump);
    }

    void Dx5iiDevice::setOutputType(int outputType)
    {
        cJSON *rootJson = cJSON_CreateObject();
        cJSON_AddNumberToObject(rootJson, "output_type", outputType);
        const char *dump = cJSON_Print(rootJson);
        cJSON_Delete(rootJson);
        mControllerClient.sendRequest(DX5II_DEVICE_CMD_SET_OUTPUT_TYPE, dump);
        cJSON_free((void *)dump);
    }

    void Dx5iiDevice::enableHeadphone(bool enable)
    {
        // 注意：DX5II_DEVICE_CMD_ENABLE_HEADPHONE 已废弃
        // 新版输出逻辑不再有独立的耳放开关，已整合至 SET_OUTPUT_TYPE
        // 这里改为设置输出类型
        int outputType = enable ? DX5II_DEVICE_OUTPUT_HEADPHONE_ALL : DX5II_DEVICE_OUTPUT_LINE_OUT_ALL;
        setOutputType(outputType);
    }

    void Dx5iiDevice::setHeadphoneGain(int gainType)
    {
        cJSON *rootJson = cJSON_CreateObject();
        cJSON_AddNumberToObject(rootJson, "gain_type", gainType);
        const char *dump = cJSON_Print(rootJson);
        cJSON_Delete(rootJson);
        mControllerClient.sendRequest(DX5II_DEVICE_CMD_SET_HEADPHONE_GAIN, dump);
        cJSON_free((void *)dump);
    }

    void Dx5iiDevice::setDisplayMode(int displayMode)
    {
        cJSON *rootJson = cJSON_CreateObject();
        cJSON_AddNumberToObject(rootJson, "display_mode", displayMode);
        const char *dump = cJSON_Print(rootJson);
        cJSON_Delete(rootJson);
        mControllerClient.sendRequest(DX5II_DEVICE_CMD_SET_DISPLAY_MODE, dump);
        cJSON_free((void *)dump);
    }

    void Dx5iiDevice::setTheme(int theme)
    {
        cJSON *rootJson = cJSON_CreateObject();
        cJSON_AddNumberToObject(rootJson, "theme", theme);
        const char *dump = cJSON_Print(rootJson);
        cJSON_Delete(rootJson);
        mControllerClient.sendRequest(DX5II_DEVICE_CMD_SET_THEME, dump);
        cJSON_free((void *)dump);
    }

    void Dx5iiDevice::setPowerTrigger(int triggerType)
    {
        cJSON *rootJson = cJSON_CreateObject();
        cJSON_AddNumberToObject(rootJson, "trigger_type", triggerType);
        const char *dump = cJSON_Print(rootJson);
        cJSON_Delete(rootJson);
        mControllerClient.sendRequest(DX5II_DEVICE_CMD_SET_POWER_TRIGGER, dump);
        cJSON_free((void *)dump);
    }

    void Dx5iiDevice::setBalance(int balance)
    {
        cJSON *rootJson = cJSON_CreateObject();
        cJSON_AddNumberToObject(rootJson, "balance", balance);
        const char *dump = cJSON_Print(rootJson);
        cJSON_Delete(rootJson);
        mControllerClient.sendRequest(DX5II_DEVICE_CMD_SET_BALANCE, dump);
        cJSON_free((void *)dump);
    }

    void Dx5iiDevice::setPcmFilter(int filterType)
    {
        cJSON *rootJson = cJSON_CreateObject();
        cJSON_AddNumberToObject(rootJson, "filter_type", filterType);
        const char *dump = cJSON_Print(rootJson);
        cJSON_Delete(rootJson);
        mControllerClient.sendRequest(DX5II_DEVICE_CMD_SET_PCM_FILTER, dump);
        cJSON_free((void *)dump);
    }

    void Dx5iiDevice::setDecodeMode(int decodeMode)
    {
        cJSON *rootJson = cJSON_CreateObject();
        cJSON_AddNumberToObject(rootJson, "decode_mode", decodeMode);
        const char *dump = cJSON_Print(rootJson);
        cJSON_Delete(rootJson);
        mControllerClient.sendRequest(DX5II_DEVICE_CMD_SET_DECODE_MODE, dump);
        cJSON_free((void *)dump);
    }

    void Dx5iiDevice::enableAudioBluetooth(bool enable)
    {
        cJSON *rootJson = cJSON_CreateObject();
        cJSON_AddBoolToObject(rootJson, "enable", enable);
        const char *dump = cJSON_Print(rootJson);
        cJSON_Delete(rootJson);
        mControllerClient.sendRequest(DX5II_DEVICE_CMD_ENABLE_AUDIO_BLUETOOTH, dump);
        cJSON_free((void *)dump);
    }

    void Dx5iiDevice::enableBluetoothAPTX(bool enable)
    {
        cJSON *rootJson = cJSON_CreateObject();
        cJSON_AddBoolToObject(rootJson, "enable", enable);
        const char *dump = cJSON_Print(rootJson);
        cJSON_Delete(rootJson);
        mControllerClient.sendRequest(DX5II_DEVICE_CMD_ENABLE_BLUETOOTH_APTX, dump);
        cJSON_free((void *)dump);
    }

    void Dx5iiDevice::enableRemote(bool enable)
    {
        cJSON *rootJson = cJSON_CreateObject();
        cJSON_AddBoolToObject(rootJson, "enable", enable);
        const char *dump = cJSON_Print(rootJson);
        cJSON_Delete(rootJson);
        mControllerClient.sendRequest(DX5II_DEVICE_CMD_ENABLE_REMOTE, dump);
        cJSON_free((void *)dump);
    }

    void Dx5iiDevice::setMultifunctionKey(int keyType)
    {
        // 注意：DX5II_DEVICE_CMD_SET_MULTIFUNCTION_KEY 已废弃
        // 被新的按键自定义逻辑取代，这里暂不实现
        // 可以通过 setMainKeyFunction, setRemoteAKeyFunction, setRemoteBKeyFunction 来实现
        TPPRINTF("setMultifunctionKey is deprecated, use new key function methods instead\n");

        /*
        cJSON *rootJson = cJSON_CreateObject();
        cJSON_AddNumberToObject(rootJson, "key_type", keyType);
        const char *dump = cJSON_Print(rootJson);
        cJSON_Delete(rootJson);
        mControllerClient.sendRequest(DX5II_DEVICE_CMD_SET_MULTIFUNCTION_KEY, dump);
        cJSON_free((void *)dump);
        */
    }

    void Dx5iiDevice::setUsbMode(int usbMode)
    {
        cJSON *rootJson = cJSON_CreateObject();
        cJSON_AddNumberToObject(rootJson, "usb_mode", usbMode);
        const char *dump = cJSON_Print(rootJson);
        cJSON_Delete(rootJson);
        mControllerClient.sendRequest(DX5II_DEVICE_CMD_SET_USB_MODE, dump);
        cJSON_free((void *)dump);
    }

    void Dx5iiDevice::setScreenBrightness(int brightnessType)
    {
        cJSON *rootJson = cJSON_CreateObject();
        cJSON_AddNumberToObject(rootJson, "brightness_type", brightnessType);
        const char *dump = cJSON_Print(rootJson);
        cJSON_Delete(rootJson);
        mControllerClient.sendRequest(DX5II_DEVICE_CMD_SET_SCREEN_BRIGHTNESS, dump);
        cJSON_free((void *)dump);
    }

    void Dx5iiDevice::setLanguage(int language)
    {
        cJSON *rootJson = cJSON_CreateObject();
        cJSON_AddNumberToObject(rootJson, "language", language);
        const char *dump = cJSON_Print(rootJson);
        cJSON_Delete(rootJson);
        mControllerClient.sendRequest(DX5II_DEVICE_CMD_SET_LANGUAGE, dump);
        cJSON_free((void *)dump);
    }

    // --- 新增方法实现 (DX5 III 新功能) ---
    void Dx5iiDevice::setVuMeterLevel(int level)
    {
        cJSON *rootJson = cJSON_CreateObject();
        cJSON_AddNumberToObject(rootJson, "level", level);
        const char *dump = cJSON_Print(rootJson);
        cJSON_Delete(rootJson);
        mControllerClient.sendRequest(DX5II_DEVICE_CMD_SET_VU_METER_LEVEL, dump);
        cJSON_free((void *)dump);
    }

    void Dx5iiDevice::setVuMeterDisplayMode(int mode)
    {
        cJSON *rootJson = cJSON_CreateObject();
        cJSON_AddNumberToObject(rootJson, "mode", mode);
        const char *dump = cJSON_Print(rootJson);
        cJSON_Delete(rootJson);
        mControllerClient.sendRequest(DX5II_DEVICE_CMD_SET_VU_METER_DISPLAY_MODE, dump);
        cJSON_free((void *)dump);
    }

    void Dx5iiDevice::setInputOptions(int options)
    {
        cJSON *rootJson = cJSON_CreateObject();
        cJSON_AddNumberToObject(rootJson, "options", options);
        const char *dump = cJSON_Print(rootJson);
        cJSON_Delete(rootJson);
        mControllerClient.sendRequest(DX5II_DEVICE_CMD_SET_INPUT_OPTIONS, dump);
        cJSON_free((void *)dump);
    }

    void Dx5iiDevice::setOutputOptions(int options)
    {
        cJSON *rootJson = cJSON_CreateObject();
        cJSON_AddNumberToObject(rootJson, "options", options);
        const char *dump = cJSON_Print(rootJson);
        cJSON_Delete(rootJson);
        mControllerClient.sendRequest(DX5II_DEVICE_CMD_SET_OUTPUT_OPTIONS, dump);
        cJSON_free((void *)dump);
    }

    void Dx5iiDevice::setVolumeStep(int step)
    {
        cJSON *rootJson = cJSON_CreateObject();
        cJSON_AddNumberToObject(rootJson, "step", step);
        const char *dump = cJSON_Print(rootJson);
        cJSON_Delete(rootJson);
        mControllerClient.sendRequest(DX5II_DEVICE_CMD_SET_VOLUME_STEP, dump);
        cJSON_free((void *)dump);
    }

    void Dx5iiDevice::setPolarity(int polarity)
    {
        cJSON *rootJson = cJSON_CreateObject();
        cJSON_AddNumberToObject(rootJson, "polarity", polarity);
        const char *dump = cJSON_Print(rootJson);
        cJSON_Delete(rootJson);
        mControllerClient.sendRequest(DX5II_DEVICE_CMD_SET_POLARITY, dump);
        cJSON_free((void *)dump);
    }

    void Dx5iiDevice::setVolumeMemoryMode(int mode)
    {
        cJSON *rootJson = cJSON_CreateObject();
        cJSON_AddNumberToObject(rootJson, "mode", mode);
        const char *dump = cJSON_Print(rootJson);
        cJSON_Delete(rootJson);
        mControllerClient.sendRequest(DX5II_DEVICE_CMD_SET_VOLUME_MEMORY_MODE, dump);
        cJSON_free((void *)dump);
    }

    void Dx5iiDevice::setPeqMemoryMode(int mode)
    {
        cJSON *rootJson = cJSON_CreateObject();
        cJSON_AddNumberToObject(rootJson, "mode", mode);
        const char *dump = cJSON_Print(rootJson);
        cJSON_Delete(rootJson);
        mControllerClient.sendRequest(DX5II_DEVICE_CMD_SET_PEQ_MEMORY_MODE, dump);
        cJSON_free((void *)dump);
    }

    void Dx5iiDevice::setMainKeyFunction(int function)
    {
        cJSON *rootJson = cJSON_CreateObject();
        cJSON_AddNumberToObject(rootJson, "function", function);
        const char *dump = cJSON_Print(rootJson);
        cJSON_Delete(rootJson);
        mControllerClient.sendRequest(DX5II_DEVICE_CMD_SET_MAIN_KEY_FUNCTION, dump);
        cJSON_free((void *)dump);
    }

    void Dx5iiDevice::setRemoteAKeyFunction(int function)
    {
        cJSON *rootJson = cJSON_CreateObject();
        cJSON_AddNumberToObject(rootJson, "function", function);
        const char *dump = cJSON_Print(rootJson);
        cJSON_Delete(rootJson);
        mControllerClient.sendRequest(DX5II_DEVICE_CMD_SET_REMOTE_A_KEY_FUNCTION, dump);
        cJSON_free((void *)dump);
    }

    void Dx5iiDevice::setRemoteBKeyFunction(int function)
    {
        cJSON *rootJson = cJSON_CreateObject();
        cJSON_AddNumberToObject(rootJson, "function", function);
        const char *dump = cJSON_Print(rootJson);
        cJSON_Delete(rootJson);
        mControllerClient.sendRequest(DX5II_DEVICE_CMD_SET_REMOTE_B_KEY_FUNCTION, dump);
        cJSON_free((void *)dump);
    }

    void Dx5iiDevice::resetSettings()
    {
        // 注意：DX5II_DEVICE_CMD_RESET_SETTINGS 已废弃
        // 新版无此功能，使用恢复出厂设置代替
        TPPRINTF("resetSettings is deprecated, use restoreFactorySettings instead\n");
        restoreFactorySettings();

        // mControllerClient.sendRequest(DX5II_DEVICE_CMD_RESET_SETTINGS, "{}");
    }

    void Dx5iiDevice::restoreFactorySettings()
    {
        mControllerClient.sendRequest(DX5II_DEVICE_CMD_RESTORE_FACTORY_SETTINGS, "{}");
    }

    void Dx5iiDevice::requestSettings()
    {
        mControllerClient.sendRequest(DX5II_DEVICE_CMD_GET_SETTINGS, "{}");
    }

    void Dx5iiDevice::requestSampling()
    {
        mControllerClient.sendRequest(DX5II_DEVICE_CMD_GET_SAMPLING, "{}");
    }

    void Dx5iiDevice::onBatchScanResults(const std::vector<ControllerScanner::ScanResult> &results)
    {
        // for (auto result : results)
        // {
        //     TPPRINTF("name:%s, rssi:%d\n", result.device.getName().c_str(), result.rssi);
        // }
        dx5ii_scan_result_t *res = new dx5ii_scan_result_t[results.size()];
        size_t i = 0;
        for (; i < results.size(); i++)
        {
            ControllerScanner::ScanResult result = results[i];
            if (result.supportedDevice->vendorId == mSupportedDevice->vendorId && result.supportedDevice->productId == mSupportedDevice->productId)
            {
                res[i].name = (char *)result.device.getName().c_str();
                res[i].device = (long)result.device.getDevice();
                res[i].rssi = result.rssi;
            }
        }
        if (m_dx5ii_device != nullptr)
        {
            m_dx5ii_device->callback.on_scan_results(m_dx5ii_device->flutter_object, res, i);
        }
        delete[] res;
    }

    void Dx5iiDevice::onScanFailed(int errorCode)
    {
        TPPRINTF("onScanFailed; errorCode:%d\n", errorCode);
        if (m_dx5ii_device != nullptr)
        {
            m_dx5ii_device->callback.on_scan_failed(m_dx5ii_device->flutter_object, errorCode);
        }
    }

    void Dx5iiDevice::onStateChange(int state)
    {
        TPPRINTF("onStateChange; state:%d\n", state);
        if (m_dx5ii_device != nullptr)
        {
            m_dx5ii_device->callback.on_state_change(m_dx5ii_device->flutter_object, state);
        }
    }

    void Dx5iiDevice::onVerifyResult(int type)
    {
        TPPRINTF("onVerifyResult; type:%d\n", type);
        if (m_dx5ii_device != nullptr)
        {
            m_dx5ii_device->callback.on_verify_result(m_dx5ii_device->flutter_object, type);
        }
    }

    void Dx5iiDevice::onReceiveRequest(int session_id, int cmd, const std::string &msg)
    {
        parseRequest(session_id, cmd, msg);
    }

    void Dx5iiDevice::onReceiveResponse(int cmd, int errcode, const std::string &errmsg, const std::string &msg)
    {
        TPPRINTF("onReceiveResponse\n");
        if (errcode != 0)
        {
            TPPRINTF("Receive error. errcode:%d; errmsg%s\n", errcode, errmsg.c_str());
            return;
        }
        parseResponse(cmd, msg);
    }

    void Dx5iiDevice::parseRequest(int sessionId, int cmd, const std::string &data)
    {
    }

    void Dx5iiDevice::parseResponse(int cmd, const std::string &data)
    {
        TPPRINTF("parseResponse; cmd:%d\n", cmd);
        switch (cmd)
        {
        case DX5II_DEVICE_CMD_POWER_ON:
            parseResponsePowerOn(data);
            break;
        case DX5II_DEVICE_CMD_SET_DEVICE_NAME:
            parseResponseSetDeviceName(data);
            break;
        case DX5II_DEVICE_CMD_SET_VOLUME:
            parseResponseSetVolume(data);
            break;
        case DX5II_DEVICE_CMD_SET_MUTE:
            parseResponseSetMute(data);
            break;
        case DX5II_DEVICE_CMD_SET_INPUT_TYPE:
            parseResponseSetInputType(data);
            break;
        case DX5II_DEVICE_CMD_SET_OUTPUT_TYPE:
            parseResponseSetOutputType(data);
            break;
        case DX5II_DEVICE_CMD_SET_HEADPHONE_GAIN:
            parseResponseSetHeadphoneGain(data);
            break;
        case DX5II_DEVICE_CMD_SET_DISPLAY_MODE:
            parseResponseSetDisplayMode(data);
            break;
        case DX5II_DEVICE_CMD_SET_THEME:
            parseResponseSetTheme(data);
            break;
        case DX5II_DEVICE_CMD_SET_POWER_TRIGGER:
            parseResponseSetPowerTrigger(data);
            break;
        case DX5II_DEVICE_CMD_SET_BALANCE:
            parseResponseSetBalance(data);
            break;
        case DX5II_DEVICE_CMD_SET_PCM_FILTER:
            parseResponseSetPcmFilter(data);
            break;
        case DX5II_DEVICE_CMD_SET_DECODE_MODE:
            parseResponseSetDecodeMode(data);
            break;
        case DX5II_DEVICE_CMD_ENABLE_AUDIO_BLUETOOTH:
            parseResponseEnableAudioBluetooth(data);
            break;
        case DX5II_DEVICE_CMD_ENABLE_BLUETOOTH_APTX:
            parseResponseEnableBluetoothAPTX(data);
            break;
        case DX5II_DEVICE_CMD_ENABLE_REMOTE:
            parseResponseEnableRemote(data);
            break;
        case DX5II_DEVICE_CMD_SET_USB_MODE:
            parseResponseSetUsbMode(data);
            break;
        case DX5II_DEVICE_CMD_SET_SCREEN_BRIGHTNESS:
            parseResponseSetScreenBrightness(data);
            break;
        case DX5II_DEVICE_CMD_SET_LANGUAGE:
            parseResponseSetLanguage(data);
            break;
        case DX5II_DEVICE_CMD_RESTORE_FACTORY_SETTINGS:
            parseResponseRestoreFactorySettings(data);
            break;
        case DX5II_DEVICE_CMD_GET_SETTINGS:
            parseResponseRequestSettings(data);
            break;
        case DX5II_DEVICE_CMD_GET_SAMPLING:
            parseResponseRequestSampling(data);
            break;
        default:
            TPPRINTF("This command %d is not supported\n", cmd);
            break;
        }
    }

    void Dx5iiDevice::parseResponsePowerOn(const std::string &data)
    {
        cJSON *rootJson = cJSON_Parse(data.c_str());
        if (NULL == rootJson)
        {
            TPPRINTF("Message format error.\n");
            return;
        }
        try
        {
            if (!cJSON_IsObject(rootJson))
            {
                TPPRINTF("Message format error.\n");
                throw 0;
            }
            cJSON *isOnJson = cJSON_GetObjectItem(rootJson, "is_on");
            if (NULL == isOnJson)
            {
                TPPRINTF("Message format error.\n");
                throw 0;
            }
            cJSON_bool isOn = cJSON_IsTrue(isOnJson);
            if (m_dx5ii_device != nullptr)
            {
                m_dx5ii_device->callback.on_power_change(m_dx5ii_device->flutter_object, isOn);
            }
        }
        catch(int& e)
        {
        }
        cJSON_Delete(rootJson);
    }

    void Dx5iiDevice::parseResponseSetDeviceName(const std::string &data)
    {
        cJSON *rootJson = cJSON_Parse(data.c_str());
        if (NULL == rootJson)
        {
            TPPRINTF("Message format error.\n");
            return;
        }
        try
        {
            if (!cJSON_IsObject(rootJson))
            {
                TPPRINTF("Message format error.\n");
                throw 0;
            }
            cJSON *deviceNameJson = cJSON_GetObjectItem(rootJson, "device_name");
            if (NULL == deviceNameJson)
            {
                TPPRINTF("Message format error.\n");
                throw 0;
            }
            const char *deviceName = cJSON_GetStringValue(deviceNameJson);
            if (m_dx5ii_device != nullptr)
            {
                m_dx5ii_device->callback.on_device_name_change(m_dx5ii_device->flutter_object, deviceName);
            }
        }
        catch(int& e)
        {
        }
        cJSON_Delete(rootJson);
    }

    void Dx5iiDevice::parseResponseSetVolume(const std::string &data)
    {
        cJSON *rootJson = cJSON_Parse(data.c_str());
        if (NULL == rootJson)
        {
            TPPRINTF("Message format error.\n");
            return;
        }
        try
        {
            if (!cJSON_IsObject(rootJson))
            {
                TPPRINTF("Message format error.\n");
                throw 0;
            }
            cJSON *volumeJson = cJSON_GetObjectItem(rootJson, "volume");
            if (NULL == volumeJson)
            {
                TPPRINTF("Message format error.\n");
                throw 0;
            }
            int volume = (int)cJSON_GetNumberValue(volumeJson);
            if (m_dx5ii_device != nullptr)
            {
                m_dx5ii_device->callback.on_device_volume_change(m_dx5ii_device->flutter_object, volume);
            }
        }
        catch(int& e)
        {
        }
        cJSON_Delete(rootJson);
    }

    void Dx5iiDevice::parseResponseSetMute(const std::string &data)
    {
        cJSON *rootJson = cJSON_Parse(data.c_str());
        if (NULL == rootJson)
        {
            TPPRINTF("Message format error.\n");
            return;
        }
        try
        {
            if (!cJSON_IsObject(rootJson))
            {
                TPPRINTF("Message format error.\n");
                throw 0;
            }
            cJSON *isMuteJson = cJSON_GetObjectItem(rootJson, "is_mute");
            if (NULL == isMuteJson)
            {
                TPPRINTF("Message format error.\n");
                throw 0;
            }
            cJSON_bool isMute = cJSON_IsTrue(isMuteJson);
            if (m_dx5ii_device != nullptr)
            {
                m_dx5ii_device->callback.on_device_mute_change(m_dx5ii_device->flutter_object, isMute);
            }
        }
        catch(int& e)
        {
        }
        cJSON_Delete(rootJson);
    }

    void Dx5iiDevice::parseResponseSetInputType(const std::string &data)
    {
        cJSON *rootJson = cJSON_Parse(data.c_str());
        if (NULL == rootJson)
        {
            TPPRINTF("Message format error.\n");
            return;
        }
        try
        {
            if (!cJSON_IsObject(rootJson))
            {
                TPPRINTF("Message format error.\n");
                throw 0;
            }
            cJSON *inputTypeJson = cJSON_GetObjectItem(rootJson, "input_type");
            if (NULL == inputTypeJson)
            {
                TPPRINTF("Message format error.\n");
                throw 0;
            }
            int inputType = (int)cJSON_GetNumberValue(inputTypeJson);
            if (m_dx5ii_device != nullptr)
            {
                m_dx5ii_device->callback.on_device_input_type_change(m_dx5ii_device->flutter_object, inputType);
            }
        }
        catch(int& e)
        {
        }
        cJSON_Delete(rootJson);
    }

    void Dx5iiDevice::parseResponseSetOutputType(const std::string &data)
    {
        cJSON *rootJson = cJSON_Parse(data.c_str());
        if (NULL == rootJson)
        {
            TPPRINTF("Message format error.\n");
            return;
        }
        try
        {
            if (!cJSON_IsObject(rootJson))
            {
                TPPRINTF("Message format error.\n");
                throw 0;
            }
            cJSON *outputTypeJson = cJSON_GetObjectItem(rootJson, "output_type");
            if (NULL == outputTypeJson)
            {
                TPPRINTF("Message format error.\n");
                throw 0;
            }
            int outputType = (int)cJSON_GetNumberValue(outputTypeJson);
            if (m_dx5ii_device != nullptr)
            {
                m_dx5ii_device->callback.on_device_output_type_change(m_dx5ii_device->flutter_object, outputType);
            }
        }
        catch(int& e)
        {
        }
        cJSON_Delete(rootJson);
    }

    void Dx5iiDevice::parseResponseSetHeadphoneGain(const std::string &data)
    {
        cJSON *rootJson = cJSON_Parse(data.c_str());
        if (NULL == rootJson)
        {
            TPPRINTF("Message format error.\n");
            return;
        }
        try
        {
            if (!cJSON_IsObject(rootJson))
            {
                TPPRINTF("Message format error.\n");
                throw 0;
            }
            cJSON *gainTypeJson = cJSON_GetObjectItem(rootJson, "gain_type");
            if (NULL == gainTypeJson)
            {
                TPPRINTF("Message format error.\n");
                throw 0;
            }
            int gainType = (int)cJSON_GetNumberValue(gainTypeJson);
            if (m_dx5ii_device != nullptr)
            {
                m_dx5ii_device->callback.on_device_headphone_gain_change(m_dx5ii_device->flutter_object, gainType);
            }
        }
        catch(int& e)
        {
        }
        cJSON_Delete(rootJson);
    }

    void Dx5iiDevice::parseResponseSetDisplayMode(const std::string &data)
    {
        cJSON *rootJson = cJSON_Parse(data.c_str());
        if (NULL == rootJson)
        {
            TPPRINTF("Message format error.\n");
            return;
        }
        try
        {
            if (!cJSON_IsObject(rootJson))
            {
                TPPRINTF("Message format error.\n");
                throw 0;
            }
            cJSON *displayModeJson = cJSON_GetObjectItem(rootJson, "display_mode");
            if (NULL == displayModeJson)
            {
                TPPRINTF("Message format error.\n");
                throw 0;
            }
            int display_mode = (int)cJSON_GetNumberValue(displayModeJson);
            if (m_dx5ii_device != nullptr)
            {
                m_dx5ii_device->callback.on_device_display_mode_change(m_dx5ii_device->flutter_object, display_mode);
            }
        }
        catch(int& e)
        {
        }
        cJSON_Delete(rootJson);
    }

    void Dx5iiDevice::parseResponseSetTheme(const std::string &data)
    {
        cJSON *rootJson = cJSON_Parse(data.c_str());
        if (NULL == rootJson)
        {
            TPPRINTF("Message format error.\n");
            return;
        }
        try
        {
            if (!cJSON_IsObject(rootJson))
            {
                TPPRINTF("Message format error.\n");
                throw 0;
            }
            cJSON *themeJson = cJSON_GetObjectItem(rootJson, "theme");
            if (NULL == themeJson)
            {
                TPPRINTF("Message format error.\n");
                throw 0;
            }
            int theme = (int)cJSON_GetNumberValue(themeJson);
            if (m_dx5ii_device != nullptr)
            {
                m_dx5ii_device->callback.on_device_theme_change(m_dx5ii_device->flutter_object, theme);
            }
        }
        catch(int& e)
        {
        }
        cJSON_Delete(rootJson);
    }

    void Dx5iiDevice::parseResponseSetPowerTrigger(const std::string &data)
    {
        cJSON *rootJson = cJSON_Parse(data.c_str());
        if (NULL == rootJson)
        {
            TPPRINTF("Message format error.\n");
            return;
        }
        try
        {
            if (!cJSON_IsObject(rootJson))
            {
                TPPRINTF("Message format error.\n");
                throw 0;
            }
            cJSON *triggerTypeJson = cJSON_GetObjectItem(rootJson, "trigger_type");
            if (NULL == triggerTypeJson)
            {
                TPPRINTF("Message format error.\n");
                throw 0;
            }
            int triggerType = (int)cJSON_GetNumberValue(triggerTypeJson);
            if (m_dx5ii_device != nullptr)
            {
                m_dx5ii_device->callback.on_device_power_trigger_change(m_dx5ii_device->flutter_object, triggerType);
            }
        }
        catch(int& e)
        {
        }
        cJSON_Delete(rootJson);
    }

    void Dx5iiDevice::parseResponseSetBalance(const std::string &data)
    {
        cJSON *rootJson = cJSON_Parse(data.c_str());
        if (NULL == rootJson)
        {
            TPPRINTF("Message format error.\n");
            return;
        }
        try
        {
            if (!cJSON_IsObject(rootJson))
            {
                TPPRINTF("Message format error.\n");
                throw 0;
            }
            cJSON *balanceJson = cJSON_GetObjectItem(rootJson, "balance");
            if (NULL == balanceJson)
            {
                TPPRINTF("Message format error.\n");
                throw 0;
            }
            int balance = (int)cJSON_GetNumberValue(balanceJson);
            if (m_dx5ii_device != nullptr)
            {
                m_dx5ii_device->callback.on_device_balance_change(m_dx5ii_device->flutter_object, balance);
            }
        }
        catch(int& e)
        {
        }
        cJSON_Delete(rootJson);
    }

    void Dx5iiDevice::parseResponseSetPcmFilter(const std::string &data)
    {
        cJSON *rootJson = cJSON_Parse(data.c_str());
        if (NULL == rootJson)
        {
            TPPRINTF("Message format error.\n");
            return;
        }
        try
        {
            if (!cJSON_IsObject(rootJson))
            {
                TPPRINTF("Message format error.\n");
                throw 0;
            }
            cJSON *filterTypeJson = cJSON_GetObjectItem(rootJson, "filter_type");
            if (NULL == filterTypeJson)
            {
                TPPRINTF("Message format error.\n");
                throw 0;
            }
            int filterType = (int)cJSON_GetNumberValue(filterTypeJson);
            if (m_dx5ii_device != nullptr)
            {
                m_dx5ii_device->callback.on_device_pcm_filter_change(m_dx5ii_device->flutter_object, filterType);
            }
        }
        catch(int& e)
        {
        }
        cJSON_Delete(rootJson);
    }

    void Dx5iiDevice::parseResponseSetDecodeMode(const std::string &data)
    {
        cJSON *rootJson = cJSON_Parse(data.c_str());
        if (NULL == rootJson)
        {
            TPPRINTF("Message format error.\n");
            return;
        }
        try
        {
            if (!cJSON_IsObject(rootJson))
            {
                TPPRINTF("Message format error.\n");
                throw 0;
            }
            cJSON *decodeModeJson = cJSON_GetObjectItem(rootJson, "decode_mode");
            if (NULL == decodeModeJson)
            {
                TPPRINTF("Message format error.\n");
                throw 0;
            }
            int decodeMode = (int)cJSON_GetNumberValue(decodeModeJson);
            if (m_dx5ii_device != nullptr)
            {
                m_dx5ii_device->callback.on_device_decode_mode_change(m_dx5ii_device->flutter_object, decodeMode);
            }
        }
        catch(int& e)
        {
        }
        cJSON_Delete(rootJson);
    }

    void Dx5iiDevice::parseResponseEnableAudioBluetooth(const std::string &data)
    {
        cJSON *rootJson = cJSON_Parse(data.c_str());
        if (NULL == rootJson)
        {
            TPPRINTF("Message format error.\n");
            return;
        }
        try
        {
            if (!cJSON_IsObject(rootJson))
            {
                TPPRINTF("Message format error.\n");
                throw 0;
            }
            cJSON *enableJson = cJSON_GetObjectItem(rootJson, "enable");
            if (NULL == enableJson)
            {
                TPPRINTF("Message format error.\n");
                throw 0;
            }
            cJSON_bool enable = cJSON_IsTrue(enableJson);
            if (m_dx5ii_device != nullptr)
            {
                m_dx5ii_device->callback.on_device_enable_audio_bluetooth(m_dx5ii_device->flutter_object, enable);
            }
        }
        catch(int& e)
        {
        }
        cJSON_Delete(rootJson);
    }

    void Dx5iiDevice::parseResponseEnableBluetoothAPTX(const std::string &data)
    {
        cJSON *rootJson = cJSON_Parse(data.c_str());
        if (NULL == rootJson)
        {
            TPPRINTF("Message format error.\n");
            return;
        }
        try
        {
            if (!cJSON_IsObject(rootJson))
            {
                TPPRINTF("Message format error.\n");
                throw 0;
            }
            cJSON *enableJson = cJSON_GetObjectItem(rootJson, "enable");
            if (NULL == enableJson)
            {
                TPPRINTF("Message format error.\n");
                throw 0;
            }
            cJSON_bool enable = cJSON_IsTrue(enableJson);
            if (m_dx5ii_device != nullptr)
            {
                m_dx5ii_device->callback.on_device_enable_bluetooth_aptx(m_dx5ii_device->flutter_object, enable);
            }
        }
        catch(int& e)
        {
        }
        cJSON_Delete(rootJson);
    }

    void Dx5iiDevice::parseResponseEnableRemote(const std::string &data)
    {
        cJSON *rootJson = cJSON_Parse(data.c_str());
        if (NULL == rootJson)
        {
            TPPRINTF("Message format error.\n");
            return;
        }
        try
        {
            if (!cJSON_IsObject(rootJson))
            {
                TPPRINTF("Message format error.\n");
                throw 0;
            }
            cJSON *enableJson = cJSON_GetObjectItem(rootJson, "enable");
            if (NULL == enableJson)
            {
                TPPRINTF("Message format error.\n");
                throw 0;
            }
            cJSON_bool enable = cJSON_IsTrue(enableJson);
            if (m_dx5ii_device != nullptr)
            {
                m_dx5ii_device->callback.on_device_enable_remote(m_dx5ii_device->flutter_object, enable);
            }
        }
        catch(int& e)
        {
        }
        cJSON_Delete(rootJson);
    }

    void Dx5iiDevice::parseResponseSetUsbMode(const std::string &data)
    {
        cJSON *rootJson = cJSON_Parse(data.c_str());
        if (NULL == rootJson)
        {
            TPPRINTF("Message format error.\n");
            return;
        }
        try
        {
            if (!cJSON_IsObject(rootJson))
            {
                TPPRINTF("Message format error.\n");
                throw 0;
            }
            cJSON *usbModeJson = cJSON_GetObjectItem(rootJson, "usb_mode");
            if (NULL == usbModeJson)
            {
                TPPRINTF("Message format error.\n");
                throw 0;
            }
            int usbMode = (int)cJSON_GetNumberValue(usbModeJson);
            if (m_dx5ii_device != nullptr)
            {
                m_dx5ii_device->callback.on_device_usb_mode_change(m_dx5ii_device->flutter_object, usbMode);
            }
        }
        catch(int& e)
        {
        }
        cJSON_Delete(rootJson);
    }

    void Dx5iiDevice::parseResponseSetScreenBrightness(const std::string &data)
    {
        cJSON *rootJson = cJSON_Parse(data.c_str());
        if (NULL == rootJson)
        {
            TPPRINTF("Message format error.\n");
            return;
        }
        try
        {
            if (!cJSON_IsObject(rootJson))
            {
                TPPRINTF("Message format error.\n");
                throw 0;
            }
            cJSON *brightnessTypeJson = cJSON_GetObjectItem(rootJson, "brightness_type");
            if (NULL == brightnessTypeJson)
            {
                TPPRINTF("Message format error.\n");
                throw 0;
            }
            int brightnessType = (int)cJSON_GetNumberValue(brightnessTypeJson);
            if (m_dx5ii_device != nullptr)
            {
                m_dx5ii_device->callback.on_device_screen_brightness_change(m_dx5ii_device->flutter_object, brightnessType);
            }
        }
        catch(int& e)
        {
        }
        cJSON_Delete(rootJson);
    }

    void Dx5iiDevice::parseResponseSetLanguage(const std::string &data)
    {
        cJSON *rootJson = cJSON_Parse(data.c_str());
        if (NULL == rootJson)
        {
            TPPRINTF("Message format error.\n");
            return;
        }
        try
        {
            if (!cJSON_IsObject(rootJson))
            {
                TPPRINTF("Message format error.\n");
                throw 0;
            }
            cJSON *languageJson = cJSON_GetObjectItem(rootJson, "language");
            if (NULL == languageJson)
            {
                TPPRINTF("Message format error.\n");
                throw 0;
            }
            int language = (int)cJSON_GetNumberValue(languageJson);
            if (m_dx5ii_device != nullptr)
            {
                m_dx5ii_device->callback.on_device_language_change(m_dx5ii_device->flutter_object, language);
            }
        }
        catch(int& e)
        {
        }
        cJSON_Delete(rootJson);
    }

    void Dx5iiDevice::parseResponseRestoreFactorySettings(const std::string &data)
    {
        if (m_dx5ii_device != nullptr)
        {
            m_dx5ii_device->callback.on_device_restore_factory_settings(m_dx5ii_device->flutter_object);
        }
    }

    void Dx5iiDevice::parseResponseRequestSettings(const std::string &data)
    {
        cJSON *rootJson = cJSON_Parse(data.c_str());
        if (NULL == rootJson)
        {
            TPPRINTF("Message format error.\n");
            return;
        }
        try
        {
            if (!cJSON_IsObject(rootJson))
            {
                TPPRINTF("Message format error.\n");
                throw 0;
            }
            dx5ii_settings_t dx5ii_settings;
            cJSON *isOnJson = cJSON_GetObjectItem(rootJson, "is_on");
            if (NULL == isOnJson)
            {
                TPPRINTF("Message format error.\n");
                throw 0;
            }
            dx5ii_settings.is_on = (int)cJSON_IsTrue(isOnJson);

            cJSON *deviceNameJson = cJSON_GetObjectItem(rootJson, "device_name");
            if (NULL == deviceNameJson)
            {
                TPPRINTF("Message format error.\n");
                throw 0;
            }
            strcpy(dx5ii_settings.device_name, cJSON_GetStringValue(deviceNameJson));

            cJSON *volumeJson = cJSON_GetObjectItem(rootJson, "volume");
            if (NULL == volumeJson)
            {
                TPPRINTF("Message format error.\n");
                throw 0;
            }
            dx5ii_settings.volume = (int)cJSON_GetNumberValue(volumeJson);

            cJSON *isMuteJson = cJSON_GetObjectItem(rootJson, "is_mute");
            if (NULL == isMuteJson)
            {
                TPPRINTF("Message format error.\n");
                throw 0;
            }
            dx5ii_settings.is_mute = (int)cJSON_IsTrue(isMuteJson);

            cJSON *inputTypeJson = cJSON_GetObjectItem(rootJson, "input_type");
            if (NULL == inputTypeJson)
            {
                TPPRINTF("Message format error.\n");
                throw 0;
            }
            dx5ii_settings.input_type = (int)cJSON_GetNumberValue(inputTypeJson);

            cJSON *outputTypeJson = cJSON_GetObjectItem(rootJson, "output_type");
            if (NULL == outputTypeJson)
            {
                TPPRINTF("Message format error.\n");
                throw 0;
            }
            dx5ii_settings.output_type = (int)cJSON_GetNumberValue(outputTypeJson);

            cJSON *headphoneEnableJson = cJSON_GetObjectItem(rootJson, "headphone_enable");
            if (NULL == headphoneEnableJson)
            {
                TPPRINTF("Message format error.\n");
                throw 0;
            }
            dx5ii_settings.headphone_enable = (int)cJSON_IsTrue(headphoneEnableJson);

            cJSON *headphoneGainJson = cJSON_GetObjectItem(rootJson, "headphone_gain");
            if (NULL == headphoneGainJson)
            {
                TPPRINTF("Message format error.\n");
                throw 0;
            }
            dx5ii_settings.headphone_gain = (int)cJSON_GetNumberValue(headphoneGainJson);

            cJSON *displayModeJson = cJSON_GetObjectItem(rootJson, "display_mode");
            if (NULL == displayModeJson)
            {
                TPPRINTF("Message format error.\n");
                throw 0;
            }
            dx5ii_settings.display_mode = (int)cJSON_GetNumberValue(displayModeJson);

            cJSON *themeJson = cJSON_GetObjectItem(rootJson, "theme");
            if (NULL == themeJson)
            {
                TPPRINTF("Message format error.\n");
                throw 0;
            }
            dx5ii_settings.theme = (int)cJSON_GetNumberValue(themeJson);

            cJSON *powerTriggerJson = cJSON_GetObjectItem(rootJson, "power_trigger");
            if (NULL == powerTriggerJson)
            {
                TPPRINTF("Message format error.\n");
                throw 0;
            }
            dx5ii_settings.power_trigger = (int)cJSON_GetNumberValue(powerTriggerJson);

            cJSON *balanceJson = cJSON_GetObjectItem(rootJson, "balance");
            if (NULL == balanceJson)
            {
                TPPRINTF("Message format error.\n");
                throw 0;
            }
            dx5ii_settings.balance = (int)cJSON_GetNumberValue(balanceJson);

            cJSON *pcmFilterJson = cJSON_GetObjectItem(rootJson, "pcm_filter");
            if (NULL == pcmFilterJson)
            {
                TPPRINTF("Message format error.\n");
                throw 0;
            }
            dx5ii_settings.pcm_filter = (int)cJSON_GetNumberValue(pcmFilterJson);

            cJSON *decodeModeJson = cJSON_GetObjectItem(rootJson, "decode_mode");
            if (NULL == decodeModeJson)
            {
                TPPRINTF("Message format error.\n");
                throw 0;
            }
            dx5ii_settings.decode_mode = (int)cJSON_GetNumberValue(decodeModeJson);

            cJSON *audioBtEnableJson = cJSON_GetObjectItem(rootJson, "audio_bt_enable");
            if (NULL == audioBtEnableJson)
            {
                TPPRINTF("Message format error.\n");
                throw 0;
            }
            dx5ii_settings.audio_bt_enable = (int)cJSON_IsTrue(audioBtEnableJson);

            cJSON *aptxEnableJson = cJSON_GetObjectItem(rootJson, "aptx_enable");
            if (NULL == aptxEnableJson)
            {
                TPPRINTF("Message format error.\n");
                throw 0;
            }
            dx5ii_settings.aptx_enable = (int)cJSON_IsTrue(aptxEnableJson);

            cJSON *remoteEnableJson = cJSON_GetObjectItem(rootJson, "remote_enable");
            if (NULL == remoteEnableJson)
            {
                TPPRINTF("Message format error.\n");
                throw 0;
            }
            dx5ii_settings.remote_enable = (int)cJSON_IsTrue(remoteEnableJson);

            cJSON *multifunctionKeyJson = cJSON_GetObjectItem(rootJson, "multifunction_key");
            if (NULL == multifunctionKeyJson)
            {
                TPPRINTF("Message format error.\n");
                throw 0;
            }
            dx5ii_settings.multifunction_key = (int)cJSON_GetNumberValue(multifunctionKeyJson);

            cJSON *usbModeJson = cJSON_GetObjectItem(rootJson, "usb_mode");
            if (NULL == usbModeJson)
            {
                TPPRINTF("Message format error.\n");
                throw 0;
            }
            dx5ii_settings.usb_mode = (int)cJSON_GetNumberValue(usbModeJson);

            cJSON *screenBrightnessJson = cJSON_GetObjectItem(rootJson, "screen_brightness");
            if (NULL == screenBrightnessJson)
            {
                TPPRINTF("Message format error.\n");
                throw 0;
            }
            dx5ii_settings.screen_brightness = (int)cJSON_GetNumberValue(screenBrightnessJson);

            cJSON *languageJson = cJSON_GetObjectItem(rootJson, "language");
            if (NULL == languageJson)
            {
                TPPRINTF("Message format error.\n");
                throw 0;
            }
            dx5ii_settings.language = (int)cJSON_GetNumberValue(languageJson);

            cJSON *samplingJson = cJSON_GetObjectItem(rootJson, "sampling");
            if (NULL == samplingJson)
            {
                TPPRINTF("Message format error.\n");
                throw 0;
            }
            dx5ii_settings.sampling = (int)cJSON_GetNumberValue(samplingJson);

            if (m_dx5ii_device != nullptr)
            {
                m_dx5ii_device->callback.on_device_settings_response(m_dx5ii_device->flutter_object, &dx5ii_settings);
            }
        }
        catch(int& e)
        {
        }
        cJSON_Delete(rootJson);
    }

    void Dx5iiDevice::parseResponseRequestSampling(const std::string &data)
    {
        cJSON *rootJson = cJSON_Parse(data.c_str());
        if (NULL == rootJson)
        {
            TPPRINTF("Message format error.\n");
            return;
        }
        try
        {
            if (!cJSON_IsObject(rootJson))
            {
                TPPRINTF("Message format error.\n");
                throw 0;
            }

            cJSON *samplingJson = cJSON_GetObjectItem(rootJson, "sampling");
            if (NULL == samplingJson)
            {
                TPPRINTF("Message format error.\n");
                throw 0;
            }
            int sampling = (int)cJSON_GetNumberValue(samplingJson);

            if (m_dx5ii_device != nullptr)
            {
                m_dx5ii_device->callback.on_device_sampling_response(m_dx5ii_device->flutter_object, sampling);
            }
        }
        catch(int& e)
        {
        }
        cJSON_Delete(rootJson);
    }

    void Dx5iiDevice::responseCmdError(int session_id, int cmd)
    {
        mControllerClient.sendResponse(session_id, cmd, ERRCODE_CMD_UNSUPPORTED, "This command is not supported.", "{}");
    }
}

extern "C"
{
    long dx5ii_device_create(long flutter_object)
    {
        dx5ii_device_t *dx5ii_device = new dx5ii_device_t;
        Topping::Dx5iiDevice *dx5iiDevice = new Topping::Dx5iiDevice(dx5ii_device);
        dx5ii_device->handle = dx5iiDevice;
        dx5ii_device->flutter_object = flutter_object;
        return (long)dx5ii_device;
    }

    void dx5ii_device_destory(long native_object)
    {
        struct dx5ii_device_t *dx5ii_device = (struct dx5ii_device_t *)native_object;
        if (dx5ii_device != nullptr)
        {
            Topping::Dx5iiDevice *dx5iiDevice = (Topping::Dx5iiDevice *)dx5ii_device->handle;
            if (dx5iiDevice != nullptr)
            {
                delete dx5iiDevice;
            }
            delete dx5ii_device;
        }
    }

    void dx5ii_device_register_callback(long native_object, struct dx5ii_device_callback_t *callback)
    {
        struct dx5ii_device_t *dx5ii_device = (struct dx5ii_device_t *)native_object;
        if (dx5ii_device != nullptr)
        {
            dx5ii_device->callback = *callback;
        }
    }

    void dx5ii_device_connect(long native_object, long device)
    {
        struct dx5ii_device_t *dx5ii_device = (struct dx5ii_device_t *)native_object;
        if (nullptr == dx5ii_device)
        {
            return;
        }
        Topping::Dx5iiDevice *dx5iiDevice = (Topping::Dx5iiDevice *)dx5ii_device->handle;
        if (dx5iiDevice != nullptr)
        {
            Topping::BluetoothDevice bluetoothDevice((void *)device);
            dx5iiDevice->connect(bluetoothDevice);
        }
    }

    void dx5ii_device_disconnect(long native_object)
    {
        struct dx5ii_device_t *dx5ii_device = (struct dx5ii_device_t *)native_object;
        if (nullptr == dx5ii_device)
        {
            return;
        }
        Topping::Dx5iiDevice *dx5iiDevice = (Topping::Dx5iiDevice *)dx5ii_device->handle;
        if (dx5iiDevice != nullptr)
        {
            dx5iiDevice->disconnect();
        }
    }

    void dx5ii_device_verify(long native_object)
    {
        struct dx5ii_device_t *dx5ii_device = (struct dx5ii_device_t *)native_object;
        if (nullptr == dx5ii_device)
        {
            return;
        }
        Topping::Dx5iiDevice *dx5iiDevice = (Topping::Dx5iiDevice *)dx5ii_device->handle;
        if (dx5iiDevice != nullptr)
        {
            dx5iiDevice->verify();
        }
    }

    void dx5ii_device_power_on(long native_object, int is_on)
    {
        struct dx5ii_device_t *dx5ii_device = (struct dx5ii_device_t *)native_object;
        if (nullptr == dx5ii_device)
        {
            return;
        }
        Topping::Dx5iiDevice *dx5iiDevice = (Topping::Dx5iiDevice *)dx5ii_device->handle;
        if (dx5iiDevice != nullptr)
        {
            dx5iiDevice->powerOn(is_on);
        }
    }

    void dx5ii_device_set_device_name(long native_object, const char *name)
    {
        struct dx5ii_device_t *dx5ii_device = (struct dx5ii_device_t *)native_object;
        if (nullptr == dx5ii_device)
        {
            return;
        }
        Topping::Dx5iiDevice *dx5iiDevice = (Topping::Dx5iiDevice *)dx5ii_device->handle;
        if (dx5iiDevice != nullptr)
        {
            dx5iiDevice->setDeviceName(name);
        }
    }

    void dx5ii_device_set_volume(long native_object, int volume)
    {
        struct dx5ii_device_t *dx5ii_device = (struct dx5ii_device_t *)native_object;
        if (nullptr == dx5ii_device)
        {
            return;
        }
        Topping::Dx5iiDevice *dx5iiDevice = (Topping::Dx5iiDevice *)dx5ii_device->handle;
        if (dx5iiDevice != nullptr)
        {
            dx5iiDevice->setVolume(volume);
        }
    }

    void dx5ii_device_set_mute(long native_object, int is_mute)
    {
        struct dx5ii_device_t *dx5ii_device = (struct dx5ii_device_t *)native_object;
        if (nullptr == dx5ii_device)
        {
            return;
        }
        Topping::Dx5iiDevice *dx5iiDevice = (Topping::Dx5iiDevice *)dx5ii_device->handle;
        if (dx5iiDevice != nullptr)
        {
            dx5iiDevice->setMute(is_mute);
        }
    }

    void dx5ii_device_set_input_type(long native_object, int input_type)
    {
        struct dx5ii_device_t *dx5ii_device = (struct dx5ii_device_t *)native_object;
        if (nullptr == dx5ii_device)
        {
            return;
        }
        Topping::Dx5iiDevice *dx5iiDevice = (Topping::Dx5iiDevice *)dx5ii_device->handle;
        if (dx5iiDevice != nullptr)
        {
            dx5iiDevice->setInputType(input_type);
        }
    }

    void dx5ii_device_set_output_type(long native_object, int output_type)
    {
        struct dx5ii_device_t *dx5ii_device = (struct dx5ii_device_t *)native_object;
        if (nullptr == dx5ii_device)
        {
            return;
        }
        Topping::Dx5iiDevice *dx5iiDevice = (Topping::Dx5iiDevice *)dx5ii_device->handle;
        if (dx5iiDevice != nullptr)
        {
            dx5iiDevice->setOutputType(output_type);
        }
    }

    void dx5ii_device_enable_headphone(long native_object, int enable)
    {
        struct dx5ii_device_t *dx5ii_device = (struct dx5ii_device_t *)native_object;
        if (nullptr == dx5ii_device)
        {
            return;
        }
        Topping::Dx5iiDevice *dx5iiDevice = (Topping::Dx5iiDevice *)dx5ii_device->handle;
        if (dx5iiDevice != nullptr)
        {
            dx5iiDevice->enableHeadphone(enable);
        }
    }

    void dx5ii_device_set_headphone_gain(long native_object, int gain_type)
    {
        struct dx5ii_device_t *dx5ii_device = (struct dx5ii_device_t *)native_object;
        if (nullptr == dx5ii_device)
        {
            return;
        }
        Topping::Dx5iiDevice *dx5iiDevice = (Topping::Dx5iiDevice *)dx5ii_device->handle;
        if (dx5iiDevice != nullptr)
        {
            dx5iiDevice->setHeadphoneGain(gain_type);
        }
    }

    void dx5ii_device_set_diaplay_mode(long native_object, int diaplay_mode)
    {
        struct dx5ii_device_t *dx5ii_device = (struct dx5ii_device_t *)native_object;
        if (nullptr == dx5ii_device)
        {
            return;
        }
        Topping::Dx5iiDevice *dx5iiDevice = (Topping::Dx5iiDevice *)dx5ii_device->handle;
        if (dx5iiDevice != nullptr)
        {
            dx5iiDevice->setDisplayMode(diaplay_mode);
        }
    }

    void dx5ii_device_set_theme(long native_object, int theme)
    {
        struct dx5ii_device_t *dx5ii_device = (struct dx5ii_device_t *)native_object;
        if (nullptr == dx5ii_device)
        {
            return;
        }
        Topping::Dx5iiDevice *dx5iiDevice = (Topping::Dx5iiDevice *)dx5ii_device->handle;
        if (dx5iiDevice != nullptr)
        {
            dx5iiDevice->setTheme(theme);
        }
    }

    void dx5ii_device_set_power_trigger(long native_object, int trigger_type)
    {
        struct dx5ii_device_t *dx5ii_device = (struct dx5ii_device_t *)native_object;
        if (nullptr == dx5ii_device)
        {
            return;
        }
        Topping::Dx5iiDevice *dx5iiDevice = (Topping::Dx5iiDevice *)dx5ii_device->handle;
        if (dx5iiDevice != nullptr)
        {
            dx5iiDevice->setPowerTrigger(trigger_type);
        }
    }

    void dx5ii_device_set_balance(long native_object, int balance)
    {
        struct dx5ii_device_t *dx5ii_device = (struct dx5ii_device_t *)native_object;
        if (nullptr == dx5ii_device)
        {
            return;
        }
        Topping::Dx5iiDevice *dx5iiDevice = (Topping::Dx5iiDevice *)dx5ii_device->handle;
        if (dx5iiDevice != nullptr)
        {
            dx5iiDevice->setBalance(balance);
        }
    }

    void dx5ii_device_set_pcm_filter(long native_object, int filter_type)
    {
        struct dx5ii_device_t *dx5ii_device = (struct dx5ii_device_t *)native_object;
        if (nullptr == dx5ii_device)
        {
            return;
        }
        Topping::Dx5iiDevice *dx5iiDevice = (Topping::Dx5iiDevice *)dx5ii_device->handle;
        if (dx5iiDevice != nullptr)
        {
            dx5iiDevice->setPcmFilter(filter_type);
        }
    }

    void dx5ii_device_set_decode_type(long native_object, int decode_type)
    {
        struct dx5ii_device_t *dx5ii_device = (struct dx5ii_device_t *)native_object;
        if (nullptr == dx5ii_device)
        {
            return;
        }
        Topping::Dx5iiDevice *dx5iiDevice = (Topping::Dx5iiDevice *)dx5ii_device->handle;
        if (dx5iiDevice != nullptr)
        {
            dx5iiDevice->setDecodeMode(decode_type);
        }
    }

    void dx5ii_device_enable_audio_bluetooth(long native_object, int enable)
    {
        struct dx5ii_device_t *dx5ii_device = (struct dx5ii_device_t *)native_object;
        if (nullptr == dx5ii_device)
        {
            return;
        }
        Topping::Dx5iiDevice *dx5iiDevice = (Topping::Dx5iiDevice *)dx5ii_device->handle;
        if (dx5iiDevice != nullptr)
        {
            dx5iiDevice->enableAudioBluetooth(enable);
        }
    }

    void dx5ii_device_enable_bluetooth_aptx(long native_object, int enable)
    {
        struct dx5ii_device_t *dx5ii_device = (struct dx5ii_device_t *)native_object;
        if (nullptr == dx5ii_device)
        {
            return;
        }
        Topping::Dx5iiDevice *dx5iiDevice = (Topping::Dx5iiDevice *)dx5ii_device->handle;
        if (dx5iiDevice != nullptr)
        {
            dx5iiDevice->enableBluetoothAPTX(enable);
        }
    }

    void dx5ii_device_enable_remote(long native_object, int enable)
    {
        struct dx5ii_device_t *dx5ii_device = (struct dx5ii_device_t *)native_object;
        if (nullptr == dx5ii_device)
        {
            return;
        }
        Topping::Dx5iiDevice *dx5iiDevice = (Topping::Dx5iiDevice *)dx5ii_device->handle;
        if (dx5iiDevice != nullptr)
        {
            dx5iiDevice->enableRemote(enable);
        }
    }

    void dx5ii_device_set_multifunction_key(long native_object, int key_type)
    {
        struct dx5ii_device_t *dx5ii_device = (struct dx5ii_device_t *)native_object;
        if (nullptr == dx5ii_device)
        {
            return;
        }
        Topping::Dx5iiDevice *dx5iiDevice = (Topping::Dx5iiDevice *)dx5ii_device->handle;
        if (dx5iiDevice != nullptr)
        {
            dx5iiDevice->setMultifunctionKey(key_type);
        }
    }

    void dx5ii_device_set_usb_mode(long native_object, int usb_mode)
    {
        struct dx5ii_device_t *dx5ii_device = (struct dx5ii_device_t *)native_object;
        if (nullptr == dx5ii_device)
        {
            return;
        }
        Topping::Dx5iiDevice *dx5iiDevice = (Topping::Dx5iiDevice *)dx5ii_device->handle;
        if (dx5iiDevice != nullptr)
        {
            dx5iiDevice->setUsbMode(usb_mode);
        }
    }

    void dx5ii_device_set_screen_brightness(long native_object, int brightness_type)
    {
        struct dx5ii_device_t *dx5ii_device = (struct dx5ii_device_t *)native_object;
        if (nullptr == dx5ii_device)
        {
            return;
        }
        Topping::Dx5iiDevice *dx5iiDevice = (Topping::Dx5iiDevice *)dx5ii_device->handle;
        if (dx5iiDevice != nullptr)
        {
            dx5iiDevice->setScreenBrightness(brightness_type);
        }
    }

    void dx5ii_device_set_language(long native_object, int language)
    {
        struct dx5ii_device_t *dx5ii_device = (struct dx5ii_device_t *)native_object;
        if (nullptr == dx5ii_device)
        {
            return;
        }
        Topping::Dx5iiDevice *dx5iiDevice = (Topping::Dx5iiDevice *)dx5ii_device->handle;
        if (dx5iiDevice != nullptr)
        {
            dx5iiDevice->setLanguage(language);
        }
    }

    void dx5ii_device_reset_settings(long native_object)
    {
        struct dx5ii_device_t *dx5ii_device = (struct dx5ii_device_t *)native_object;
        if (nullptr == dx5ii_device)
        {
            return;
        }
        Topping::Dx5iiDevice *dx5iiDevice = (Topping::Dx5iiDevice *)dx5ii_device->handle;
        if (dx5iiDevice != nullptr)
        {
            dx5iiDevice->resetSettings();
        }
    }

    void dx5ii_device_restore_factory_settings(long native_object)
    {
        struct dx5ii_device_t *dx5ii_device = (struct dx5ii_device_t *)native_object;
        if (nullptr == dx5ii_device)
        {
            return;
        }
        Topping::Dx5iiDevice *dx5iiDevice = (Topping::Dx5iiDevice *)dx5ii_device->handle;
        if (dx5iiDevice != nullptr)
        {
            dx5iiDevice->restoreFactorySettings();
        }
    }

    void dx5ii_device_request_settings(long native_object)
    {
        struct dx5ii_device_t *dx5ii_device = (struct dx5ii_device_t *)native_object;
        if (nullptr == dx5ii_device)
        {
            return;
        }
        Topping::Dx5iiDevice *dx5iiDevice = (Topping::Dx5iiDevice *)dx5ii_device->handle;
        if (dx5iiDevice != nullptr)
        {
            dx5iiDevice->requestSettings();
        }
    }

    void dx5ii_device_request_sampling(long native_object)
    {
        struct dx5ii_device_t *dx5ii_device = (struct dx5ii_device_t *)native_object;
        if (nullptr == dx5ii_device)
        {
            return;
        }
        Topping::Dx5iiDevice *dx5iiDevice = (Topping::Dx5iiDevice *)dx5ii_device->handle;
        if (dx5iiDevice != nullptr)
        {
            dx5iiDevice->requestSampling();
        }
    }

    // --- 新增C接口函数实现 (DX5 III 新功能) ---
    void dx5ii_device_set_vu_meter_level(long native_object, int level)
    {
        struct dx5ii_device_t *dx5ii_device = (struct dx5ii_device_t *)native_object;
        if (nullptr == dx5ii_device)
        {
            return;
        }
        Topping::Dx5iiDevice *dx5iiDevice = (Topping::Dx5iiDevice *)dx5ii_device->handle;
        if (dx5iiDevice != nullptr)
        {
            dx5iiDevice->setVuMeterLevel(level);
        }
    }

    void dx5ii_device_set_vu_meter_display_mode(long native_object, int mode)
    {
        struct dx5ii_device_t *dx5ii_device = (struct dx5ii_device_t *)native_object;
        if (nullptr == dx5ii_device)
        {
            return;
        }
        Topping::Dx5iiDevice *dx5iiDevice = (Topping::Dx5iiDevice *)dx5ii_device->handle;
        if (dx5iiDevice != nullptr)
        {
            dx5iiDevice->setVuMeterDisplayMode(mode);
        }
    }

    void dx5ii_device_set_input_options(long native_object, int options)
    {
        struct dx5ii_device_t *dx5ii_device = (struct dx5ii_device_t *)native_object;
        if (nullptr == dx5ii_device)
        {
            return;
        }
        Topping::Dx5iiDevice *dx5iiDevice = (Topping::Dx5iiDevice *)dx5ii_device->handle;
        if (dx5iiDevice != nullptr)
        {
            dx5iiDevice->setInputOptions(options);
        }
    }

    void dx5ii_device_set_output_options(long native_object, int options)
    {
        struct dx5ii_device_t *dx5ii_device = (struct dx5ii_device_t *)native_object;
        if (nullptr == dx5ii_device)
        {
            return;
        }
        Topping::Dx5iiDevice *dx5iiDevice = (Topping::Dx5iiDevice *)dx5ii_device->handle;
        if (dx5iiDevice != nullptr)
        {
            dx5iiDevice->setOutputOptions(options);
        }
    }

    void dx5ii_device_set_volume_step(long native_object, int step)
    {
        struct dx5ii_device_t *dx5ii_device = (struct dx5ii_device_t *)native_object;
        if (nullptr == dx5ii_device)
        {
            return;
        }
        Topping::Dx5iiDevice *dx5iiDevice = (Topping::Dx5iiDevice *)dx5ii_device->handle;
        if (dx5iiDevice != nullptr)
        {
            dx5iiDevice->setVolumeStep(step);
        }
    }

    void dx5ii_device_set_polarity(long native_object, int polarity)
    {
        struct dx5ii_device_t *dx5ii_device = (struct dx5ii_device_t *)native_object;
        if (nullptr == dx5ii_device)
        {
            return;
        }
        Topping::Dx5iiDevice *dx5iiDevice = (Topping::Dx5iiDevice *)dx5ii_device->handle;
        if (dx5iiDevice != nullptr)
        {
            dx5iiDevice->setPolarity(polarity);
        }
    }

    void dx5ii_device_set_volume_memory_mode(long native_object, int mode)
    {
        struct dx5ii_device_t *dx5ii_device = (struct dx5ii_device_t *)native_object;
        if (nullptr == dx5ii_device)
        {
            return;
        }
        Topping::Dx5iiDevice *dx5iiDevice = (Topping::Dx5iiDevice *)dx5ii_device->handle;
        if (dx5iiDevice != nullptr)
        {
            dx5iiDevice->setVolumeMemoryMode(mode);
        }
    }

    void dx5ii_device_set_peq_memory_mode(long native_object, int mode)
    {
        struct dx5ii_device_t *dx5ii_device = (struct dx5ii_device_t *)native_object;
        if (nullptr == dx5ii_device)
        {
            return;
        }
        Topping::Dx5iiDevice *dx5iiDevice = (Topping::Dx5iiDevice *)dx5ii_device->handle;
        if (dx5iiDevice != nullptr)
        {
            dx5iiDevice->setPeqMemoryMode(mode);
        }
    }

    void dx5ii_device_set_main_key_function(long native_object, int function)
    {
        struct dx5ii_device_t *dx5ii_device = (struct dx5ii_device_t *)native_object;
        if (nullptr == dx5ii_device)
        {
            return;
        }
        Topping::Dx5iiDevice *dx5iiDevice = (Topping::Dx5iiDevice *)dx5ii_device->handle;
        if (dx5iiDevice != nullptr)
        {
            dx5iiDevice->setMainKeyFunction(function);
        }
    }

    void dx5ii_device_set_remote_a_key_function(long native_object, int function)
    {
        struct dx5ii_device_t *dx5ii_device = (struct dx5ii_device_t *)native_object;
        if (nullptr == dx5ii_device)
        {
            return;
        }
        Topping::Dx5iiDevice *dx5iiDevice = (Topping::Dx5iiDevice *)dx5ii_device->handle;
        if (dx5iiDevice != nullptr)
        {
            dx5iiDevice->setRemoteAKeyFunction(function);
        }
    }

    void dx5ii_device_set_remote_b_key_function(long native_object, int function)
    {
        struct dx5ii_device_t *dx5ii_device = (struct dx5ii_device_t *)native_object;
        if (nullptr == dx5ii_device)
        {
            return;
        }
        Topping::Dx5iiDevice *dx5iiDevice = (Topping::Dx5iiDevice *)dx5ii_device->handle;
        if (dx5iiDevice != nullptr)
        {
            dx5iiDevice->setRemoteBKeyFunction(function);
        }
    }
}