# 环境配置说明

本项目支持三种环境：开发环境(dev)、测试环境(staging)和生产环境(prod)。

## 环境特点

1. **开发环境 (dev)**
   - 应用名称: Topping Dev
   - 应用 ID 后缀: `.dev`
   - 版本名称后缀: `-dev`
   - API 地址: http://192.168.2.121:8080/

2. **测试环境 (staging)**
   - 应用名称: Topping Test
   - 应用 ID 后缀: `.staging`
   - 版本名称后缀: `-staging`
   - API 地址: http://test.api.topping.com:8080/

3. **生产环境 (prod)**
   - 应用名称: Topping
   - 无应用 ID 后缀
   - 无版本名称后缀
   - API 地址: http://49.232.108.42:8080/

## 版本号管理

本项目使用 `android/version.properties` 文件管理版本号，开发环境和测试环境使用相同的版本号，生产环境使用单独的版本号。

当需要更新版本号时，请修改 `android/version.properties` 文件：

```properties
# 开发和测试环境版本号
devVersionCode=2
devVersionName=1.0.1

# 生产环境版本号
prodVersionCode=1002
prodVersionName=1.0.0
```

注意：
- `versionCode` 是整数，每次发布新版本时需要增加
- `versionName` 是字符串，通常采用 `主版本.次版本.补丁版本` 的格式

### Android 和 iOS 版本号

对于 Android 和 iOS 平台，我们都使用 `android/version.properties` 文件中的版本号。在使用 `build_flavors.sh` 脚本构建应用时，脚本会自动设置正确的版本号。

注意：`pubspec.yaml` 文件中的版本号不再用于应用构建，但仍然保留作为参考。

## iOS 环境配置

与 Android 不同，iOS 需要额外的配置才能支持不同的环境。我们提供了一个脚本来自动配置 iOS 环境：

```bash
# 运行这个脚本来设置 iOS 环境
./setup_ios_environments.sh
```

这个脚本会：
1. 为不同环境创建 Xcode scheme（dev、staging 和 prod）
2. 为不同环境创建 Info.plist 文件，设置不同的应用名称
3. 修改 Podfile，支持不同环境的构建配置

配置完成后，您可以使用以下命令运行不同环境的 iOS 应用：

```bash
# 运行开发环境
flutter run --flavor dev --dart-define=FLAVOR=dev -t lib/main.dart

# 运行测试环境
flutter run --flavor staging --dart-define=FLAVOR=staging -t lib/main.dart

# 运行生产环境
flutter run --flavor prod --dart-define=FLAVOR=prod -t lib/main.dart
```

## 在 Android Studio 中配置运行环境

1. 打开 Android Studio，加载项目
2. 点击工具栏中的运行配置下拉菜单
3. 你应该能看到三个配置选项：
   - main-dev
   - main-staging
   - main-prod
4. 选择你想要运行的环境，然后点击运行按钮

如果你没有看到这些配置选项，请按照以下步骤手动添加：

1. 点击工具栏中的运行配置下拉菜单
2. 选择"Edit Configurations..."
3. 点击"+"按钮，选择"Flutter"
4. 配置以下内容：
   - Name: 输入配置名称，如"main-dev"
   - Dart entrypoint: 选择 lib/main.dart
   - Build flavor: 输入环境名称，如"dev"
5. 点击"Apply"和"OK"
6. 重复上述步骤，为其他环境创建配置

## 使用命令行运行

你也可以使用命令行运行不同环境的应用：

```bash
# 开发环境
flutter run --flavor dev --dart-define=FLAVOR=dev

# 测试环境
flutter run --flavor staging --dart-define=FLAVOR=staging

# 生产环境
flutter run --flavor prod --dart-define=FLAVOR=prod
```

## 构建不同环境的应用

### 使用命令行构建

使用以下命令构建不同环境的应用：

```bash
# 构建开发环境的APK
./build_flavors.sh dev apk

# 构建测试环境的APK
./build_flavors.sh staging apk

# 构建生产环境的APK
./build_flavors.sh prod apk
```

### 在 Android Studio 中构建

您也可以直接在 Android Studio 中构建不同环境的应用：

1. **选择构建变体（Build Variant）**：
   - 在 Android Studio 左侧边栏找到 "Build Variants" 选项卡（通常在窗口底部）
   - 点击打开后，您会看到一个表格，显示模块和构建变体
   - 在 "app" 模块行中，您可以从下拉菜单选择不同的构建变体：
     - `devDebug` 或 `devRelease`（开发环境）
     - `stagingDebug` 或 `stagingRelease`（测试环境）
     - `prodDebug` 或 `prodRelease`（生产环境）

2. **使用 Build 菜单打包**：
   - 选择好构建变体后，点击菜单 "Build" -> "Flutter"
   - 根据需要选择：
     - "Build APK" - 构建 APK 文件
     - "Build App Bundle" - 构建 AAB 文件（用于 Google Play 发布）
     - "Build iOS" - 构建 iOS 应用（需要在 Mac 上）

3. **找到构建输出**：
   - APK 文件通常位于：`build/app/outputs/flutter-apk/` 目录
   - 根据选择的构建变体，文件名会有所不同，例如：
     - `app-dev-release.apk`
     - `app-staging-release.apk`
     - `app-prod-release.apk`
